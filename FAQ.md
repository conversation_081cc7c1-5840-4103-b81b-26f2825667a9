## 项目常见问题

### 1. aerich migrate 遇到 no changes detected，但实际上 mygpt/models.py 中已经修改了模型

**触发条件：**
1. 先修改 mygpt/models.py 中的模型定义
2. 再运行 `aerich upgrade` 命令
3. 再运行 `aerich migrate` 命令

**原因分析：**
1. upgrade 命令会把 models.py 里最新的修改保存到数据库的 aerich 表里。
2. migrate 命令是通过检测 aerich 表里的记录来决定是否执行迁移的，而不是预期的 mygpt/models.py 文件最新的代码，尽管代码有变更。它仍然会认为没有变更。

**解决办法：**
如果是本地数据库的话：
1. 删除本地的数据库，再重新创建同名数据库
2. 先移除本地代码里对 mygpt/models.py 的修改
3. 重新走一遍 [REAEDME.md](REAEDME.md) 里 aerich 相关的步骤。
  1. aerich init -t mygpt.settings.TORTOISE_ORM
  2. aerich init-db
  3. aerich upgrade
4. 把 mygpt/models.py 的修改重新加回来，再运行 `aerich migrate` 命令。就不会提示 no changes detected 了。因为 aerich 表里没存储你最新的模型修改。
> 所以这里可能单独把 aerich 表里不应该存在的那些记录删掉，再执行 migrate 命令可能也行，不过我还没试。

P.S. 相关 issue：https://github.com/tortoise/aerich/issues/133