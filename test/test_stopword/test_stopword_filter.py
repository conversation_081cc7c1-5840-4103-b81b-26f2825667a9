import unittest

from mygpt.util.utils_stopword import filter_stopwords


class TestStopwordFilter(unittest.TestCase):
    def test_english_stopwords(self):
        # Basic stopword filtering
        text = "This is a test of the stopword filter in English."
        filtered = filter_stopwords(text, language="english")
        self.assertEqual(filtered, "test stopword filter English")

        # Case-insensitive stopword filtering
        text2 = "THE cat and the dog are in the house."
        filtered2 = filter_stopwords(text2, language="english")
        # "THE" should be filtered out regardless of case
        self.assertEqual(filtered2, "cat dog house")

        # No stopwords
        text3 = "python code unittest"
        filtered3 = filter_stopwords(text3, language="english")
        self.assertEqual(filtered3, "python code unittest")

    def test_chinese_stopwords(self):
        # Basic stopword filtering
        text = "这是一个中文的停用词过滤测试。"
        filtered = filter_stopwords(text, language="chinese")
        # "的" and "这" and "一个" are stopwords, so they should be removed
        self.assertEqual(filtered, "中文 停用词过滤测试。")

        # No stopwords
        text2 = "编程语言人工智能"
        filtered2 = filter_stopwords(text2, language="chinese")
        self.assertEqual(filtered2, "编程语言人工智能")

    def test_japanese_particles(self):
        # Basic particle filtering
        text = "これはテストです。あなたはどう思いますか？"
        filtered = filter_stopwords(text, language="japanese")
        # Should keep protected phrases and remove particles
        self.assertIn("テスト", filtered)
        self.assertIn("どう思いますか", filtered)
        # Should not contain isolated particles like "は", "です"
        self.assertNotIn("は", filtered.replace("これは", ""))
        self.assertNotIn("です", filtered.replace("テストです", ""))

        # No particles
        text2 = "人工知能と機械学習"
        filtered2 = filter_stopwords(text2, language="japanese")
        self.assertEqual(filtered2, "人工知能と機械学習")

    def test_auto_language_detection(self):
        # English
        text = "This is a test."
        filtered = filter_stopwords(text)
        self.assertEqual(filtered, "test")
        # Chinese
        text2 = "我有一个梦想"
        filtered2 = filter_stopwords(text2)
        self.assertEqual(filtered2, "梦想")
        # Japanese
        text3 = "これはテストです"
        filtered3 = filter_stopwords(text3)
        self.assertIn("テスト", filtered3)

    def test_edge_cases(self):
        # Empty string
        self.assertEqual(filter_stopwords(""), "")
        # Only stopwords
        self.assertEqual(filter_stopwords("the and or"), "")
        self.assertEqual(filter_stopwords("的了在是我有和就不人都一一个上也很到说要去你会着没有看好自己这", language="chinese"), "人")

    def test_chinese_multiword_stopwords(self):
        # 多字与单字停用词混合
        text = "我有一个梦想和目标"
        filtered = filter_stopwords(text, language="chinese")
        self.assertEqual(filtered, "梦想 目标")

    def test_mixed_language(self):
        # 中英文混合
        text = "This is 一个 test"
        filtered = filter_stopwords(text, language="auto")
        # 结果应去除英文和中文停用词
        self.assertIn("test", filtered)
        self.assertNotIn("一个", filtered)

    def test_punctuation_handling(self):
        # 标点符号不应被过滤
        text = "我有一个梦想！"
        filtered = filter_stopwords(text, language="chinese")
        self.assertEqual(filtered, "梦想！")

    def test_stopwords_at_edges(self):
        # 停用词在句首、句中、句尾
        text = "的梦想和"
        filtered = filter_stopwords(text, language="chinese")
        self.assertEqual(filtered, "梦想")

    def test_auto_language_detection_mixed(self):
        # 混合文本，自动检测
        text = "This 是 a 测试"
        filtered = filter_stopwords(text)
        # 英文和中文停用词都应被替换为空格
        self.assertIn("测试", filtered)
        self.assertNotIn("是", filtered)

    def test_language_priority_japanese(self):
        # 混合日文、中文、英文，优先按日文处理
        text = "This 是 a テスト"
        filtered = filter_stopwords(text)
        # 应用日语助词过滤，保留“テスト”，不做中文/英文停用词过滤
        self.assertIn("テスト", filtered)
        # 英文和中文停用词不会被去除
        self.assertIn("This", filtered)
        self.assertIn("是", filtered)

    def test_only_punctuation_and_spaces(self):
        # 标点符号不应被过滤，空格应被保留但合并
        self.assertEqual(filter_stopwords(" ，。！？ "), "，。！？")

    def test_only_stopwords_and_punctuation(self):
        # 停用词被替换为空格，标点保留
        self.assertEqual(filter_stopwords("的了，在。"), "， 。")

    def test_english_case_insensitivity(self):
        # Test that stopword detection is case-insensitive but output preserves case
        text = "The THE tHe and AND aN a"
        filtered = filter_stopwords(text, language="english")
        self.assertEqual(filtered, "")

    def test_chinese_nested_stopwords(self):
        # 多字与单字停用词混合，替换为空格
        text = "一个一个梦想"
        filtered = filter_stopwords(text, language="chinese")
        self.assertEqual(filtered, "梦想")

    def test_japanese_special_particles(self):
        text = "私は学生ですか？"
        filtered = filter_stopwords(text, language="japanese")
        # “ですか”被保护，“は”被去除
        self.assertIn("ですか", filtered)
        self.assertNotIn("は", filtered.replace("私は", ""))

    def test_long_text_with_repeated_stopwords(self):
        text = "的了在是我有和就不" * 5 + "梦想"
        filtered = filter_stopwords(text, language="chinese")
        self.assertEqual(filtered, "梦想")

    def test_numbers_and_mixed(self):
        self.assertEqual(filter_stopwords("123 456 的 789", language="chinese"), "123 456 789")
        self.assertEqual(filter_stopwords("one 1 two 2", language="english"), "one 1 two 2")

    def test_emoji_and_symbols(self):
        text = "我有一个梦想😊！"
        filtered = filter_stopwords(text, language="chinese")
        self.assertEqual(filtered, "梦想😊！")

    def test_auto_language_detection_unknown(self):
        # 无法识别的语言，原样返回
        text = "🙂👍"
        filtered = filter_stopwords(text)
        self.assertEqual(filtered, "🙂👍")

    def test_english_case_preservation(self):
        # Test that case is preserved in English text after stopword filtering
        text = "The Quick Brown Fox Jumps Over the Lazy Dog"
        filtered = filter_stopwords(text, language="english")
        self.assertEqual(filtered, "Quick Brown Fox Jumps Lazy Dog")
        
        # Test with mixed case stopwords
        text2 = "ThIs Is A TeSt Of ThE case PRESERVATION"
        filtered2 = filter_stopwords(text2, language="english")
        self.assertEqual(filtered2, "TeSt case PRESERVATION")
        
        # Test with technical terms and acronyms
        text3 = "The API and SDK for OpenSearch are available on GitHub"
        filtered3 = filter_stopwords(text3, language="english")
        self.assertEqual(filtered3, "API SDK OpenSearch available GitHub")
        
    def test_english_case_preservation_with_punctuation(self):
        # Test case preservation with punctuation
        text = "Hello, World! This is a TEST."
        filtered = filter_stopwords(text, language="english")
        # Punctuation is removed by the regex, but case should be preserved
        self.assertEqual(filtered, "Hello World TEST")
        
    def test_english_case_preservation_with_numbers(self):
        # Test case preservation with numbers
        text = "The 3 Quick Brown Foxes and 2 Lazy Dogs"
        filtered = filter_stopwords(text, language="english")
        self.assertEqual(filtered, "3 Quick Brown Foxes 2 Lazy Dogs")
        
    def test_opensearch_query_case_preservation(self):
        # Test specifically for OpenSearch query scenarios
        text = "How to Configure OpenSearch With Elasticsearch"
        filtered = filter_stopwords(text, language="english")
        # Should preserve "OpenSearch" and "Elasticsearch" capitalization
        # and filter out stopwords like "How", "to", "With"
        self.assertEqual(filtered, "Configure OpenSearch Elasticsearch")

if __name__ == "__main__":
    unittest.main()
