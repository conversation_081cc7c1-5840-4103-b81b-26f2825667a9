from mygpt.utils import is_url_match_rules


def test_is_url_match_rules():
    urls = ["https://sparticle.com"]
    rules = ["https://sparticle.com"]
    for url in urls:
        assert is_url_match_rules(url, rules) is True

    urls = ["https://sparticle.com"]
    rules = ["https://sparticle.com/**"]
    for url in urls:
        assert is_url_match_rules(url, rules) is True

    urls = ["https://sparticle.com"]
    rules = ["https://sparticle.com/*"]
    for url in urls:
        assert is_url_match_rules(url, rules) is True

    urls = [
        "https://sparticle.com/article2024-2",
        "https://sparticle.com/events2024-2A",
        "https://sparticle.com/summary2024-2B",
        "https://sparticle.com/articles/2024-2",
        "https://sparticle.com/users/2024-2A/profile"
        "https://sparticle.com/events/2024-2A/details",
    ]
    rules = ["https://sparticle.com/**"]
    for url in urls:
        assert is_url_match_rules(url, rules) is True

    rules = ["https://sparticle.com/**2024-2**"]
    for url in urls:
        assert is_url_match_rules(url, rules) is True

    urls = [
        "https://sparticle.com/article/2024-1.html",
        "https://sparticle.com/article/24/2024-1.html",
        "https://sparticle.com/article/20/24/2024-1.html",
    ]
    rules = ["https://sparticle.com/**/*24*.html"]
    for url in urls:
        assert is_url_match_rules(url, rules) is True

    urls = [
        "https://sparticle.com/article/20/24/2023-1.html",
    ]
    rules = ["https://sparticle.com/**/*24*.html"]
    for url in urls:
        assert is_url_match_rules(url, rules) is False


def test_is_url_match_rules_trailing_backslash():
    urls = ["https://redis.io/commands/xadd", "https://redis.io/commands/xadd/"]
    rules = ["https://redis.io/commands/*"]
    for url in urls:
        assert is_url_match_rules(url, rules) is True

    urls = ["https://redis.io/commands/xadd", "https://redis.io/commands/xadd/"]
    rules = ["https://redis.io/commands/xa*"]
    for url in urls:
        assert is_url_match_rules(url, rules) is True


def test_url_match_w_wo_final_slash():
    urls = ["https://redis.io/commands/xadd", "https://redis.io/commands/xadd/"]
    rules = ["https://redis.io/commands/xadd"]
    for url in urls:
        assert is_url_match_rules(url, rules) is True

    urls = ["https://redis.io/commands/xadd", "https://redis.io/commands/xadd/"]
    rules = ["https://redis.io/commands/xadd/"]
    for url in urls:
        assert is_url_match_rules(url, rules) is True

    # 规则 a.com 可以匹配 a.com/
    # 规则 a.com/ 可以匹配 a.com
    # 规则 a.com/b 可以匹配 a.com/b/
    # 规则 a.com/b/ 可以匹配 a.com/b
    rules = ["https://a.com"]
    urls = ["https://a.com", "https://a.com/"]
    for url in urls:
        assert is_url_match_rules(url, rules) is True

    rules = ["https://a.com/"]
    urls = ["https://a.com", "https://a.com/"]
    for url in urls:
        assert is_url_match_rules(url, rules) is True


# 定义 2种特殊字符串：* 和 **
#
# *， 匹配任意0个或多个非"/"的字符，可选匹配url最后一个 "/"
# a.com/* 子级目录/文件，可选匹配最后 "/"
# 示例：a.com/*
# 匹配：a.com， a.com/x， a.com/y/ ，a.com/abc.html, a.com/abc/, a.com/abc
# 不匹配：a.com/x/y，a.com/a/b.html
#
# **，匹配0个或多个任意字符
# a.com/**，爬 a.com整站，等价原来创建 bot 时输入 a.com
# a.com/**faq**，爬所有以a.com开始，path里面含有faq的url，如：
# a.com/faq, a.com/faq1, a.com/b/c/dfaq/e.html
def test_url_match_with_asterisk():
    rules = ["https://a.com/*"]
    urls = [
        "https://a.com",
        "https://a.com/",
        "https://a.com/x",
        "https://a.com/y/",
        "https://a.com/abc.html",
        "https://a.com/abc/",
        "https://a.com/abc",
    ]
    for url in urls:
        assert is_url_match_rules(url, rules) is True

    urls = ["https://a.com/x/y", "https://a.com/a/b.html"]
    for url in urls:
        assert is_url_match_rules(url, rules) is False


def test_url_match_with_double_asterisk():
    rules = ["https://a.com/**"]
    urls = [
        "https://a.com",
        "https://a.com/",
        "https://a.com/x",
        "https://a.com/y/",
        "https://a.com/abc.html",
        "https://a.com/abc/",
        "https://a.com/abc",
        "https://a.com/x/y",
        "https://a.com/a/b.html",
    ]
    for url in urls:
        assert is_url_match_rules(url, rules) is True

    rules = ["https://a.com/**faq**"]
    urls = ["https://a.com/faq", "https://a.com/faq1", "https://a.com/b/c/dfaq/e.html"]
    for url in urls:
        assert is_url_match_rules(url, rules) is True

    rules = ["https://a.com/**faq**"]
    urls = ["https://a.com/fa", "https://a.com/aq1", "https://a.com/b/c/daq/e.html"]
    for url in urls:
        assert is_url_match_rules(url, rules) is False


# @pytest.mark.asyncio
# async def test_refresh_file_list():
#     import mygpt.app
#
#     print(mygpt.app.ENABLE_PHOENIX)
#     dataset_id = "b383f100-aa90-46f8-bb80-bdaf14eef925"
#     file_id_list = [
#         "a74f42a2-b0ad-446b-aaee-859b26cac00e",
#         "47f99c65-36be-4a02-8ef6-827b04bc9444",
#     ]
#     for file_id in file_id_list:
#         await refresh_dataset_file(uuid.UUID(dataset_id), uuid.UUID(file_id))


# from tortoise.contrib.test import finalizer, initializer


# @pytest.mark.asyncio
# async def test_refresh_file_list():
#     initializer(["mygpt.models"], db_url="sqlite://:memory:")
#     try:
#         dataset_id = "b383f100-aa90-46f8-bb80-bdaf14eef925"
#         file_id_list = [
#             "a74f42a2-b0ad-446b-aaee-859b26cac00e",
#             "47f99c65-36be-4a02-8ef6-827b04bc9444",
#         ]
#         for file_id in file_id_list:
#             await refresh_dataset_file(uuid.UUID(dataset_id), uuid.UUID(file_id))
#     finally:
#         finalizer()
