import unittest
import uuid

import requests


class TestSendRoute(unittest.TestCase):

    def check_send(self, user_question: str, expected_result: dict):
        url = f"http://localhost:8000/v1/function_call_api/send/{uuid.UUID('5347a217-5abb-47ef-9936-f9f8bfe36240')}?user_question={user_question}"
        response = requests.post(
            url,
        )
        self.assertEqual(200, response.status_code)
        result = response.json()
        self.assertEqual(expected_result, result)

    def test_send_1(self):
        self.check_send(
            "江苏省句容维修点在哪",
            {
                "function_call_sta": True,
                "ask_content": {
                    "answer": "服务点名称：句容市康飞办公设备有限公司 \n"
                    " 位置：江苏省句容市人民路29-8号 \n"
                    " 联系电话：0511-85170033 \n"
                    " 服务时间：周一至周日  8:30  至17:30 \n"
                    "\n"
                    "\n",
                    "messages": None,
                },
            },
        )

    def test_send_2(self):
        self.check_send(
            "维修点在哪",
            {
                "function_call_sta": True,
                "ask_content": {"answer": "没有找到相关的服务站信息", "messages": None},
            },
        )

    def test_send_3(self):
        self.check_send(
            "服务中心联系方式",
            {
                "function_call_sta": True,
                "ask_content": {"answer": "没有找到相关的服务站信息", "messages": None},
            },
        )

    def test_send_4(self):
        self.check_send(
            "通化市东昌打印机坏了找谁",
            {
                "function_call_sta": True,
                "ask_content": {
                    "answer": "服务点名称：通化市东昌区强晟办公耗材销售中心 \n"
                    " 位置：通化市东昌区新华大街1-20号（立源小区2号楼20号车库） \n"
                    " 联系电话：13943537503 \n"
                    " 服务时间：周一至周五  9:00  至18:00 \n"
                    "\n"
                    "\n",
                    "messages": None,
                },
            },
        )

    def test_send_5(self):
        self.check_send(
            "我想找下你们服务中心，不知道怎么走",
            {
                "function_call_sta": True,
                "ask_content": {"answer": "没有找到相关的服务站信息", "messages": None},
            },
        )

    def test_send_6(self):
        self.check_send(
            "附近修打印机去哪,我在新疆维吾尔自治区",
            {
                "function_call_sta": True,
                "ask_content": {
                    "answer": "服务点名称：乌鲁木齐市优源佳业电子科技有限公司 C \n"
                    " 位置：新疆维吾尔自治区乌鲁木齐市红旗路天禧大厦1312室 \n"
                    " 联系电话：13565875954 \n"
                    " 服务时间：周一至周日10:00-19:30 \n"
                    "\n"
                    "\n"
                    "服务点名称：乌鲁木齐联奥科技乌苏分公司 C \n"
                    " 位置：新疆维吾尔自治区乌苏市新华路80号 \n"
                    " 联系电话：18083931177 \n"
                    " 服务时间：周一至周五  10:00  至19:00 \n"
                    "\n"
                    "\n"
                    "服务点名称：乌鲁木齐联奥科技有限公司喀什分公司 \n"
                    " 位置：新疆维吾尔自治区喀什市赛特电脑城3楼321室 \n"
                    " 联系电话：0998-2870490 \n"
                    " 服务时间：周一至周五  10:00  至19:00 \n"
                    "\n"
                    "\n"
                    "服务点名称：乌鲁木齐联奥科技伊宁分公司 C \n"
                    " 位置：新疆维吾尔自治区伊宁市新华西路566号滨河家园西区北门 \n"
                    " 联系电话：0999-8326558 \n"
                    " 服务时间：周一至周五  10:00  至19:00 \n"
                    "\n"
                    "\n"
                    "服务点名称：乌鲁木齐联奥科技阿克苏分公司 C \n"
                    " 位置：新疆维吾尔自治区阿克苏市建设路美家物流园C栋3001号 \n"
                    " 联系电话：0997-2513227 \n"
                    " 服务时间：周一至周五  10:00  至19:00 \n"
                    "\n"
                    "\n",
                    "messages": None,
                },
            },
        )

    def test_send_7(self):
        self.check_send(
            "客服电话多少",
            {
                "function_call_sta": True,
                "ask_content": {"answer": "没有找到相关的服务站信息", "messages": None},
            },
        )

    def test_send_8(self):
        self.check_send(
            "慈溪市的服务网点",
            {
                "function_call_sta": True,
                "ask_content": {
                    "answer": "服务点名称：阳光雨露信息技术服务（北京）有限公司宁波服务部慈溪 \n"
                    " 位置：浙江省宁波市慈溪市慈甬路410号 \n"
                    " 联系电话：0574-23881981 \n"
                    " 服务时间：周一至周日9:00-18:00 \n"
                    "\n"
                    "\n",
                    "messages": None,
                },
            },
        )

    def test_send_9(self):
        self.check_send(
            "珠海市修联想打印机几点上班，在哪",
            {
                "function_call_sta": True,
                "ask_content": {
                    "answer": "服务点名称：珠海万维商贸有限公司 C \n"
                    " 位置：广东省珠海市凤凰南路1030号瀚高大厦17层1701 \n"
                    " 联系电话：15907565923 \n"
                    " 服务时间：周一到周日9:00-18:00 \n"
                    "\n"
                    "\n",
                    "messages": None,
                },
            },
        )

    def test_send_11(self):
        self.check_send(
            "新购买的打印机，打印效果不清楚",
            {"function_call_sta": False, "ask_content": ""},
        )

    def test_send_12(self):
        self.check_send(
            "打印机卡纸了怎么弄", {"function_call_sta": False, "ask_content": ""}
        )

    def test_send_13(self):
        self.check_send(
            "K550型号电脑不好用了", {"function_call_sta": False, "ask_content": ""}
        )

    def test_send_14(self):
        self.check_send(
            "突然电脑无法打印", {"function_call_sta": False, "ask_content": ""}
        )

    def test_send_15(self):
        self.check_send(
            "M280W型号电脑打印失败", {"function_call_sta": False, "ask_content": ""}
        )

    def test_send_16(self):
        self.check_send("在不", {"function_call_sta": False, "ask_content": ""})

    def test_send_17(self):
        self.check_send(
            "搬家了更换了网络打印机K887",
            {"function_call_sta": False, "ask_content": ""},
        )

    def test_send_18(self):
        self.check_send(
            "打印机坏了,型号是M7360DN,问题安装驱动，自定义驱动安装说明",
            {"function_call_sta": False, "ask_content": ""},
        )

    def test_send_19(self):
        self.check_send(
            "如何小程序打印", {"function_call_sta": False, "ask_content": ""}
        )

    def test_send_20(self):
        self.check_send("你好", {"function_call_sta": False, "ask_content": ""})

    def test_send_31(self):
        self.check_send(
            "打印机坏了,型号是M7360DN,问题安装驱动，我在句容去哪维修",
            {
                "function_call_sta": True,
                "ask_content": {
                    "answer": "服务点名称：句容市康飞办公设备有限公司 \n"
                    " 位置：江苏省句容市人民路29-8号 \n"
                    " 联系电话：0511-85170033 \n"
                    " 服务时间：周一至周日  8:30  至17:30 \n"
                    "\n"
                    "\n",
                    "messages": None,
                },
            },
        )

    # def test_send_21(self):
    #     self.check_send("你好",
    #                     {
    #                         "function_call_sta": False,
    #                     })

    # def test_send_22(self):
    #     self.check_send("辣条10块钱",
    #                     {
    #                         "function_call_sta": True,
    #                         "ask_content": "提取的价格是10"
    #                     })
    #
    # def test_send_23(self):
    #     self.check_send("今天天气怎么样",
    #                     {
    #                         "function_call_sta": True,
    #                         "ask_content": "南京word"
    #                     })

    # def test_send_24(self):
    #     self.check_send("珠海的打印机维修点",
    #                     {
    #                         "function_call_sta": True,
    #                         "ask_content": [
    #                             "位置：广东省珠海市凤凰南路1030号瀚高大厦17层1701 联系电话：15907565923 服务时间：周一到周日9:00-18:00 服务点名称：珠海万维商贸有限公司 C"
    #                         ]
    #                     })
