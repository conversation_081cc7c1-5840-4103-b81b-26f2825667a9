import asyncio
import json
import re
from typing import List
import unittest
from mygpt.aes_util import AESCipher
from mygpt.enums import StreamingMessageDataType
from mygpt.schemata import StreamOut
from mygpt.settings import AES_SECRETS

from mygpt.utils import num_tokens_from_string

# from mygpt.utils import is_valid_url


class TestUtil(unittest.TestCase):
    def test_num_tokens_from_string(self):
        answer = ""
        print(num_tokens_from_string(answer))

    def test_num_tokens_from_string_3(self):
        text = """U3116-8GE8GF-I-DC\n産業用8ポートマネージド・イーサネット・スイッチ\n機能とメリット\n• 10/100BaseT(X)（RJ45コネクタ）\n• 10/100/1000Base-Tx (RJ45コネクタ)\n• 簡単に設置可能なコンパクトなサイズ\n• IP40保護等級の金属製筐体\n• -40 ～85℃の広い動作温度範囲\n認証\nCE FCC RoHS \n製品紹介\nU3112-8GE8GF-I は、8 個ギガビットイーサネットポート、 8個ギガSFP 光ポートで構成です。す\nべてのイーサネットスイッチは、完全なイーサネットスイッチングパフォーマンスと、 VLAN仮\n想LAN、STP/RSTPなどの強力なレイヤ2のネットワーク機能を備えています。 ネットワークの 冗長性、 SNMP\nネットワークの管理プロトコル、 IGMP マルチキャストパケット検出管理、 QoS、 ACL などの高度なレイヤ 2\nスイッチ機能を同時に提供する WEB/Telnet/Console/CLI など、さま\nざまなネットワーク管理方式に対応できしています。工業用デバイスおよびハウジング設計によ\nり、過酷なリングに確実に対応 長期にわたって安定、信頼性、安全な使用が可能です。交通、\n電力、電気通信、公共安全、エネルギー、銀行などの業界にも適応しています。\n仕様\nEthernet Interface\nIEEE 802.3 10Base-T\nIEEE 802.3u 100Base-Tx\nIEEE 802.3ab 1000Base-T\nIEEE 802.3z 1000Base-X\nConsole Ports: 1 RS-232 (RJ45)\nOptical Ports: 8 1000Base-X (SFP)\nElectrical Ports: 8 10/100/1000/Base-T (RJ45)\nFront of Indicator\nPower LED: PWR\nInterface LED: RJ45 (Link&ACT) Fiber (Link&ACT)\nPower Parameters\nPower Terminal: 4-pin 5.08mm-spacing plug-in terminal block\nFull load power consumption: < 8W\nOverload Protection: Support\nReverse Connection Protection: Support Redundancy Protection: Support\nStandards and Certifications\nEMI:\nFCC CFR47 Part 15, EN55022/CISPR22, Class A\nEMS:\nIEC61000-4-2 (ESD): ±8kV (contact), ±15kV (air) IEC61000-4-3 (RS): 10V/m (80MHz-2GHz)"""
        print(num_tokens_from_string(text))

    def test_decrpt(self):
        d = "DXauS+ySMRW1zmFkB4/0mLkshYQr+EzUFCjuwLU49ESLCUiNDMWuvGaOimLxBfCwEV5UQu3veC0Av+2UNj/nwg0fKK+H8xTnup0uPN8JEfUVgJf31lfjRFx2nuzjt2nqG0oqR4HhgUSoZsQCk1+Uw/iriamI9Ok0isG7DcyzhvjJUKd7XzKNLtM7BW36N+re18OORbkGJOpFTNM6HAA2cMhav2poIdQWMbSLafqk/Pw8xVU8DQwQGYLtF4wfej7DBpAugcFHrwGqxSMsN1/36IvZD/pR0byzBA0nACXR98FOBlyUewSBB3aGXMaK3q7vVGUm78pkI5ef1hlUSkNN+7yXF3fIGzeW8MDVkJMgZTLwDlj8LLe7qbTcvLP+FXvfUVrxD1DAZ/xwSLzU60VYHHYo98Xe499QVlHGFieGkad/RtGBBySuuaI9EoQO5nMSAUqGrFp2fiJT7p3s82kjyet1XSf5oAqMDq/D8xpQrQgSpG9GFz8ZD2k9aBf0WWrzaNTBeX0tC8VUHzKaEPFO/35N5uLOU0UM/CZUH0ezZhQZLyNv0LxLYArAHkQBgWhRGNOMtR5C8I5lvtZBgFPWqZMh0zc5S+rdpM0FK0q6zk0="
        print(AESCipher(AES_SECRETS).decrypt(d).decode("utf-8"))

    def test_01(self):
        def create_stream_out(
            message_type: StreamingMessageDataType, content: str | List | dict
        ):
            return StreamOut(
                message_id=None,
                message_type=message_type,
                content=content,
                use_faq=False,
                tokens=0,
            )

        rs = create_stream_out(
            message_type=StreamingMessageDataType.PLAIN_TEXT,
            content="""ください""",
        ).json(ensure_ascii=False)
        print(rs)
        segments = re.split(r"(\[\^(\d+)\^\])", rs)
        print(segments)
        charset = "utf-8"
        chunk = segments[0].encode(charset)
        print(str(chunk, charset))
