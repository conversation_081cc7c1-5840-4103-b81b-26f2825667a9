import pytest

from mygpt.openai_utils import (
    _keywords_generation_4_turbo,
    check_llm_can_answer,
)


@pytest.mark.asyncio
async def test__keywords_generation_4_turbo():
    # res = await _keywords_generation_4_turbo(
    #     "受信時でも通話料金はかかりますか？", ai_language="ja"
    # ) # 受信 通話料金
    res = await _keywords_generation_4_turbo(
        "hismobile是一家什么公司", ai_language="ja"
    )
    # res = await _keywords_generation_4_turbo("家族割はありますか？", ai_language="ja")
    print(res)


@pytest.mark.asyncio
async def test_check_llm_can_answer():
    qa_v_pairs = [
        [("circleo重视客户的数据安全吗", "重视"), (True, True)],
        [("新中国的成立时间", "我不知道。"), (False, False)],
    ]
    for qa_pair, v_pair in qa_v_pairs:
        print(qa_pair, v_pair)
        res = await check_llm_can_answer(qa_pair[0], qa_pair[1])
        assert res == v_pair


@pytest.mark.asyncio
async def test_check_llm_can_answer_1():
    qa_v_pairs = [
        [
            (
                "今天的天气",
                "很抱歉，我无法回答这个问题，因为我的知识中没有关于今天天气的信息。请问您还有其他问题吗？",
            ),
            (False, False),
        ],
    ]
    for qa_pair, v_pair in qa_v_pairs:
        # v_pair = (llm_can_answer, show_reference_button)
        print(qa_pair, v_pair)
        res = await check_llm_can_answer(qa_pair[0], qa_pair[1])
        assert res == v_pair
