import asyncio
import unittest

from mygpt.openai_utils import user_intent, user_intent_detect
from mygpt.prompt_history import PromptCache


class TestUserIntentDetect(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        # 在所有测试开始前执行一次。
        asyncio.run(PromptCache.setup(use_latest=True))

    async def _run_user_intent_detect(self, chat_history_str, question, ai_language):
        resp, total_tokens = await user_intent_detect(
            session_id="",
            # subject='【公式】Mecha-Tok（メチャ得）新幹線・飛行機代・ホテル代がとにかくメチャ得！',
            # description='新幹線・飛行機代・ホテル代がとにかくメチャ得！',
            question=question,
            chat_history_str=chat_history_str,
            ai_language=ai_language,
        )

        return resp, total_tokens

    def test_user_intent_detect_1(self):
        source_lang = "zh"
        chat_history_str = """
            User: 企业会员条件的人数限制？
            AI: 1团体最少5人，没有上限。
         """
        question = "企业会员条件的人数限制？"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "企业 会员 限制")
        print("实际输出：", query_key_words)
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "zh" in response_language
        assert "企业" in query_key_words

    def test_user_intent_detect_2(self):
        source_lang = "zh"
        chat_history_str = """
        Human: 上午上什么课？
        AI:物理。
        """
        question = "哪个老师讲的？"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "物理 老师")
        print("实际输出：", query_key_words)
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "zh" in response_language
        assert "物理" in query_key_words

    def test_user_intent_detect_3(self):
        source_lang = "zh"
        chat_history_str = """
        Human: 你们在线会议产品叫什么？ 
        AI: Felo Meet 
        """
        question = "价格怎么样？"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "Felo Meet 价格")
        print("实际输出：", query_key_words)
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "zh" in response_language
        assert "Felo Meet" in query_key_words

    def test_user_intent_detect_4(self):
        source_lang = "zh"
        chat_history_str = """
        Human: 你用的是什么手机？ 
        AI: iPhone14 
        """
        question = "多少钱买的？"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "iPhone14 价格")
        print("实际输出：", query_key_words)
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "zh" in response_language
        assert "iPhone14" in query_key_words

    def test_user_intent_detect_5(self):
        source_lang = "zh"
        chat_history_str = """
        Human: 哪天去珠海玩？ 
        AI: 6.12 
        """
        question = "那天天气怎么样？"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "6.12 珠海 天气")
        print("实际输出：", query_key_words)
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "zh" in response_language
        assert "6.12" in query_key_words

    def test_user_intent_detect_6(self):
        source_lang = "zh"
        chat_history_str = """
        Human: 张三去哪里发展了？ 
        AI: 他去上海了
        """
        question = "那里冷吗？"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "上海 天气")
        print("实际输出：", query_key_words)
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "zh" in response_language
        assert "上海" in query_key_words

    def test_user_intent_detect_7(self):
        source_lang = "zh"
        chat_history_str = """
        Human: 注册资本是多少？ 
        AI: 残念ながら、私は現在の知識ではこの質問に答えることができません。他の何かをお聞きいただけますか？
        """
        question = "注册资本是多少？"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "注册资本")
        print("实际输出：", query_key_words)
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "zh" in response_language
        assert "注册资本" in query_key_words

    def test_user_intent_detect_8(self):
        source_lang = "zh"
        chat_history_str = """
        Human: 邓小平是谁？
        AI: 抱歉，我不知道。
        """
        question = "邓小平是谁？"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "邓小平")
        print("实际输出：", query_key_words)
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "zh" in response_language
        assert "邓小平" in query_key_words

    def test_user_intent_detect_9(self):
        source_lang = "zh"
        chat_history_str = """
        Human: 怎么复活恐龙？ 
        AI: 要复活恐龙，首先需要获取恐龙的DNA。然而，由于时间的流逝以及环境条件的影响，恐龙DNA的保存变得极为困难。
        恐龙生活在数百万年前的地球上，其遗骸经过化石化和自然分解的过程，导致DNA的分解和破坏。
        目前科学家正在尝试通过在古代化石中寻找和提取DNA片段，或者通过利用与恐龙相关的现代生物的遗传信息来获取恐龙DNA。
        接下来，科学家将尝试重建恐龙的完整基因组，这是一个复杂而困难的过程，涉及到比对DNA序列、填补缺失的部分以及解决其他遗传学问题。
        此外，由于恐龙与现代生物的基因组差异很大，科学家还需要解决如何处理和纠正这些差异的挑战。
        一旦完整的恐龙基因组被重建，科学家可能会尝试使用基因编辑和合成技术来复制恐龙的基因组。这可能涉及到合成DNA序列，并将其插入到相关的细胞中。
        然而，这种基因编辑和合成的技术仍然面临许多技术和伦理挑战，需要更多的研究和发展。\n\n复活恐龙不仅仅是重
        """
        question = "怎么认识马云？"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "马云")
        print("实际输出：", query_key_words)
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "zh" in response_language
        assert "马云" in query_key_words

    def test_user_intent_detect_10(self):
        source_lang = "zh"
        chat_history_str = """
        Human: 微软2020年的营业额是多少？ 
        AI: 300亿美元。
        """
        question = "利润呢？"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "微软 2020年 利润")
        print("实际输出：", query_key_words)
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "zh" in response_language
        assert "微软" in query_key_words

    def test_user_intent_detect_11(self):
        source_lang = "en"
        chat_history_str = """
        Human: When did Microsoft Corp established?" 
        AI: 2020
        """
        question = "Who's the sponsor?"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "Microsoft 2020 sponsor")
        print("实际输出：", query_key_words)
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "en" in response_language
        assert "Microsoft" in query_key_words

    def test_user_intent_detect_12(self):
        source_lang = "en"
        chat_history_str = """"""
        question = "Can you help me?"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        answer = resp.get("answer", None)
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "Can you help me?")
        print("实际输出：", answer)
        print("resp:", resp)

        assert "BOT" in user_intent
        # assert 'help' in answer

    def test_user_intent_detect_13(self):
        source_lang = "en"
        chat_history_str = """"""
        question = "What can you do?"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        answer = resp.get("answer", None)
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "I am ...")
        print("实际输出：", answer)
        print("resp:", resp)

        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        assert "BOT" in user_intent
        assert "en" in response_language

    def test_user_intent_detect_14(self):
        source_lang = "zh"
        chat_history_str = """
        Human: 你们在线会议产品叫什么？ 
        AI: Felo Meet 
        """
        question = "How much？"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "Felo Meet price")
        print("实际输出：", query_key_words)
        print("resp：", resp)

        assert "COMPANY" in user_intent
        assert "en" in response_language
        assert "Felo Meet" in query_key_words

    def test_user_intent_detect_15(self):
        source_lang = "ja"
        chat_history_str = """
        Human: When did Microsoft Corp established?" 
        AI: 2020
        """
        question = "Who's the sponsor?"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "マイクロソフト社のスポンサー")
        print("实际输出：", query_key_words)
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "en" in response_language
        assert "マイクロソフト" in query_key_words

    def test_user_intent_detect_16(self):
        source_lang = "en"
        chat_history_str = """
            Human: 微软2020年的营业额是多少？ 
            AI: 300亿美元。
        """
        question = "Please tell me the functions of U1005-5FE-I-DC"
        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")

        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "U1005-5FE-I-DC functions")
        print("实际输出：", query_key_words)
        print("resp of 15", resp)

        assert "COMPANY" in user_intent
        assert "en" in response_language
        assert "function" in query_key_words.lower()

    # def test_user_intent_detect_16(self):
    #     source_lang = 'zh'
    #     chat_history_str = '''
    #         Human: what's you company's market cap?
    #         AI: 300 billion。
    #     '''
    #     question = '电脑没法打印了M260DW'
    #     resp, _ = asyncio.run(self._run_user_intent_detect(chat_history_str, question, source_lang))
    #     response_language = resp.get('question_language', None)
    #     query_key_words = resp.get("query_key_words")

    #     print('历史记录：')
    #     print(chat_history_str)
    #     print('提问：', question)
    #     print('预期输出：', 'M260DW 电脑 没法 打印')
    #     print('实际输出：', query_key_words)
    #     print('resp of 16', resp)

    #     assert 'zh' in response_language

    #     assert '法' in query_key_words     #无法 没法
    #     assert 'M260DW' in query_key_words
    #     assert '打印' in query_key_words

    # def test_user_intent_detect_17(self):
    #     source_lang = 'ja'
    #     chat_history_str = '''
    #         Human: feiji M(スマホ)な
    #         AI: 「feiji M(スマホ)な」という表現について、提供された文脈では詳細な情報が提供されていません。そのため、この表現に関する具体的な回答はできません。申し訳ありませんが、他の質問がありましたらお答えいたします。
    #         Human: what about everest mountain
    #         AI: Unfortunately, I can't answer this question based on my current knowledge. Can you please ask me something else ?
    #     '''
    #     question = '电脑没法打印了M260DW'
    #     resp, _ = asyncio.run(self._run_user_intent_detect(chat_history_str, question, source_lang))
    #     response_language = resp.get('question_language', None)
    #     query_key_words = resp.get("query_key_words")

    #     print('历史记录：')
    #     print(chat_history_str)
    #     print('提问：', question)
    #     print('预期输出：', 'M260DW 电脑 没法 打印')
    #     print('实际输出：', query_key_words)
    #     print('resp of 17', resp)

    #     # '电脑 无法 打印 M260DW'
    #     assert 'zh' in response_language
    #     assert 'M260DW' in query_key_words
    #     assert '印刷' in query_key_words

    #     prohibited_words = ['できない', '無'] #无法 没法
    #     for word in prohibited_words:
    #         if word in query_key_words:
    #             break
    #     else:
    #         raise AssertionError("Neither 'できない' nor '無' found in query_key_words.")

    def test_user_intent_detect_18(self):
        source_lang = "ja"
        chat_history_str = """
        """
        question = "社长是谁？"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "社长")
        print("实际输出：", query_key_words)
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "zh" in response_language

    def test_user_intent_detect_19(self):
        source_lang = "ja"
        chat_history_str = """
        """
        question = "我能问你什么问题？"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "问题")
        print("实际输出：", query_key_words)
        print("resp:", resp)

        assert "BOT" in user_intent
        assert "zh" in response_language

    def test_user_intent_detect_20(self):
        source_lang = "en"
        chat_history_str = """
        """
        question = "circleo是什么"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "问题")
        print("实际输出：", query_key_words)
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "zh" in response_language

    def test_user_intent_detect_21(self):
        source_lang = "ja"
        chat_history_str = """
        """
        question = "30歳男性にぴったりのがん保険、3軒お勧めしてください"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "问题")
        print("实际输出：", query_key_words)
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "ja" in response_language
        prohibited_words = ["がん", "ガン", "癌"]
        for word in prohibited_words:
            if word in query_key_words:
                break
        else:
            raise AssertionError("Neither 'がん' nor '癌症' found in query_key_words.")

    def test_user_intent_detect_22(self):
        source_lang = "ja"
        chat_history_str = """
        """
        question = "30岁男性合适的癌症保险，给我推荐三家"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "问题")
        print("实际输出：", query_key_words)
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "zh" in response_language
        prohibited_words = ["がん", "ガン", "癌"]
        for word in prohibited_words:
            if word in query_key_words:
                break
        else:
            raise AssertionError("Neither 'がん' nor '癌症' found in query_key_words.")

    def test_user_intent_detect_23(self):
        source_lang = "ja"
        chat_history_str = """
        """
        question = "DozoFreesh Fruits Tea Set Of 6Bags内容？"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "DozoFreesh Fruits Tea Set Of 6Bags 内容")
        print("实际输出：", query_key_words)
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "ja" in response_language
        assert "DozoFreesh Fruits Tea Set" in query_key_words

    def test_user_intent_detect_24(self):
        source_lang = "en"
        chat_history_str = """
        """
        question = "hello"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("预期输出：", "hello")
        print("resp:", resp)

        assert "en" in response_language
        assert "BOT" in user_intent

    def test_user_intent_detect_25(self):
        source_lang = "en"
        chat_history_str = """
        """
        question = "来客用車場はあるか（Welcome Park)英語で回答してください"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "en" in response_language

    def test_user_intent_detect_26(self):
        source_lang = "ja"
        chat_history_str = """
        """
        question = "amber pine サイズ"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        # query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        # {'query_key_words': 'amber pine サイズ', 'query_key_words_in_ja': 'アンバーパイン サイズ', 'question_language': 'en', 'user_intent': 'ASK_INFO_ABOUT_COMPANY'}
        # better not translate to アンバーパイン サイズ
        # assert 'amber pine' in query_key_words_in_ja
        print("resp:", resp)
        # it is en now, better be ja
        assert "COMPANY" in user_intent
        assert "ja" in response_language or "en" in response_language

    def test_user_intent_detect_27(self):
        source_lang = "ja"
        chat_history_str = """
        """
        question = "aslead とは"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        assert "aslead とは" in query_key_words
        print("resp:", resp)

        assert "COMPANY" in user_intent
        assert "ja" in response_language

    def test_CONCURRENT_INTENT_DETECT_env(self):
        pass

    def test_user_intent_detect_28(self):
        source_lang = "en"
        chat_history_str = """
        """
        question = "how are you"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        user_intent = resp.get("user_intent", None)
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        assert "ABOUT_BOT" in user_intent
        print("resp:", resp)

    def test_user_intent_detect_29(self):
        source_lang = "ja"
        chat_history_str = """
        """
        question = "aslead とは"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        user_intent = resp.get("user_intent", None)
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        assert "aslead とは" in query_key_words
        print("resp:", resp)
        assert "ABOUT_COMPANY" in user_intent
        assert "ja" in response_language

    def test_user_intent_detect_30(self):
        source_lang = "ja"
        chat_history_str = """
        """
        question = "His Mobileの440パッケージの5GBのデータはいくらですか？"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        user_intent = resp.get("user_intent", None)
        response_language = resp.get("question_language", None)
        query_key_words = resp.get("query_key_words")
        user_intent = resp.get("user_intent", None)
        print("历史记录：")
        print(chat_history_str)
        print("提问：", question)
        print("resp:", resp)
        assert "価格" in query_key_words
        assert "ABOUT_COMPANY" in user_intent
        assert "ja" in response_language

    def test_user_intent_detect_31(self):
        source_lang = "ja"
        chat_history_str = """
        """
        question = "H.I.S.Mobile株式会社所在地"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        response_language = resp.get("question_language", None)
        assert "ja" in response_language

    def test_user_intent_detect_32(self):
        source_lang = "ja"
        chat_history_str = """
        """
        question = "Freesh株式会社支払方法？"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        response_language = resp.get("question_language", None)
        assert "ja" in response_language

    def test_user_intent_detect_33(self):
        source_lang = "ja"
        chat_history_str = """
        """
        question = "S4210-8GE2XF-I-AC Power Consumption？"

        resp, _ = asyncio.run(
            self._run_user_intent_detect(chat_history_str, question, source_lang)
        )
        response_language = resp.get("question_language", None)
        assert "en" in response_language

    def test_user_intent_detect_34(self):
        question = "generate a random user"
        resp = asyncio.run(
            user_intent(question, None, None),
        )
        assert "ABOUT_COMPANY" in resp

    def test_user_intent_detect_35(self):
        question = "自分が臭いのではないかと気になります。"
        resp = asyncio.run(
            user_intent(question, None, None),
        )
        assert "ABOUT_COMPANY" in resp

    def test_user_intent_detect_36(self):
        question = "三个一流指的是什么"
        resp = asyncio.run(
            user_intent(question, None, None),
        )
        assert "ABOUT_COMPANY" in resp

    def test_user_intent_detect_37(self):
        question = "如何撬茶"
        resp = asyncio.run(
            user_intent(question, None, None),
        )
        assert "ABOUT_COMPANY" in resp

    def test_user_intent_detect_38(self):
        question = "hi,帮我生成15个名字"
        resp = asyncio.run(
            user_intent(question, None, None),
        )
        assert "ABOUT_COMPANY" in resp

    def test_user_intent_detect_39(self):
        question = "谢谢"
        resp = asyncio.run(
            user_intent(question, None, None),
        )
        assert "ABOUT_BOT" in resp

    def test_user_intent_detect_40(self):
        question = "再见"
        resp = asyncio.run(
            user_intent(question, None, None),
        )
        assert "ABOUT_BOT" in resp

    def test_user_intent_detect_40(self):
        question = "gbase 可以做什么"
        resp = asyncio.run(
            user_intent(question, None, None),
        )
        assert "ABOUT_COMPANY" in resp

    def test_user_intent_detect_41(self):
        question = "请把这个答案用中文再说一遍"
        resp = asyncio.run(
            user_intent(question, None, None),
        )
        assert "ABOUT_COMPANY" in resp

    def test_user_intent_detect_42(self):
        question = "你好"
        resp = asyncio.run(
            user_intent(question, None, None),
        )
        assert "ABOUT_COMPANY" in resp
