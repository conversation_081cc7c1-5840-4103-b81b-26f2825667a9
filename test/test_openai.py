import asyncio
import json
import unittest

from mygpt.file_embeddings import spliter_short_text
from mygpt.openai_utils import get_chat_history, save_chat_message
from mygpt.pinecone import get_pinecone_index
from langchain.chat_models import AzureChatOpenAI, ChatOpenAI
from langchain.chains import QAGener<PERSON><PERSON>hain
from langchain.schema import Document

from mygpt.utils import num_tokens_from_string


class TestOpenAi(unittest.TestCase):

    # def test_explain(self):
    #     messages = [
    #         {
    #             "role": "system",
    #             "content": QUESTION_EXPLAIN_SYSTEM_PROMPT.format(subject='Sentry Documentation')
    #         },
    #         {
    #             "role": "user",
    #             "content": QUESTION_EXPLAIN_USER_PROMPT.format(
    #                 question='参数sample_rate的作用是什么？',
    #             )
    #         }

    #     ]
    #     print(messages)
    #     rsp = asyncio.run(chat_acreate(messages, 800, openai_api_type='azure'))
    #     print(json.dumps(rsp.choices[0].message, ensure_ascii=False))

    # def test_explain_questoin(self):
    #     rsp = asyncio.run(explain_question('CircleoO', '你好'))
    #     print(rsp)

    #     def test_chat_acreate(self):
    #         summaries = '''
    # Content: Basic Options
    # SDKs are configurable using a variety of options. The options are largely standardized among SDKs, but there are some differences to better accommodate platform peculiarities. Options are set when the SDK is first initialized.

    # Options are passed to the init() function as optional keyword arguments:
    # import sentry_sdk

    # sentry_sdk.init(
    #     dsn="https://<EMAIL>/0",
    #     max_breadcrumbs=50,
    #     debug=True,

    #     # Set traces_sample_rate to 1.0 to capture 100%
    #     # of transactions for performance monitoring.
    #     # We recommend adjusting this value in production,
    #     traces_sample_rate=1.0,

    #     # By default the SDK will try to use the SENTRY_RELEASE
    #     # environment variable, or infer a git commit
    #     # SHA as release, however you may want to set
    #     # something more human-readable.
    #     # release="myapp@1.0.0",
    # )
    # Source: https://docs.sentry.io/platforms/python/configuration/options/'''
    #         messages = [
    #             {
    #                 "role": "system",
    #                 "content": SYSTEM_CONTENT
    #             },
    #             {
    #                 "role": "user",
    #                 "content": USER_CONTENT.format(
    #                     question='如何接入sentry sdk？',
    #                     summaries=summaries,
    #                 )
    #                 # 'content': USER_CONTENT.format(
    #                 #     question='python 如何进行基础配置？',
    #                 #     summaries='empty',
    #                 # )
    #             }

    #         ]
    #         print(messages)
    #         rsp = asyncio.run(chat_acreate(messages, 800, openai_api_type='azure'))
    #         print(json.dumps(rsp.choices[0].message, ensure_ascii=False))
    #         rsp = asyncio.run(chat_acreate(messages, 800, openai_api_type='azure'))
    #         print(json.dumps(rsp.choices[0].message, ensure_ascii=False))
    #         rsp = asyncio.run(chat_acreate(messages, 800, openai_api_type='azure'))
    #         print(json.dumps(rsp.choices[0].message, ensure_ascii=False))

    def test_embedding_acreate(self):
        messages = ["有多少房间可发布广告"]
        print(messages)
        rsp = asyncio.run(embeddings_acreate(messages, openai_api_type="azure"))
        print(rsp)

    # def test_pinecone(self):
    #     rs = asyncio.run(get_pinecone_index().update(
    #         id="ba87e716575eaa805ae2e8a69bf935b8020b32a7e09ad4b71e38b2c363e45ea7",
    #         set_metadata={'text': ''},
    #         namespace='89d7a4ec-caf5-4a16-9397-35c74188d36c',
    #     ))
    #     print(rs)

    # def test_get_pinecone_index(self):
    #     rs = get_pinecone_index().index.query(
    #         id='74901980-e0b5-4d35-8b17-071c04cc03b8',
    #         top_k=1,
    #         include_metadata=True,
    #         namespace='eb31a329-29b4-48be-99d2-6532f840fdfa',
    #     )

    #     #     index.query(
    #     #     id="648836d0fb313998f17017ebe9406a5a5e62c49ceb4a811cdb157796212a6a85",
    #     # )
    #     print(rs)

    # def test_get_by_metadata(self):
    #     rs = get_pinecone_index().index.query(
    #         top_k=10,
    #         include_metadata=True,
    #         namespace='2edb80b0-91fb-475b-9ec9-e456303b53b6',
    #         filter={'vectorfile': {'$eq': '77dde81c-7394-4fe4-8a6e-1a8e7e8ae10e'}}
    #     )
    #     print(rs)

    #     def test_genertor_question(self):
    #         text = '''
    # Meta Consulting
    # Enhance professional practice for business growth!
    # Through virtual consulting rooms, you can more easily establish online consultations with remote clients, and an immersive consulting experience can help you gain a clear insight into your clients and achieve growth in your consulting business
    # Consultation under Privacy
    # Reap rewards of successful remote consultation with secure privacy protection
    # Immersion Consulting
    # Automatically receive online consulting remote customers, and get a personalized consulting experience
    # AI for unprecedented insights
    # Unearth customer insights quickly and Identify customer possibilities and find the answer
    # Collaborate with remote customers
    # Make remote consulting more efficient, higher quality and accurate
    # Easily manage consulting business
    # Get more customers and bookings for your remote consultations
    # '''
    #         chain = QAGenerationChain.from_llm(ChatOpenAI(temperature=0))
    #         result = chain.run(text)
    #         print(result)

    def test_chat_history(self):
        answer = """
Meta Consulting
Enhance professional practice for business growth!
Through virtual consulting rooms, you can more easily establish online consultations with remote clients, and an immersive consulting experience can help you gain a clear insight into your clients and achieve growth in your consulting business
Consultation under Privacy
Reap rewards of successful remote consultation with secure privacy protection
Immersion Consulting
Automatically receive online consulting remote customers, and get a personalized consulting experience
AI for unprecedented insights
Unearth customer insights quickly and Identify customer possibilities and find the answer
Collaborate with remote customers
Make remote consulting more efficient, higher quality and accurate
Easily manage consulting business
Get more customers and bookings for your remote consultations
"""
        for i in range(100):
            asyncio.run(save_chat_message("for_test", f"what is circleo {i}?", answer))
        rs = asyncio.run(get_chat_history("for_test"))
        print(rs)
        print(num_tokens_from_string(rs, "p50k_base"))

    def test_spliter_short_text(self):
        text = """但从另一个角度来说，从生活逻辑和生活哲学的多样性上来说，这是比较单一的逻辑。这就造成了一个问题，如果你恰好生在这个时代，那么在你成长的过程当中，你所受到的影响，你见到的很多东西，这一切会让你产生一种感觉——就好像只有过上这样的生活才是正常的，才是世界上唯一正常的出路。\n\n学者袁长庚这样分析移居鹤岗的人。\n\n在鹤岗，我见到的这些人似乎都在寻找一个新的自己，它决定脱离我们大多数人身处的那个社会——那个要求房子、教育、工作、自我增值，分秒时间都要输出价值，\n\n好像总在填写一张绩效考核表的社会。人们不敢停歇，自我鞭笞，自我厌倦，有时还会服用阿普挫仑片来解决焦虑。\n\n来鹤岗的年轻人停了下来，像是进入了一种生活实验，实验品是他们自己。\n\n我不知道这是不是有点危险，但这是他们自由的选择。\n\n一次聚餐，我见到一对来鹤岗的恋人。他们做留学中介，正在尝试“数字游民”的生活。男孩说，他能察觉出来到这里的人们是在反对什么，但是他并不知道，这些人支持的和提倡的是什么。\n\n到远方去，到某地去，在路上，"""
        #         text = """Hi.\n\nI'm Harrison.\n\nHow? Are? You?\nOkay then f f f f.
        # This is a weird text to write, but gotta test the splittingggg some how.
        # Bye!\n\n-H."""
        doc = Document(page_content=text, metadata={"source": "test"})
        docs = spliter_short_text(doc)
        for d in docs:
            print(num_tokens_from_string(d.page_content, "p50k_base"))
            print(d)
        # print(docs)
