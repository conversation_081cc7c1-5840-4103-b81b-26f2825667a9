import unittest

from mygpt.opensearch_faq import OpenSearchFaqClient


class TestClassOpensearchFaq(unittest.TestCase):
    """Test class for OpensearchFaq."""

    def test_opensearch_faq(self):
        """Test case for OpensearchFaq."""
        client = OpenSearchFaqClient.get_instance()
        client.add_documents(
            dataset_id="test2",
            ids=["3", "4"],
            documents=[
                "彩色打印重影： 几种基色错开，不能正常重叠导致重影2",
                "迷惑メールの対策は行っていますか？",
            ],
        )

    def test_search(self):
        """Test case for OpensearchFaq."""
        client = OpenSearchFaqClient.get_instance()
        rs = client.search(
            dataset_ids=["test2"],
            query="彩色打印重影対策",
        )
        print(rs)
        rs = client.search(
            dataset_ids=["test"],
            query="迷惑メールの",
        )
        print(rs)

    def test_delete(self):
        """Test case for OpensearchFaq."""
        client = OpenSearchFaqClient.get_instance()
        client.delete_documents(
            ids=["3", "4", "5"],
        )
        # rs = client.search(
        #     dataset_ids=["test"],
        #     query="彩色打印重影対策",
        # )
        # print(rs)
