import unittest
import asyncio
from mygpt.opensearch_knowledge import OpenSearchKnowledgeClient


class TestClassOpensearchFaq(unittest.TestCase):

    def test_query_by_doc_ids(self):
        client = OpenSearchKnowledgeClient.get_instance()
        doc_ids = ["doc001", "doc003", "doc004"]
        rs = client.get_by_doc_ids(
            doc_ids=doc_ids,
        )
        print(rs)

    def test_query_by_project_id(self):
        client = OpenSearchKnowledgeClient.get_instance()
        project_id = "proj001"
        rs = client.get_by_project_id(
            project_id=project_id,
        )
        print(rs)
