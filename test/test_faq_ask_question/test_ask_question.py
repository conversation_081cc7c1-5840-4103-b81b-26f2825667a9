import unittest
import uuid

import requests


class TestAskQuestion(unittest.TestCase):

    def test_ask_gptbase_1(self):
        ask_gptbase_question(
            "我们家无线网怎么重置 ", "待机状态下长按wifi键，直到wifi键闪烁，网络被重置"
        )

    def test_ask_gptbase_2(self):
        ask_gptbase_question(
            "怎么重置无线网", "待机状态下长按wifi键，直到wifi键闪烁，网络被重置"
        )

    def test_ask_gptbase_3(self):
        ask_gptbase_question(
            "无线网重置的方案", "待机状态下长按wifi键，直到wifi键闪烁，网络被重置"
        )

    def test_ask_gptbase_4(self):
        ask_gptbase_question(
            "无线网坏了重置好弄", "待机状态下长按wifi键，直到wifi键闪烁，网络被重置"
        )

    def test_ask_gptbase_5(self):
        ask_gptbase_question(
            "Orchestrator の選択インターフェイスで、特定の端末のみを表示するようにフォルダーごとに設定できますか?",
            "unfortunately, i can't answer this question based on my current knowledge",
        )

    def test_ask_gptbase_6(self):
        ask_gptbase_question(
            "Orchestrator の選択インターフェイスで、特定の端末のみを表示するようにフォルダーごとに設定できますか?",
            "unfortunately, i can't answer this question based on my current knowledge",
        )

    def test_ask_gptbase_7(self):
        ask_gptbase_question(
            "我的联想打印机出现问题，打印机只能有打印，打印APP状态也在线",
            "unfortunately, i can't answer this question based on my current knowledge",
        )

    def test_ask_gptbase_8(self):
        ask_gptbase_question(
            "我的联想打印机出现问题，打印机只能有打印，显示状态离线了",
            "首先确保打印机状态正常，没有报错误，待机状态下快速按三次电源键可以打印配置页，说明设备没有故障；",
        )

    def test_ask_gptbase_9(self):
        ask_gptbase_question(
            "中国有多少人", "14亿", "d28c84ba-7e47-48f8-beaf-bc48cb2d9dcc"
        )

    def test_ask_gptbase_10(self):
        ask_gptbase_question(
            "如何去美国", "visa", "d28c84ba-7e47-48f8-beaf-bc48cb2d9dcc"
        )

    def test_ask_gptbase_11(self):
        ask_gptbase_question(
            "怎么去美国", "visa", "d28c84ba-7e47-48f8-beaf-bc48cb2d9dcc"
        )

    def test_ask_gptbase_12(self):
        ask_gptbase_question(
            "如何重置有线网",
            "unfortunately, i can't answer this question based on my current knowledge",
        )

    def test_ask_gptbase_13(self):
        ask_gptbase_question(
            "如何开启无线网",
            "unfortunately, i can't answer this question based on my current knowledge",
        )

    # def test_ask_gptbase_12(self):
    #     ask_gptbase_question(
    #         '美国去',
    #         "visa",
    #         'd28c84ba-7e47-48f8-beaf-bc48cb2d9dcc'
    #     )

    # def test_ask_gptbase_13(self):
    #     ask_gptbase_question(
    #         'iPhone14pro内存为256G多少钱',
    #         "9899",
    #         'd28c84ba-7e47-48f8-beaf-bc48cb2d9dcc'
    #     )
    #
    # def test_ask_gptbase_14(self):
    #     ask_gptbase_question(
    #         'iPhone14pro内存为256G多少钱',
    #         "9899",
    #         'd28c84ba-7e47-48f8-beaf-bc48cb2d9dcc'
    #     )
    #
    # def test_ask_gptbase_15(self):
    #     ask_gptbase_question(
    #         'iPhone14pro内存为256G多少钱',
    #         "9899",
    #         'd28c84ba-7e47-48f8-beaf-bc48cb2d9dcc'
    #     )


def ask_gptbase_question(
    question, assert_text, uid="43432b58-4e1f-477c-833b-50aa9fd0b1c5"
):
    url = "http://localhost:8000/v1/question/" + uid
    headers = {
        "Content-Type": "application/json",
    }
    session_id = uuid.uuid4()
    data = {
        "session_id": f"{session_id}",
        "stream": False,
        "simple_question": True,
        "with_source": True,
        "question": question,
    }
    response = requests.post(url, headers=headers, json=data)
    assert response.status_code == 200
    answer = response.json()["answer"].lower()
    if assert_text not in answer:
        print(
            f"Assertion Failed: "
            f"问题 => {question}\n"
            f"回答 => {answer} \n"
            f"应该要回答=> {assert_text}"
        )
    assert assert_text in answer
