import asyncio
import unittest

from mygpt.core.utils import task_first_completed


async def task_1():
    print("Task 1 started")
    await asyncio.sleep(1)
    print("Task 1 done")
    return 1


async def task_2():
    print("Task 2 started")
    await asyncio.sleep(2)
    print("Task 2 done")
    return 2


class TestCoreUtils(unittest.IsolatedAsyncioTestCase):
    async def test_task_first_completed(self):
        # ...
        tasks = [
            task_1(),
            task_2(),
            task_2(),
        ]

        rs = await task_first_completed(*tasks)
        self.assertEqual(rs, 1)
