import unittest

from mygpt.core.retriever import Retriever
from mygpt.core.vector_storage import VectorStorageFactory
from mygpt.enums import VectorStorageType, EMBEDDINGS_MODEL


class TestSimple(unittest.IsolatedAsyncioTestCase):
    async def test_retriever(self):
        retriever = Retriever(
            collection_name="20dfbc69-de7d-42e9-aaf0-594b727ab2f2",
            vector_storage=VectorStorageFactory.get_instance(
                VectorStorageType.QDRANT_ONE_COLLECTION
            ),
            embedding_model=EMBEDDINGS_MODEL.OPENAI,
        )
        result = await retriever.retrieve("what is circleo?")
        for item in result:
            print(f"{item.id} score:{item.score} context:{repr(item.text)}")
