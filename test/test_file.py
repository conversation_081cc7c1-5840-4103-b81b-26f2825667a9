import asyncio
import unittest
from asyncio.exceptions import TimeoutError

from mygpt.loader.document import DocumentLoader
from mygpt.loader.enums import UnstructuredType


async def call_api(message, result=1000, delay=3):
    print(message)
    await asyncio.sleep(delay)
    print(f"{message} done")
    return result


async def main1():
    task = asyncio.create_task(call_api("Calling API...", result=2000, delay=2))

    MAX_TIMEOUT = 3
    try:
        rs = await asyncio.wait_for(task, timeout=MAX_TIMEOUT)
        print(rs)
    except TimeoutError:
        print("The task was cancelled due to a timeout")
    await asyncio.sleep(10)


class TestFileToLineText(unittest.TestCase):
    def setUp(self):
        # initialize any resources needed for the tests
        pass

    def tearDown(self):
        # release any resources allocated during the tests
        pass

    def test_file_to_chunks_pdf(self):
        # expected_output = ['This is a sample text from a PDF file.']
        document_loader = DocumentLoader(
            "/var/folders/cj/gk3lx_qj5k573d0qc9m9qgd80000gn/T/tmpd4bopiou",
            type=UnstructuredType.PDF,
        )
        # paragraphs = document_loader.load()
        # with open("/Users/<USER>/Downloads/out/Getting-Things-Done-pdf-free-download.txt", "w") as file:
        #     for p in paragraphs:
        #         file.write(f'{p}\n\n')
        # [print(len(p)) ]
        # print(len(paragraphs))
        # self.assertEqual(paragraphs, expected_output)

    def test_file_to_chunks_docx(self):
        document_loader = DocumentLoader(
            "/Users/<USER>/Downloads/03a70cd324284035ad5d70f0a14ed34c.docx"
        )
        # paragraphs = document_loader.load()
        # print(paragraphs)

    def test_file_to_chunks_txt(self):
        document_loader = DocumentLoader(
            "/Users/<USER>/Downloads/2023年政府工作报告.txt"
        )
        # paragraphs = document_loader.load()
        # for para in paragraphs:
        #     print(para)

    def test_file_to_chunks_invalid_extension(self):
        document_loader = DocumentLoader("")
        # with self.assertRaisesRegexp(ValueError,
        #                              "Invalid file extension: '.csv'"):
        #     document_loader.load()

    def test_file_to_chunks_timeout(self):
        # pass
        document_loader = DocumentLoader(
            "/Users/<USER>/Downloads/人間力ハンドブック220920.txt"
        )
        # chunks, = asyncio.run(document_loader.async_load())
        # print(chunks)


if __name__ == "__main__":
    unittest.main()
