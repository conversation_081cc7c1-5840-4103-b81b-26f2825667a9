import unittest
import uuid
from mygpt.core.embedding import Embedding<PERSON>actory
from mygpt.enums import EMBEDDINGS_MODEL, LLM_PROVIDER, OpenAIModel, VectorStorageType


from mygpt.core.vector_storage import VectorStorageFactory
from langchain.schema import Document


class TestVectorStorage(unittest.IsolatedAsyncioTestCase):

    async def test_index_query_v2(self):
        query_key_words_embeddings = await embedding_questions(
            ["普洱茶产地在哪里？"], EMBEDDINGS_MODEL.OPENAI
        )

        storage = get_vector_storage(VectorStorageType.QDRANT_ONE_COLLECTION)
        response = await storage.index_query_v2(
            collection_name="18771e60-4cca-47e8-bbc8-d65536a2c0a5",
            vectors=query_key_words_embeddings[0],
        )

        print(response)

    async def test_create_dataset(self):
        vector_size = 771
        collection_name = f"gptbase_col_{vector_size}"
        storage = await VectorStorageFactory.create_collection(
            storage_type=VectorStorageType.QDRANT,
            host="localhost",
            grpc_port=6334,
            collection_name=collection_name,
            vector_size=vector_size,
        )
        embeddings = EmbeddingFactory.get_instance(
            llm_provider=LLM_PROVIDER.OPENAI,
            model_name="text-embedding-3-large",
            async_mode=False,
            dimensions=vector_size,
        )

        storage.from_documents(
            embeddings=embeddings,
            collection_name=collection_name,
            group_id="001",
            documents=[
                Document(
                    page_content="普洱茶产地在哪里？",
                )
            ],
        )

    async def test_save_points(self):
        embedding_model = EMBEDDINGS_MODEL.OPENAI
        model_name = OpenAIModel.TEXT_EMBEDDING_ADA_002
        embeddings = EmbeddingFactory.get_instance(
            llm_provider=embedding_model,
            async_mode=False,
            model_name=model_name,
        )
        await VectorStorageFactory.save_points(
            embeddings=embeddings,
            doc_ids=[str(uuid.uuid4())],
            documents=[Document(page_content="普洱茶产地在哪里？")],
            group_id="002",
            host="localhost",
            grpc_port=6334,
            storage_type=VectorStorageType.QDRANT,
        )

    async def test_single(self):
        vector_size = None
        storage = await VectorStorageFactory.create_collection(
            VectorStorageType.QDRANT,
            collection_name="004682e6-5877-4130-9fd6-72a28aa8ec1f",
            vector_size=vector_size,
        )

        embeddings = EmbeddingFactory.get_instance(
            llm_provider=LLM_PROVIDER.OPENAI,
            model_name=OpenAIModel.TEXT_EMBEDDING_ADA_002,
            dimensions=vector_size,
        )

        arrs = await embeddings.aembed_documents(["Generator 函数"])
        rs = await storage.index_query(
            collection_name="004682e6-5877-4130-9fd6-72a28aa8ec1f",
            vectors=arrs[0],
        )
        print(rs)
