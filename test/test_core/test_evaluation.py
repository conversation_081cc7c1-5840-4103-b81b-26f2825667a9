from langchain_core.documents import Document
import os
import re
import time
import unittest
import uuid
from mygpt.core.embedding import Embedding<PERSON>actory
from mygpt.core.evaluation import Evaluation, generate_dataset
from llama_index.llms.anthropic import Anthropic

from mygpt.core.retriever import CustomRetriever, Retriever
from llama_index.core.evaluation import (
    RetrieverEvaluator,
    EmbeddingQAFinetuneDataset,
    get_retrieval_results_df,
)

from mygpt.core.vector_storage import VectorStorageFactory
from mygpt.core.entity import speed_test_data
from mygpt.enums import LLM_PROVIDER, OpenAIModel

anthropic_api_key = "************************************************************************************************************"


class TestEvaluation(unittest.IsolatedAsyncioTestCase):
    async def test_evaluate_gptbase_dataset(self):
        llm = Anthropic(model="claude-3-opus-20240229", api_key=anthropic_api_key)
        path = "E:\workspace\\felo-mygpt\data"
        dataset_id = "09dae22c-d6dd-4973-87a1-d02548894f31"
        max_retires = 100
        while max_retires > 0:
            try:
                ea = Evaluation(dataset_id)
                await ea.generate_qa_pairs(llm, 500)
                break
            except Exception as e:
                print(e)
                time.sleep(5)
                max_retires -= 1
                continue
        custom_retriever = CustomRetriever(
            retriever=Retriever.get_instance(),
            dataset_ids=[dataset_id],
        )
        await ea.aevaluate_dataset(
            f"{path}\\{dataset_id}_qa_pairs.json",
            custom_retriever,
            save_path=f"{path}\\{dataset_id}_retrieval_results.csv",
        )

    async def test_large_gptbase_dataset(self):
        path = "E:\workspace\\felo-mygpt\data"
        dataset_id = "09dae22c-d6dd-4973-87a1-d02548894f31"
        storage = VectorStorageFactory.get_instance(
            host="localhost",
            grpc_port=6334,
        )
        ea = Evaluation(dataset_id)
        csv_path = f"{path}\\{dataset_id}.csv"
        # await ea.save_csv_database(
        #     csv_path=csv_path,
        #     storage=storage,
        # )
        custom_retriever = CustomRetriever(
            retriever=Retriever.get_instance(),
            dataset_ids=[dataset_id],
            model_name="text-embedding-3-large",
        )
        await ea.aevaluate_dataset(
            f"{path}\\{dataset_id}_qa_pairs.json",
            custom_retriever,
            save_path=f"{path}\\{dataset_id}_retrieval_results_large.csv",
        )

    async def test_evaluate(self):
        while True:
            try:
                ea = Evaluation("09dae22c-d6dd-4973-87a1-d02548894f31")

                llm = Anthropic(
                    model="claude-3-opus-20240229", api_key=anthropic_api_key
                )
                await ea.generate_qa_pairs(llm, 500)
                break
            except Exception as e:
                print(e)
                time.sleep(5)
                continue

    async def test_save_dataset(self):
        ea = Evaluation("bcddcb88-f5cd-45b0-add8-96672d5235df")
        storage = VectorStorageFactory.get_instance(
            host="localhost",
            grpc_port=6334,
        )
        await ea.save_csv_database(
            csv_path="./data/bcddcb88-f5cd-45b0-add8-96672d5235df.csv",
            storage=storage,
        )

    async def test_save_dataset_2(self):
        ea = Evaluation("longbench")
        storage = VectorStorageFactory.get_instance(
            host="localhost",
            grpc_port=6334,
        )
        await ea.save_database(
            dataset_path="./data/longbench_qa_pairs.json",
            storage=storage,
            collection_name="gptbase_col_1536",
        )

    async def test_save_dataset_3(self):
        ea = Evaluation("longbench")
        storage = VectorStorageFactory.get_instance(
            host="localhost",
            grpc_port=6334,
        )
        await ea.save_database(
            dataset_path="./data/longbench_qa_pairs.json",
            storage=storage,
            collection_name="gptbase_col",
            model_name="text-embedding-3-large",
        )

    async def test_aevaluate_dataset(self):
        custom_retriever = CustomRetriever(
            retriever=Retriever.get_instance(),
            dataset_ids=["bcddcb88-f5cd-45b0-add8-96672d5235df"],
        )
        ea = Evaluation("bcddcb88-f5cd-45b0-add8-96672d5235df")
        await ea.aevaluate_dataset(
            f"./data/bcddcb88-f5cd-45b0-add8-96672d5235df_qa_pairs.json",
            custom_retriever,
        )

    async def test_aevaluate_dataset_2(self):
        retriever = Retriever.get_instance(
            host="localhost",
            grpc_port=6334,
        )
        dataset_id = "longbench"
        custom_retriever = CustomRetriever(
            retriever=retriever,
            collection_name="gptbase_col_1536",
            dataset_ids=[dataset_id],
        )
        ea = Evaluation(dataset_id)
        path = "E:\workspace\\felo-mygpt\data"
        await ea.aevaluate_dataset(
            f"{path}\longbench_qa_pairs.json",
            custom_retriever,
            save_path=f"{path}\{dataset_id}_retrieval_results.csv",
        )

    async def test_aevaluate_dataset_large(self):
        retriever = Retriever.get_instance(
            host="localhost",
            grpc_port=6334,
        )
        dataset_id = "longbench"
        custom_retriever = CustomRetriever(
            retriever=retriever,
            collection_name="gptbase_col",
            dataset_ids=[dataset_id],
            model_name="text-embedding-3-large",
        )
        ea = Evaluation(dataset_id)
        path = "E:\workspace\\felo-mygpt\data"
        await ea.aevaluate_dataset(
            f"{path}\longbench_qa_pairs.json",
            custom_retriever,
            save_path=f"{path}\{dataset_id}_retrieval_results.csv",
        )

    async def test_01(self):
        custom_retriever = CustomRetriever(retriever=Retriever.get_instance())
        retriever_evaluator = RetrieverEvaluator.from_metric_names(
            ["mrr", "hit_rate"], retriever=custom_retriever
        )
        qa_dataset = EmbeddingQAFinetuneDataset.from_json(
            f"./data/bcddcb88-f5cd-45b0-add8-96672d5235df_qa_pairs.json"
        )
        eval_results = await retriever_evaluator.aevaluate_dataset(qa_dataset)
        data = get_retrieval_results_df(
            names=["retriever"],
            results_arr=[eval_results],
            metric_keys=["mrr", "hit_rate"],
        )
        print(data)
        data.to_csv("./data/retrieval_results.csv", index=False)

    async def test_02(self):
        from datasets import load_dataset

        dataset = load_dataset("facebook/belebele")
        print(dataset["zho_Hans"][0])
        queries = {}
        corpus = {}
        content_to_id = {}
        relevant_docs = {}
        for item in dataset["zho_Hans"]:
            content = item["flores_passage"]
            if content not in content_to_id:
                doc_id = uuid.uuid4().hex
                corpus[doc_id] = content
                content_to_id[content] = doc_id
            else:
                doc_id = content_to_id[content]
            query_id = uuid.uuid4().hex
            queries[query_id] = item["question"]
            relevant_docs[query_id] = [doc_id]
        qa_dataset = EmbeddingQAFinetuneDataset(
            queries=queries, corpus=corpus, relevant_docs=relevant_docs
        )
        qa_dataset.save_json("./data/belebele_qa_pairs.json")

    async def test_03(self):
        from datasets import load_dataset

        dataset = load_dataset("THUDM/LongBench", "passage_retrieval_zh")
        print(dataset["test"][0])
        queries = {}
        corpus = {}
        relevant_docs = {}
        for item in dataset["test"]:
            input = item["input"]
            query_id = uuid.uuid4().hex
            queries[query_id] = input
            context = item["context"]
            # 使用正则表达式"段落\d+："作为分隔符拆分字符串
            paragraphs = re.split("段落\d+：", context)
            answers = item["answers"]
            # 遍历返回的列表，打印每个段落
            for i, paragraph in enumerate(paragraphs):
                if paragraph:  # 忽略空段落
                    doc_id = uuid.uuid4().hex
                    corpus[doc_id] = paragraph.strip()
                    if f"段落{i}" in answers:
                        if query_id in relevant_docs:
                            relevant_docs[query_id].append(doc_id)
                        else:
                            relevant_docs[query_id] = [doc_id]
        qa_dataset = EmbeddingQAFinetuneDataset(
            queries=queries, corpus=corpus, relevant_docs=relevant_docs
        )
        qa_dataset.save_json("./data/longbench_qa_pairs.json")

    async def test_retriever_speed_check(self):
        dataset_id = "bcddcb88-f5cd-45b0-add8-96672d5235df"
        queries = speed_test_data[dataset_id]
        ea = Evaluation(dataset_id)
        retriever = Retriever.get_instance()
        await ea.retriever_speed_check(queries, retriever)

    async def test_to_large(self):
        dataset_id = "e6796c65-edd1-4528-9825-3d4e2cf01b25"
        data = await generate_dataset(dataset_id)
        docs, doc_ids = [], []
        for item in data:
            context = item["content"]
            doc_id = item["id"]
            docs.append(
                Document(
                    page_content=context,
                    metadata=item["metadata"],
                )
            )
            doc_ids.append(doc_id)
        model_name = OpenAIModel.TEXT_EMBEDDING_3_LARGE
        embeddings = EmbeddingFactory.get_instance(
            provider=LLM_PROVIDER.OPENAI,
            async_mode=False,
            model_name=model_name,
        )
        storage = VectorStorageFactory.get_instance(
            host="localhost",
            grpc_port=6334,
        )
        # 把docs 拆成多个sub_docs，每个sub_docs的长度为100
        sub_docs = []
        sub_doc_ids = []
        for i in range(0, len(docs), 100):
            sub_docs.append(docs[i : i + 100])
            sub_doc_ids.append(doc_ids[i : i + 100])
        print(len(sub_docs))
        for idx, item in enumerate(sub_docs):
            step = 5
            while step > 0:
                try:
                    storage.from_documents(
                        embeddings=embeddings,
                        collection_name="gptbase_col_large",
                        group_id=dataset_id,
                        documents=item,
                        ids=sub_doc_ids[idx],
                    )
                    break
                except Exception as e:
                    print(e)
                    step -= 1
                    continue
