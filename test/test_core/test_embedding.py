import time
import unittest
from typing import Optional
import uuid

import numpy as np
import pandas as pd
from llama_index.core.evaluation import (
    MRR,
    EmbeddingQAFinetuneDataset,
    HitRate,
    RetrieverEvaluator,
    generate_question_context_pairs,
)
from llama_index.core.schema import TextNode
from llama_index.llms.anthropic import Anthropic
from qdrant_client.conversions.conversion import GrpcToRest
from mygpt.core.embedding import EmbeddingFactory

from mygpt.core.retriever import Rerank, Retriever
from mygpt.core.vector_storage import VectorStorageFactory
from mygpt.enums import EMBEDDINGS_MODEL, LLM_PROVIDER, OpenAIModel
from mygpt.models import EmbeddingParams
from mygpt.settings import COHERE_API_KEY
from langchain_core.documents import Document

anthropic_api_key = "************************************************************************************************************"

QA_GENERATE_PROMPT_TMPL = """\
Context information is below.

---------------------
{context_str}
---------------------

Given the context information and not prior knowledge. \
generate only questions based on the below query. 

You are a Professor. Your task is to setup 1 questions \
for an upcoming quiz/examination. The questions should be \
diverse in nature across the document. \
The questions should not contain options, \
not start with Q1/ Q2. Restrict the questions \
to the context information provided.\
The questions language should base on the context information provided.

Question 1:\
"""


# 异步函数，用于生成数据集的CSV文件


async def generate_dataset_csv(
    dataset_id: str,  # 数据集ID
    data: Optional[list] = None,  # 可选的数据列表，默认为None
    save_path: str = "./data",  # 保存路径，默认为'./data'
    offset: Optional[str] = None,  # 可选的偏移量，默认为None
    limit: int = 100,  # 限制获取的数据量，默认为100
):

    # 获取VectorStorageFactory的实例
    storage = VectorStorageFactory.get_instance()

    # 定义过滤器
    filters = {
        "must": [{"key": "group_id", "match": {"any": [dataset_id]}}],
        "must_not": [
            {
                "key": "metadata.short_chunk",
                "match": {
                    "value": True,
                },
            }
        ],
    }

    # 从存储中索引点
    result = await storage.index_points(
        collection_name="gptbase_col",
        offset=offset,
        limit=limit,
        filters=filters,
        with_vectors=False,
    )

    # 将结果的下一页偏移量转换为字符串
    try:
        next_page_offset = (
            GrpcToRest.convert_point_id(result.next_page_offset)
            if result.next_page_offset
            else None
        )
    except Exception as e:
        next_page_offset = None

    # 如果结果的下一页偏移量等于当前偏移量
    if not next_page_offset or next_page_offset == offset:
        # 创建一个DataFrame
        df = pd.DataFrame(data)
        # 显示前三行数据
        df.head(3)
        # 定义保存路径
        save_path = f"{save_path}/{dataset_id}.csv"
        # 将DataFrame保存为CSV文件
        df.to_csv(save_path, index=False, encoding="utf-8-sig")
        return

    # 将结果中的点转换为记录
    points = [GrpcToRest.convert_record(point) for point in result.result]

    # 如果数据为空
    if not data:
        data = []

    # 将点的数据添加到数据列表中
    data.extend(
        [
            {
                "id": doc.id,
                "content": doc.payload["page_content"],
                "doc_id": doc.payload["metadata"]["doc_id"],
                "chunk_index": doc.payload["metadata"]["chunk_index"],
                "source": doc.payload["metadata"]["source"],
                "file_id": doc.payload["metadata"]["file_id"],
            }
            for doc in points
        ]
    )

    # 递归调用自身，以获取下一页的数据
    await generate_dataset_csv(
        dataset_id,
        data,
        save_path,
        next_page_offset,
        limit,
    )


# function to clean the dataset


def filter_qa_dataset(qa_dataset):
    """
    Filters out queries from the qa_dataset that contain certain phrases and the corresponding
    entries in the relevant_docs, and creates a new EmbeddingQAFinetuneDataset object with
    the filtered data.

    :param qa_dataset: An object that has 'queries', 'corpus', and 'relevant_docs' attributes.
    :return: An EmbeddingQAFinetuneDataset object with the filtered queries, corpus and relevant_docs.
    """

    def check_for_phrases_in_dict_values(query_string: str):
        phrase_arrs = ["Here are 2", "Here are two"]
        return any(phrase in query_string for phrase in phrase_arrs)

    # Extract keys from queries and relevant_docs that need to be removed
    queries_relevant_docs_keys_to_remove = {
        k for k, v in qa_dataset.queries.items() if check_for_phrases_in_dict_values(v)
    }
    doc_ids_key = {}
    for k, v in qa_dataset.relevant_docs.items():
        doc_id = v[0]
        if doc_id in doc_ids_key:
            doc_ids_key[doc_id].append(k)
        else:
            doc_ids_key[doc_id] = [k]
        if len(doc_ids_key[doc_id]) > 1:
            # 如果一个文档，生成了多个question，那么不采用这个文档，因为很可能生成问题时出现了幻觉
            # 把对应doc_id的query_id都加入到queries_relevant_docs_keys_to_remove
            queries_relevant_docs_keys_to_remove.update(doc_ids_key[doc_id])

    # Filter queries and relevant_docs using dictionary comprehensions
    filtered_queries = {
        k: v
        for k, v in qa_dataset.queries.items()
        if k not in queries_relevant_docs_keys_to_remove
    }
    filtered_relevant_docs = {
        k: v
        for k, v in qa_dataset.relevant_docs.items()
        if k not in queries_relevant_docs_keys_to_remove
    }

    # Create a new instance of EmbeddingQAFinetuneDataset with the filtered data
    return EmbeddingQAFinetuneDataset(
        queries=filtered_queries,
        corpus=qa_dataset.corpus,
        relevant_docs=filtered_relevant_docs,
    )


async def filter_qa_dataset_rerank(path, rerank_client):
    data = pd.read_csv(path)
    try:
        to_remove_idx = []
        for idx, item in data.iterrows():
            high_relevance_score_num = item.get("high_relevance_score_num")
            if not pd.isna(high_relevance_score_num):
                continue
            question = item["question"]
            if pd.isna(question):
                continue
            retriever = Retriever.get_instance()
            rs = await retriever.retrieve(
                ["bcddcb88-f5cd-45b0-add8-96672d5235df"],
                question,
                limit=10,
            )
            docs = [doc.text for doc in rs]
            rerank_result = await rerank_client.rerank(
                question,
                docs,
            )
            high_relevance_score_num = 0
            max_allow_num = 3
            for i in rerank_result:
                if i.relevance_score > 0.999:
                    high_relevance_score_num += 1
            data.at[idx, "high_relevance_score_num"] = high_relevance_score_num
            if high_relevance_score_num > max_allow_num:
                print(
                    f"idx:{idx} removed Q:{question} for high_relevance_score_num: {high_relevance_score_num}"
                )
                to_remove_idx.append(idx)
                continue
            print(
                f"idx:{idx} Q:{question} high_relevance_score_num: {high_relevance_score_num}"
            )
    finally:
        data = data.drop(to_remove_idx)
        data.to_csv(
            path,
            index=False,
            encoding="utf-8-sig",
        )


def generate_qa_pairs(nodes: list):
    llm = Anthropic(model="claude-3-opus-20240229", api_key=anthropic_api_key)
    qa_dataset = generate_question_context_pairs(
        nodes,
        llm=llm,
        qa_generate_prompt_tmpl=QA_GENERATE_PROMPT_TMPL,
        num_questions_per_chunk=1,
    )
    qa_dataset = filter_qa_dataset(qa_dataset)
    return qa_dataset


class TestEmbedding(unittest.IsolatedAsyncioTestCase):

    async def test_01(self):
        # await generate_dataset_csv("bcddcb88-f5cd-45b0-add8-96672d5235df")
        data = pd.read_csv("./data/bcddcb88-f5cd-45b0-add8-96672d5235df.csv")

        sub_data = data[293:500]
        try:
            for idx, item in sub_data.iterrows():
                nodes = [
                    TextNode(
                        id_=item["id"],
                        text=item["content"],
                    )
                ]
                rs = generate_qa_pairs(nodes)
                print(rs)
                if len(rs.query_docid_pairs) > 1:
                    raise ValueError(
                        f"生成的问题数量大于1，当前数量为{len(rs.query_docid_pairs)}"
                    )
                if len(rs.query_docid_pairs) == 0:
                    print(f"生成的问题数量为0，当前数量为{len(rs.query_docid_pairs)}")
                    continue
                query, _ = rs.query_docid_pairs[0]
                # 修改data的值，将生成的问题添加到data中，作为一个新的列
                sub_data.at[idx, "question"] = query
        except Exception as e:
            print(f"Error: {e}")
        finally:
            sub_data.to_csv(
                "./data/bcddcb88-f5cd-45b0-add8-96672d5235df_new2.csv",
                index=False,
                encoding="utf-8-sig",
            )

    async def test_02(self):
        max_retries = 30
        while max_retries > 0:
            try:
                rerank_client = Rerank.get_instance(
                    api_key=COHERE_API_KEY,
                    max_retries=1,
                )
                await filter_qa_dataset_rerank(
                    "./data/bcddcb88-f5cd-45b0-add8-96672d5235df_new4.csv",
                    rerank_client,
                )
                break
            except Exception as e:
                print(f"Error max_retries:{max_retries} : {e}")
                max_retries -= 1
                time.sleep(30)

    async def test_03(self):
        data = pd.read_csv("./data/bcddcb88-f5cd-45b0-add8-96672d5235df_new6.csv")
        try:
            for idx, item in data.iterrows():
                hit_rate = item.get("hit_rate")
                mrr = item.get("mrr")
                if not pd.isna(hit_rate) and not pd.isna(mrr):
                    continue
                question = item["question"]
                query_id = item["id"]
                if pd.isna(question):
                    continue
                retriever = Retriever.get_instance()
                rs = await retriever.retrieve(
                    ["bcddcb88-f5cd-45b0-add8-96672d5235df"],
                    question,
                    limit=10,
                )
                context_ids = [doc.id for doc in rs]
                metric_hit_result = HitRate().compute(
                    query=question,
                    expected_ids=[query_id],
                    retrieved_ids=context_ids,
                )
                metric_mrr_result = MRR().compute(
                    query=question,
                    expected_ids=[query_id],
                    retrieved_ids=context_ids,
                )
                data.at[idx, "hit_rate"] = metric_hit_result.score
                data.at[idx, "mrr"] = metric_mrr_result.score
        finally:
            data.to_csv(
                "./data/bcddcb88-f5cd-45b0-add8-96672d5235df_new6.csv",
                index=False,
                encoding="utf-8-sig",
            )

    async def test_04(self):
        storage = VectorStorageFactory.get_instance(
            host="localhost",
            grpc_port=6334,
        )
        embeddings = EmbeddingFactory.get_instance(
            llm_provider=LLM_PROVIDER.OPENAI, model_name="text-embedding-3-large"
        )
        docs = [
            Document(
                page_content="The quick brown fox jumps over the lazy dog.",
                metadata={},
            )
        ]
        doc_ids = [uuid.uuid4().hex]
        storage.from_documents(
            embeddings=embeddings,
            collection_name="gptbase_col",
            group_id="001",
            documents=docs,
            ids=doc_ids,
        )

    async def test_05(self):
        embeddings = EmbeddingFactory.get_instance(
            provider=LLM_PROVIDER.OPENAI,
            model_name=OpenAIModel.TEXT_EMBEDDING_3_LARGE,
            dimensions=1024,
            async_mode=False,
        )
        async_embeddings = EmbeddingFactory.get_instance(
            provider=LLM_PROVIDER.OPENAI,
            model_name=OpenAIModel.TEXT_EMBEDDING_3_LARGE,
            dimensions=1024,
        )
        rs = await async_embeddings.aembed_documents(["Hello"])
        print(len(rs[0]))
        rs2 = embeddings.embed_documents(["Hello"])
        print(len(rs2[0]))
        rs = await async_embeddings.aembed_documents(["你好"])
        print(len(rs[0]))
        rs2 = embeddings.embed_documents(["你好"])
        print(len(rs2[0]))

    def test_eq(self):
        ep1 = EmbeddingParams(
            provider=EMBEDDINGS_MODEL.OPENAI,
            model_name=OpenAIModel.TEXT_EMBEDDING_3_LARGE,
            dimensions=1024,
        )
        ep2 = EmbeddingParams(
            provider=EMBEDDINGS_MODEL.OPENAI,
            model_name=OpenAIModel.TEXT_EMBEDDING_3_LARGE,
            dimensions=1024,
        )
        self.assertEqual(ep1, ep2)
        ep3 = EmbeddingParams(
            provider=EMBEDDINGS_MODEL.OPENAI,
            model_name=OpenAIModel.TEXT_EMBEDDING_3_LARGE,
            dimensions=1021,
        )
        self.assertNotEqual(ep1, ep3)
        ep4 = EmbeddingParams(
            provider=EMBEDDINGS_MODEL.OPENAI,
            model_name=OpenAIModel.TEXT_EMBEDDING_ADA_002,
            dimensions=1021,
        )
        self.assertNotEqual(ep3, ep4)
