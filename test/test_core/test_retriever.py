from mygpt.enums import EMBEDDINGS_MODEL, OpenAIModel, VectorStorageType
from mygpt.core.retriever import Retriever
import unittest


class TestRetriever(unittest.IsolatedAsyncioTestCase):
    async def test_retrieve_faq(self):
        retriever = Retriever.get_instance(
            VectorStorageType.QDRANT_ONE_COLLECTION, EMBEDDINGS_MODEL.OPENAI
        )
        response = await retriever.retrieve_faq(
            collection_name="gptbase_faq_question1",
            dataset_ids=["18771e60-4cca-47e8-bbc8-d65536a2c0a5"],
            query="普洱茶产地在哪里？",
            score_threshold=0.1,
        )

        print(response)

    async def test_retrieve(self):
        retriever = Retriever.get_instance()
        response = await retriever.retrieve(
            dataset_ids=[
                "18771e60-4cca-47e8-bbc8-d65536a2c0a5",
                "c66fccfa-eb70-4b6f-81ba-3d2ecd4ceb8c",
                "b53920b9-5c52-4ef3-b9ae-4d4725d4dd80",
                "a301dd59-f711-4eff-bf9f-dd982a0d522a",
            ],
            query="横須賀市的観光宣伝",
            score_threshold=0.1,
        )

        print(response)

    async def test_retrieve_large(self):
        retriever = Retriever.get_instance()
        response = await retriever.retrieve(
            dataset_ids=["004f5f71-51bc-4df7-a5c4-afbd1347a959"],
            query="what is circleo?",
            collection_name="gptbase_col_large",
            score_threshold=0.3,
            model_name=OpenAIModel.TEXT_EMBEDDING_3_LARGE,
        )
        print(response)

    async def test_retrieve_large_2(self):
        dataset_id = "e6796c65-edd1-4528-9825-3d4e2cf01b25"
        retriever = Retriever.get_instance()
        response = await retriever.retrieve(
            dataset_ids=[dataset_id],
            query="茶窝网仓储的环境温度？",
            embedding_model_name=OpenAIModel.TEXT_EMBEDDING_3_LARGE,
            collection_name="gptbase_col_large",
            score_threshold=0.3,
            limit=75,
        )
        for item in response:
            print(f'id:{item.id} {repr(item.text)} source:{item.metadata["source"]}')
