import unittest

import numpy as np
import requests


class TestEmbeddings(unittest.TestCase):
    API_URL = "http://8.215.4.134:9001/v1/embeddings"

    def do_senten_check_local(self, text1: str, text2: str) -> float:
        """Calculate cosine similarity between two texts using local embedding model."""
        # Get embeddings for both texts
        emb1 = self._get_embedding(text1)
        emb2 = self._get_embedding(text2)

        # Calculate cosine similarity
        return self._cosine_similarity(emb1, emb2)

    def _get_embedding(self, text: str) -> list:
        """Get embedding vector for a text using the API."""
        payload = {"model": "local-embedding", "input": [text]}
        response = requests.post(
            self.API_URL, headers={"Content-Type": "application/json"}, json=payload
        )
        response.raise_for_status()
        return response.json()["data"][0]["embedding"]

    def _cosine_similarity(self, v1: list, v2: list) -> float:
        """Calculate cosine similarity between two vectors."""
        v1, v2 = np.array(v1), np.array(v2)
        dot = np.dot(v1, v2)
        l1 = np.linalg.norm(v1)
        l2 = np.linalg.norm(v2)
        # print(f"Dot: {dot}, L1: {l1}, L2: {l2}")
        return dot / (l1 * l2)

    def test_sentence_zh_en(self):
        check_list = [
            ("fluent", "流畅的"),
            ("早上", "morning"),
            ("你好", "您好"),
            ("hello", "hi"),
            ("good morning", "早上好"),
            ("good night", "晚安"),
            ("book", "书"),
            ("computer", "计算机"),
            ("happy", "joyful"),
            ("sad", "unhappy"),
            ("good night", "sunny day"),
        ]

        for query1, query2 in check_list:
            cos_sim = self.do_senten_check_local(query1, query2)
            print(f"LocalModel Cosine Similarity: [{query1}<=>{query2}] {cos_sim}")


if __name__ == "__main__":
    unittest.main()
