import unittest

import numpy as np
from langchain.embeddings.openai import OpenAIEmbeddings
from langchain.embeddings.voyageai import VoyageEmbeddings
from scipy import spatial

from mygpt import settings
from mygpt.core.embedding import EmbeddingFactory


class TestSentenceSimilarity(unittest.TestCase):
    def do_sentence_check(self, query1: str, query2: str):
        embedding_function = OpenAIEmbeddings()
        vectors = embedding_function.embed_documents([query1, query2])
        cos_sim = 1 - spatial.distance.cosine(vectors[0], vectors[1])
        return cos_sim

    def do_senten_check_local(self, query1: str, query2: str):
        # embedding_function = TransformerEmbeddings(
        #     url=settings.TRANSFORMERS_URL,
        # )
        # vectors = OpenAIEmbeddings().embed_documents([query1, query2])
        f = EmbeddingFactory.get_instance(
            dimensions=settings.LOCAL_EMBEDDING_VECTOR_SIZE,
            async_mode=False,
            model_name=settings.LOCAL_EMBEDDING_MODEL_NAME,
        )
        v1 = f.embed_documents([query1])[0]
        v2 = f.embed_documents([query2])[0]
        # cos_sim = spatial.distance.cosine(vectors[0], vectors[1])
        v1, v2 = np.array(v1), np.array(v2)
        dot = np.dot(v1, v2)
        l1 = np.linalg.norm(v1)
        l2 = np.linalg.norm(v2)
        # print(f"Dot: {dot}, L1: {l1}, L2: {l2}")
        return dot / (l1 * l2)

    def test_sentence_check_local(self):
        n = self.do_senten_check_local("你好", "您好")
        print(f"LocalModel Cosine Similarity: {n}")

    def do_senten_check_voyage(self, query1: str, query2: str):
        embedding_function = VoyageEmbeddings(
            voyage_api_key="pa-KnmoEaIfgbBlXLvWpy7L_OFxiLRgmjwIf3pTvIWyij8"
        )
        vectors = embedding_function.embed_documents([query1, query2])
        cos_sim = 1 - spatial.distance.cosine(vectors[0], vectors[1])
        return cos_sim

    def test_sentence_similarity(self):
        check_list = [
            ("Flex plan", "フレックス"),
            ("フレックスプランとは?", "what is flex plan？"),
            (
                "フレックスプランとは",
                "Flexプランにはどのようなライセンスが含まれていますか？",
            ),
            ("フレックスプランとは", "Flexプランはどのような利点がありますか？"),
            (
                "フレックスプランとは",
                "Flexプラン（フレックスプラン）について教えてください。",
            ),
            (
                "フレックスプランとは",
                "Flexプランにはどのような環境が含まれていますか？",
            ),
            (
                "what is flex plan？",
                "Flexプランにはどのようなライセンスが含まれていますか？",
            ),
            ("what is flex plan？", "Flexプランはどのような利点がありますか？"),
            (
                "what is flex plan？",
                "Flexプラン（フレックスプラン）について教えてください。",
            ),
            ("what is flex plan？", "Flexプランにはどのような環境が含まれていますか？"),
        ]
        rs = []
        for query1, query2 in check_list:
            cos_sim = self.do_sentence_check(query1, query2)
            rs.append((query1, query2, cos_sim))
        for query1, query2, cos_sim in rs:
            print(f"OpenAI Cosine Similarity: [{query1}<=>{query2}] {cos_sim}")

    def test_sentence_zh_en(self):
        check_list = [
            ("fluent", "流畅的", 0.85),  # English-Chinese
            ("早上", "morning", 0.90),  # Chinese-English
            ("你好", "您好", 0.95),  # Chinese-Chinese (synonyms)
            ("hello", "hi", 0.90),  # English-English (synonyms)
            ("good morning", "早上好", 0.80),  # English-Chinese
            ("good night", "晚安", 0.85),  # English-Chinese
            ("book", "书", 0.75),  # English-Chinese
            ("computer", "计算机", 0.80),  # English-Chinese
            ("happy", "joyful", 0.88),  # English-English (synonyms)
            ("sad", "unhappy", 0.87),  # English-English (synonyms)
            ("good night", "sunny day", 0.6),
        ]
        rs = []
        for query1, query2, expected_sim in check_list:
            cos_sim = self.do_senten_check_local(query1, query2)
            rs.append((query1, query2, cos_sim, expected_sim))
        for query1, query2, cos_sim, expected_sim in rs:
            print(
                f"LocalModel Cosine Similarity: [{query1}<=>{query2}] {cos_sim}, Expected: {expected_sim}"
            )

    def test_sentence_similarity_local(self):
        check_list = [
            ("Flex", "フレックス"),
            (
                "フレックスプランとは",
                "Flexプランにはどのようなライセンスが含まれていますか？",
            ),
            ("フレックスプランとは", "Flexプランはどのような利点がありますか？"),
            (
                "フレックスプランとは",
                "Flexプラン（フレックスプラン）について教えてください。",
            ),
            (
                "フレックスプランとは",
                "Flexプランにはどのような環境が含まれていますか？",
            ),
            (
                "what is flex plan？",
                "Flexプランにはどのようなライセンスが含まれていますか？",
            ),
            ("what is flex plan？", "Flexプランはどのような利点がありますか？"),
            (
                "what is flex plan？",
                "Flexプラン（フレックスプラン）について教えてください。",
            ),
            ("what is flex plan？", "Flexプランにはどのような環境が含まれていますか？"),
        ]
        rs = []
        for query1, query2 in check_list:
            cos_sim = self.do_senten_check_local(query1, query2)
            rs.append((query1, query2, cos_sim))
        for query1, query2, cos_sim in rs:
            print(f"LocalModel Cosine Similarity: [{query1}<=>{query2}] {cos_sim}")

    def test_sentence_similarity_voyage(self):
        check_list = [
            ("Flex", "フレックス"),
            (
                "フレックスプランとは",
                "Flexプランにはどのようなライセンスが含まれていますか？",
            ),
            ("フレックスプランとは", "Flexプランはどのような利点がありますか？"),
            (
                "フレックスプランとは",
                "Flexプラン（フレックスプラン）について教えてください。",
            ),
            (
                "フレックスプランとは",
                "Flexプランにはどのような環境が含まれていますか？",
            ),
            (
                "what is flex plan？",
                "Flexプランにはどのようなライセンスが含まれていますか？",
            ),
            ("what is flex plan？", "Flexプランはどのような利点がありますか？"),
            (
                "what is flex plan？",
                "Flexプラン（フレックスプラン）について教えてください。",
            ),
            ("what is flex plan？", "Flexプランにはどのような環境が含まれていますか？"),
        ]
        rs = []
        for query1, query2 in check_list:
            cos_sim = self.do_senten_check_voyage(query1, query2)
            rs.append((query1, query2, cos_sim))
        for query1, query2, cos_sim in rs:
            print(f"Voyaga Cosine Similarity: [{query1}<=>{query2}] {cos_sim}")

    def test_sentence_similarity_2(self):
        zh_text = """
使用Automation Cloud或UiPath网站上可用的自助服务选项在线购买许可证的用户现在会被升级到新的Pro计划。

此前，采用此购买方式的组织已被升级到Enterprise计划。如今，要升级到Enterprise计划，您必须联系销售团队购买定制的许可证包。

要详细了解许可证计划之间的区别，请查看“比较计划”。

现有用户：如果有在线购买的有效计划，那么在当前的计费周期结束之前，Enterprise计划将继续适用。更新一个月的计划后，新的周期开始时，将被升级到Pro计划。

Pro的免费试用计划

利用免费的许可证计划，想要尝试更高级的自动化功能或高级平台功能的组织现在可以请求升级到60天的Pro的免费试用计划。

在使用这个计划期间，组织可以利用在付费的Pro计划中可用的高级平台功能，评估这些功能如何帮助扩展自动化作业。

Pro的免费试用

Enterprise 的免费试用同样无法从Automation Cloud获取。想要测试高级功能，可以开始使用Pro的免费试用版本，或者联系销售团队。

已激活的免费试用：如果您正在使用Enterprise免费试用计划，那么您可以在有效期结束之前继续使用这个计划。
"""
        en_text = """
Users who purchased a license online using the self-service options available on the Automation Cloud or UiPath websites have been upgraded to the new Pro plan.

Previously, organizations that used this purchase method were upgraded to the Enterprise plan. Now, in order to upgrade to the Enterprise plan, you need to contact sales and purchase a custom license bundle.

For more information about the differences between license plans, please see "Plan Comparison."

Existing Users: If you have an active plan that was purchased online, the Enterprise plan will continue to apply until the end of your current billing cycle. When you renew your monthly plan, you will be upgraded to the Pro plan when the new cycle begins.

Pro Free Trial Plan

Organizations that are using a free license plan and want to try out more advanced automation features and premium platform features can now request an upgrade to a 60-day Pro free trial plan.

While using this plan, organizations can use the premium platform features available in the paid Pro plan to evaluate how these features can help expand automation work.

Pro Free Trial

Enterprise Free Trial

You can no longer request an Enterprise Free Trial from Automation Cloud.

If you want to test premium features, start a Pro Free Trial or contact sales.

Active Free Trial: If you are currently using the Enterprise Free Trial Plan, you can use this plan until it expires.
"""
        ja_text = "\n\n        Automation Cloud または UiPath の web サイトで利用可能なセルフサービスのオプションを使用して オンラインでライセンスを購入 したユーザーは、新しい Pro プランにアップグレードされるようになりました。\n\n        以前は、この購入方法を利用した組織は Enterprise プランにアップグレードされていました。現在は、 Enterprise プランにアップグレードするには、 セールスに連絡 してカスタム ライセンス バンドルを購入する必要があります。\n\n        ライセンス プラン間の違いについて詳しくは、「 プランの比較 」をご覧ください。\n\n    既存のユーザー : オンラインで購入したアクティブなプランがある場合は、  現在の請求サイクル  の終わりまでは Enterprise プランが引き続き適用されます。一か月プランを更新すると、新しいサイクルがはじまった際に代わりに Pro プランにアップグレードされます。\n\n    Pro の無料トライアル プラン\n\n        無料のライセンス プランを利用しており、より高度な自動化機能やプレミアムなプラットフォーム機能を試したい組織は、60 日間の Pro の無料トライアル プランへのアップグレードをリクエストできるようになりました。\n\n        このプランを使用している間は、組織は有料の Pro プランで利用可能なプレミアムなプラットフォーム機能を利用して、これらの機能が自動化作業の拡張にどのように役立つか評価できます。\n\n         Pro の無料トライアル\n\n        \n\n        Enterprise 無料トライアル\n\n            Automation Cloud から Enterprise 無料トライアル をリクエストできなくなりました。\n\n            プレミアム機能のテストを希望する場合は、 Pro の無料トライアル を開始するか、セールスに問い合わせてください。\n\n        アクティブな無料トライアル : 現在 Enterprise 無料トライアル プランを利用している場合は、有効期限が切れるまでこのプランを利用できます。"
        zh_text_2 = """
Flex计划提供的SKU允许在以下3种环境中部署和授权产品。
- 自动化云 - UiPath作为SaaS提供的云原生平台，托管在Azure上，由UiPath管理
- 自动化套件 - UiPath平台以单一的，打包的套件形式在选择的环境上提供-本地或向任何主要提供商订阅的云。
- 个别产品 - UiPath平台产品单独安装和在本地管理。产品安装在支持的Windows操作系统和本地的物理/虚拟硬件上。

产品能力基于在自动化云，自动化套件，和/或个别产品（独立）上的可用性。请参考产品可用性在这里:https://docs.uipath.com/overview-guide/docs/product-availability。
Flex计划还包括对100个自动化快速起步许可证的访问，以便用户可以快速开始自动化个别任务。自动化 Express运行在自动化云上，使用户能够访问StudioX, Integration Service 和 Serverless Automation Cloud Robot units.
用户等级
"""
        ja_text_2 = """
フレックスプランで提供されるSKUは、以下の3つの環境で製品を展開し、ライセンスする権利を付与します。
-   Automation Cloud - UiPathがAzureでホストし、UiPathが管理するSaaSとして提供されるクラウドネイティブのUiPathプラットフォーム
-   Automation Suite - 選択した環境で提供される、シングルでコンテナ化されたUiPathプラットフォーム。オンプレミスまたは任意の主要プロバイダーのクラウドサブスクリプションで利用可能
-   個別製品 - オンプレミスで個別にインストールおよび管理されるUiPathプラットフォーム製品。サポートされているWindowsオペレーティングシステムとオンプレミスの物理/仮想ハードウェアに製品をインストール

製品機能は、Automation Cloud、Automation Suite、および/または個別製品（スタンドアローン）の可用性に基づいています。製品の可用性についてはこちらを参照してください：https://docs.uipath.com/overview-guide/docs/product-availability 。
フレックスプランには、個別のタスクの自動化をすばやく開始するための100のAutomation Expressライセンスへのアクセスも含まれています。Automation ExpressはAutomation Cloud上で実行され、ユーザーはStudioX、Integration Service、およびサーバーレスAutomation Cloud Robotユニットへのアクセスを提供します。
ユーザーレベル
"""
        en_text_2 = """\n\n    The SKUs offered in the Flex Plan provide entitlements to deploy and license the product in any of the 3 environments below.\n    -   Automation Cloud - The Cloud Native UiPath Platform delivered as SaaS from UiPath - hosted in Azure, Managed by UiPath\n    -   Automation Suite - UiPath Platform delivered as a single, containerized suite on environment of choice - on-premises or cloud subscription to any of the major providers.\n    -   Individual Products - UiPath Platform products individually installed and managed on-premises. Products are installed on supported windows operating systems and physical/virtual hardware on-premises.\n\n    Product capabilities are based on availability on Automation Cloud, Automation Suite, and/or individual products (standalone). Refer to Product Availability here: https://docs.uipath.com/overview-guide/docs/product-availability .\n    Flex plan also includes access to 100 Automation Express licenses for users to get started quickly with automating individual tasks. Automation Express runs on Automation Cloud, giving users access to StudioX, Integration Service and Serverless Automation Cloud Robot units.\n    User Tiers"""
        check_list = [
            # ("フレックスプランとは?", "\n\n    The SKUs offered in the Flex Plan provide entitlements to deploy and license the product in any of the 3 environments below.\n    -   Automation Cloud - The Cloud Native UiPath Platform delivered as SaaS from UiPath - hosted in Azure, Managed by UiPath\n    -   Automation Suite - UiPath Platform delivered as a single, containerized suite on environment of choice - on-premises or cloud subscription to any of the major providers.\n    -   Individual Products - UiPath Platform products individually installed and managed on-premises. Products are installed on supported windows operating systems and physical/virtual hardware on-premises.\n\n    Product capabilities are based on availability on Automation Cloud, Automation Suite, and/or individual products (standalone). Refer to Product Availability here: https://docs.uipath.com/overview-guide/docs/product-availability .\n    Flex plan also includes access to 100 Automation Express licenses for users to get started quickly with automating individual tasks. Automation Express runs on Automation Cloud, giving users access to StudioX, Integration Service and Serverless Automation Cloud Robot units.\n    User Tiers"),
            # ("什么是Flex plan？","\n\n        注: Pro プランで購入できるのは一部のライセンスのみです。 Pro プランでは利用できないライセンスを取得するには、 Enterprise プランにアップグレードする必要があります。\n\n        UiPath ライセンスの完全なリストについては、「 UiPath Licensing 」ページの [Flex Plan] タブをご覧ください。\n\n        Enterprise\n        link\n\n        Enterprise プランは、UiPath のセールス チームのサポートのもと、ライセンスの種類とライセンス数がユーザーの特定のニーズに合わせてカスタマイズされた、年間プランです。開始するには、 セールスにお問い合わせ ください。\n\n        各種類のライセンスはいくつでも購入でき、このプランに含まれるプレミアム プランの特典にすべてアクセスできます。\n\n        Enterprise のティア\n\n            Enterprise プランには、 Standard と Advanced の 2 つのティアがあります。各ティアに含まれる機能は異なります。詳しくは、「 UiPath Licensing 」のページをご覧ください。\n\n        プランの内訳\n        link\n\n        The following benefits are available for Automation Cloud :\n"),
            ("フレックスプランとは?", ja_text_2),
            ("什么是Flex plan？", ja_text_2),
            ("what is flex plan？", ja_text_2),
        ]
        rs = []
        for query1, query2 in check_list:
            cos_sim = self.do_sentence_check(query1, query2)
            rs.append((query1, query2, cos_sim))
        for query1, query2, cos_sim in rs:
            print(f"OpenAI Cosine Similarity: [{query1}] {cos_sim}")

    def test_sentence_similarity_3(self):
        text = """\n\n利用規約\n\nプライバシー ポリシー\n\nCookie ポリシー\n\n© 2005-2023 UiPath. All rights reserved.\n"""
        query = """現有活用中のライセンスにつきまして、有効期限を画面表示させるための手順についてGuide頂くようにお願いいたします。"""
        cos_sim = self.do_sentence_check(query, text)
        print(f"OpenAI Cosine Similarity: [{query}] {cos_sim}")
