import unittest
from copy import deepcopy

import chromadb
import tiktoken
from langchain.document_loaders import TextLoader
from langchain.embeddings.openai import OpenAIEmbeddings
from langchain.schema import Document
from langchain.vectorstores.chroma import Chroma
from scipy import spatial

from mygpt.core.embedding import EmbeddingFactory
from mygpt.enums import OpenAIModel


class TestOpenAIEmbeddings(unittest.TestCase):
    def setUp(self) -> None:
        persistent_client = chromadb.PersistentClient("./dist/chroma")
        self.persistent_client = persistent_client
        self.collection_name = "openai_1"

    def test_openai_embeddings(self):
        loader = TextLoader(
            "test/test_embeddings/uipath_new_license.txt",
            autodetect_encoding=True,
        )
        docs = loader.load()
        print(docs)

        # split it into chunks
        separator = "\n\n\n"
        splits = docs[0].page_content.split(separator)
        docs = [
            Document(page_content=split, metadata=deepcopy(docs[0].metadata))
            for split in splits
        ]
        for idx, doc in enumerate(docs):
            doc.metadata["page_number"] = idx

        tokenizer = tiktoken.get_encoding("cl100k_base")
        for doc in docs:
            doc.metadata["tokens"] = len(tokenizer.encode(doc.page_content))
        # tokens = [doc.metadata["tokens"] for doc in docs]
        # df = pd.DataFrame({"tokens": tokens})
        # print(df)
        # df.tokens.hist()
        # plt.show()

        embedding_function = OpenAIEmbeddings()

        db = Chroma.from_documents(
            docs,
            embedding_function,
            client=self.persistent_client,
            collection_name=self.collection_name,
            collection_metadata={"hnsw:space": "cosine"},
        )

        # query it
        query = "What did the president say about Ketanji Brown Jackson"
        docs = db.similarity_search(query)

        # print results
        print(docs[0].page_content)

    def do_query(self, query: str):
        embedding_function = OpenAIEmbeddings()

        langchain_chroma = Chroma(
            client=self.persistent_client,
            collection_name=self.collection_name,
            collection_metadata={"hnsw:space": "cosine"},
            embedding_function=embedding_function,
        )
        docs = langchain_chroma.similarity_search_with_relevance_scores(
            query,
            k=7,
        )

        # print results
        for idx, item in enumerate(docs):
            doc, score = item
            if doc.metadata["page_number"] == 4:
                if idx != 0:
                    print(f"{item[0].page_content} -> {item[1]}")
                return idx, score
        return None, None

    def test_openai_query(self):
        query_list = [
            "Flexブランとは?",
            "フレックスプランとは?",
            "什么是弹性计划？",
            "什么是Flex plan？",
            "what is flex plan？",
            "Flex",
            "フレックス",
        ]
        rs = []
        for query in query_list:
            idx, score = self.do_query(query)
            rs.append((query, idx, score))
        # print result
        for query, idx, score in rs:
            print(f"{query} -> {idx}, {score}")

    def embed(self, query1: str, query2: str):
        # embedding_function = TransformerEmbeddings(
        #     url=settings.TRANSFORMERS_URL,
        # )
        # vectors = OpenAIEmbeddings().embed_documents([query1, query2])
        f = EmbeddingFactory.get_instance(
            model_name=OpenAIModel.TEXT_EMBEDDING_3_LARGE, async_mode=False
        )
        vectors = f.embed_documents([query1, query2])
        cos_sim = 1 - spatial.distance.cosine(vectors[0], vectors[1])
        return cos_sim

    def test_sentence_zh_en(self):
        check_list = [
            ("fluent", "流畅的", 0.85),  # English-Chinese
            ("早上", "morning", 0.90),  # Chinese-English
            ("你好", "您好", 0.95),  # Chinese-Chinese (synonyms)
            ("hello", "hi", 0.90),  # English-English (synonyms)
            ("good morning", "早上好", 0.80),  # English-Chinese
            ("good night", "晚安", 0.85),  # English-Chinese
            ("book", "书", 0.75),  # English-Chinese
            ("computer", "计算机", 0.80),  # English-Chinese
            ("happy", "joyful", 0.88),  # English-English (synonyms)
            ("sad", "unhappy", 0.87),  # English-English (synonyms)
        ]
        rs = []
        for query1, query2, expected_sim in check_list:
            cos_sim = self.embed(query1, query2)
            rs.append((query1, query2, cos_sim, expected_sim))
        for query1, query2, cos_sim, expected_sim in rs:
            print(f"LocalModel Cosine Similarity: [{query1}<=>{query2}] {cos_sim}")
