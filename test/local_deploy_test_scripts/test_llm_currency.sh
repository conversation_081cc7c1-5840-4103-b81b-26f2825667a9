#!/bin/bash

# 定义API密钥和日志目录
API_KEY="YOUR_API_KEY"
LOG_DIR="./logs"

# 确保日志目录存在
mkdir -p $LOG_DIR

# 生成随机字符串的函数
generate_random_string() {
  cat /dev/urandom | tr -dc 'a-zA-Z0-9' | fold -w 1000 | head -n 1
}

N=3
# 发送N个并发请求
for i in $(seq 1 $N);
do
  # 生成300字符的随机内容
  RANDOM_CONTENT=$(generate_random_string)

  curl "http://localhost:9009/v1/chat/completions" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $API_KEY" \
    -d "{
      \"model\": \"gpt-4-1106-preview\",
      \"messages\": [
        {\"role\": \"user\", \"content\": \"$RANDOM_CONTENT\"}
      ],
      \"temperature\": 0
    }" | tee "$LOG_DIR/response_$i.log" &
done

# 等待所有后台任务完成
wait
echo "所有请求已完成并记录到日志文件中。"
