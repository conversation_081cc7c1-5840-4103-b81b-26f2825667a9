import asyncio
import unittest

from mygpt.endpoints.faqs import ask_for_model_detect_turbo
from mygpt.schemata import QuestionIn


class TestAskForModelDetect(unittest.TestCase):
    async def _run_ask_for_model_detect(self, question):
        q = QuestionIn(question=question, session_id="")
        inquire, _ = await ask_for_model_detect_turbo(
            question=q,
            message_tokens=0,
        )

        return inquire

    def test_ask_for_model_detect_1(self):
        question = "卡纸"
        inquire = asyncio.run(self._run_ask_for_model_detect(question))
        self.assertTrue(inquire)

    def test_ask_for_model_detect_2(self):
        question = "忘记了"
        inquire = asyncio.run(self._run_ask_for_model_detect(question))
        self.assertFalse(inquire)

    def test_ask_for_model_detect_3(self):
        question = "如何加墨水？"
        inquire = asyncio.run(self._run_ask_for_model_detect(question))
        self.assertTrue(inquire)

    def test_ask_for_model_detect_4(self):
        question = "型号在哪查？"
        inquire = asyncio.run(self._run_ask_for_model_detect(question))
        self.assertFalse(inquire)

    def test_ask_for_model_detect_5(self):
        question = "手机怎么进行打印"
        ask_mode = asyncio.run(self._run_ask_for_model_detect(question))
        self.assertTrue(ask_mode)

    def test_ask_for_model_detect_6(self):
        question = "我的电脑不能打印了"
        inquire = asyncio.run(self._run_ask_for_model_detect(question))
        self.assertTrue(inquire)

    def test_ask_for_model_detect_7(self):
        question = "如何配网？"
        inquire = asyncio.run(self._run_ask_for_model_detect(question))
        self.assertTrue(inquire)
