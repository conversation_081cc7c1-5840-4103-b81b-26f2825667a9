import unittest
from datetime import datetime, timezone, timedelta
from unittest.mock import patch, MagicMock, AsyncMock
from uuid import UUID

import pytest

from mygpt.models import (
    User,
    Plan,
    PlanSubscription,
    AddOn,
    AddOnSubscription,
    UsageType,
    PlanType,
    DurationUnit,
    QuotaGrantType,
)
from mygpt.services.quota_service import QuotaService
from mygpt.endpoints.user_quota import get_user_quota


class TestQuotaIntegration:
    """Integration tests for the quota system."""

    def setup_method(self, method):
        """Set up test data."""
        # Create a test user
        self.user_id = UUID("12345678-1234-5678-1234-************")
        self.user = MagicMock(spec=User)
        self.user.id = self.user_id
        self.user.user_id = self.user_id
        self.user.corporate = False

        # Create a test trial plan
        self.trial_plan = MagicMock(spec=Plan)
        self.trial_plan.id = UUID("*************-4321-8765-************")
        self.trial_plan.plan_type = PlanType.TRIAL
        self.trial_plan.name = "Trial Plan"
        self.trial_plan.duration_unit = DurationUnit.DAY
        self.trial_plan.duration_length = 14
        self.trial_plan.storage_quota = 50 * 1024 * 1024  # 50MB
        self.trial_plan.bot_quota = 3
        self.trial_plan.message_quota = 100
        self.trial_plan.message_quota_grant_type = QuotaGrantType.TOTAL
        self.trial_plan.multi_modal_parsing_quota = 10
        self.trial_plan.web_page_quota = 100

        # Create a test plan subscription
        self.plan_subscription = MagicMock(spec=PlanSubscription)
        self.plan_subscription.id = UUID("11111111-1111-1111-1111-111111111111")
        self.plan_subscription.user_id = self.user_id
        self.plan_subscription.plan_id = self.trial_plan.id
        self.plan_subscription.plan = self.trial_plan
        self.plan_subscription.is_cancelled = False
        self.plan_subscription.start_at = datetime.now(timezone.utc)
        self.plan_subscription.expires_at = self.plan_subscription.start_at + timedelta(
            days=14
        )

    @pytest.mark.asyncio
    @patch("mygpt.services.quota_service.IS_USE_LOCAL_VLLM", False)
    @patch("mygpt.services.quota_service.QUOTA_CHECK", True)
    @patch("mygpt.services.quota_service.User.get_or_none")
    @patch("mygpt.services.quota_service.Robot.filter")
    @patch("mygpt.services.quota_service.QuotaService._get_total_concurrent_limit")
    @patch("mygpt.services.quota_service.QuotaService.get_quota_details")
    @patch("mygpt.services.quota_service.QuotaService.get_storage_quota_details")
    async def test_quota_check_and_api_integration(
        self,
        mock_get_storage_quota_details,
        mock_get_quota_details,
        mock_get_total_concurrent_limit,
        mock_robot_filter,
        mock_get_user,
    ):
        """Test the integration between quota checks and the API."""
        # Setup mocks
        mock_get_user.return_value = self.user
        mock_get_total_concurrent_limit.return_value = 3  # User can have 3 bots
        mock_robot_filter.return_value.count = AsyncMock(return_value=2)

        # Mock quota details for different resource types
        def mock_get_quota_details_side_effect(user_id, resource_type):
            if resource_type == UsageType.MESSAGE_SENT:
                return 50, 100  # 50 available, 100 total
            elif resource_type == UsageType.WEB_PAGE_LEARNED:
                return 70, 100  # 70 available, 100 total
            elif resource_type == UsageType.MULTI_MODAL_PARSING:
                return 5, 10  # 5 available, 10 total
            return 0, 0

        mock_get_quota_details.side_effect = mock_get_quota_details_side_effect
        mock_get_storage_quota_details.return_value = (
            25 * 1024 * 1024,
            50 * 1024 * 1024,
        )  # 25MB used, 50MB limit

        # Mock get_active_plan_subscription
        self.user.get_active_plan_subscription = AsyncMock(
            return_value=self.plan_subscription
        )

        # First, check if the user can create a bot
        result = await QuotaService.check_bot_quota(self.user_id)
        assert result is True

        # Then, get the user quota info from the API
        quota_info = await get_user_quota(user=self.user)

        # Verify the API response matches the expected quota values
        assert quota_info.user_id == str(self.user_id)
        assert quota_info.plan_type == PlanType.TRIAL
        assert quota_info.plan_name == "Trial Plan"
        assert quota_info.has_active_plan is True
        assert quota_info.effective_is_corporate is False

        # Check bot quota
        assert quota_info.bot_quota.current == 2
        assert quota_info.bot_quota.limit == 3
        assert quota_info.bot_quota.available == 1

        # Check message quota
        assert quota_info.message_quota.current == 50  # 100 - 50 available
        assert quota_info.message_quota.limit == 100
        assert quota_info.message_quota.available == 50

        # Check web page quota
        assert quota_info.web_page_quota.current == 30  # 100 - 70 available
        assert quota_info.web_page_quota.limit == 100
        assert quota_info.web_page_quota.available == 70

        # Check multi-modal parsing quota
        assert quota_info.multi_modal_parsing_quota.current == 5  # 10 - 5 available
        assert quota_info.multi_modal_parsing_quota.limit == 10
        assert quota_info.multi_modal_parsing_quota.available == 5

        # Check storage quota
        assert quota_info.storage_quota.current == 25 * 1024 * 1024
        assert quota_info.storage_quota.limit == 50 * 1024 * 1024
        assert quota_info.storage_quota.available == 25 * 1024 * 1024

    @pytest.mark.asyncio
    @patch("mygpt.services.quota_service.IS_USE_LOCAL_VLLM", False)
    @patch("mygpt.services.quota_service.QUOTA_CHECK", True)
    @patch("mygpt.services.quota_service.User.get_or_none")
    @patch("mygpt.services.quota_service.Robot.filter")
    @patch("mygpt.services.quota_service.QuotaService.get_quota_details")
    @patch("mygpt.services.quota_service.QuotaService.get_storage_quota_details")
    async def test_corporate_user_quota_integration(
        self,
        mock_get_storage_quota_details,
        mock_get_quota_details,
        mock_robot_filter,
        mock_get_user,
    ):
        """Test the integration for a corporate user."""
        # Setup mocks
        self.user.corporate = True
        mock_get_user.return_value = self.user
        mock_robot_filter.return_value.count = AsyncMock(return_value=5)

        # Mock quota details for different resource types
        def mock_get_quota_details_side_effect(user_id, resource_type):
            return None, None  # Unlimited for all resource types

        mock_get_quota_details.side_effect = mock_get_quota_details_side_effect
        mock_get_storage_quota_details.return_value = (
            100 * 1024 * 1024,
            None,
        )  # 100MB used, unlimited

        # Mock get_active_plan_subscription
        self.user.get_active_plan_subscription = AsyncMock(
            return_value=None
        )  # No active plan for corporate users

        # First, check if the user can create a bot
        result = await QuotaService.check_bot_quota(self.user_id)
        assert result is True

        # Then, get the user quota info from the API
        quota_info = await get_user_quota(user=self.user)

        # Verify the API response matches the expected quota values
        assert quota_info.user_id == str(self.user_id)
        assert quota_info.plan_type is None
        assert quota_info.plan_name is None
        assert quota_info.has_active_plan is False
        assert quota_info.effective_is_corporate is True

        # Check bot quota
        assert quota_info.bot_quota.current == 5
        assert quota_info.bot_quota.limit is None
        assert quota_info.bot_quota.available is None

        # Check message quota
        assert quota_info.message_quota.current == 0
        assert quota_info.message_quota.limit is None
        assert quota_info.message_quota.available is None

        # Check web page quota
        assert quota_info.web_page_quota.current == 0
        assert quota_info.web_page_quota.limit is None
        assert quota_info.web_page_quota.available is None

        # Check multi-modal parsing quota
        assert quota_info.multi_modal_parsing_quota.current == 0
        assert quota_info.multi_modal_parsing_quota.limit is None
        assert quota_info.multi_modal_parsing_quota.available is None

        # Check storage quota
        assert quota_info.storage_quota.current == 100 * 1024 * 1024
        assert quota_info.storage_quota.limit is None
        assert quota_info.storage_quota.available is None


if __name__ == "__main__":
    unittest.main()
