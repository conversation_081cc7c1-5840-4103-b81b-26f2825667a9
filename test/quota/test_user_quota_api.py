from datetime import datetime, timezone, timedelta
from unittest.mock import patch, MagicMock, AsyncMock

import pytest

from mygpt.endpoints.user_quota import get_user_quota
from mygpt.models import User, UsageType, PlanType, Plan, PlanSubscription


# Use pytest fixtures instead of setUp
@pytest.fixture
def mock_user():
    user = MagicMock(spec=User)
    user.id = "test-user-id"
    user.user_id = "test-user-id"
    user.corporate = False
    user.get_active_plan_subscription = AsyncMock()
    return user


@pytest.fixture
def mock_plan():
    plan = MagicMock(spec=Plan)
    plan.id = "test-plan-id"
    plan.plan_type = PlanType.TRIAL
    plan.name = "Trial Plan"
    return plan


@pytest.fixture
def mock_plan_subscription(mock_user, mock_plan):
    plan_subscription = MagicMock(spec=PlanSubscription)
    plan_subscription.id = "test-subscription-id"
    plan_subscription.user_id = mock_user.user_id
    plan_subscription.plan_id = mock_plan.id
    plan_subscription.plan = mock_plan
    plan_subscription.is_cancelled = False
    plan_subscription.start_at = datetime.now(timezone.utc)
    plan_subscription.expires_at = plan_subscription.start_at + timedelta(days=14)
    return plan_subscription


# Mark tests with @pytest.mark.asyncio
@pytest.mark.asyncio
@patch(
    "mygpt.endpoints.user_quota.QuotaService.get_bot_quota_details",
    new_callable=AsyncMock,
)
@patch(
    "mygpt.endpoints.user_quota.QuotaService.get_quota_details", new_callable=AsyncMock
)
@patch(
    "mygpt.endpoints.user_quota.QuotaService.get_storage_quota_details",
    new_callable=AsyncMock,
)
async def test_get_user_quota(
    mock_get_storage_quota_details,
    mock_get_quota_details,
    mock_get_bot_quota_details,
    mock_user,  # Inject fixtures
    mock_plan_subscription,
):
    """Test getting the user quota."""
    # Setup mocks' return values
    mock_get_bot_quota_details.return_value = (2, 3)

    async def mock_get_quota_details_side_effect(user_id, resource_type):
        if resource_type == UsageType.MESSAGE_SENT:
            return 50, 100
        elif resource_type == UsageType.WEB_PAGE_LEARNED:
            return 70, 100
        elif resource_type == UsageType.MULTI_MODAL_PARSING:
            return 5, 10
        return 0, 0

    mock_get_quota_details.side_effect = mock_get_quota_details_side_effect
    mock_get_storage_quota_details.return_value = (25 * 1024 * 1024, 50 * 1024 * 1024)
    mock_user.get_active_plan_subscription.return_value = mock_plan_subscription

    # Call the function directly
    quota_info = await get_user_quota(user=mock_user)

    # Verify the response (use assert instead of self.assertEqual)
    assert quota_info.user_id == mock_user.user_id
    assert quota_info.plan_type == PlanType.TRIAL
    assert quota_info.plan_name == "Trial Plan"
    assert quota_info.has_active_plan is True
    assert quota_info.effective_is_corporate is False

    # Check bot quota
    assert quota_info.bot_quota.current == 2
    assert quota_info.bot_quota.limit == 3
    assert quota_info.bot_quota.available == 1

    # Check message quota
    assert quota_info.message_quota.current == 50
    assert quota_info.message_quota.limit == 100
    assert quota_info.message_quota.available == 50

    # Check web page quota
    assert quota_info.web_page_quota.current == 30
    assert quota_info.web_page_quota.limit == 100
    assert quota_info.web_page_quota.available == 70

    # Check multi-modal parsing quota
    assert quota_info.multi_modal_parsing_quota.current == 5
    assert quota_info.multi_modal_parsing_quota.limit == 10
    assert quota_info.multi_modal_parsing_quota.available == 5

    # Check storage quota
    assert quota_info.storage_quota.current == 25 * 1024 * 1024
    assert quota_info.storage_quota.limit == 50 * 1024 * 1024
    assert quota_info.storage_quota.available == (50 - 25) * 1024 * 1024


@pytest.mark.asyncio
@patch(
    "mygpt.endpoints.user_quota.QuotaService.get_bot_quota_details",
    new_callable=AsyncMock,
)
@patch(
    "mygpt.endpoints.user_quota.QuotaService.get_quota_details", new_callable=AsyncMock
)
@patch(
    "mygpt.endpoints.user_quota.QuotaService.get_storage_quota_details",
    new_callable=AsyncMock,
)
async def test_get_user_quota_corporate_user(
    mock_get_storage_quota_details,
    mock_get_quota_details,
    mock_get_bot_quota_details,
    mock_user,  # Inject fixture
):
    """Test getting the user quota for a corporate user."""
    # Setup mocks
    mock_user.corporate = True  # Modify the user from the fixture
    mock_get_bot_quota_details.return_value = (5, None)

    async def mock_get_quota_details_side_effect(user_id, resource_type):
        return None, None

    mock_get_quota_details.side_effect = mock_get_quota_details_side_effect
    mock_get_storage_quota_details.return_value = (100 * 1024 * 1024, None)
    mock_user.get_active_plan_subscription.return_value = None

    # Call the function directly
    quota_info = await get_user_quota(user=mock_user)

    # Verify the response
    assert quota_info.user_id == mock_user.user_id
    assert quota_info.plan_type is None
    assert quota_info.plan_name is None
    assert quota_info.has_active_plan is False
    assert quota_info.effective_is_corporate is True

    # Check bot quota
    assert quota_info.bot_quota.current == 5
    assert quota_info.bot_quota.limit is None
    assert quota_info.bot_quota.available is None

    # Check message quota
    assert quota_info.message_quota.current == 0
    assert quota_info.message_quota.limit is None
    assert quota_info.message_quota.available is None

    # Check web page quota
    assert quota_info.web_page_quota.current == 0
    assert quota_info.web_page_quota.limit is None
    assert quota_info.web_page_quota.available is None

    # Check multi-modal parsing quota
    assert quota_info.multi_modal_parsing_quota.current == 0
    assert quota_info.multi_modal_parsing_quota.limit is None
    assert quota_info.multi_modal_parsing_quota.available is None

    # Check storage quota
    assert quota_info.storage_quota.current == 100 * 1024 * 1024
    assert quota_info.storage_quota.limit is None
    assert quota_info.storage_quota.available is None
