import uuid

# import aioredis
import pytest
from qdrant_client import <PERSON>dra<PERSON><PERSON><PERSON>
from qdrant_client.http.models import <PERSON><PERSON><PERSON>
from redis.exceptions import ResponseError
from tortoise import Tortoise

from mygpt.app import poll_clone_task_streams
from mygpt.endpoints.dataset import clone_dataset, generate_clone_name
from mygpt.opensearch import get_open_search_client
from mygpt.settings import VECTOR_STORAGE_QDRANT_HOST, TORTOISE_ORM, RedisClient

print(VECTOR_STORAGE_QDRANT_HOST)

from mygpt.models import Dataset, VectorFile, Faqs

qdrant = QdrantClient("************", port=30100, timeout=100)

dataset_id_to_be_cloned = old_dataset_id = "f1cab0f9-be6b-4296-a1a7-f81acf5151ea"
import asyncio

Tortoise.init_models(["mygpt.models"], "models")
asyncio.run(Tortoise.init(config=TORTOISE_ORM))


# 初始化 Redis 客户端
redis_client = RedisClient.get_client()


@pytest.mark.asyncio
async def test_dataset_clone(
    old_dataset_id: str = "f1cab0f9-be6b-4296-a1a7-f81acf5151ea",
):
    old_dataset_info = await Dataset.get(id=old_dataset_id, deleted_at=None)
    old_v_type = old_dataset_info.metadata["vector_storage"]
    if old_v_type != "qdrant" and old_v_type != "qdrant_one_collection":
        raise "only support qdrant and qdrant_one_collection now"
    if not old_dataset_info:
        raise "dataset not found"
    new_dataset_info = dict(old_dataset_info)
    new_dataset_id = str(uuid.uuid4())
    new_dataset_info["collection_name"] = str(new_dataset_id)
    new_dataset_info["id"] = new_dataset_id
    del new_dataset_info["created_at"]
    del new_dataset_info["updated_at"]
    new_dataset = await Dataset.create(**new_dataset_info)

    files = await VectorFile.filter(dataset_id=old_dataset_id)
    new_files = []
    os_client = get_open_search_client()
    for file in files:
        old_file_dict = dict(file)
        old_file_dict["id"] = str(uuid.uuid4())
        old_file_dict["dataset_id"] = new_dataset_id
        del old_file_dict["created_at"]
        del old_file_dict["updated_at"]
        index_ids = []
        for index_id in old_file_dict["index_ids"]:
            os_res = os_client.get_document(old_dataset_id, index_id)
            new_index_id = str(uuid.uuid4())
            index_ids.append(new_index_id)
            os_res["_id"] = new_index_id
            os_res["dataset_id"] = new_dataset_id
            os_client.os_client.index(index=new_dataset_id, body=os_res["_source"])

            if old_v_type == "qdrant":
                qdrant_res = qdrant.get_collection(collection_name=index_id)
                qdrant_res["collection_name"] = new_dataset_id
                qdrant.upsert(new_index_id, [qdrant_res])
            else:  # old_v_type == "qdrant_one_collection":
                scroll_filter = {
                    "must": [{"key": "group_id", "match": {"value": old_dataset_id}}]
                }

                # Retrieve points using the scroll request
                result = qdrant.scroll(
                    collection_name="gptbase_col",
                    scroll_filter=scroll_filter,
                    with_vectors=True,
                    with_payload=True,
                )

                # Access the retrieved points
                points = result[0]

                for point in points:
                    point.id = new_index_id
                    point.payload["group_id"] = new_index_id
                    point.payload["metadata"]["dataset_id"] = new_dataset_id
                new_points = [
                    PointStruct(id=point.id, vector=point.vector, payload=point.payload)
                    for point in points
                ]
                qdrant.upsert(collection_name="gptbase_col", points=new_points)

        old_file_dict["index_ids"] = index_ids
        new_file = await VectorFile.create(
            **old_file_dict,
        )
        new_files.append(new_file)
    print(new_files)


@pytest.mark.asyncio
async def test_task_queue(old_dataset_id: str = "f1cab0f9-be6b-4296-a1a7-f81acf5151ea"):
    # 尝试创建消费者组，如果已存在则忽略异常
    stream_name = "clone_task_stream-" + old_dataset_id
    consumer_group = "consumer_group-" + old_dataset_id
    try:
        await redis_client.xgroup_create(
            stream_name, consumer_group, id="0", mkstream=True
        )
    except ResponseError as e:
        if "BUSYGROUP Consumer Group name already exists" not in str(e):
            raise

    # 清空 Redis Stream
    await redis_client.xtrim(stream_name, 0, approximate=False)

    file_list = [
        {"file_id": "12345-dc5e-46ec-9cdf-cc18a77bf149"},
        {"file_id": "67890-de8d-4832-970b-e15f96e84db0"},
        {"file_id": "abcde-de8d-4832-970b-e15f96e84db0"},
        {"file_id": "11256-de8d-4832-970b-e15f96e84db0"},
    ]
    # 使用 xadd 将任务添加到 Redis Stream 中
    for file_obj in file_list:
        await redis_client.xadd(stream_name, file_obj)

    # 使用 xread 从 Redis Stream 中读取所有任务，但不删除
    all_entries = await redis_client.xread({stream_name: "0"})

    # 检查读取到的任务与添加的任务是否匹配
    for index, entry in enumerate(all_entries[0][1]):
        assert entry[1] == file_list[index]

    consumer_name = f"consumer-{old_dataset_id}"

    # 使用 xread 从 Redis Stream 中读取一个任务
    # entry = await redis_client.xread({"your_stream_name": "0"}, count=1)
    entry = await redis_client.xreadgroup(
        consumer_group, consumer_name, {stream_name: ">"}, count=1
    )

    # 检查读取到的任务与添加的任务是否匹配
    assert entry[0][1][0][1] == file_list[0]

    # 读取下一个任务
    # last_id = entry[0][1][0][0]
    entry = await redis_client.xreadgroup(
        consumer_group, consumer_name, {stream_name: ">"}, count=1
    )

    # 检查读取到的任务与添加的任务是否匹配
    assert entry[0][1][0][1] == file_list[1]

    entry = await redis_client.xreadgroup(
        consumer_group, consumer_name, {stream_name: ">"}, count=1
    )

    # 检查读取到的任务与添加的任务是否匹配
    assert entry[0][1][0][1] == file_list[2]

    entry = await redis_client.xreadgroup(
        consumer_group, consumer_name, {stream_name: ">"}, count=1
    )

    # 检查读取到的任务与添加的任务是否匹配
    assert entry[0][1][0][1] == file_list[3]

    entry = await redis_client.xreadgroup(
        consumer_group, consumer_name, {stream_name: ">"}, count=1
    )
    # print("entry", entry)
    assert entry == []


@pytest.mark.asyncio
async def test_poll_clone_task_streams():
    await poll_clone_task_streams()


@pytest.mark.asyncio
async def test_create_task():
    dataset_id_to_be_cloned = "f1cab0f9-be6b-4296-a1a7-f81acf5151ea"
    await clone_dataset(uuid.UUID(dataset_id_to_be_cloned))


def test_generate_clone_name():
    dataset_name = "abc"
    existing_names = ["abc"]
    cloned_name = "abc-clone"
    assert cloned_name == generate_clone_name(dataset_name, existing_names)

    dataset_name = "abc"
    existing_names = ["abc", "abc-clone"]
    cloned_name = "abc-clone(2)"
    assert cloned_name == generate_clone_name(dataset_name, existing_names)

    dataset_name = "abc"
    existing_names = ["abc", "abc-clone", "abc-clone(2)"]
    cloned_name = "abc-clone(3)"
    assert cloned_name == generate_clone_name(dataset_name, existing_names)

    dataset_name = "abc"
    existing_names = ["abc", "abc-clone", "abc-clone(2)", "abc-clone(3)"]
    cloned_name = "abc-clone(4)"
    assert cloned_name == generate_clone_name(dataset_name, existing_names)

    dataset_name = "abc-clone"
    existing_names = ["abc", "abc-clone", "abc-clone(2)", "abc-clone(3)"]
    cloned_name = "abc-clone-clone"
    assert cloned_name == generate_clone_name(dataset_name, existing_names)

    dataset_name = "abc-clone"
    existing_names = [
        "abc",
        "abc-clone",
        "abc-clone(2)",
        "abc-clone(3)",
        "abc-clone-clone",
    ]
    cloned_name = "abc-clone-clone(2)"
    assert cloned_name == generate_clone_name(dataset_name, existing_names)

    dataset_name = "abc-clone"
    existing_names = [
        "abc",
        "abc-clone",
        "abc-clone(2)",
        "abc-clone(3)",
        "abc-clone(2)-clone",
        "abc-clone-clone(2)",
    ]
    cloned_name = "abc-clone-clone"
    assert cloned_name == generate_clone_name(dataset_name, existing_names)

    dataset_name = "abc-clone(2)"
    existing_names = [
        "abc",
        "abc-clone",
        "abc-clone(2)",
        "abc-clone(3)",
        "abc-clone(2)-clone",
        "abc-clone-clone(2)",
    ]
    cloned_name = "abc-clone(2)-clone(2)"
    assert cloned_name == generate_clone_name(dataset_name, existing_names)

    dataset_name = "abc-clone(3)"
    existing_names = [
        "abc",
        "abc-clone",
        "abc-clone(2)",
        "abc-clone(3)",
        "abc-clone(2)-clone",
        "abc-clone-clone(2)",
    ]
    cloned_name = "abc-clone(3)-clone"
    assert cloned_name == generate_clone_name(dataset_name, existing_names)

    dataset_name = "abc-clone-clone"
    existing_names = [
        "abc",
        "abc-clone",
        "abc-clone(2)",
        "abc-clone(3)",
        "abc-clone(2)-clone",
        "abc-clone-clone(2)",
    ]
    cloned_name = "abc-clone-clone-clone"
    assert cloned_name == generate_clone_name(dataset_name, existing_names)

    dataset_name = "abc-clone"
    existing_names = [
        "abc",
        "abc-clone",
        "abc-clone(2)",
        "abc-clone(3)",
        "abc-clone(2)-clone",
        "abc-clone-clone(2)",
    ]
    cloned_name = "abc-clone-clone"
    assert cloned_name == generate_clone_name(dataset_name, existing_names)

    dataset_name = "abc-clone"
    existing_names = [
        "abc",
        "abc-clone",
        "abc-clone(2)",
        "abc-clone(3)",
        "abc-clone(2)-clone",
        "abc-clone-clone",
        "abc-clone-clone(2)",
        "abc-clone-clone(3)",
    ]
    cloned_name = "abc-clone-clone(4)"
    assert cloned_name == generate_clone_name(dataset_name, existing_names)

    dataset_name = "image-doc.docx's dataset-clone"
    existing_names = [
        "image-doc.docx's dataset-clone-clone",
        "image-doc.docx's dataset-clone",
    ]
    cloned_name = "image-doc.docx's dataset-clone-clone(2)"
    assert cloned_name == generate_clone_name(dataset_name, existing_names)

    # def test_generate_clone_name_1():
    dataset_name = "abc"
    existing_names = [
        "abc",
        "abc-clone",
        "abc-clone(2)",
        "abc-clone(3)",
        "abc-clone(8)",
        "abc-clone(2)-clone",
        "abc-clone-clone(2)",
    ]
    cloned_name = "abc-clone(4)"
    assert cloned_name == generate_clone_name(dataset_name, existing_names)

    # def test_generate_clone_name_1():
    dataset_name = "abc"
    existing_names = [
        "abc-clone",
        "abc-clone(1)",
        "abc-clone(2)",
        "abc-clone(2)",
        "abc-clone(3)",
    ]
    cloned_name = "abc-clone(4)"
    assert cloned_name == generate_clone_name(dataset_name, existing_names)


@pytest.mark.asyncio
async def test_tortoise_update_or_create():
    # faq = await Faqs.get(id="6aadafff-e0b9-497b-aeb2-65498a35b6f5")
    # # faq_dict = dict(faq)
    # # faq_dict["id"] = str(uuid.uuid4())
    # # faq_dict["question"] = "new question 123567"
    # faq.question = "q111" + str(uuid.uuid4())
    # # faq_dict["answer"] = "answer 000"
    # # new_faq = await Faqs.update_or_create()
    # # faq.id = str(uuid.uuid4())
    # faq.id = uuid.uuid4()
    # # new_faq = await Faqs.update_or_create(
    # #     id="6aadafff-e0b9-497b-aeb2-65498a35b6f5", defaults=dict(faq)
    # # )
    # new_faq = await Faqs.update_or_create(id=faq.id, defaults=dict(faq))
    # print(new_faq)
    # suppose entry exists
    faq = await Faqs.get(id="6aadafff-e0b9-497b-aeb2-65498a35b6f5")
    # modify question
    faq.question = "q111234"
    # assign new id
    # faq.id = uuid.uuid4()
    # update_or_create, KeyError
    new_faq = await Faqs.update_or_create(id=faq.id, defaults=dict(faq))


@pytest.mark.asyncio
async def test_faq_save_update_time():
    # faq = await Faqs.get(id="6aadafff-e0b9-497b-aeb2-65498a35b6f5")
    # # faq_dict = dict(faq)
    # # faq_dict["id"] = str(uuid.uuid4())
    # # faq_dict["question"] = "new question 123567"
    # faq.question = "q111" + str(uuid.uuid4())
    # # faq_dict["answer"] = "answer 000"
    # # new_faq = await Faqs.update_or_create()
    # # faq.id = str(uuid.uuid4())
    # faq.id = uuid.uuid4()
    # # new_faq = await Faqs.update_or_create(
    # #     id="6aadafff-e0b9-497b-aeb2-65498a35b6f5", defaults=dict(faq)
    # # )
    # new_faq = await Faqs.update_or_create(id=faq.id, defaults=dict(faq))
    # print(new_faq)
    # suppose entry exists
    faq = await Faqs.get(id="6aadafff-e0b9-497b-aeb2-65498a35b6f5")
    # modify question
    faq.question = "q111234"
    # assign new id
    # faq.id = uuid.uuid4()
    # update_or_create, KeyError
    # new_faq = await Faqs.save()
    # faq_s = await faq.save(update_updated_at=False)
    faq_s = await faq.save()
    print(faq_s)
