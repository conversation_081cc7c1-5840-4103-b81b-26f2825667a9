import copy
from uuid import uuid4

import pytest

from mygpt.file_doc_search import co
from mygpt.loader.service import split_by_file
from mygpt.schemata import Embeddings
from mygpt.site_crawler import SiteCrawler
from mygpt.utils import num_tokens_from_string


@pytest.mark.asyncio
async def test_check_url_and_get_html_success():
    url = "https://his-mobile.com/column/2022_0225_4"
    site_crawler = SiteCrawler(url)
    doc = await site_crawler.parse_base(url)

    assert doc is not None == True
    assert "title" in doc.metadata
    assert "description" in doc.metadata


@pytest.mark.asyncio
async def test_check_url_and_get_html_and_doc_rerank():
    # url = "https://his-mobile.com/column/2022_0225_4"
    # url = "https://his-mobile.com/support/faq/00115"
    url = "https://his-mobile.com/column/2022_0228_3"
    site_crawler = SiteCrawler(url)
    doc = await site_crawler.parse_base(url)

    assert doc is not None == True
    assert "title" in doc.metadata
    assert "description" in doc.metadata
    file_id: str = doc.metadata.get("parser_file_id")
    del doc.metadata["parser_file_id"]
    data = await split_by_file(file_id)
    embeddings = []
    doc_id = str(uuid4())
    for index, chunk in enumerate(data):
        embeddings.append(
            Embeddings(
                id=str(uuid4()),
                text=chunk.content,
                metadata={
                    "title": result["title"],
                    "description": result["description"],
                    "doc_id": doc_id,
                    "chunk_index": index,
                },
            )
        )
    docs = []
    for e in embeddings:
        docs.append(
            {
                "id": e.id,
                "text": e.text,
                "metadata": e.metadata,
            }
        )
    rerank_res = await co.rerank(
        # query="受信 料金", documents=docs, model="rerank-multilingual-v2.0"
        query="家族割 サービス",
        documents=docs,
        model="rerank-multilingual-v2.0",
    )
    # rerank_res_1 = await co.rerank(
    #     query="受信 通話料金", documents=docs, model="rerank-multilingual-v2.0"
    # )
    # print(rerank_res)
    max_score = 0
    for e in embeddings:
        for result in rerank_res.results:
            if result.document["id"] == e.id:
                e.score = result.relevance_score
                max_score = max(max_score, result.relevance_score)
    embeddings.sort(key=lambda x: x.score, reverse=True)
    return_embeddings = []
    tc_sum = 0
    for e in embeddings:
        if tc_sum > 3500:
            break
        tc_sum += num_tokens_from_string(e.text)
        return_embeddings.append(copy.deepcopy(e))
    # return_embeddings = copy.deepcopy(embeddings[:10])
    for e in return_embeddings:
        e.score = max_score
    tcl = []
    tc = 0
    for e in return_embeddings:
        tc1 = num_tokens_from_string(e.text)
        tc += tc1
        tcl.append(tc1)
    return return_embeddings
