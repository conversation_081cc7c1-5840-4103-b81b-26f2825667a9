import pytest

from mygpt.lark_utils import get_tenant_access_token


@pytest.mark.integration
def test_get_tenant_access_token_real_api():
    # 从环境变量获取 app_id 和 app_secret
    app_id = "cli_a634a14ef839100e"
    app_secret = "fHh01lWMJ8cn6D7PLPxsWdfuv8ZDjO5r"

    # 确保环境变量已设置
    assert app_id is not None, "FEISHU_APP_ID 环境变量未设置"
    assert app_secret is not None, "FEISHU_APP_SECRET 环境变量未设置"

    # 调用函数
    result = get_tenant_access_token(app_id, app_secret)

    # 验证结果
    assert result != "", "未能获取有效的 tenant_access_token"
    assert len(result) > 20, "获取的 tenant_access_token 长度异常"

    print(f"成功获取 tenant_access_token: {result}")


import pytest


@pytest.mark.integration
def test_get_tenant_access_token_real_api():
    # 从环境变量获取 app_id 和 app_secret
    app_id = "cli_a634a14ef839100e"
    app_secret = "fHh01lWMJ8cn6D7PLPxsWdfuv8ZDjO5r"

    # 确保环境变量已设置
    assert app_id is not None, "FEISHU_APP_ID 环境变量未设置"
    assert app_secret is not None, "FEISHU_APP_SECRET 环境变量未设置"

    # 调用函数
    result = get_tenant_access_token(app_id, app_secret)

    # 验证结果
    assert result != "", "未能获取有效的 tenant_access_token"
    assert len(result) > 20, "获取的 tenant_access_token 长度异常"

    print(f"成功获取 tenant_access_token: {result}")


import pytest
from mygpt.lark_utils import download_single_file


@pytest.mark.asyncio
async def test_download_single_file():
    # 测试参数
    app_id = "cli_a7921f030ab8d02d"
    app_secret = "nt6pPLlfBCxw19bVkKcH2ddSnpBbRF8W"
    file_token = "On3RbNqeJo43nixVpgqjBH2FpO3"

    # 调用函数
    result = await download_single_file(app_id, app_secret, file_token)

    # 验证结果
    assert result is not None
    assert isinstance(result, bytes)

    # 可选：打印文件大小
    print(f"Downloaded file size: {len(result)} bytes")
