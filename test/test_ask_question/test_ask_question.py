import unittest
import uuid

import requests


class TestAskQuestion(unittest.TestCase):
    # def test_ask_ai(self):
    #     print('testing test_ask_ai')
    #     load_dotenv('.env')
    #     file_content = b'who is jack\'s father? It\'s johnson'
    #     file_name = 'test-ask-ai.txt'

    #     asyncio.run(Tortoise.init(config=TORTOISE_ORM))

    #     # create user
    #     user_id = uuid.uuid4().hex

    #     def override_get_current_user():
    #         return Auth0User(sub=user_id, permissions=[])
    #     app.dependency_overrides[get_current_user] = override_get_current_user

    #     asyncio.run(User.create(
    #         user_id=user_id, name='123', email='<EMAIL>'))

    #     # create bot
    #     bot = asyncio.run(Robot.create(
    #         user_id=user_id,
    #         ai_status=AIStatus.INIT,
    #         name='test bot'
    #     ))
    #     asyncio.run(Tortoise.close_connections())

    #     ai_id = bot.id

    #     with tempfile.SpooledTemporaryFile() as temp_file:
    #         temp_file.write(file_content)
    #         temp_file.seek(0)  # reset file pointer to the beginning
    #         _file = (file_name, temp_file.read(), 'text/plain')
    #         with TestClient(app) as client:
    #             response = client.post(
    #                 f'/embeddings/analysis/file/{ai_id}', files={'file': _file})
    #             print('feed res', response.json())
    #             assert response.status_code == 200

    #             question_data = {'question': '谁是jack的父亲？'}
    #             ai_id = bot.id

    #             response = client.post(
    #                 f'/v1/question/{ai_id}', json=question_data)
    #             assert response.status_code == 200
    #             print('ai answer', response.json())
    #             assert 'johnson' in response.json()['answer'].lower()

    def test_ask_gptbase_1(self):
        url = (
            "https://api-dev.gbase.ai/v1/question/dfeafaa2-3c8d-423d-9113-d1152f97a0c7"
        )
        headers = {
            "Content-Type": "application/json",
        }
        session_id = uuid.uuid4()
        data = {
            "session_id": f"{session_id}",
            "stream": False,
            "simple_question": True,
            "with_source": True,
            "question": "What abilities do GBase chatbots have?",
        }
        response = requests.post(url, headers=headers, json=data)
        self.assertEqual(response.status_code, 200)
        answer = response.json()["answer"].lower()
        print("answer1", answer)
        assert "**understanding ability**" in answer
        assert "**quick response**" in answer
        assert "**interactive q&a**" in answer
        assert "**query support**" in answer
        assert "service" in answer

    def test_ask_gptbase_2(self):
        url = (
            "https://api-dev.gbase.ai/v1/question/dfeafaa2-3c8d-423d-9113-d1152f97a0c7"
        )
        headers = {
            "Content-Type": "application/json",
        }
        session_id = uuid.uuid4()
        data = {
            "session_id": f"{session_id}",
            "stream": False,
            "simple_question": True,
            "with_source": True,
            "question": "你们是免费的吗？",
        }
        response = requests.post(url, headers=headers, json=data)
        self.assertEqual(response.status_code, 200)
        answer = response.json()["answer"]
        print("answer2", answer)
        assert "是免费" in answer

    def test_ask_gptbase_3(self):
        url = (
            "https://api-dev.gbase.ai/v1/question/dfeafaa2-3c8d-423d-9113-d1152f97a0c7"
        )
        headers = {
            "Content-Type": "application/json",
        }
        session_id = uuid.uuid4()
        data = {
            "session_id": f"{session_id}",
            "stream": False,
            "simple_question": True,
            "with_source": True,
            "question": "支持日语吗?",
        }
        response = requests.post(url, headers=headers, json=data)
        self.assertEqual(response.status_code, 200)
        answer = response.json()["answer"]
        print("qa3", data["question"], answer)
        # sometimes test fail, answer is:
        # はい、サポートしています。GBaseは現在、約95の言語をサポートしており、どの言語でも質問をすることができます。**Chatbotはあらゆる言語を理解し、お好みの言語で返答します**。
        assert "支持" in answer
        assert "日语" in answer
        assert "任何语言" in answer

    def test_ask_gptbase_4(self):
        url = (
            "https://api-dev.gbase.ai/v1/question/dfeafaa2-3c8d-423d-9113-d1152f97a0c7"
        )
        headers = {
            "Content-Type": "application/json",
        }
        session_id = uuid.uuid4()
        data = {
            "session_id": f"{session_id}",
            "stream": False,
            "simple_question": True,
            "with_source": True,
            "question": "如何分享一个bot?",
        }
        response = requests.post(url, headers=headers, json=data)
        self.assertEqual(response.status_code, 200)
        answer = response.json()["answer"]
        print("qa4", data["question"], answer)
        assert "朋友" in answer or "社交" in answer
        assert "共享" in answer or "分享" in answer
        assert "私有" in answer
        assert "公开" in answer or "公共" in answer

    def test_ask_gptbase_jp_q(self):
        url = (
            "https://api-dev.gbase.ai/v1/question/dfeafaa2-3c8d-423d-9113-d1152f97a0c7"
        )
        headers = {
            "Content-Type": "application/json",
        }
        session_id = uuid.uuid4()
        data = {
            "session_id": f"{session_id}",
            "stream": False,
            "simple_question": True,
            "with_source": True,
        }
        question = "GBaseの強みは何ですか？"
        data["question"] = question
        response = requests.post(url, headers=headers, json=data)
        self.assertEqual(response.status_code, 200)
        answer = response.json()["answer"]
        print(f"q: {question}, a: {answer}")
        assert "顧客満足度" in answer
        assert "解決" in answer
        assert "迅速" in answer

    def test_ask_gptbase_en_reply(self):
        url = (
            "https://api-dev.gbase.ai/v1/question/dfeafaa2-3c8d-423d-9113-d1152f97a0c7"
        )
        headers = {
            "Content-Type": "application/json",
        }
        session_id = uuid.uuid4()
        data = {
            "session_id": f"{session_id}",
            "stream": False,
            "simple_question": True,
            "with_source": True,
        }
        question = "GBase强项是什么，请用英文回答。"
        data["question"] = question
        response = requests.post(url, headers=headers, json=data)
        self.assertEqual(response.status_code, 200)
        answer = response.json()["answer"]
        print(f"q: {question}, a: {answer}")
        assert "understand" in answer
        assert "questions" in answer
        assert "answer" in answer
