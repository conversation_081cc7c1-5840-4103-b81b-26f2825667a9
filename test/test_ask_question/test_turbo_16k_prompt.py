import asyncio
import time
import unittest
from typing import Optional

import openai
from langchain_core.messages import SystemMessage, HumanMessage

from mygpt.endpoints.questions import create_final_chat_question
from mygpt.enums import OpenAIModel
from mygpt.openai_utils import (
    chat_acreate,
    create_langchain_chat_history_from_openai_json,
)
from mygpt.prompt import get_talking_style, get_unknown_text
from mygpt.prompt_history import PromptCache
from mygpt.schemata import QuestionIn
from mygpt.utils import convert_language_code


async def print_stream(stream):
    rs = ""
    async for item in stream:
        print(item)
        rs += item
    return rs


def ask_question(
    question: str,
    context: Optional[str],
    response_language: str,
    chat_history=None,
    use_gpt4=False,
):
    if chat_history is None:
        chat_history = []
    try:
        response_language = convert_language_code(response_language)
        embeddings = list()
        questionIn = QuestionIn(question=question, session_id="abc")  # , stream=True
        response, _, _, _, _ = asyncio.run(
            create_final_chat_question(
                ai_id="19452db9-8636-4815-9bf4-11c3323bc152",
                question=questionIn,
                embeddings=embeddings,
                context=context,
                chat_history=chat_history,
                response_language=response_language,
                final_question_use_gpt_4=use_gpt4,
                # format=StreamingMessageDataFormat.JSON_AST,
            )
        )
        # answer = asyncio.run(print_stream(response))

    except openai.RateLimitError:
        print("retry after 20s")
        time.sleep(20)
        return ask_question(question, context, response_language)

    answer = response.content
    print(answer)
    return answer


class TestTurbo16kFinalQA(unittest.IsolatedAsyncioTestCase):
    @classmethod
    def setUpClass(cls):
        # 在所有测试开始前执行一次。
        asyncio.run(PromptCache.setup(use_latest=True))

    def test_cannot_answer_zh(self):
        context = None
        question = "ELK是什么?"
        rsp = ask_question(question, context, "zh")
        # print('rsp0', rsp)
        assert "很抱歉" in rsp

    def test_cannot_answer_jp(self):
        context = '''
"""
博報堂プロダクツサステナビリティ方針 \n1.組織統治 \n〈1-1〉コーポレートガバナンス／内部統制
取引先のビジネスニーズに応え、企業価値の向上を追求しながら、社会規範、社会情勢、法令、及び「博報堂 DY グループ行動規
範」、並びに当社の「行動の基本 10 の宣言」を遵守し、その遵守を励行する組織体制を備え、透明性のある倫理的な企業経営を
遂行する。 \n〈1-2〉事業継続性 \n事業継続を阻害するリスクを分析し、事業への影響の精査と事前対策方針、その取り組み状況\
をまとめた事業継続計画（BCP）の\n策定に努める。 \n2.人権
〈2-１〉人権へのコミットメントの表示・各種国際基準の尊重
当社は「国際人権章典」、国際労働機関（ILO）の「労働における基本的原則及び権利に関する宣言」、ユニセフの「子どもの権利と
ビジネス原則」および、博報堂 DY グループ人権方針に基づき、当社の取引先や、グループ会社およびサービスを提供するすべての関
係者と協力しながら、その尊重に努める。 \n〈2-2〉デューディリジェンスをはじめとする、人権への影響対処
国連の「ビジネスと人権に関する指導原則」および「OECD 多国籍企業方針」に基づいて、当社の事業活動における人権に対する負
の影響を特定し、防止、軽減する取り組みを行う。\n"""\n\n"""\n- サステナブル調達\nガイドライン
- 博報堂プロダクツと各協力会社が一体となり、社会や環境に配慮した責任ある調達を実現していくために、\
2030年までに取り組むべき30事項を定めました。\n- 協力会社との信頼構築
- 協力会社との円滑なコミュニケーションやビジネスの透明性を高めることを目的とした総合ポータルサイト
「PRODUCT’S Partner Portal」を運用。電子契約をスムーズに行える機能のほか、各種お問い合わせへの対応にも活用しています。
- サプライチェーン\n構築支援\n- 販促・MDグッズの持続可能なサプライチェーン構築を支援する「サステナブル・\
サプライチェーン・ガイドブック」を作成。調達、生産、物流、販売の4フェーズごとに、企業の課題に合わせた適切なソ\
リューションを提供します。\n博報堂プロダクツ\n調達基本方針\n私たちは、調達に関する情報を発信し、国内外すべて\
の企業に対し、公正な競争機会を提供します。\n私たちは、経営の信頼性だけでなく、ビジネスニーズに応えるため品質、\
コスト、納期を追求します。\n私たちは、法令や社会規範を遵守し、企業倫理に基づいた公正な取引を行います。
私たちは、人権と環境に配慮した責任ある調達活動に取り組みます。\n私たちは、協力会社との協働と共創を大切にし、\
ともに信頼できる関係構築に努めます。
'''
        failed_msg = "残念"  # 'Unfortunately'
        print(context)
        rsp = ask_question("今日の天気はいかがですか？", context, "ja")
        # typical rsp: 残念ながら、私の現在の知識ではこの質問には答えることができません。他の質問をしていただけますか？
        assert failed_msg in rsp or "申し訳" in rsp

    def test_cannot_answer(self):
        context = '''
"""
第二十四届冬季奥林匹克运动会（英语：the XXIV Olympic \
Winter Games；法语：les XXIVes Jeux olympiques d'hiver），\
又称2022年冬季奥运会，一般称为北京冬奥，于2022年2月4日至2月20日在中华人民共和国首都北京市举行， \
此外，河北省西北部城市张家口也承办了本届冬奥会大部分户外冰雪项目。这是中国首次举办冬季奥运会，\
而作为主办地点之一的北京也因而成为首座“双奥之城”（既举办过夏季奥运会，又举办过冬季奥运会的城市）。\
[2]本次冬奥会与2018年平昌冬奥会、2020年东京奥运会连续在亚洲举行，\
是奥运会历史上首次在欧洲以外的大洲连续举行奥运会。
"""'''

        context2 = '''
"""
第二十四届冬季奥林匹克运动会（英语：the XXIV Olympic
Winter Games；法语：les XXIVes Jeux olympiques d'hiver），
又称2022年冬季奥运会，一般称为北京冬奥，于2022年2月4日至2月20日在中华人民共和国首都北京市举行，
此外，河北省西北部城市张家口也承办了本届冬奥会大部分户外冰雪项目。这是中国首次举办冬季奥运会，
而作为主办地点之一的北京也因而成为首座“双奥之城”（既举办过夏季奥运会，又举办过冬季奥运会的城市）。
[2]本次冬奥会与2018年平昌冬奥会、2020年东京奥运会连续在亚洲举行，
是奥运会历史上首次在欧洲以外的大洲连续举行奥运会。
"""'''
        print(context)
        failed_msg = "无法"  # '残念ながら'
        # rsp = ask_question('习近平是谁？', context, 'zh')
        # assert failed_msg in rsp
        rsp = ask_question("哪个队伍获得了女子冰壶项目的金牌？", context, "zh")
        assert failed_msg in rsp
        rsp = ask_question("公司员工有多少？", context, "zh")
        assert failed_msg in rsp
        rsp = ask_question("库克是谁？", context, "zh")
        assert failed_msg in rsp
        rsp = ask_question("What is the meaning of life?", context, "en")
        assert "Unfortunately" in rsp

    def test_dataset_about(self):
        context = '''
"""
ABOUT\n企業情報\nNEWS\nニュース\nTOPICS\nトピックス\nSOLUTIONS
ソリューション\nPROJECTS\n実績紹介\nSUSTAINABILITY\nサステナビリティ\nCAREERS
採用情報\nお問い合わせ\n▶ PRODUCT'S Partner Portal\nABOUT\n社長メッセージ\n事業領域
私たちの強み\n創業の理念\n行動指針\n会社概要\nアクセス\n支社・グループ会社\n会社概要
社名\n株式会社博報堂プロダクツ (HAKUHODO PRODUCT'S INC.)\n設立\n2005年10月1日
資本金\n1億円［株式会社博報堂（100%出資）]\n社員数\n2,102名（2023年4月現在）
代表者\n代表取締役社長　岸 直彦\n本社所在地\n〒135-8619　東京都江東区豊洲5-6-15 NBF豊洲ガーデンフロント
事業領域\n総合制作事業\n役員一覧\n代表取締役社長\n岸 直彦\n取締役常務執⾏役員\n大垣 剛久\n石井 孝次郎
高橋 秀行\n取締役執行役員\n和田 圭司\n武内 寛雄\n中村 武司\n取締役（非常勤）\n藤井 久\n波多野 昌樹
禿河 毅\n望月 圭介\n安保 鉄也\n執行役員\n鍬形 治\n茂木 敦\n菊地 友幸\n小澤 浩\n鈴木 剛\n小幡 朋州
西脇 寛文\n瀧澤 武仁\n摺澤 和浩\n久保田 浩司\n長田 芳曉\n渡部 直之\n石﨑 優\n森 真吾\n北川 和毅
監査役\n小竹 伸幸\n監査役（非常勤）\n後藤 裕一\n今泉 智幸\n許認可等・認証\n\n［制作・運営事業領域関連］
建設業許可（国土交通大臣許可 特29第22397号）\n一級建築士 事務所登録（東京都知事登録 第49733号）
警備業認定〔1号警備～4号警備〕（東京都公安委員会 第30003353号）\n［プレミアム事業領域］
一般酒類小売業免許（所轄税務署）\n［情報セキュリティ関係の認証］\nプライバシーマーク
JIPDEC（一般財団法人 日本情報経済社会推進協会）により認証\n10830233（09）
ISO 27001/JIS Q 27001適合認証\n博報堂グループとしてANAB（米国規格協会-米国品質協会による認定機関）
ならびにJIPDEC（一般財団法人 日本情報経済社会推進協会）により認証\nIS 86392/ISO/IEC 27001:2013
IS 86392/JIS Q 27001:2014\n\nアクセス\nTOP\nABOUT\nプライバシーポリシー
（個人情報保護方針～個人情報に関する公表事項）\n採用応募者の個人情報の取り扱いについて
お問い合わせに関する個人情報の取り扱いについて\n情報セキュリティ\nソーシャルメディアポリシー
DXの取り組みについて\nサイトマップ\nこのサイトについて\nFacebook\n\n© 2023 HAKUHODO PRODUCT’S
\nABOUT\nNEWS\nTOPICS\nSOLUTIONS\nPROJECTS\nSUSTAINABILITY\nDIVISIONS\nフォトクリエイティブ
映像クリエイティブ\nREDHILL\n動画ビジネスデザイン\n統合クリエイティブ\nデジタルプロモーション
カスタマーリレーション\nデータビジネスデザイン\nイベント・スペースプロモーション\nプレミアム
リテールプロモーション\nプロモーションプロデュース\n関西支社\n中部支社\n九州支社\nCAREERS
新卒採用\n中途採用\n障がい者採用\nSPECIAL\nSUSTAINABLE ENGINE\nVisual Transformation by PRODUCT′S
ショッパーマーケティング・イニシアティブ\nCONTACT US\nENGLISH
"""
'''
        rs2 = ask_question("設立", context=context, response_language="ja")
        assert "2005年10月1日" in rs2
        rs2 = ask_question("資本金", context=context, response_language="ja")
        assert "1億円" in rs2
        rs2 = ask_question("資本金は", context=context, response_language="ja")
        assert "1億円" in rs2
        rs2 = ask_question("資本金は？", context=context, response_language="ja")
        assert "1億円" in rs2
        rs2 = ask_question("注册资本是多少？", context=context, response_language="ja")
        assert "1億円" in rs2
        rs2 = ask_question("所在地", context=context, response_language="ja")
        assert "5-6-15" in rs2
        rs2 = ask_question("社員数", context=context, response_language="ja")
        assert "2,102名" in rs2
        rs2 = ask_question("公司员工有多少？", context=context, response_language="ja")
        assert "2,102名" in rs2
        rs2 = ask_question(
            "会社取得了哪些证书认证？", context=context, response_language="zh"
        )
        assert "建设" in rs2

        context = '''
"""
What can GBase chatbots do? \nCurrently, chatbots created in GBase can support the following
types of chatbots: \n• \nPaper-reading chatbots can learn from the documents you provide
and can answer any questions about the contents of the \ndocuments. \n•
External service chatbots can learn from the customer service
manual and can answer customers' common questions and \nconsultations. \n•
Service query chatbots can learn from service content and can \nprovide help to customers.
• \nQ&A role chatbots can converse with you in various roles to
eliminate customer doubts and helplessness. \nUser Guide
Getting started | How to use GBase's \nservices?
Step 1: Launch your computer's browser (Chrome \\ Edge \\ Firefox \\ \nSafari).
Step 2: Visit the https://gbase.ai website in your browser.
Step 3: Once you have successfully accessed https://gbase.ai, you \nwill see the homepage.
Step 4: You can try out other users' public chatbots on the homepage. \n
Registering an account | How to create a \nGBase account?
Step 1: Visit https://gbase.ai in your web browser and access the
homepage.
"""

"""
GBase.ai For Curious Minds: \nunlock a wealth of knowledge with Chatbot. Discover new insights and
answers from historical documents, poetry, and literature
effortlessly. Chatbot can understand any language and reply to your \npreferred one. \n
How can GBase help me? \nIf you have a website or web-based product, GBase can help you in
the following ways: \n• \nOnline consultation services: Using ChatGPT-based chatbots,
you can understand customers' complex questions and give \nappropriate answers. \n•
Online query support: Using natural language, you can quickly
help customers query the company's or product's services and \npurchasing policies. \n•
Online Q&A dialogue: By providing personalized services to
customers through Q&A dialogue, you can free your team from \nrepetitive Q&A. \n•
Customer operation guides: Understand customers' problems and
give accurate operation guides, thereby eliminating customer \nhelplessness.
What abilities do GBase chatbots have? \nUnderstanding how chatbots work and what \
they can do for your \nbusiness: \n• \nUnderstanding ability: Understand customers' complex questions
and provide appropriate answers. \n• \nQuick response: Help customers quickly query related services
and provide detailed guidance. \n• \nInteractive Q&A: Eliminate customers' doubts and helplessness
through interactive Q&A. \n• \nQuery support: Help customers quickly understand product or
service policies and service support.
"""

"""
You can base your questions on the content, keywords, and other
relevant information learned by the chatbot, and you can get relevant
answers. Please note that if your question is not within the scope of
the chatbot's training, the chatbot may not be able to answer your \nquestion accurately.
Where is my data stored, and is it safe? \nGBase.ai takes data security very seriously, \
and we encrypt user \ndocuments and store them on the world's recognized secure Amazon
cloud server. \nDoes GBase.ai use ChatGPT or GPT-4?
By default, your chatbot uses ChatGPT (gpt-3.5-turbo). We will soon
open the operation of editing the chatbot, and you can easily switch
the chatbot to the GPT-4 setting option. (Please note that the system
currently calculates tokens based on the length of the text. For
English text, 1 token is approximately 4 characters or 0.75 words.)
Does GBase.ai support language recognition?
Yes, it is fully supported. Gbase currently supports about 95
languages, and you can ask chatbot questions in any language.
Can I share the chatbot with my friends?
Yes, we support and encourage the sharing of chatbots. By default,
any document chatbot you create is private, but you can set whether
to make it public. Public chatbots will appear on the GBase.ai
homepage or can be shared with friends or social networks on IM. When
creating a website chatbot only, it will be shown by default as \npublic.
"""
        '''
        question = "What abilities do GBase chatbots have?"
        rsp = ask_question(question, context=context, response_language="en")
        rsp = rsp.lower()
        print("gbase res1", rsp)
        assert "understand" in rsp
        assert "quick" in rsp
        assert "interactive q&a" in rsp
        assert "support" in rsp
        assert "service" in rsp
        context = '''
"""
Frequently Asked Questions \nIf you cant find your question, email <EMAIL>
Language: \n中文(简体) | 中文(繁体) | 日本語 \nIntroduction：
Create your own chatbot using your data and publish it to your \nwebsite.
GBase.ai with a single link, you can train a chatbot that can
answer any questions derived from information within that link. Then,
this chatbot can be added to your website as a chat widget to assist
outbound-oriented customers. \nFeatures：
GBase.ai allows for training of an outbound service chatbot on data
derived from an enterprise's customer service knowledge base. It is
the only customer service solution you need, solving problems faster
and increasing customer satisfaction, while reducing team workload. \n
📚GBase.ai For Students: \nChatbot effortlessly comprehends textbooks, research papers, academic
articles, handouts, and presentations. It enhances learning
experience and academic growth, enabling you to achieve success
efficiently and responsibly in your studies. \n GBase.ai For Work:
efficiently analyze your documents. From financial and sales reports
to project and business proposals, training manuals, and legal
contracts, Chatbot can quickly provide you with the information you
need.
"""

"""
GBase.ai For Curious Minds: \nunlock a wealth of knowledge with Chatbot. Discover new insights and
answers from historical documents, poetry, and literature
effortlessly. Chatbot can understand any language and reply to your
preferred one. \n \nHow can GBase help me?
If you have a website or web-based product, GBase can help you in
the following ways: \n• \nOnline consultation services: Using ChatGPT-based chatbots,
you can understand customers\' complex questions and give \nappropriate answers. \n•
Online query support: Using natural language, you can quickly
help customers query the company\'s or product\'s services and \npurchasing policies. \n•
Online Q&A dialogue: By providing personalized services to
customers through Q&A dialogue, you can free your team from \nrepetitive Q&A. \n•
Customer operation guides: Understand customers\' problems and
give accurate operation guides, thereby eliminating customer \nhelplessness.
What abilities do GBase chatbots have? \nUnderstanding how chatbots work and what they can do for your \nbusiness: \n•
Understanding ability: Understand customers\' complex questions \nand provide appropriate answers. \n•
Quick response: Help customers quickly query related services \nand provide detailed guidance. \n•
Interactive Q&A: Eliminate customers\' doubts and helplessness \nthrough interactive Q&A. \n•
Query support: Help customers quickly understand product or \nservice policies and service support.
"""

"""
Content: What can GBase chatbots do? Currently, chatbots created in GBase can support the following
types of chatbots: \n• \nPaper-reading chatbots can learn from the documents you provide
and can answer any questions about the contents of the \ndocuments. \n•
External service chatbots can learn from the customer service
manual and can answer customers\' common questions and \nconsultations. \n•
Service query chatbots can learn from service content and can \nprovide help to customers. \n•
Q&A role chatbots can converse with you in various roles to
eliminate customer doubts and helplessness. \nUser Guide
Getting started | How to use GBase\'s \nservices?
Step 1: Launch your computer\'s browser (Chrome \\ Edge \\ Firefox \\ \nSafari).
Step 2: Visit the https://gbase.ai website in your browser.
Step 3: Once you have successfully accessed https://gbase.ai, you \nwill see the homepage.
Step 4: You can try out other users\' public chatbots on the homepage. \n
Registering an account | How to create a \nGBase account?
Step 1: Visit https://gbase.ai in your web browser and access the \nhomepage.\n
"""

"""
Content: Step 2: Find the "Login" button on the homepage and click it to enter \nthe login page.
Step 3: Please use "Continue with Google" to log in. \n
Creating a chatbot | How to create a chatbot \nin GBase?
Step 1: Find and click the "Create Chatbot for Website" button on the \nhomepage. \n
Step 2: Enter contents such as "Help Center", "FAQ", "Knowledge \nBase", and other link contents.
Step 3: In the chatbot training interface, there will be a \n"Success/Processing" status in the training list. \n
Step 4: When the connection icon changes to "Success", it means the \nlearning is complete. \n
Step 5: Click the "Try it Now" button to chat with the chatbot and \ntest it out by asking questions. \n
Training Robots | How to train robots and \nensure they can serve customers?\n\n
"""
'''
        question = "怎么在GBase.ai上创建一个聊天机器人？"
        rsp = ask_question(question, context=context, response_language="zh")
        assert "主页上" in rsp
        assert "链接内容" in rsp
        assert "训练界面" in rsp
        assert "学习完成" in rsp
        assert "通过提问" in rsp

    def test_dataset_olympics_with_chat_history(self):
        context = '''
"""
List of 2022 Winter Olympics medal winners

==Curling==

{{main|Curling at the 2022 Winter Olympics}}
{|{{MedalistTable|type=Event|columns=1|width=225|labelwidth=200}}
|-valign="top"
|Men<br/>{{DetailsLink|Curling at the 2022 Winter Olympics – Men's tournament}}
|{{flagIOC|SWE|2022 Winter}}<br/>[[Niklas Edin]]<br/>[[Oskar Eriksson]]<br/> \
    [[Rasmus Wranå]]<br/>[[Christoffer Sundgren]]<br/>[[Daniel Magnusson (curler)|Daniel Magnusson]]
|{{flagIOC|GBR|2022 Winter}}<br/>[[Bruce Mouat]]<br/>[[Grant Hardie]]<br/>\
    [[Bobby Lammie]]<br/>[[Hammy McMillan Jr.]]<br/>[[Ross Whyte]]
|{{flagIOC|CAN|2022 Winter}}<br/>[[Brad Gushue]]<br/>[[Mark Nichols (curler)|Mark Nichols]]<br/>\
    [[Brett Gallant]]<br/>[[Geoff Walker (curler)|Geoff Walker]]<br/>[[Marc Kennedy]]
|-valign="top"
|Women<br/>{{DetailsLink|Curling at the 2022 Winter Olympics – Women's tournament}}
|{{flagIOC|GBR|2022 Winter}}<br/>[[Eve Muirhead]]<br/>[[Vicky Wright]]<br/>[[Jennifer Dodds]]<br/>\
[[Hailey Duff]]<br/>[[Mili Smith]]
|{{flagIOC|JPN|2022 Winter}}<br/>[[Satsuki Fujisawa]]<br/>[[Chinami Yoshida]]<br/>[[Yumi Suzuki]]<br/>\
[[Yurika Yoshida]]<br/>[[Kotomi Ishizaki]]
|{{flagIOC|SWE|2022 Winter}}<br/>[[Anna Hasselborg]]<br/>[[Sara McManus]]<br/>[[Agnes Knochenhauer]]<br/>\
[[Sofia Mabergs]]<br/>[[Johanna Heldin]]
|-valign="top"
|Mixed doubles<br/>{{DetailsLink|Curling at the 2022 Winter Olympics – Mixed doubles tournament}}
|{{flagIOC|ITA|2022 Winter}}<br/>[[Stefania Constantini]]<br/>[[Amos Mosaner]]
|{{flagIOC|NOR|2022 Winter}}<br/>[[Kristin Skaslien]]<br/>[[Magnus Nedregotten]]
|{{flagIOC|SWE|2022 Winter}}<br/>[[Almida de Val]]<br/>[[Oskar Eriksson]]
|}
"""

"""
Curling at the 2022 Winter Olympics

==Results summary==

===Women's tournament===

====Playoffs====

=====Gold medal game=====

''Sunday, 20 February, 9:05''
{{#lst:Curling at the 2022 Winter Olympics – Women's tournament|GM}}
{{Player percentages
| team1 = {{flagIOC|JPN|2022 Winter}}
| [[Yurika Yoshida]] | 97%
| [[Yumi Suzuki]] | 82%
| [[Chinami Yoshida]] | 64%
| [[Satsuki Fujisawa]] | 69%
| teampct1 = 78%
| team2 = {{flagIOC|GBR|2022 Winter}}
| [[Hailey Duff]] | 90%
| [[Jennifer Dodds]] | 89%
| [[Vicky Wright]] | 89%
| [[Eve Muirhead]] | 88%
| teampct2 = 89%
}}
"""
'''
        # messages = get_message('哪个队伍获得了女子冰壶项目的金牌？', context, 'zh')
        # rsp = ask_question_with_history(messages)
        # messages.append({'role': 'assistant', 'content': rsp})
        # assert 'GBR' in rsp
        # messages.append({'role': 'user', 'content': '都有哪些成员？'})
        # rsp = ask_question_with_history(messages)
        # messages.append({'role': 'assistant', 'content': rsp})
        # assert 'Hailey Duff' in rsp
        # messages.append({'role': 'user', 'content': '这个队伍谁的得分最高？'})
        # rsp = ask_question_with_history(messages)
        # messages.append({'role': 'assistant', 'content': rsp})
        # assert 'Hailey Duff' in rsp
        # messages.append({'role': 'user', 'content': '最终得分是多少？'})
        # rsp = ask_question_with_history(messages)
        # messages.append({'role': 'assistant', 'content': rsp})
        # assert '89%' in rsp
        # messages.append({'role': 'user', 'content': '日本队的得分呢？'})
        # rsp = ask_question_with_history(messages)
        # messages.append({'role': 'assistant', 'content': rsp})
        # assert '78%' in rsp
        # 这个case无法通过，存在答偏的情况
        # messages.append({'role': 'user', 'content': '那队伍里谁的得分最低？'})
        # rsp = ask_question_with_history(messages)
        # messages.append({'role': 'assistant', 'content': rsp})
        # assert 'Eve Muirhead' in rsp

    def test_no_hallucination_with_history(self):
        context = """
    doc_id: 3716451e-323e-4ac6-ad97-16d7a1f8bee8
    source: https://www.freesh.com/product-62.html


    Twitterでシェア](https://twitter.com/share?text=DozoFreeshフルーツティー%E3%80%80アップルシナモンとアッサムティー%E3%80%80無添加%E3%80%80食べられるお茶%E3%80%80砂糖不使用%E3%80%80カロリーゼロ%E3%80%80カフェインレス%E3%80%80水分補給%E3%80%80プレゼント%E3%80%80ギフト%E3%80%80贈り物&url=https://www.freesh.com/shop/shop/detail?id=62)
    [![](/assets/cca31ca5/icon-line.svg)
    LINEでシェア](https://lineit.line.me/share/ui?url=https://www.freesh.com/shop/shop/detail?id=62)
    ** マルベリーワイン **

    ＜製品情報＞
    原材料：オレンジ、リンゴ、桑の実、キンカン、シナモン、竜眼、アッサムティー
    賞味期限：製造日より9ヶ月
    JANコード：4595123988176
    内容量：32g
    サイズ：幅173×奥行10×高270mm
    砂糖不使用、着色料不使用、保存料不使用
    """
        chat_history_json = [
            {"role": "user", "content": "天安门在哪"},
            {
                "role": "assistant",
                "content": "很抱歉，根据我当前的知识，无法回答您关于天安门在哪的问题。您可以问我其他问题吗？",
            },
            {"role": "user", "content": "felo是什么"},
            {
                "role": "assistant",
                "content": '很抱歉，根据我当前的知识，无法回答您关于"felo"是什么的问题。您可以问我其他问题吗？',
            },
        ]
        chat_history = create_langchain_chat_history_from_openai_json(
            chat_history_json=chat_history_json
        )

        answer = ask_question(
            question="賞味期限?",
            context=context,
            response_language="ja",
            chat_history=chat_history,
        )
        print("answer0", answer)
        assert "9ヶ月" in answer

        answer = ask_question(
            question="ELK是什么?",
            context=context,
            response_language="zh-Hans",
            chat_history=chat_history,
        )
        print("answer1", answer)
        assert "不知道" in answer or "无法回答" in answer

        answer = ask_question(
            question="what is ELK?",
            context=context,
            response_language="en",
            chat_history=chat_history,
        )
        print("answer2", answer)
        assert (
            "do not known" in answer
            or "can not answer" in answer
            or "can't answer" in answer
        )

        answer = ask_question(
            question="马云是谁",
            context=context,
            response_language="zh",
            chat_history=chat_history,
        )
        print("answer3", answer)
        assert "不知道" in answer or "无法回答" in answer

    def test_no_hallucination_empty_context_no_history(self):
        context = None
        chat_history_json = []
        chat_history = create_langchain_chat_history_from_openai_json(
            chat_history_json=chat_history_json
        )
        answer = ask_question(
            question="ELK是什么?",
            context=context,
            response_language="zh-Hans",
            chat_history=chat_history,
        )
        print("repeat ask answer xx", answer)
        assert "不知道" in answer or "无法回答" in answer

    def test_no_hallucination_empty_context_repeat_ask(self):
        context = None
        chat_history_json = [
            {"role": "user", "content": "ELK是什么"},
            {
                "role": "assistant",
                "content": '很抱歉，根据我当前的知识，无法回答您关于"ELK"是什么的问题。您可以问我其他问题吗？',
            },
        ]
        chat_history = create_langchain_chat_history_from_openai_json(
            chat_history_json=chat_history_json
        )
        answer = ask_question(
            question="ELK是什么?",
            context=context,
            response_language="zh-Hans",
            chat_history=chat_history,
        )
        print("test_no_hallucination_empty_context_repeat_ask", answer)
        assert "不知道" in answer or "无法回答" in answer

    def test_no_hallucination_empty_context_long_history_repeat_ask(self):
        context = None
        chat_history_json = [
            {"role": "user", "content": "天安门在哪"},
            {
                "role": "assistant",
                "content": "很抱歉，根据我当前的知识，无法回答您关于天安门在哪的问题。您可以问我其他问题吗？",
            },
            {"role": "user", "content": "felo是什么"},
            {
                "role": "assistant",
                "content": '很抱歉，根据我当前的知识，无法回答您关于"felo"是什么的问题。您可以问我其他问题吗？',
            },
            {"role": "user", "content": "ELK是什么"},
            {
                "role": "assistant",
                "content": '很抱歉，根据我当前的知识，无法回答您关于"ELK"是什么的问题。您可以问我其他问题吗？',
            },
        ]
        chat_history = create_langchain_chat_history_from_openai_json(
            chat_history_json=chat_history_json
        )
        answer = ask_question(
            question="ELK是什么?",
            context=context,
            response_language="zh-Hans",
            chat_history=chat_history,
        )
        print("repeat ask answer4", answer)
        assert "不知道" in answer or "无法回答" in answer

    def test_no_hallucination_context_with_history_repeat_ask_important(self):
        chat_history_json = [
            {"role": "user", "content": "天安门在哪"},
            {
                "role": "assistant",
                "content": "很抱歉，根据我当前的知识，无法回答您关于天安门在哪的问题。您可以问我其他问题吗？",
            },
            {"role": "user", "content": "felo是什么"},
            {
                "role": "assistant",
                "content": '很抱歉，根据我当前的知识，无法回答您关于"felo"是什么的问题。您可以问我其他问题吗？',
            },
            {"role": "user", "content": "ELK是什么"},
            {
                "role": "assistant",
                "content": '很抱歉，根据我当前的知识，无法回答您关于"ELK"是什么的问题。您可以问我其他问题吗？',
            },
            {"role": "user", "content": "ELK是什么"},
            {
                "role": "assistant",
                "content": '很抱歉，根据我当前的知识，无法回答您关于"ELK"是什么的问题。您可以问我其他问题吗？',
            },
        ]
        context = """
    doc_id: 3716451e-323e-4ac6-ad97-16d7a1f8bee8
    source: https://www.freesh.com/product-62.html


    Twitterでシェア](https://twitter.com/share?text=DozoFreeshフルーツティー%E3%80%80アップルシナモンとアッサムティー%E3%80%80無添加%E3%80%80食べられるお茶%E3%80%80砂糖不使用%E3%80%80カロリーゼロ%E3%80%80カフェインレス%E3%80%80水分補給%E3%80%80プレゼント%E3%80%80ギフト%E3%80%80贈り物&url=https://www.freesh.com/shop/shop/detail?id=62)
    [![](/assets/cca31ca5/icon-line.svg)
    LINEでシェア](https://lineit.line.me/share/ui?url=https://www.freesh.com/shop/shop/detail?id=62)
    ** マルベリーワイン **

    ＜製品情報＞
    原材料：オレンジ、リンゴ、桑の実、キンカン、シナモン、竜眼、アッサムティー
    賞味期限：製造日より9ヶ月
    JANコード：4595123988176
    内容量：32g
    サイズ：幅173×奥行10×高270mm
    砂糖不使用、着色料不使用、保存料不使用
    """
        chat_history = create_langchain_chat_history_from_openai_json(
            chat_history_json=chat_history_json
        )
        answer = ask_question(
            question="ELK是什么?",
            context=context,
            response_language="zh-Hans",
            chat_history=chat_history,
        )
        print("repeat ask answer5", answer)
        assert (
            "不知道" in answer
            or "无法回答" in answer
            or "无法确定" in answer
            or "无法给出" in answer
            or "没有提供" in answer
        )

    def test_should_answer_formatted_pdf_text(self):
        context = """
         S4210series L3DIN-Rail Industry Switch

 産業用マネージド レイヤ3のイーサネット ・スイッチ

                                                      機能とメリット

                                                       • 10/100BaseT(X)   （RJ45コネクタ）
                                                       • 10/100/1000Base-Tx (RJ45 コネクタ)
                                                       • 簡単に設置可能なコンパクトなサイズ
                                                       • IP40保護等級の金属製筐体
                                                       • -40～85℃の広い動作温度範囲

                                                      認証
                                                      CE  FCC        RoHS
   製品紹介
 S4210 シリーズスイッチは、エンタープライズネットワークやオペレーター顧客向けのコスト効
 果の高いギガビットアクセスと10ギガビットアップリンクのニーズを満たす、レイヤー3 イーサ
 ネットスイッチです。S4210 は、高性能で低消費電力のネットワークプロセッサを使用し、ギガ
  ビットラインスピードの転送性能を提供し、グリーンイーサネットラインスリープ機能をサポー
  トし、業界内で同レベルの機器の中で最も低い消費電力を持ちます。また、強力なQoSおよび
 ACL機能をサポートし、IP + MAC +ポートバインディングサポートサービスフロー分類およびパ
 ケット優先度マーキングなどのセキュリティ機能をサポートしています。静的なデータパケット
 サンプリングをサポートし、SFLOW機能をサポートし、多ポートミラーリング分析機能をサポー
  トし、静的および柔軟なQinQ機能をサポートし、Ethernet OAM 802.3ag                         （CFM）、802.3ah
   （EFM）をサポートし、戦略に基づくIPV4/6ユニキャストルーティングをサポートしています。
 製品は工業用クラスで設計され、EMC工業レベル4要件を満たしています。製品は-40 から+85°C
 の広い動作温度範囲をサポートしています。スペース節約のためのコンパクトなサイズに加えて、
 各製品は85°Cでの100％バーンインテストをパスしており、高品質で高信頼性の転送を保証して
 います。これらすべての機能により、ビデオ監視、料金システム、ITS、工場自動化など、高信
 頼性の転送が必要なアプリケーションに最適な製品となっています。

   製品特長                                          Carrier-level equipment stability
                                                 ⚫ Based on Linux operating system，support
  Perfect safety mechanism                         IPv4/IPv6 dual protocol stack platform
  ⚫ Support ACL security filtering mechanism, which  ⚫ Support multiple link redundancy and network
    can provide security control functions based on  redundancy protocols such as
    MAC, IP, L4 port                               STP/RSTP/MSTP/ERPS/LACP
  ⚫ Support ARP attack automatic protection and user  Enhanced multi-service capabilities
    blocking functions
                                                 ⚫   Support DHCP Server and DHCP Relay
  ⚫ Support DHCP attack automatic protection based
                                                 ⚫   Support L2-Tunnel
    on Mac Address and user blocking functions
                                                 ⚫   Support Ethernet OAM protocol such as CFM 、EFM
  ⚫ Support DDOS and virus attack protection
  ⚫ Support DHCP-snooping/IP-source-Guard/802.1X  Rich routing protocol
    and other security features                  ⚫    Support static route
  ⚫ Support link protection functions such as BFD,  ⚫ Support dynamic route such as RIP, OSPF,BGP,
    FlexLink, LACP and ERRP                           ISIS
  ⚫ Support remote loop detection function       ⚫    Support IPV4/IPV6 dual protocol stack，Support
  ⚫ Support CFM、EFM                                   RIPng
  ⚫ Support remote user authentication based on  ⚫   Simple and easy-to-use network management
    Tacacs+, Radius, and Local user authentication   function
                                                 ⚫    Support CLI based on RS232 serial port, Telnet
  Powerful QoS capabilities
                                                      and SSH
  ⚫ Support port speed and flow speed limit function ⚫ Support WEB-based configure operation
  ⚫ Support classification based on service flow and  management, support SNMP V1/V2/V3
    Qos flow control function                    ⚫    Support remote upgrade or equipment through
  ⚫ Support queue scheduling algorithms such as WRR   FTP and TFTP
  ⚫ Support port-based and service flow-based
    mirroring function                           Industrial level hardware
                                                 ⚫    Level 4 EMC Protection
                                                 ⚫   Aluminum alloy housing, IP40 protection, fanless
                                                      design
                                                 ⚫    DIN-Rail structure

UNEA X                                                    1

----------------------- Page 2-----------------------

   Hardware Specifications
         Item                     S4210-8GE2XF-I-AC                                S4218-16GE2XF-I-DC
                     8*10/100/1000M Base-T, 2*1/10G Base-X           16*10/100/1000M Base-T, 2*1/10G Base-X
  Physical port      SFP/SFP+                                        SFP/SFP+
                      1*CONSOLE (RJ45)                               1*CONSOLE (RJ45)
  Switching
                     56Gbps                                          72Gbps
  Capacity
  Packet
                     41.66Mpps                                       53.568Mpps
  Forwarding Rate
  MAC Address         16K
  Multicast Group     1K
  Routing Table      512
  Memory         and
                     Memory：512MB, storage ：32MB
  storage
  Power Supply       DC:12~48V, dual power input
  Power Connector  4-pin 5.08mm-spacing plug-in terminal block
  Power
                     <17W                                            <22W
  Consumption
  Dimension
                      178X 152X39                                    178X 152X73
  (W*H*D)(mm)
  Weight(Kg)         ≤0.76kg                                         ≤1.1kg
  Relative Humidity 5%~95% non-condensing
  Temperature        Working -40°C~85°C, storage -40°C~85°C
  Cooling            Fanless
  IP      Protection IP40
  Class
                     EMI:
                     FCC CFR47 Part 15, EN55022/CISPR22, Class A
                     EMS:
                     IEC61000-4-2 (ESD): ±8kV (contact), ±15kV (air)
  EMS                IEC61000-4-3 (RS): 10V/m (80MHz-2GHz)
                     IEC61000-4-4 (EFT): Power Port: ±4kV; Data Port: ±2kV
                     IEC61000-4-5 (Surge): Power Port: ±2kV/DM, ±4kV/CM; Data Port: ±2kV
                     IEC61000-4-6 (CS): 3V (10kHz-150kHz); 10V (150kHz-80MHz)
                     IEC61000-4-16 (Common mode conduction): 30V (cont.), 300V (1s)
                     IEC60068-2-6 (Vibration)
  Machinery          IEC60068-2-27 (Shock)
                     IEC60068-2-32 (Free Fall)
  MTBF               360,000 hrs

  Software Specifications

  Items                                 Main Features
                                        16K Mac address
                     MAC                Support static MAC address setting
                                        Support port MAC address limit
                                        Support 4K VLAN
                     VLAN               Support Vlan based on port, MAC and protocol
                                        Support dual Tag VLAN, port-based static QinQ
                                        Support two-way bandwidth control
   L2 Features
                     Port Control       Support port storm suppression
                                        Support 9K Jumbo ultra-long frame forwarding
                                        Support port mirroring
                     Port Mirroring
                                        Support stream mirroring
                                        Support static link aggregation
                     Port Aggregation   Support dynamic LACP
                                        Each aggregation group supports a maximum of 8 ports
                     ARP                Support ARP table item aging
                                        Support ARP proxy
                                        Support the identification of customer IP address conflicts
                     IPv4               Support static IPv4 routing
   L3 Features
                                        Support Ping, traceroute
                                        Support VRRP
                                        Support OSPF, RIP, PIM, BGP

UNEA X                                                                     2

----------------------- Page 3-----------------------

  Software Specifications
  Items                                Main Features
                                       Support standard and extended ACL
                                       Support ACL policy based on time range
                    ACL                Provide flow classification and flow definition based on source/destination
                                       MAC address, VLAN, 802.1p, ToS, DSCP, source/destination IP address,
                                       TCP/UDP port number, protocol type, etc.
                                       Support flow rate limiting function based on custom business flow
  Service                              Supports mirroring and redirection functions based on custom business flows
                    QoS                Support priority marking based on custom service flow, support 802.1P, DSCP
                                       priority Remark capability Support port-based priority scheduling function,
                                       support queue scheduling algorithms such as WRR
                                       Support IGMPv1 / V2 / V3 snooping
                    Multicast          Support MVR
                                       Support IGMP proxy
                                       Support anti-ARP-Spoofing
                                       Support anti-ARP-flooding
                                       Support IP source guard and IP+MAC+port+VLAN binding table
                    User Security      Support port isolation
                                       Support port MAC binding and MAC filtering

                                       Support IEEE 802.1X authentication

                                       Support Radius&TACACS+ authentication
                                       Support SNMPv3 encryption management
                    Switch Security    Supporting Security-IP telnet mechanism
  Security                             Support user hierarchical management and password protection
                                       Support ARP traffic detection based on per user MAC address
                                       Support ARP message suppression or user blocking based on ARP traffic
                                       detection
                                       Support IP-MAC binding based on dynamic ARP table
                                       Support manual P+MAC+port+VLAN binding
                    Network Security
                                       Support MAC address learning limit, support black hole MAC function

                                       Support port isolation

                                       Support port broadcast
                                       Support DHCP option82 and PPPoE+
                                       Support STP/RSTP/MSTP
                     Ring Protection   Support ERPS Ethernet ring network protection protocol
                                       Support Loopback-detection port loopback detection

                     Line Protection   Support manual and dynamic LACP

                                       Support LLDP
                     Network           Support Ethernet OAM, 802.3ah EFM and 802.1ag CFM
                     Maintenance       Support RMON (remote monitoring) groups 1,2,3,9
                                       Support Ping and traceroute tool

                                       Support CLI, WEB and SNMPv1/v2c/v3 management
                                       Support Console/Telnet/SSH
                                       Support virtual cable detection (VCT)
                     Management        Support SNTP protocol
                                       Support user hierarchical management
                                       Support Radius/TACACS+ authentication
                                       Support FTP,TFTP file upload and download

  注文情報

 Model Name              10/100/1000BaseT(X)              1/10G Base-X                      Operating
                         Ports (RJ45 connector)           (SFP/SFP+ connector)              Temperature

 S4210-8GE2XF-I-DC       8                                2                                 -40°C~85°C

 S4218-16GE2XF-IDC       16                               2                                 -40°C~85°C

 UNEAX Co., Ltd. All rights reserved. 2023年5月12 日更新。UNEAX株式会社の明白な許可を書面
 で取得しない限り、本書およびその一部の複製や使用はいかなる方法やいかなる場合でも許可さ
 れません。製品の仕様は予告なく変更されることがあります。最新の製品情報については当社の
 Webサイトをご覧ください。
"""

        answer = ask_question(
            question="S4210-8GE2XF-I-AC 工作温度？",
            context=context,
            response_language="zh-Hans",
            chat_history=[],
        )
        print("ask answer6", answer)
        assert "-40°C" in answer and "85°C" in answer

        chat_history_json = [
            {"role": "user", "content": "ELK是什么"},
            {
                "role": "assistant",
                "content": '很抱歉，根据我当前的知识，无法回答您关于"ELK"是什么的问题。您可以问我其他问题吗？',
            },
            {"role": "user", "content": "ELK是什么"},
            {
                "role": "assistant",
                "content": '很抱歉，根据我当前的知识，无法回答您关于"ELK"是什么的问题。您可以问我其他问题吗？',
            },
            {"role": "user", "content": "S4210-8GE2XF-I-AC 工作温度？"},
            {
                "role": "assistant",
                "content": "很抱歉，根据我当前的知识，无法回答这个问题。您可以问我其他问题吗？",
            },
        ]
        chat_history = create_langchain_chat_history_from_openai_json(
            chat_history_json=chat_history_json
        )

        answer = ask_question(
            question="S4210-8GE2XF-I-AC 工作温度？",
            context=context,
            response_language="zh-Hans",
            chat_history=chat_history,
        )
        print("ask answer6", answer)
        assert "-40°C" in answer and "85°C" in answer

        answer = ask_question(
            question="S4218-16GE2XF-I-DC 功耗？",
            context=context,
            response_language="zh-Hans",
            chat_history=chat_history,
        )
        print("ask answer7", answer)
        assert "<" in answer or "小于" in answer
        assert "22W" in answer

        answer = ask_question(
            question="S4218-16GE2XF-I-DC 电源电压？",
            context=context,
            response_language="zh-Hans",
            chat_history=chat_history,
        )
        print("ask answer8", answer)
        assert "12" in answer and "48" in answer

    def test_should_answer_what_is_circleo_with_history(self):
        context = """
doc_id: c192a898-127f-4f6f-97e2-0b0c515f97f1

Create a Digital Space for Remote Collaboration
An exciting new era of digital intelligence experience is coming — one that's poised to radically change how we work, host events and communicate
LOVED NY REMOTE COLLABORATION TEAMS WORLDWIDE
Online Consulting Solutions
Meta Consulting
Enhance professional practice for business growth!
Virtual Workspaces
Virtual Office
Boost creativity with an immersive workspace!
Launch your online business quickly with CircleO!
Remote Service
Take Remote outof Remote Service
Virtual Events
Move Business Eventsto Virtual Space
CircleO
Put Customer Data Security First
All-round protection to meet the security level of the bank, so you can enjoy the service with peace of mind
Data Transfer Security
Ensure your data is secure with bank-level encryption
Data Storage Security
Keep your data safe through encryption and device binding
Communications encryption
Keep your conversations secure with advanced encryption
Product Safety Certificate
Ensure your accounts are secure with two-factor authentication
COLLABORATE USING YOUR FAVORITE TOOLS
CircleO Create more value for users with cutting-edge application integration
CircleO
A partner you can trust
An effortless and stress-free
office experience
office experience
78 mins
Weekly average member
online activity duration
online activity duration
Team interactions with higher
levels of engagement
levels of engagement
94 %
Average weekly space
member activity online
member activity online
Team communication
Collaboration is more frequent
Collaboration is more frequent
8 +
Weekly usage rate of space
conference rooms
conference rooms
CircleO digital space to unleash digital productivity for online consulting , virtual offices , virtual events and more to achieve maximum balance between career and life
Product
Support
Contact
© 2023 Sparticle. All rights reserved.
"""

        answer = ask_question(
            question="什么叫circleo?",
            context=context,
            response_language="zh-Hans",
            chat_history=[],
        )
        print("ask answer x0", answer)
        assert "远程" in answer or "在线" in answer or "虚拟" in answer

        chat_history_json = [
            {"role": "user", "content": "什么叫circleo?"},
            {
                "role": "assistant",
                "content": "CircleO是一个数字空间，用于远程协作、在线咨询、虚拟办公和虚拟活动等。它提供了创造力和沟通加密等功能，以确保用户的数据安全。CircleO还支持与其他应用程序的集成，旨在为用户创造更多价值。",
            },
        ]
        chat_history = create_langchain_chat_history_from_openai_json(
            chat_history_json=chat_history_json
        )

        answer = ask_question(
            question="circleo 是什么？ ",
            context=context,
            response_language="zh-Hans",
            chat_history=chat_history,
        )
        print("ask answer x1", answer)
        assert "远程" in answer or "在线" in answer
        assert "虚拟" in answer

    def test_should_no_hallucination_with_history(self):
        context = """
doc_id: 1568f359-555a-4bc6-b823-f73853c73981
source: https://helpdesk.aslead.cloud/wiki//pages/viewpage.action?pageId=15664012



-   【aslead Remote Standardプラン】クライアントアプリから接続する時、「証明書がありません」エラーが発生する                 -   【aslead Remote Standardプラン】リモートデスクトップしようとすると「内部エラー」が発生する                 -   【aslead Remote Standardプラン】ログイン後、「No applications available. Please contact your IT administrator for assistance.」と表示される                 -   【aslead Remote Standardプラン】ログイン後に「リモートデスクトップ」をクリックすると、「No streaming resource are available」が表示される             -   【aslead Remote Standardプラン】キー・ショートカットキーに関する事象一覧                 -   【aslead Remote Standardプラン】Alt + Tabでアプリケーションが切り替えられない                 -   【aslead Remote Standardプラン】DELETEキーを押すと、. (ピリオド)が入力される                 -   【aslead Remote Standardプラン】Escキーを使用したい                 -   【aslead Remote Standardプラン】キーボードのDeleteキー、Insertキーが使えない                 -   【aslead Remote Standardプラン】クライアント版での既知の問題                 -   【aslead Remote Standardプラン】ショートカットキーを使用して画面キャプチャを取りたい                 -   【aslead Remote Standardプラン】文字入力時、英数変換、全角・半角変換に切り替わってしまう             -   【aslead Remote Standardプラン】リモートデスクトップの動作が遅いので改善したい         -   OSSプラン             -   【aslead Remote OSSプラン】https://remote.aslead.cloud/ossにアクセスすると、「このページは動作していません」と表示される             -   【aslead Remote OSSプラン】OSSプランを利用するために何が必要か知りたい             -   【aslead Remote OSSプラン】「＠」「:」「_」を押すと別の文字が表示される             -   【aslead Remote OSSプラン】エラー一覧・対処方法             -   【aslead Remote OSSプラン】カテゴリ内での利用（申請）状況を確認したい             -   【aslead Remote OSSプラン】パートナー推進部貸出のシンクライアント端末でGuacamole Desktop版アプリを利用したい



... (chunk index 10 - 16 skipped) ...


-   【aslead Survey】フィールドの説明文の掲載位置は変更可能ですか？         -   【aslead Survey】リマインド機能を使いたい         -   【aslead Survey】ログインに失敗する         -   【aslead Survey】一部の投稿データがREPORTSに反映されない         -   【aslead Survey】匿名の状態(非ログイン状態)でなければアンケートページが閲覧できないようにしたい         -   【aslead Survey】回答を送信する前に確認画面を表示したい         -   【aslead Survey】回答後に設問を非表示にしたい         -   【aslead Survey】回答者が改行のつもりでEnterを押下した際に誤送信を防ぐ方法が知りたい         -   【aslead Survey】投稿一覧で必須項目の回答データが空欄で表示される         -   【aslead Survey】無記名の場合、管理者にも回答者がわからないようにする方法が知りたい         -   【aslead Survey】通知設定「送信元」情報を変更したい
    -   FAQ(aslead Drive)         -   【aslead Drive】エラー／トラブルシュート             -   【aslead Drive】初回接続時に発生するエラーを対処したい。             -   【aslead Drive】インターネット接続権限不足でログインできません。対処方法を教えてください。             -   【aslead Drive】ポートが使用されてログインできません。対処方法を教えてください。             -   【aslead Drive】「PCM-OIDC-0006 トークンレスポンスがエラーです。」の対処方法を教えてください。             -   【aslead Drive】「ファイルをコピーできません」が発生しました。対処方法を教えてください。             -   【aslead Drive】ファイルのロック解除は誰ができますか。             -   【aslead Drive】ブラウザ(Chrome・Edge)にて、編集・一括アップロード・一括ダウンロード時のエラーの対処を教えてください。

-----------------------------------
doc_id: 85b36285-60b3-4376-8df6-8207b75afa53
source: https://helpdesk.aslead.cloud/wiki//pages/viewpage.action?pageId=131728029



+-----------------------+--------------------------------------------------------------------------------------------+-----------------------+         | 5.1.0                 | -   デスクトップアプリの自動アップデーターの追加                                           | ダウンロード          |         |                       | -   デスクトップアプリを常にフルスクリーンで開く設定/環境設定の追加                        |                       |         |                       | -   その他バグフィックス                                                                   |                       |         +-----------------------+--------------------------------------------------------------------------------------------+-----------------------+         | 5.0.4                 | -   デスクトップ通知が表示されない問題の修正                                               | ダウンロード          |         +-----------------------+--------------------------------------------------------------------------------------------+-----------------------+         | 5.0.3                 | -   「セッションの有効期限が切れました」というエラーが表示され、ログインできない問題の修正 | ダウンロード          |         |                       | -   ロード中のページをリロードしようとすると、アプリがクラッシュする可能性がある問題の修正 |                       |         |                       | -   その他バグフィックス                                                                   |                       |         +-----------------------+--------------------------------------------------------------------------------------------+-----------------------+         | 5.0.2                 | -   タスクバーのアイコンのクリックからクライアントアプリが開かない問題の修正               | ダウンロード          |         +-----------------------+--------------------------------------------------------------------------------------------+-----------------------+         | 5.0.0                 | -   タイトルバーのデザインを変更し、                                                       | ダウンロード          |         |                       |     フォーカルボード、プレイブックをシームレスに利用出来るようにしました                   |                       |         |                       |     (aslead社内環境では今後のバージョンアップで対応予定です)                               |                       |         |                       | -   サーバ選択をタブからドロップダウンメニューに変更しました                               |                       |         |                       | -   ローディング画面を変更しました                                                         |                       |         |                       | -   設定画面をダークモードに対応しました                                                   |                       |         +-----------------------+--------------------------------------------------------------------------------------------+-----------------------+         | 4.7.2                 | -   Electronをv12.0.16にアップグレード                                                     | ダウンロード          |         |                       | -   軽微な問題の修正                                                                       |                       |



... (chunk index 4 - 5 skipped) ...

+-----------------------+--------------------------------------------------------------------------------------------+-----------------------+          ----------------table-break-end-----------------      1.  Mattermostのデスクトップアプリを起動する                   ※インストール時.NETのエラーが表示される場合は、下記をご覧ください。          【Mattermost】デスクトップアプリをインストールしようとすると、.NET 4.5のインストールが求められる      2.  初回のサーバ設定を行う         ・Server URL： https://mattermost.aslead.cloud/         ・ Server Display Name：任意（asleadなど）                3.  設定したサーバを選択するとログイン画面が表示されます。         緑色の「aslead IDでログイン」ボタンをクリックしてください。         ※青色の「Sign in」のボタンではログインできません。               4.  左側のテキストボックスに、下記を入力して、緑色の「ログイン」ボタンをクリックしてください。         ID・パスワードの組み合わせは aslead IDのID/パスワードを忘れた・確認したい を参照してください。                        5.  初めてのログイン後に エラー画面 が表示されますが、 正常な動作です 。         ※詳細は「 初回のログイン後にエラーメッセージが表示される 」ページをご確認ください。               6.  時間をおいてログインする         時間をおいてログインすると、Mattermostを利用できるようになります。               ログイン方法（標準VDIの場合）      ------------------------------------------------------------------------      シンクラ用の標準VDIをお使いの場合、Windows上にデスクトップアプリをインストールできません。
    回避策として、ZIP版のデスクトップアプリを使って設定を行います。      1.  古いバージョンをアンイストールする         ※初めて利用される場合は、 この手順をスキップ してください。          アンイストール方法はこちらをクリック          アンイストール手順

... (chunk index 7 - 9 skipped) ...


+-----------------------+----------------------------------+-----------------------+          ----------------table-break-end-----------------          ※ 最新版 を利用した場合、アプリケーションが正常に起動しないとのご報告を頂いております。      3.  ZIPファイルを展開（解凍）する。         ダウンロードしたZIPファイルを、Hドライブ配下に展開（解凍）します。         ※画像はバージョン4.6.2のフォルダ名です。               4.  PC起動時にデスクトップアプリを自動起動させる         ※自動起動が不要な場合は、 この手順をスキップ してください。          自動起動させたい場合は、クリックして展開してください...          ①ショートカットを作成する。         Hドライブ配下に展開（解凍）したフォルダにある「Mattermost.exe」を右クリックし、「ショートカットの作成」をクリックする。                   同じフォルダに、  といったファイルが作成されていることを確認してください。          ②「スタートアップ」フォルダを開く。         デスクトップ左下のWindowsマークを右クリックし、「ファイル名を指定して実行」をクリックします。         「ファイル名を指定して実行」のウィンドウで、「shell:startup」と入力し「OK」をクリックします。                   ③スタートアップに登録する。         「スタートアップ」フォルダに、「Mattermost.exe - ショートカット」ファイルを移動します。               5.  Mattermostのデスクトップアプリを起動する          展開したHドライブ配下のフォルダから「Mattermost.exe」をダブルクリックする。                   ※インストール時.NETのエラーが表示される場合は、下記をご覧ください。          【Mattermost】デスクトップアプリをインストールしようとすると、.NET 4.5のインストールが求められる      6.  初回のサーバ設定を行う         ・Server Display Name：任意（asleadなど）         ・Server URL： https://mattermost.aslead.cloud/7.  設定したサーバを選択してください。               8.  緑色の「aslead IDでログイン」ボタンをクリックしてください。         ※青色の「Sign in」のボタンではログインできません。               9.  左側のテキストボックスに、下記を入力して、緑色の「ログイン」ボタンをクリックして下さい。         ・ID / メールアドレス ： メールアドレスの@より前(n-nomuraなど)         ・パスワード ： 青鍵パスワード                        10. 初めてのログイン後に エラー画面 が表示されますが、 正常な動作です 。         ※詳細は「 初回のログイン後にエラーメッセージが表示される 」ページをご確認ください。               11. 時間をおいてログインする         時間をおいてログインすると、Mattermostを利用できるようになります。               ログインでお困りの場合      ------------------------------------------------------------------------      次のページをご参照ください。      -   Mattermostのログインができない。検索してもユーザが見つからない。      使い方      ------------------------------------------------------------------------      操作マニュアル【Mattermost】 をご覧ください。

 ブラウザでのログイン方法【Mattermost】  デスクトップアプリのバージョン確認方法【Mattermost】

更新日時：2023-06-20 Powered by Atlassian Confluence and Scroll Viewport .

2021-06-18T23:39:52.021Z 2023-06-20T11:02:07.223Z

-----------------------------------
doc_id: c66eb649-847d-48b7-9d40-98a1b927425e
source: https://helpdesk.aslead.cloud/wiki//pages/viewpage.action?pageId=174948423


This content cannot be displayed without JavaScript.
Please enable JavaScript and reload the page.

ページリンクをコピー

?

 
CPHD 

aslead ポータル

Language

中国語 日本語

-   aslead ポータル
-   ...
-   サービス一覧・マニュアル
-   Mattermost(チャット)
-   マニュアル・手順【Mattermost】
-   困ったときは【Mattermost】

Mattermostのログインができない。検索してもユーザが見つからない。      概要      ------------------------------------------------------------------------      「Mattermostのログインができない」場合や、「検索してもユーザが見つからない」場合にご参照ください。      ログインができない      ------------------------------------------------------------------------      ログイン方法については、「 申請・導入【Mattermost】 」をご確認ください。      [(警告)] IE（Internet Explorer）は非対応 です。      Google ChromeまたはFirefoxで下記URLにアクセスしてください。
    https://mattermost.aslead.cloud/       [(警告)]   青色の「Sign in」のボタンではログインできません。             緑色の「aslead IDでログイン」ボタンをクリックしてください。                         [(警告)]  Mattermostの権限が付与されていない場合はログインできません。            次の画面が表示される方は、Mattermostを利用するための権限が付与されておりません。          カテゴリ/ロールにユーザを手動所属させる をご覧いただき、ご自身が所属するプロジェクト(asleadカテゴリ)へ申請行ってください。                                  ログイン後にエラーが表示される      ------------------------------------------------------------------------

[(警告)]  初めてのログインの場合、エラーメッセージが表示されます。            Mattermostに初めてログインしてからアカウントの同期処理が行われるため、初回はエラー画面が表示されることがあります。         ※詳細は「 初回のログイン後にエラーメッセージが表示される 」ページをご確認ください 。                                      初回ログイン後、同期処理が完了するまで次のことが制限されます。          -    チーム/チャネルへ参加する         -   他のユーザから当該ユーザを検索してもらう         -   他のユーザからチーム/チャネルへ招待してもらう                        Mattermostでユーザを招待したいが、検索してもユーザが見つからない      ------------------------------------------------------------------------       [(警告)]  対象者がMattermostに一度もログインしたことがない場合、ユーザが見つかりません。           ※初回ログインしてから少し待つと検索できるようになります。         ※詳細は「 【Mattermost】Mattermost上で、ユーザがリストに表示されない / 見つからない 」ページをご確認ください。                          [(警告)]  ユーザが対象のMattermostチームに参加していない場合、ユーザを検索しても見つかりません。            対象者をMattermostチームに参加させるには、ご利用頂いているカテゴリの「 利用ユーザ 」ロールにユーザを追加してください。         [(情報)] 追加手順は「 カテゴリ/ロールにユーザを手動所属させる 」をご参照ください。                     困ったときは【Mattermost】  Mattermostキャッシュクリア手順

更新日時：2023-04-03 Powered by Atlassian Confluence and Scroll Viewport .
"""

        chat_history_json = [
            {"role": "user", "content": "カテゴリ/ロールへの所属申請手順"},
            {
                "role": "assistant",
                "content": "カテゴリ/ロールへの所属申請手順は以下の通りです：\n\n1. 申請画面にログインする：https://idm.aslead.cloud/ にアクセスし、aslead IDでログインします。\n2. 所属したいロールをカートに入れる：ロールの要求をクリックします。\n3. 申請を送信する：申請したいロールを選択し、申請ボタンをクリックします。\n4. 承認を待つ：申請が承認されるまで待ちます。承認までには時間がかかる場合があります。\n5. 必要に応じて連絡する：承認依頼メールに気づかない可能性があるため、カテゴリ管理者/カテゴリ管理者補佐に連絡することを推奨します。\n6. 承認の確認：申請した内容の承認状況を確認するために、以下のURLにアクセスします：http://idm-dev.aslead.cloud/midpoint/admin/workItem/bfcbab41-e801....\n\n申請状況や申請履歴の確認手順については、以下のページを参照してください：https://helpdesk.aslead.cloud/wiki/pages/viewpage.action?pageId=11800907\n\n注意：申請が承認されてからJIRA/Confluence/Mattermostへの反映までにはタイムラグがあります。JIRA/Confluenceは最大1時間、Mattermostは最大1日かかる場合があります。",
            },
        ]
        chat_history = create_langchain_chat_history_from_openai_json(
            chat_history_json=chat_history_json
        )

        answer = ask_question(
            question="ログイン後にエラーが表示される",
            context=context,
            response_language="ja",
            chat_history=chat_history,
        )
        print("ask answer xx", answer)
        assert "同期" in answer
        assert "別のブラウザを" not in answer

    def test_should_not_repeat_user_message_important(self):
        context = """doc_id: 60c513fb-1a23-4474-95a7-5a04d069849d
source: /tmp/BU知見リスト_サンプル(2).xlsx - 01_タグリスト作成


01_タグリスト作成 - BU知見リスト_サンプル(2)

  -------------------------------- -------------------------------- ----------------------------- ----------------------------------------- ------------------------------------------ ----------------------------------------------- -------------------------------------------------

  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   34319                         28492                                     90002                                      34324                                           35878

  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Manager                       Senior Consultant                         SSP                                        Consultant                                      Senior Consultant

  知見                             知見）業界・市場                 #小売/#百貨店/#アパレル       #食品製造/#化粧品製造/#賃貸管理/#公共     #金融/#IT/#SI                              #ITサービス/#通信                               #公共/#次世代高速道路

  知見                             知見）VC・職種                   #事業戦略                     #製造/#コーポレート/#エンジニアリング     #システム開発                              #PM                                             #PM/#システムエンジニア

  知見                             知見）Skill&Tech                 #デザイン                     #PLC/#ETL/#データ分析/#PMO                #プログラミング/#マイクロサービス/#Linux   #プロジェクト管理/#通信インフラ構築推進         #システム広く浅く/#スマホアプリ/#チャットボット
                                                                                                  #DB設計/#業務フロー                       #データマネジメント                        #WEBメディア                                    

  知見                             知見）その他なんでも                                           #溶接/#G検定/#外郭団体                                                               #英語                                           #気候変動

  社員の興味・関心                 社員の興味・関心                 #D2C/#デザイン思考            #OPC-UA/#製造aaS/#技術伝承                #携帯業界                                  #アート                                         #IoT/#技術伝承/#表現方法

  -------------------------------- -------------------------------- ----------------------------- ----------------------------------------- ------------------------------------------ ----------------------------------------------- -------------------------------------------------



... (chunk 1 - 1 skipped) ...


  -------------------------------- -------------------------------- ------------------------------------------------ ---------------------------------------------------- -------------------------------- --------------------------------- --------------------------------------------------
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   34553                                            24914                                                36570                            24936                             31513
  -------------------------------- -------------------------------- ------------------------------------------------ ---------------------------------------------------- -------------------------------- --------------------------------- --------------------------------------------------
  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Consultant                                       Consultant                                           Senior Consultant                Consultant                        Senior Consultant

  知見                             知見）その他なんでも                                                                                                                                                                                      #コンピューテーショナルデザイン

  社員の興味・関心                 社員の興味・関心                 ＃Smartfactory/＃データ分析                      #AI/#IoT/#データレイク                               #NFT/#xR/#AI                     #AI/#XR                           #XR/#AI/#IoT/#BIM

  社員の趣味・私生活               社員の趣味・私生活               ＃海外旅行                                       #子育て                                              #写真/#ダイエット/#ラーメン      #ウイスキー/#野球/#ボードゲーム   #ワイン
  -------------------------------- -------------------------------- ------------------------------------------------ ---------------------------------------------------- -------------------------------- --------------------------------- --------------------------------------------------


  -------------------------------- -------------------------------- --------------------------------------- ------------------------------------------------- ----------------------------------- ----------------------------------------- ---------------------------------------------

  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   31223                                   33580                                             24986                               32668                                     90001

  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Manager                                 Senior Consultant                                 Senior Consultant                   Consultant                                PO

  知見                             知見）業界・市場                 #石油化学/#商社/#製薬                   #通信/#高速道路/#自動車保険                       #金融/#銀行/#投資銀行               #食品                                     

  知見                             知見）VC・職種                   #製造/#R&D・エンジニアリング            #プロジェクトマネジメント                         #投資/#リスク管理/#営業企画         #製造                                     

  知見                             知見）Skill&Tech                 #現場改善/#デジタルツイン/#DX人材育成   #プロジェクトマネジメント/#ベンダーコントロール   #SAS/#Oracle/#VBA/#PowerBI          #Python/#データ分析/#組み込み系           
                                                                    #新規事業                                                                                 #データマネジメント                 #Web系                                    
  -------------------------------- -------------------------------- --------------------------------------- ------------------------------------------------- ----------------------------------- ----------------------------------------- ---------------------------------------------

... (chunk 3 - 5 skipped) ...


  -------------------------------- -------------------------------- ---------------------- ----------------------------- ----------------------------- ------------------------ -------------------------------------
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   35159                  20196                         32199                         36571                    31895
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   31226                                     32666                                                35879                                     31225                                    31510
----------------table-break-start-----------------
  -------------------------------- -------------------------------- ---------------------- ----------------------------- ----------------------------- ------------------------ -------------------------------------
  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Consultant                                Senior Consultant                                    Consultant                                Consultant                               Senior Consultant

  社員の興味・関心                 社員の興味・関心                 #データ分析/#データ活用戦略               #新規事業開発/#営業戦略案件（なんでも興味あり）      #Industrie4.0/#CIOF                       ＃Society5.0/＃教育/＃スマートシティ     #クラウド/#夏休み
                                                                                                              #The Model                                                                                                                              

  社員の趣味・私生活               社員の趣味・私生活               #音楽鑑賞/#キャンプ                       #幹事/#キャンプ/#カラオケ（AKB・郷ひろみ踊れます）   #ギター                                   ＃野球/＃漫画                            #猫/#散歩、ラジオ体操/#カメラ
                                                                                                              #特殊小型船舶免許                                                                                                                       
  -------------------------------- -------------------------------- ----------------------------------------- ---------------------------------------------------- ----------------------------------------- ---------------------------------------- -------------------------------


  -------------------------------- -------------------------------- ----------------------------------- --------------------------------- ------------------------------------------- ---------------------------------------------------- -----------------------------------------------

  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   31036                               33153                             18095                                       36120                                                11766

  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Senior Consultant                   Senior Consultant                 Senior Consultant                           IC                                                   Senior Manager

  知見                             知見）業界・市場                 #化学/#自動車/#ゴム・タイヤ/#公共   #公共                             #自動車部品/#不動産/#エネルギー             ＃ドラッグストア/＃アパレル/＃医療材料               ＃電機メーカ/＃食品/＃商社/＃不動産/＃酪農
                                                                                                                                                                                      ＃家電/＃モビリティ/＃自動認識（RFID、画像認識等）   
                                                                                                                                                                                      ＃フィンテック                                       
  -------------------------------- -------------------------------- ----------------------------------- --------------------------------- ------------------------------------------- ---------------------------------------------------- -----------------------------------------------
  -------------------------------- -------------------------------- ----------------------------------- --------------------------------- ------------------------------------------- ---------------------------------------------------- -----------------------------------------------
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   31036                               33153                             18095                                       36120                                                11766
  -------------------------------- -------------------------------- ----------------------------------- --------------------------------- ------------------------------------------- ---------------------------------------------------- -----------------------------------------------
  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Senior Consultant                   Senior Consultant                 Senior Consultant                           IC                                                   Senior Manager

  知見                             知見）VC・職種                   ＃R&D 製品開発/# 工場               #エンジニア                       #スタートアップ系                                                                                ＃製造SE/＃Sier

  知見                             知見）Skill&Tech                 ＃CAD/CAE/＃PLM/BOM                 #RPA/#ワークフローシステム        #新規事業/#RPA/#PMO                         ＃コンソーシアム運営/＃ファシリテーター              #SCM/#VB/＃しゃべること

  知見                             知見）その他なんでも             ＃アメリカ/# イベント                                                 #イスラエル/#ハノーバー/#イベント/#深セン   ＃元みずほ/＃元三菱UFJ                               ＃ハノーバー/＃営業（新規開拓）

  社員の興味・関心                 社員の興味・関心                 #最近の流行りを知る                 #メタバース/#ふるさと納税         #中国語                                                                                          ＃ハイエース/＃キャンプ、サウナ/＃草野球
                                                                                                                                                                                                                                           ＃阪神

  社員の趣味・私生活               社員の趣味・私生活               #飲み会/＃Youtube見る               #キャンプ/#ボードゲーム/#サウナ   #野球/#ビール/#街歩き                       ＃千ベロ/＃フットサル                                ＃自治会の班長/＃夏祭り担当/＃娘１人/＃流山TX
  -------------------------------- -------------------------------- ----------------------------------- --------------------------------- ------------------------------------------- ---------------------------------------------------- -----------------------------------------------


  -------------------------------- -------------------------------- ----------------------------------------- ---------------------------------- ------------------------------- ------------------------------------------- --------------------------------

  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   26719                                     27961                              36573                           28287                                       34554

  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Senior Consultant                         Consultant                         Consultant                      Manager                                     Consultant
  -------------------------------- -------------------------------- ----------------------------------------- ---------------------------------- ------------------------------- ------------------------------------------- --------------------------------

... (chunk 8 - 8 skipped) ...



  -------------------------------- -------------------------------- ---------------------------------------------- ------------------- --------------------------- ------------------------------- ------------------
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   35881                                          34100               36572                       29204                           28218
  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Consultant                                     Senior Consultant   Consultant                  Consultant                      Consultant
  知見                             知見）業界・市場                 # 自動車/# 製造業                              #医薬品/#日用品     #ガス/#電力/#建設           #公共/#環境/#生物多様性/#商社   #電力会社/#銀行
  知見                             知見）VC・職種                   # 製造                                         #研究開発           #エンジニアリング           -                               -
  知見                             知見）Skill&Tech                 # マネジメント/# データ分析/# データドリブン                       #電気設備/#冷凍機/#ボイラ   #GIS/#iOS/#英語                 #PMO/#管理会計
  知見                             知見）その他なんでも                                                                                                            -                               #Anaplan/#海外PJ
  社員の興味・関心                 社員の興味・関心                 # なんでも                                                                                     #スタートアップ                 #金融
  社員の趣味・私生活               社員の趣味・私生活               # 写真/# バイク/# スポーツ全般                 #スポーツ観戦       #キャンプ/#格闘技           #ワイン/#コーヒー               #ポーカー
  -------------------------------- -------------------------------- ---------------------------------------------- ------------------- --------------------------- ------------------------------- ------------------

----------------table-break-end-----------------

----------------table-break-start-----------------

  -------------------------------- -------------------------------- ------------------------------- ------------------- ------------------------------------- ------------------------------- -----------------------------------------------------

  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   35564                           28497               31894                                 33579                           29711

  -------------------------------- -------------------------------- ---------------------------------------------- ------------------- --------------------------- ------------------------------- ------------------
  -------------------------------- -------------------------------- ---------------------------------------------- ------------------- --------------------------- ------------------------------- ------------------
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   35881                                          34100               36572                       29204                           28218
  -------------------------------- -------------------------------- ---------------------------------------------- ------------------- --------------------------- ------------------------------- ------------------
----------------table-break-end-----------------
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   35564                           28497               31894                                 33579                           29711
----------------table-break-start-----------------

  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Analyst                         Senior Consultant   Manager                               Senior Consultant               Senior Consultant

  知見                             知見）業界・市場                 #ライフサイエンス/#医療DX       #金融               #エンタメ/#スタートアップ/#製造業     #公共/#自動車/#PC/#医療         #防災/#鉄鋼/#素材化学/#中央省庁・自治体

  知見                             知見）VC・職種                                                   #先端技術           #新規事業開発                         #機械設計/#エンジニアリング     #製造/#研究開発/#エンジニアリング/#技術調査

  知見                             知見）Skill&Tech                 #英語/#仏語勉強中               #PMO                #Web3/#デザイン                       #3DCAD/#PLM                     #技術調査/#デジタルツイン/#CAE/CFD
                                                                                                                                                                                              #AI、機械学習/#ロボット

  知見                             知見）その他なんでも             #簿記                                               #中国語/#お酒/#料理                                                   #

  社員の興味・関心                 社員の興味・関心                 #地方創生/#農業                 #web3               #新しいこと全般                       #デジタルツイン/#Industrie4.0   #防災/#デジタルツイン

  社員の趣味・私生活               社員の趣味・私生活               #ゴルフ/#ピアノとギター/#お酒   #子育て/#腰痛       #スマートホーム/#料理/#スポーツ観戦   #子育て/#蒲田                   #子育て/#小金井市/#中央線/#コーヒーインストラクター
                                                                                                                        #湾岸/#酒                                                             #トレーニング/#サウナ
  -------------------------------- -------------------------------- ------------------------------- ------------------- ------------------------------------- ------------------------------- -----------------------------------------------------



  -------------------------------- -------------------------------- --------------------------------------------------------- ------------------------------------ ----------------------------------------- ----------------------- -------------------------
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   31896                                                     24357                                35880                                     29798                   34323
  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Senior Consultant                                         Manager                              Consultant                                Consultant              Consultant
  知見                             知見）業界・市場                 #石油化学/#化学/#エラストマー/#光学樹脂                   #家電/#人材派遣/#官公庁/#ODA         #化学/#高分子化学/#バリアフィルム/#印刷   #公共/#自動車           #公共/#スタートアップ
  知見                             知見）VC・職種                   #製造 #プラントエンジニア #生産管理 #品質管理 #設備管理   #IT/#SCM/#与信/#スタートアップ共創   #製造/#生産技術/#品質管理                 #製造/#需給/#生産       #ICT/#研究開発
  知見                             知見）Skill&Tech                 #プラント設計/#コーチング/#GHG関連                        #Big Data                            #画像認識AI                               #DeepLearning/#Python   #クラウド/#人間拡張技術
  知見                             知見）その他なんでも                                                                                                            #IoT検定                                  #バリスタ               
  社員の興味・関心                 社員の興味・関心                 #エンゲージメント向上                                                                                                                    #データアナリティクス   #スタートアップ共創
  社員の趣味・私生活               社員の趣味・私生活               #登山/#日本酒                                                                                  #呪術廻戦/#水球                           #ギター/#日本酒         
  -------------------------------- -------------------------------- --------------------------------------------------------- ------------------------------------ ----------------------------------------- ----------------------- -------------------------

----------------table-break-end-----------------

----------------table-break-start-----------------
  -------------------------------- -------------------------------- --------------------------------------------------------- ------------------------------------ ----------------------------------------- ----------------------- -------------------------

... (chunk 12 - 12 skipped) ...


  -------------------------------- -------------------------------- --------------------------------------------------------- ------------------------------------ ----------------------------------------- ----------------------- -------------------------
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   31896                                                     24357                                35880                                     29798                   34323
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   28099                       36119                                                                                                     36121                                     31899                                  36304
----------------table-break-start-----------------
  -------------------------------- -------------------------------- --------------------------------------------------------- ------------------------------------ ----------------------------------------- ----------------------- -------------------------
  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Consultant                  Director                                                                                                  Senior Consultant                         Consultant                             Consultant

  社員の興味・関心                 社員の興味・関心                 #SX                         #ヘルスケア/#産業・事業創出/#国の所得UP                                                                   #環境問題                                 #Robot/#QoL/#SMART HOME                #大企業×スタートアップ共創（CVC）/#国ごとのスタートアップエコシステム
                                                                                                                                                                                                                                                                                           #企業カルチャー醸成

  社員の趣味・私生活               社員の趣味・私生活               #スキー/#サーフィン/#湘南   ＃子育て/#野球観戦                                                                                        #バスケ部/#ゴルフ/#飲み歩き               #レコード/#音楽/#映像                  #料理/#食べ歩き/#健康
  -------------------------------- -------------------------------- --------------------------- --------------------------------------------------------------------------------------------------------- ----------------------------------------- -------------------------------------- -----------------------------------------------------------------------


  -------------------------------- -------------------------------- --------------------------------------- --------------------------------------------- --------------------------------------------- ----------------------------------- -------------------------------

  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   25016                                   22063                                         35877                                         36569                               28025

  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Consultant                              Senior Consultant                             Senior Consultant                             Senior Consultant                   Consultant

  知見                             知見）業界・市場                 #不動産/#行政法人/#地方公共団体/#印刷   ＃公共/＃金融/＃製造                          #セラミック/#製造/#フィルター/#ディーゼル     #製造/#エネルギー                   #基盤/#運用保守/#金融
                                                                    #エンタメ                                                                                                                                                               

  知見                             知見）VC・職種                   #コンサル                                                                             #製造/#エンジニアリング/#物理工学             #製造/#開発/#エンジニアリング       
  -------------------------------- -------------------------------- --------------------------------------- --------------------------------------------- --------------------------------------------- ----------------------------------- -------------------------------
  -------------------------------- -------------------------------- --------------------------------------- --------------------------------------------- --------------------------------------------- ----------------------------------- -------------------------------
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   25016                                   22063                                         35877                                         36569                               28025
  -------------------------------- -------------------------------- --------------------------------------- --------------------------------------------- --------------------------------------------- ----------------------------------- -------------------------------
  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Consultant                              Senior Consultant                             Senior Consultant                             Senior Consultant                   Consultant

  知見                             知見）Skill&Tech                 #業務改革/#業務立上/#PMO/#RPA           ＃ノーコードツール/＃システム選定支援/＃SQL   #プロセス開発/#焼成/#設計開発                 #機械設計/#電気設計/#品質サービス   #環境構築

  知見                             知見）その他なんでも             #英語/#ドイツ語/#RPA                    ＃英語                                        #G検定                                        #英語/#日本語/#モンゴル語           

  社員の興味・関心                 社員の興味・関心                 #Web3.0/#メタバース                     ＃ヘルスケア/＃社会課題                       #SCM/#情報通信/#量子技術                      #教育/#技術伝承/#資源リサイクル     #宇宙/#サステナ

  社員の趣味・私生活               社員の趣味・私生活               #漫画/#ゲーム実況/#旅行                 ＃映画/＃国内・海外旅行/＃ロシア料理          #ハンガリー/#哲学/#ヨーロッパ旅行/#西洋美術   #ラーメン/#スパイス/#ポイ活         #ベトナム/#ボクシング/#筋トレ
                                                                                                                                                          #岐阜                                                                             
  -------------------------------- -------------------------------- --------------------------------------- --------------------------------------------- --------------------------------------------- ----------------------------------- -------------------------------


  -------------------------------- -------------------------------- -------------------------------------------------- ------------------------------------------------- ------------------------------------- ----------------------------------------- ---------------------------------------

  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   34099                                              35883                                             90003                                 31035                                     31511

  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Senior Consultant                                  XP                                                Senior Consultant                     Senior Consultant                         Consultant
  -------------------------------- -------------------------------- -------------------------------------------------- ------------------------------------------------- ------------------------------------- ----------------------------------------- ---------------------------------------
  -------------------------------- -------------------------------- -------------------------------------------------- ------------------------------------------------- ------------------------------------- ----------------------------------------- ---------------------------------------
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   34099                                              35883                                             90003                                 31035                                     31511
  -------------------------------- -------------------------------- -------------------------------------------------- ------------------------------------------------- ------------------------------------- ----------------------------------------- ---------------------------------------
  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Senior Consultant                                  XP                                                Senior Consultant                     Senior Consultant                         Consultant

  知見                             知見）業界・市場                 ＃電力（原発）/＃食品/＃プラントエンジニアリング   #情報通信/#化粧品                                 #リース                               #医薬/#食品/#プラント建築/＃化学          #自動車/＃食品/#インフラ
                                                                    ＃金融                                                                                                                                                                               

  知見                             知見）VC・職種                   ＃工事管理/＃プラント設計/＃営業                   #研究開発/#アプリ開発/#デジタル推進               #アプリ開発                           #製造/#生産技術・管理/#エンジニアリング   #生産技術

  知見                             知見）Skill&Tech                 ＃CAD/＃ロボットプログラミング                     #プロトタイピング/#技術標準化/#Web                #Java/#AWS                            #制御/#フィールドバス/#PLC/#センサー      #生産設備設計/#PowerBI/#R,Python
                                                                                                                                                                                                               ＃故障予知/＃設備エネルギー管理           

  知見                             知見）その他なんでも                                                                #英語                                                                                   ＃電気工事                                #フィルム現像/#英語/#カナダ

  社員の興味・関心                 社員の興味・関心                 ＃量子技術/＃衛星技術/＃人材育成                   #オープンデータ                                                                                                                   #スマートファクトリー/#福祉/#サブカル
                                                                                                                                                                                                                                                         #伝統工芸

  -------------------------------- -------------------------------- -------------------------------------------------- ------------------------------------------------- ------------------------------------- ----------------------------------------- ---------------------------------------
  -------------------------------- -------------------------------- -------------------------------------------------- ------------------------------------------------- ------------------------------------- ----------------------------------------- ---------------------------------------
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   34099                                              35883                                             90003                                 31035                                     31511
  -------------------------------- -------------------------------- -------------------------------------------------- ------------------------------------------------- ------------------------------------- ----------------------------------------- ---------------------------------------
  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Senior Consultant                                  XP                                                Senior Consultant                     Senior Consultant                         Consultant
  社員の興味・関心                 社員の興味・関心                 ＃量子技術/＃衛星技術/＃人材育成                   #オープンデータ                                                                                                                   #スマートファクトリー/#福祉/#サブカル
  知見                             知見）業界・市場                 ＃電力（原発）/＃食品/＃プラントエンジニアリング   #情報通信/#化粧品                                 #リース                               #医薬/#食品/#プラント建築/＃化学          #自動車/＃食品/#インフラ
                                                                    ＃金融                                                                                                                                                                               
  知見                             知見）VC・職種                   ＃工事管理/＃プラント設計/＃営業                   #研究開発/#アプリ開発/#デジタル推進               #アプリ開発                           #製造/#生産技術・管理/#エンジニアリング   #生産技術
  知見                             知見）Skill&Tech                 ＃CAD/＃ロボットプログラミング                     #プロトタイピング/#技術標準化/#Web                #Java/#AWS                            #制御/#フィールドバス/#PLC/#センサー      #生産設備設計/#PowerBI/#R,Python
                                                                                                                                                                                                               ＃故障予知/＃設備エネルギー管理           
  知見                             知見）その他なんでも                                                                #英語                                                                                   ＃電気工事                                #フィルム現像/#英語/#カナダ

  社員の趣味・私生活               社員の趣味・私生活               ＃昼ドラのような離婚エピソード/＃スポーツ          #横浜市青葉区/#シュノーケリング/#クラフトビール   #子育て/#サウナ/#旅行の計画を立てる   ＃サーフィン/＃スケートボード/＃子育て    #写真/#海外旅行/#婚活
                                                                    ＃新橋の飲食店開拓                                                                                                                                                                   
  -------------------------------- -------------------------------- -------------------------------------------------- ------------------------------------------------- ------------------------------------- ----------------------------------------- ---------------------------------------


  -------------------------------- -------------------------------- ----------------------------------------------------------------- --------------------------- ------------------------------------------------------ ----------------------------- -----------------------------------

  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   30869                                                             29802                       23712                                                  24976                         34322

  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Consultant                                                        Consultant                  Senior Consultant                                      Consultant                    Senior Consultant

  知見                             知見）業界・市場                 #食品/#中央省庁/#自動車部品                                       #アウトドア/#商社           #公共/#データ流通/#カーボンニュートラル                #ラーメン/#商社/#銀行         #自動車/#化学

  知見                             知見）VC・職種                   #製造                                                             #製造/#卸売                                                                        #製造/#トレード               #製造

  知見                             知見）Skill&Tech                 # 構想/# 要件定義/# 実装/# 保守                                   #SAP/#DXロードマップ策定    #ノーコード・ローコード開発（BIツール）                #データ分析/#リサーチ         #PLC/#保全/#生産技術/#施工管理
                                                                    # アジャイル/# Python/# VBA                                                                   #GIS（地図情報）/#リサーチ/#プロジェクトマネジメント                                 #業務改善
                                                                    # MySQL/# InfluxDB/# Grafana                                                                                                                                                       
  -------------------------------- -------------------------------- ----------------------------------------------------------------- --------------------------- ------------------------------------------------------ ----------------------------- -----------------------------------
  -------------------------------- -------------------------------- ----------------------------------------------------------------- --------------------------- ------------------------------------------------------ ----------------------------- -----------------------------------
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   30869                                                             29802                       23712                                                  24976                         34322
  -------------------------------- -------------------------------- ----------------------------------------------------------------- --------------------------- ------------------------------------------------------ ----------------------------- -----------------------------------
  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Consultant                                                        Consultant                  Senior Consultant                                      Consultant                    Senior Consultant

  知見                             知見）その他なんでも             #ものづくり製造工程/# リスクアセスメント                          #ドイツ                     #英語                                                  #ITAP                         #英語/#マレーシア
                                                                    # リサーチ/# 対策立案/# PJMO                                                                                                                                                       
                                                                    # 監査/# BCP対応フロー作成支援/# 攻撃シナリオに基づいた机上訓練                                                                                                                    

  社員の興味・関心                 社員の興味・関心                 # OTセキュリティ/# データ分析/# AI                                #AI/#海外ロールアウト案件   #スマートシティ/#データ流通                                                          #Smart factory/#EPR(生産計画関連)
                                                                    # プロセス変革/# SmartFactoryの最適解                                                                                                                                              #AI
                                                                    # DX構想/# 人材、組織                                                                                                                                                              

  社員の趣味・私生活               社員の趣味・私生活               #子育て/#ピアノ                                                   #釣り/#野球観戦             #野球観戦/#旅行                                        #魚釣り/#キャンプ/#パチスロ   #旅行
  -------------------------------- -------------------------------- ----------------------------------------------------------------- --------------------------- ------------------------------------------------------ ----------------------------- -----------------------------------


  -------------------------------- -------------------------------- ------------------------------------- ---------------------------------------------- --------------------------------------- --------------------------------------------- -------------------------------

  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   90004                                 26712                                          30282                                   35874                                         35568

  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   SP                                    Senior Consultant                              Senior Consultant                       Manager                                       Analyst
  -------------------------------- -------------------------------- ------------------------------------- ---------------------------------------------- --------------------------------------- --------------------------------------------- -------------------------------
  -------------------------------- -------------------------------- ------------------------------------- ---------------------------------------------- --------------------------------------- --------------------------------------------- -------------------------------
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   90004                                 26712                                          30282                                   35874                                         35568
  -------------------------------- -------------------------------- ------------------------------------- ---------------------------------------------- --------------------------------------- --------------------------------------------- -------------------------------
  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   SP                                    Senior Consultant                              Senior Consultant                       Manager                                       Analyst

  知見                             知見）業界・市場                 # 製造業/# プリンター/# 食品/# 人事   ＃IJプリンター/＃酪農業/＃製薬/＃不動産        # 製造業/# 品質保証/# 昇降機/# 加工機   #タイヤ/#内閣府/#ゴム/#公共/#スマートシティ   #製造業/#宇宙産業
                                                                    # IT                                  ＃航空機                                       # 半導体プロセス/# 空調機                                                             

  知見                             知見）VC・職種                   # 技術開発/# 製造/# プログラマー      ＃研究・開発/＃設計/＃製造（ディスクリート）   # 設計開発/# データ分析                 #設備保全/#データ分析/#事業開発               #設計開発
                                                                                                          ＃マーケティング                                                                                                                     

  知見                             知見）Skill&Tech                 # システム開発/# プログラミング       ＃メカ設計                                     # データ分析/# データエンジニアリング   #データ                                       #制御系/#データ分析/#衛星画像
                                                                                                                                                         # AI/# 1DCAE/# プログラミング                                                         

  知見                             知見）その他なんでも                                                                                                  # 最適化アルゴリズム/# ハプティクス     #うさぎ/#絵画鑑賞/#対話型鑑賞                 

  社員の興味・関心                 社員の興味・関心                 # データ構造とアルゴリズム/# 自動化   ＃戦略策定/＃不動産/＃資産運用                 # 生成AI                                #YOASOBI/#DAO/#ブロックチェーン               #人工衛星
                                                                    # ゼロトラストネットワーキング                                                                                                                                             

  -------------------------------- -------------------------------- ------------------------------------- ---------------------------------------------- --------------------------------------- --------------------------------------------- -------------------------------
  -------------------------------- -------------------------------- ------------------------------------- ---------------------------------------------- --------------------------------------- --------------------------------------------- -------------------------------
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   90004                                 26712                                          30282                                   35874                                         35568
  -------------------------------- -------------------------------- ------------------------------------- ---------------------------------------------- --------------------------------------- --------------------------------------------- -------------------------------
  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   SP                                    Senior Consultant                              Senior Consultant                       Manager                                       Analyst
  社員の興味・関心                 社員の興味・関心                 # データ構造とアルゴリズム/# 自動化   ＃戦略策定/＃不動産/＃資産運用                 # 生成AI                                #YOASOBI/#DAO/#ブロックチェーン               #人工衛星
  知見                             知見）業界・市場                 # 製造業/# プリンター/# 食品/# 人事   ＃IJプリンター/＃酪農業/＃製薬/＃不動産        # 製造業/# 品質保証/# 昇降機/# 加工機   #タイヤ/#内閣府/#ゴム/#公共/#スマートシティ   #製造業/#宇宙産業
                                                                    # IT                                  ＃航空機                                       # 半導体プロセス/# 空調機                                                             
  知見                             知見）VC・職種                   # 技術開発/# 製造/# プログラマー      ＃研究・開発/＃設計/＃製造（ディスクリート）   # 設計開発/# データ分析                 #設備保全/#データ分析/#事業開発               #設計開発
                                                                                                          ＃マーケティング                                                                                                                     
  知見                             知見）Skill&Tech                 # システム開発/# プログラミング       ＃メカ設計                                     # データ分析/# データエンジニアリング   #データ                                       #制御系/#データ分析/#衛星画像
                                                                                                                                                         # AI/# 1DCAE/# プログラミング                                                         
  知見                             知見）その他なんでも                                                                                                  # 最適化アルゴリズム/# ハプティクス     #うさぎ/#絵画鑑賞/#対話型鑑賞                 

  社員の趣味・私生活               社員の趣味・私生活               # ゼルダの伝説/# お食事               ＃子育て/＃居酒屋/＃音楽＃フェス/＃テニス      # 競馬/# 資産運用                       #ストレッチ/#下落合                           #ハンドボール/#ドラクエ
                                                                                                          ＃ダイエット                                                                                                                         
  -------------------------------- -------------------------------- ------------------------------------- ---------------------------------------------- --------------------------------------- --------------------------------------------- -------------------------------


  -------------------------------- -------------------------------- ------------------------------------------ --------------------------------------------- ---------------------------- ----------------------------------------------- -------------------------------------

  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   35875                                      23394                                         29748                        33353                                           31901

  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Manager                                    Senior Consultant                             Consultant                   Manager                                         Consultant

  知見                             知見）業界・市場                 #製造業/#電気機器                          #製薬/#公共                                   #鉄道業界                    #紙パルプ/#製造業/#不動産/#スタートアップ       #製造業/#光ファイバ/#タイヤ

  知見                             知見）VC・職種                   #製造                                                                                                                 #営業/#生産計画/#物流/#経営企画/#海外事業       #生産技術開発/#製造技術/#設計・開発
                                                                                                                                                                                          #貿易                                           

  知見                             知見）Skill&Tech                 #高周波回路設計/#制御監視/＃システム設計   #クラウド/#Web開発（ほぼ素人）/#BI            ＃SAP/＃物流                 #中小企業診断士/#ITストラテジスト/#宅建         #ノーコードツール開発/#AI/#VBA
                                                                                                               #構想系/#プロジェクト管理                                                  #事業計画/#経営企画/#インド駐在                 #G検定
  -------------------------------- -------------------------------- ------------------------------------------ --------------------------------------------- ---------------------------- ----------------------------------------------- -------------------------------------
  -------------------------------- -------------------------------- ------------------------------------------ --------------------------------------------- ---------------------------- ----------------------------------------------- -------------------------------------
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   35875                                      23394                                         29748                        33353                                           31901
  -------------------------------- -------------------------------- ------------------------------------------ --------------------------------------------- ---------------------------- ----------------------------------------------- -------------------------------------
  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Manager                                    Senior Consultant                             Consultant                   Manager                                         Consultant

  知見                             知見）その他なんでも                                                                                                                                   #インド/#けいはんな/#イスラエル/#シンガポール   

  社員の興味・関心                 社員の興味・関心                 #GX/#情報通信/#電波                        #スマートシティ/#スポーツ/#ブロックチェーン   ＃Web3/#スタートアップ共創   #事業承継/#創業/#創業支援/#自治体               #量子情報通信

  社員の趣味・私生活               社員の趣味・私生活               #ピアノ/#フットサル/#バイク                #サッカー/#子育て/#仮想通貨/#FX               ＃ワイン/＃サッカー/＃旅行   #バイク/#筋トレ/#ゴルフ                         #千葉県民/#麻雀/#散歩
                                                                                                               #株                                                                                                                        
  -------------------------------- -------------------------------- ------------------------------------------ --------------------------------------------- ---------------------------- ----------------------------------------------- -------------------------------------


  -------------------------------- -------------------------------- --------------------------------------------- ------------------------------------------------ ----------------------------- -------------------------------------------------------------------- -----------------------------------------

  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   31667                                         34977                                            34320                         18099                                                                30604

  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Consultant                                    Consultant                                       Manager                       Senior Consultant                                                    Senior Consultant

  知見                             知見）業界・市場                 #製造業/#官公庁/#鉄道                         #製造業                                          #素材化学                     #不動産/#自動車部品/#農業/#消費者金融                                ＃製造業/#官公庁
  -------------------------------- -------------------------------- --------------------------------------------- ------------------------------------------------ ----------------------------- -------------------------------------------------------------------- -----------------------------------------

... (chunk 21 - 22 skipped) ...


  -------------------------------- -------------------------------- --------------------------------------------- ------------------------------------------- --------------------------------------- ------------------------------ ---------------------------------
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   30870                                         35158                                       33163                                   21717                          31512
  -------------------------------- -------------------------------- --------------------------------------------- ------------------------------------------- --------------------------------------- ------------------------------ ---------------------------------
  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Senior Consultant                             Senior Consultant                           Senior Consultant                       XP                             Consultant
  社員の興味・関心                 社員の興味・関心                 #生成AI/#PMBOK/#笹塚                          #ポイ活/#推しの子/#ハンターハンターの続き   #航空宇宙/#製造業/#統計・データ分析     #自動車                        #データ分析
  知見                             知見）業界・市場                 #医療/#内視鏡/#官公庁/#観光/#教育             #製造業/#電池                               #製造業/#布                             #製造業/#飲料メーカー          #食品
  知見                             知見）VC・職種                   #医療機器メーカーのサービスメンテ/#新規事業   #生産技術/#情報システム                     #研究開発/#設計・開発/#共同研究         #物流                          #研究/#商品開発/#基礎研究/#特許
                                                                    #研究開発                                                                                                                                                        
  知見                             知見）Skill&Tech                 #プロマネ/#データ分析/#Azure/#BI              #PLC/#MES                                   #伝熱工学・熱力学/#流体力学/#航空宇宙   #CBM/TBM/#データ分析/#統計学   #BI/#Python/#SQL/#PLC
                                                                                                                                                              #数値計算                               #機械学習/#Webアプリ開発       
  知見                             知見）その他なんでも             #ローコードツール（Copilot）/#Microsoft製品                                               #英語/#イギリス                                                        #有機化学/#カビ

  社員の趣味・私生活               社員の趣味・私生活               #ゲーム/#筋トレ/#英会話                       #子育て/#フォークリフト/#ポイ活             #合気道/#ピアノ初心者/#たまにドライブ   #釣り/#野球/#料理              #子育て/#双子/#100km
  -------------------------------- -------------------------------- --------------------------------------------- ------------------------------------------- --------------------------------------- ------------------------------ ---------------------------------


  -------------------------------- -------------------------------- -------------------------------------------- --------------------------------------------- ------------------------ ----------------------------------- --------------------------- -----------------------

  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   32948                                        30284                                         35665                    31898                               32667                       35476

  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Senior Consultant                            Senior Consultant                             Senior Manager           Senior Consultant                   Senior Consultant           Analyst

  知見                             知見）業界・市場                 ＃製造業/＃非鉄                              ＃自動車部品/＃機械/＃産業インフラ/＃製造業   産業インフラ、素材科学   #石油/#化学/#AI                     #重工業/#電力/#エネルギー   

  知見                             知見）VC・職種                   ＃研究開発/＃製造                            ＃機械設計/＃機構設計/＃コンサルタント        デザイン                 #エンジニア/#製造                   #設計/#技術営業             

  知見                             知見）Skill&Tech                 ＃データ加工、分析                                                                                                  #DCS #センサー #プラント #制御      #CAD                        #補助金申請/#G検定
                                                                                                                                                                                        #python #VB ＃数学                                              

  知見                             知見）その他なんでも             ＃セミ取り                                   ＃チャイナ住んでました                                                 #英語 #中国語 #スマートホーム       #中国語                     #SNS
  -------------------------------- -------------------------------- -------------------------------------------- --------------------------------------------- ------------------------ ----------------------------------- --------------------------- -----------------------
  -------------------------------- -------------------------------- -------------------------------------------- --------------------------------------------- ------------------------ ----------------------------------- --------------------------- -----------------------
  社員ID（コンサルタントの名前）   社員ID（コンサルタントの名前）   32948                                        30284                                         35665                    31898                               32667                       35476
  -------------------------------- -------------------------------- -------------------------------------------- --------------------------------------------- ------------------------ ----------------------------------- --------------------------- -----------------------
  クラス（コンサルタントの役職）   クラス（コンサルタントの役職）   Senior Consultant                            Senior Consultant                             Senior Manager           Senior Consultant                   Senior Consultant           Analyst

  社員の興味・関心                 社員の興味・関心                 #julia                                       ＃知らないこと                                                         #メタバース #Web3 #BC #ガジェット                               

  社員の趣味・私生活               社員の趣味・私生活               ＃ベランダ園芸/＃安価な昼飯作り/＃マラソン   ＃仕事                                                                 #映画 #漫画 #読書 #買い物           #ゴルフ/#エスパルス         #サッカー/#ランニング
  -------------------------------- -------------------------------- -------------------------------------------- --------------------------------------------- ------------------------ ----------------------------------- --------------------------- -----------------------

Sheet1 - BU知見リスト_サンプル(2)
"""

        answer = ask_question(
            question="Manager的人数是多少", context=context, response_language="zh"
        )
        print("ask answer xx1", answer)
        assert "抱歉" in answer or "无法确定" in answer

    def test_should_correct_ans_and_ref_important(self):
        context = """

===================== DOCUMENT 1 BEGIN ======================

### Metadata
- document_number: 1
- source: /tmp/事業方法書-1907修正.docx
- doc_id: 094675f4-58e9-489f-a15a-dbf5c1a9d3a5
- chunk_indexes: [0, 10, 11, 12]


### Content
一般社団法人 ぜんかれん共済会

事業方法書

第1条（保険の種類および保険金額の限度）

保険の種類、保険金額の限度および保険金額刻み幅は、以下のとおりとする。

1.傷害保険

  ------------------------- ------------------------------- ------------------
  保険金の種類              保険金額限度                    保険金額刻み幅
  (1)傷害死亡保険金         10万円以上1,000万円まで         1万円
  (2)傷害後遺障害保険金     10万円以上1,000万円まで         1万円
  (3)傷害入院保険金         日額1,000円以上10,000円まで     100円
  (4)傷害通院保険金         日額500円以上5,000円まで        100円
  (5)傷害手術保険金         2万円以上20万円まで             2,000円
  ------------------------- ------------------------------- ------------------


第2条（保険契約者の範囲）

保険契約者となることができる者の範囲は、 「障害者の日常生活及び社会生活を総合的に支援するための法律」（平成17年法律第123号）に基づく指定障害福祉サービス事業者（以下「社会復帰施設」という。）である法人とする。

第3条（被保険者の範囲）

被保険者となることができる者の範囲は、以下の各号のいずれかに該当する個人とする。




... (chunk 1 - 9 skipped) ...



1.   保険契約の種類
2.   保険契約者名および所在地
3.   被保険者名
4.   被保険者の満年齢
5.   被保険者の職業
6.   保険期間
7.   加入コース
8.   保険金額
9.   保険料
10.  申込日
11.  保険契約者連絡先電話番号
12.  申込みおよび重要事項確認の押印
13.  被保険者による加入同意の押印
14.  保険証券不発行への同意
15.  特約の有無（有の場合、特約名）

第25条（保険契約の特約の種類および内容）

当社団は、以下の各号の特約を付帯してこの保険契約を引受けることがある。

1.   個人賠償責任担保特約（別紙特約条項1.参照）

 +----------------------+---------------------------- +------------------
|  保険金の種類               |       保険金額限度                    |   保険金額刻み幅
+----------------------+----------------------------+------------------
|  個人賠償責任保険金 |   1,000円以上1,000万円まで  |   1,000円
+----------------------+----------------------------+------------------


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------------------------------------------------------------------+
| 保険金の支払事由                                                                                                                                                                   | 保険金の支払額                                                                                                                 |
+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------------------------------------------------------------------+
| 被保険者が保険期間内に日本国内において生じた以下の各号の偶然な事故により、他人の身体の障害または財物の損壊に対して、法律上の損害賠償責任を負担することによって損害を被ったこと。   | １回の事故について、保険証券に記載の個人賠償責任保険金額を限度として、特約条項に定める損害賠償金の額および費用の額を支払う。   |
|                                                                                                                                                                                    |                                                                                                                                |
| 1.   被保険者の居住の用に供される住宅の所有、使用または管理に起因する偶然な事故                                                                                                    |                                                                                                                                |
| 2.   被保険者の日常生活に起因する偶然な事故                                                                                                                                        |                                                                                                                                |+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------------------------------------------------------------------+
| 保険金の支払事由                                                                                                                                                                   | 保険金の支払額                                                                                                                 |

+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------------------------------------------------------------------+


第26条（契約者配当）

当社団は、契約者配当を行わない。

第27条（契約者の申し出による保険期間中の契約内容の変更に関する事項）

当社団は、保険期間中において、保険契約者の申し出による以下の契約内容の変更を受付ける。

-    加入コース

-    特約の付帯または取り外し

2. 保険契約者より第1項の変更を行おうとする場合には、書面により当社団に申請し、保険料の追徴がある場合には、当社団が指定した日までにこれを払い込んだ上、当社団の承認を得なければ、その効力を有しない。

3. 第1項の変更により、保険料の追徴または返戻が生じた場合には、当社団は、変更日から保険期間満了日までの残期間に対応する保険料を月割りの方法にて算出し、保険契約者に対して追徴または返戻を行う。ただし、残期間に1ヶ月に満たない端数がある場合には、追徴のときはこれを切上げ、返戻のときはこれを切捨てる。

第28条（特別勘定および区分勘定）

当社団は、特別勘定および区分勘定を設けない。

第29条（団体契約および団体扱いの取扱い）


======================= DOCUMENT 2 BEGIN =====================

### Metadata
- document_number: 2
- source: /tmp/個人賠償特約-提出用.docx
- doc_id: 9678a304-170e-4fad-a32b-8a35efe3dd2e
- chunk_indexes: [1, 2, 3, 4, 5, 7, 8, 10, 11]


### Content


2. 第1項に記載のない用語の定義については、普通保険約款の定義に準じます。

第3条（被保険者の範囲）

この特約の被保険者は、主契約における被保険者（以下「本人」といいます。）の他、以下の各号に該当する方をいいます。

1.   本人の配偶者
2.   本人または本人の配偶者と生計を共にする同居の親族
3.   本人または本人の配偶者と生計を共にする別居の未婚の子

2. 第1項の続柄は、損害の原因となった事故発生時におけるものをいいます。

第4条（個人賠償責任保険金）




+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------+
| 保険金の支払事由                                                                                                                                                                   | 保険金の支払額                                                                                                                                              |
+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------+
| 被保険者が保険期間内に日本国内において生じた以下の各号の偶然な事故により、他人の身体の障害または財物の損壊に対して、法律上の損害賠償責任を負担することによって損害を被ったこと。   | １回の事故について、保険証券に記載の個人賠償��任保険金額を限度として、第5条（個人賠償責任保険金の範囲）に定める損害賠償金の額および費用の額を支払います。   |
|                                                                                                                                                                                    |                                                                                                                                                             |
| 1.   被保険者の居住の用に供される住宅の所有、使用または管理に起因する偶然な事故                                                                                                    |                                                                                                                                                             |
| 2.   被保険者の日常生活に起因する偶然な事故                                                                                                                                        |                                                                                                                                                             |+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------+
| 保険金の支払事由                                                                                                                                                                   | 保険金の支払額                                                                                                                                              |

+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------+


第5条（個人賠償責任保険金の範囲）

第4条（個人賠償責任保険金）の個人賠償責任保険金の範囲は、以下の各号のとおりです。

1.   被保険者が損害賠償請求権者に支払うべき損害賠償金。この損害賠償金については、判決により支払を命ぜられた訴訟費用または判決日までの遅延損害金を含み、また、損害賠償金を支払うことによって、被保険者が代位取得するものがあるときは、その価額をこれから差引きます。
2.   第8条（事故の発生）第1項第(2)号および第(3)号の場合において、被保険者が支出した必要または有益な費用
3.   第(2)号の手段を講じた後において、被保険者に損害賠償責任がないと判明した場合、被害者のために支出した応急手当、護送、その他緊急措置に要した費用およびその支出についてあらかじめ当社団の書面による同意を得た費用
4.   損害賠償請求の解決について、被保険者が当社団の書面による同意を得て支出した訴訟費用、弁護士報酬、仲裁、和解、調停または示談交渉に要した費用
5.   第10条（損害賠償責任の解決）の協力のために被保険者が直接要した費用

第6条（保険金を支払わない場合）

以下の各号の事由によって生じた損害または費用に対しては、当社団は、個人賠償責任保険金を支払いません。

1.   保険契約者、被保険者またはこれらの方の法定代理人の故意
2.   地震もしくは噴火またはこれらによる津波
3.   戦争、外国の武力行使、革命、政権奪取、内乱、武装反乱その他これらに類似の事変または暴動
4.   核燃料物質もしくは核燃料物質によって汚染されたものの放射性、爆発性その他の有害な特性
5.   原因の如何を問わず、また、同時発生かあるいは連続して発生したかにかかわらず、テロリズム
6.   第(2)号から第(5)号までの事由に随伴して生じた事故またはこれらに伴う秩序の混乱に基づいて生じた事故
7.   第(4)号以外の放射線照射または放射能汚染
8.   被保険者の職務遂行またはアルバイト業務の遂行に直接起因する損害賠償責任
9.   被保険者と同居する方に対する損害賠償責任
10.  被保険者と生計を共にする別居の親族に対する損害賠償責任
11.  被保険者と第三者との間に損害賠償責任に関する約定がある場合において、その約定により、加重された損害賠償責任
12.  被保険者が所有、使用または管理する財物の破損について、その財物について正当な権利を有する者に対して負担する損害賠償責任
13.  被保険者の心神喪失に起因する損害賠償責任

... (chunk 6 - 6 skipped) ...



第7条（他の保険契約等がある場合の保険金の支払額）

当社団が保険金を支払うべき事由に対して、保険金等を支払うべき他の保険契約等がある場合において、それぞれの支払責任額の合計額が、損害賠償金の額および費用の額を超えるときは、当社団は、以下の各号に定める額を保険金として支払います。

1.   他の保険契約等から保険金または保険金等が支払われていない場合

・・・この特約の支払責任額

2.   他の保険契約等から保険金または保険金等が支払われた場合

・・・損害賠償金の額および費用の額から、他の保険契約等から支払われた保険金または共済金等の合計額を差引いた残額。ただし、この特約の支払責任額を限度とします。

第8条（事故の発生）

保険契約者または被保険者は、保険金の支払事由に該当する他人の身体の障害または財物の損壊が発生したことを知ったときは、以下の各号の事項を行わなければなりません。

1.   事故の発生の日時、場所、被害者の住所、氏名、年齢、職業、事故の状況およびこれらの事項の証人となる方があるときはその住所、氏名を、また損害賠償の請求を受けたときはその内容を、遅滞なく書面をもって当社団に通知すること
2.   他人から損害の賠償を受けることができる場合には、その権利の保全または行使について必要な手続きをとること
3.   損害を防止または軽減するために必要な措置を講ずること
4.   損害賠償責任の全部または一部を承認しようとするときは、あらかじめ当社団の承認を得ること
5.   損害賠償責任に関する訴訟を提起しようとするとき、または提訴されたときは、直ちに書面をもって当社に通知すること
6.   第(1)号から第(5)号の他、当社団が特に必要とする書類または証拠となるものを求めたときは、遅滞なくこれを提出し、また当社団が行う調査に協力すること

2. 保険契約者または被保険者が、正当な理由がないのに第1項の規定に違反しまたは第1項の規定に応じないときは、当社団は、それによって当社団が被った損害の額を差引いて保険金を支払います。

第9条（保険金の請求）

個人賠償責任保険金を請求できる方は、当社団が特に認めた場合を除き被保険者とします。

... (chunk 9 - 9 skipped) ...



個人賠償責任保険金の支払事由が生じたことにより被保険者が損害賠償請求権その他の債権（共同不法行為等の場合における連帯債務者相互間の求償権を含みます。）を取得した場合において、当社団がその損害に対して個人賠償責任保険金を支払ったときは、その債権は、当社団に移転します。ただし、移転するのは、以下の各号のいずれかの額を限度とします。

1.   当社団が損害賠償金の額および費用の額の全額を個人賠償責任保険金として支払った場合

・・・被保険者が取得した債権の全額

2.   第(1)号以外の場合

・・・被保険者が取得した債権の額から、個人賠償責任共済金が支払われていない損害賠償金の額および費用の額を差引いた額

2. 第1項第(2)号の場合において、当社団に移転せずに被保険者が引き続き有する債権は、当社団に移転した債権よりも優先して弁済されるものとします。

3. 保険契約者および被保険者は、当社団が取得する第1項の債権の保全および行使ならびにそのために当社団が必要とする証拠および書類の入手に協力しなければなりません。このために必要な費用は、当社団の負担とします。

第12条（損害賠償請求権者の特別先取特権）

損害賠償請求権者は、個人賠償責任保険金を請求する権利について特別先取特権（法律で定められた一定の債権を有する方が債務者の財産から他の債権者に優先して弁済を受ける権利をいいます。）を有します。

2. 被保険者は、第1項の損害賠償請求権者への債務について弁済をした額、または損害賠償請求権者の承諾があった額の限度においてのみ、当社団に対して保険金を請求できる権利を行使することができます。

第13条（主契約との関係）

主契約が解約、無効、失効、解除または取消となった場合には、この特約も同様に取扱います。

2. この特約においては、普通保険約款第12条（作業中・通退所中のみ担保の特則）を適用しません。

3. この特約に別段の定めのない事項は、この特約の趣旨に反しない限り、主契約の規定を準用します。




===================== DOCUMENT 3 BEGIN =====================

### Metadata
document_number: 3
source: /tmp/普通保険約款-1907修正.docx
doc_id: 98988bd5-bab9-4a24-8b6d-0de4a471a47b
chunk_indexes: [9, 10, 11, 12, 26, 27, 28, 32]


### Content


3. 別表2の各号に該当しない後遺障害であっても、それらの後遺障害に相当すると認められるものについては、身体の障害の程度に応じ、かつ、別表2の各号の区分に準じて、傷害後遺障害保険金の支払額を決定します。ただし、別表2の第1項第(3)号、第(4)号、第2項第(3)号、第4項第(4)号および第5項第(2)号の機能障害に至らない障害を除きます。

4. 同一の事由により、2種以上の後遺障害が生じた場合には、当社団は、各々の後遺障害に対して第1項から第3項までの規定を適用し、その合計額を支払います。ただし、別表2の第7項から第9項までに定める上肢（腕および手）または下肢（脚および足）の後遺障害に対しては、1肢ごとの傷害後遺障害保険金は、保険証券に記載の傷害後遺障害保険金額の60％をもって限度とします。

5. 既に身体の障害（以下「既存障害」といいます。）がある被保険者が第1項の支払事由に該当し、同一部位に新たな後遺障害が加わったときは、加重された後の後遺障害の状態に対応する割合から、既存障害に対応する割合を差引いて得た割合により、傷害後遺障害保険金を支払います。




6. 第1項から第5項までの規定により、当社団が支払うべき傷害後遺障害保険金の額は、保険期間を通じ、保険証券に記載の傷害後遺障害保険金額をもって限度とします。

第9条（傷害入院保険金）

  ------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------
  保険金の支払事由                                                                                              保険金の支払額
  被保険者が保険期間内に傷害を被り、その直接の結果として事故の日からその日を含めて180日以内に入院したこと。     １回の事故について180日を限度として、1日につき、保険証券に記載の傷害入院保険金額を支払います。
  ------------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------


2. 被保険者が傷害入院保険金の支払いを受けられる期間中、新たに他の傷害の治療を目的とした入院を開始したとしても、当社団は、重複しては傷害入院保険金を支払いません。

3. 第1項により、当社団が傷害入院保険金を支払う場合、以下の各号によるときは、当社団は、被保険者の入院日数よりその対象となる日数を差引いて傷害入院保険金を支払います。

1.   入院中に外泊またはこれに準ずる外出（医師の許可の有無を問いません。）をした場合
2.   入院中において就学または家事等日常生活に支障がないと判断される場合

4. 事故の日からその日を含めて180日を経過した後の入院に対しては、当社団は、傷害入院保険金を支払いません。

第10条（傷害通院保険金）

  ------------------------------------------------------------------------------------------------------------- ------------------------------------------------------------------------------------------------
  保険金の支払事由                                                                                              保険金の支払額
  被保険者が保険期間内に傷害を被り、その直接の結果として事故の日からその日を含めて180日以内に通院したこと。     1回の事故について90日を限度として、1日につき、保険証券に記載の傷害通院保険金額を支払います。
  ------------------------------------------------------------------------------------------------------------- ------------------------------------------------------------------------------------------------


2. 被 保険者が通院しない場合においても、骨折等の傷害を被った部位を固定するために医師の指示により、別表3に定めるギプス等を常時装着した結果、平常の生活に著しい支障が生じたと認められるときは、当社団は、その日数に対して、傷害通院保険金を支払います。

3. 被保険者が傷害通院保険金または第9条（障害入院保険金）の障害入院保険金の支払いを受けられる期間中に新たに他の傷害を被ったとしても、当社団は、重複しては傷害通院保険金を支払いません。

4. 第1項および第2項の規定により当社団が傷害通院保険金を支払う場合、当社団は、被保険者の通院日数より以下の各号の日数を差引いた通院日数に対して傷害通院保険金を支払います。

1.   就業、就学または平常の生活に支障がない程度に治ったとき以降の通院
2.   事故の日からその日を含めて180日を経過した後の通院

第11条（傷害手術保険金）

  ---------------------------------------------------------------------------------------------- ----------------------------------------------------------------------------------------
  保険金の支払事由                                                                               保険金の支払額
  傷害入院保険金が支払われる入院中に、被保険者がその傷害の治療を目的として手術を受けたこと。     1回の事故について1回の手術を限度として、保険証券に記載の傷害手術保険金を支払います。
  ---------------------------------------------------------------------------------------------- ----------------------------------------------------------------------------------------


第12条（作業中・通退所中のみ担保の特則）

この保険契約のうち、「Aコース」、「Bコース」および「Cコース」においては、第2項に定める作業中・通退所中に発生した保険金の支払事由に対してのみ、保険金を支払います。

2. この約款において、作業中・通退所中とは、以下の各号のいずれかに該当する間をいいます。

... (chunk 13 - 25 skipped) ...



保険金の支払事由が生じたときに、既に存在していた身体障害もしくは傷害の影響により、または保険金を支払うべき傷害を被った後にその原因となった事故と関係なく発生した傷害の影響により、その保険金を支払うべき傷害の程度が重大となったときは、当社団は、その影響がなかった場合に相当する程度を認定して保険金を支払います。

2. 保険金の支払事由が生じたときに当社団の認める正当な理由がなく被保険者が治療を怠ったりまたは被保険者もしくは保険金受取人が治療をさせなかったために傷害の程度が重大となった場合は、当社団は、第1項と同様の方法で保険金を支払います。

第26条（保険金受取人）

第7条（傷害死亡保険金）の傷害死亡保険金の受取人は、被保険者の戸籍上の配偶者、子（子が死亡している場合はその直系卑属）、父母、祖父母、兄弟姉妹の順位に従います。ただし、同一の順位に保険金受取人が2人以上ある場合には、代表者1人を定め他の受取人を代理するものとします。

2． 第1項以外の保険金の受取人は、被保険者とします。ただし、被保険者が保険金を請求できない特別な事情があるときは、請求時において以下の各号の範囲内の方に限り、その事情を示す書類をもってその旨を当社団に申し出て、当社団の承認を得たうえで、保険金受取人として代理請求することができます。

1.   被保険者と同居または生計を一にしている被保険者の戸籍上の配偶者
2.   第(1)号に該当する方がいない場合または保険金を請求できない事情がある場合には、被保険者と同居または生計を一にしている被保険者の3親等内の親族
3.   第(1)号および第(2)号に該当する方がいない場合または保険金を請求できない事情がある場合には、第(1)号以外の配偶者（社会通念上、夫婦共同生活と認められる社会的事実が存在するいわゆる「事実婚」の配偶者をいいます。）または第(2)号以外の3親等内の親族

3. 第2項の代理請求人が保険金受取人として保険金を請求するときは、特別な事情を示す書類を当社団に提出しなければなりません。

4. 第2項および第3項の規定により、保険金が代理請求人に支払われた場合には、その支払後に保険金の請求を受けても、当社団は、これを支払いません。

第27条（事故の発生）

保険契約者、被保険者または保険金受取人は、この保険により当社団が保険金を支払うべき傷害を被った場合には、事故の発生の日時、場所、概要、傷害の程度および経過を、遅滞なく当社団に通知しなければなりません。

2. 保険契約者、被保険者または保険金受取人が正当な理由がなく第1項に定める通知について、知っている事実を告げなかった場合または事実でないことを告げた場合には、当社団は、それによって当社団が被った損害の額を差引いて保険金を支払います。

第28条（保険金の請求）

保険金受取人は、当社団の定める保険金請求書に必要事項を記入し、保険金の種類ごとの必要書類と併せて、当社団に提出することにより、保険金の請求を行うことができます。

2. 保険金受取人は、所定の書面を当社団に提出することにより、別の方に保険金の請求を委任することができます。この場合、同委任を証する書類、同委任を委ねた方および受けた方、双方の印鑑証明書を提出しなければなりません。

第29条（当社団が指定する医師による診断）

... (chunk 29 - 31 skipped) ...



保険金の支払事由の発生が著しく増加し、この保険契約の計算の基礎に重大な影響を及ぼす状況の変化が生じたときは、当社団は、当社団の定めるところにより、保険期間中に保険料の増額または保険金の減額を行うことがあります。

2. 巨大災害または船舶・航空機事故等により保険金の支払事由が一時に多数発生し、当該保険事故による保険金を全額支払うとした場合、当社団の収支状況を著しく悪化させると認められるときは、当社団は、該当する保険金の全部または一部を削減して支払うことがあります。

3. 第1項または第2項の場合、当社団は、速やかに保険契約者または保険金受取人に通知します。

第32条（保険料の見直しおよび引受けの停止）

当社団は、この保険商品の収支を計算し、次年度以降の当社団の経営に著しい影響を及ぼすことが予想されると判断した場合は、当社団の定めるところにより、次年度以降の保険料の増額または保険金額の減額を行うことがあります。

2. 第1項の場合、当社団は、更新後の内容を毎年2月1日までに保険契約者に通知します。


"""

        answer = ask_question(
            question="事業方法書に記載がある個人賠償責任保険金の保険金額限度はいくらですか。",
            context=context,
            response_language="ja",
        )
        print("ask answer xx12", answer)
        assert "1,000" in answer
        assert "1,000万" in answer
        assert "10万" not in answer
        # assert "1" in answer

    def test_should_correct_ans_freezh_important(self):
        context = """


================== DOCUMENT 1 BEGIN ==================

### Metadata
document_number: 1
source: https://www.freesh.com/shop/shop/index?category_id=3&page=2&per-page=12
doc_id: c224a419-3fb1-4b92-a167-2a94b39e8592
chunk_indexes: [0, 1]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
オンラインショップ

  

 

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

オンラインショップ

ログイン
カート
 Dozo Freeshの 魅力  フルーツティーの 特徴  フルーツティーの 淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

  
ホーム  > オンラインショップ

オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
DozoFreesh Fruits Tea Set Of 4Bags

DozoFreeshフルーツティー オリジナル全4種お試しセット 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物 （紅茶 ルイボスティー ジャスミンティー ウーロン茶）

¥ 1,556（税込）
DozoFreesh Fruits Tea Set Of 8Bags




DozoFreeshフルーツティー オリジナル4種セット×2 8袋おまとめセット 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物

¥ 3,112（税込）

-   «
-   1
-   2
-    »
Freesh株式会社

Freesh Inc.

  
会社概要 よくある質問 プライバシーポリシー 特定商取引法に基づく表記 お問い合わせ

DozoFreesh オンラインショップ DozoFreesh アマゾン DozoFreesh 楽天 DozoFreesh au PAY マーケット

Copyright © Freesh Inc. All Rights Reserved.


================== DOCUMENT 2 BEGIN ==================

### Metadata
document_number: 2
source: https://www.freesh.com/shop/shop/index?page=2&per-page=12
doc_id: 8769c1e4-f237-4c42-854f-b18f9e47c5f0
chunk_indexes: [0, 1, 2]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
オンラインショップ

  

 

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

オンラインショップ

ログイン
カート
 Dozo Freeshの 魅力  フルーツティーの 特徴  フルーツティーの 淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

  
ホーム  > オンラインショップ

オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
DozoFreesh Starter Set (Smoky Green Bottle)

DozoFreesh初めての方向けお得セット オリジナル４種+新たに4種類セット（全8種）+HARIOフィルターインボトル スモーキーグリーン 400ml 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン 水分補給 プレゼント ギフト 贈り物




¥ 5,312（税込）
DozoFreesh Fruits Tea Set Of 4Bags

DozoFreeshフルーツティー 新たに4種類セット 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物 （龍井茶 プーアル茶 アッサムティー 緑茶）

¥ 1,556（税込）
DozoFreesh Fruits Tea Set Of 4Bags

DozoFreeshフルーツティー オリジナル全4種お試しセット 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物 （紅茶 ルイボスティー ジャスミンティー ウーロン茶）

¥ 1,556（税込）
DozoFreesh Fruits Tea Set Of 8Bags

DozoFreeshフルーツティー オリジナル4種セット×2 8袋おまとめセット 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物

¥ 3,112（税込）
GIFT PACKAGE SET

ギフトボックス&紙袋セット 母の日 父の日 贈り物 内祝 誕生日 新築祝 結婚祝 プレゼント

¥ 545（税込）
GIFT BOX

ギフトボックス ギフト箱 母の日 父の日 贈り物 内祝 誕生日 新築祝 結婚祝 プレゼント

¥ 380（税込）
GIFT BAG

ギフトセット箱専用 ギフト紙袋 手提げ紙袋 ギフトバッグ 贈り物 内祝 誕生日 新築祝 結婚祝 プレゼント

¥ 165（税込）
HARIO Filter-in-bottle portable

HARIO (ハリオ) フィルターインボトル ポータブル スモーキーピンク フルーツティーボトル 水出し茶ポット 冷茶ポット 茶こし付き 400ml 樹脂製 プレゼント ギフト 贈り物

¥ 3,080（税込）
HARIO Filter-in-bottle portable

HARIO (ハリオ) フィルターインボトル ポータブル スモーキーグリーン フルーツティーボトル 水出し茶ポット 冷茶ポット 茶こし付き 400ml 樹脂製 プレゼント ギフト 贈り物

¥ 3,080（税込）
HARIO Filter-in Bottle

HARIO (ハリオ) フィルターインボトル ポータブル スモーキーグリーン フルーツティーボトル 水出し茶ポット 冷茶ポット 茶こし付き 300ml ガラスボトル 耐熱ガラス プレゼント ギフト 贈り物

¥ 1,980（税込）
HARIO Filter-in Bottle

HARIO (ハリオ) フィルターインボトル ポータブル スモーキーピンク フルーツティーボトル 水出し茶ポット 冷茶ポット 茶こし付き 300ml ガラスボトル 耐熱ガラス プレゼント ギフト 贈り物

¥ 1,980（税込）
HARIO Filter-in Bottle

HARIO (ハリオ) フィルターインボトル ポータブル ホワイト フルーツティーボトル 水出し茶ポット 冷茶ポット 茶こし付き 300ml ガラスボトル 耐熱ガラス プレゼント ギフト 贈り物

¥ 1,980（税込）

-   «
-   1
-   2
-   3
-   4
-   5
-   »
Freesh株式会社

Freesh Inc.

  
会社概要 よくある質問 プライバシーポリシー 特定商取引法に基づく表記 お問い合わせ

DozoFreesh オンラインショップ DozoFreesh アマゾン DozoFreesh 楽天 DozoFreesh au PAY マーケット

Copyright © Freesh Inc. All Rights Reserved.



================== DOCUMENT 3 BEGIN ==================

### Metadata
document_number: 3
source: https://www.freesh.com/shop-9.html
doc_id: 874e4819-b048-4ae9-af7f-6d595c25f246
chunk_indexes: [0, 1]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
オンラインショップ

  

 

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

オンラインショップ

ログイン
カート
 Dozo Freeshの 魅力  フルーツティーの 特徴  フルーツティーの 淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

  
ホーム  > オンラインショップ

オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
DozoFreesh Gift Set (Smoky Pink Bottle)

ギフトセット ギフトボックス+オリジナル4種+新たに4種類セット（全8種）+HARIOフィルターインボトル スモーキーピンク 400ml 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン 水分補給 プレゼント ギフト 贈り物

¥ 5,692（税込）
DozoFreesh Gift Set (Smoky Green Bottle)




ギフトセット ギフトボックス+オリジナル4種+新たに4種類セット（全8種）+HARIOフィルターインボトル スモーキーグリーン 400ml 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン 水分補給 プレゼント ギフト 贈り物

¥ 5,692（税込）
GIFT PACKAGE SET

ギフトボックス&紙袋セット 母の日 父の日 贈り物 内祝 誕生日 新築祝 結婚祝 プレゼント

¥ 545（税込）
GIFT BOX

ギフトボックス ギフト箱 母の日 父の日 贈り物 内祝 誕生日 新築祝 結婚祝 プレゼント

¥ 380（税込）
GIFT BAG

ギフトセット箱専用 ギフト紙袋 手提げ紙袋 ギフトバッグ 贈り物 内祝 誕生日 新築祝 結婚祝 プレゼント

¥ 165（税込）
Freesh株式会社

Freesh Inc.

  
会社概要 よくある質問 プライバシーポリシー 特定商取引法に基づく表記 お問い合わせ

DozoFreesh オンラインショップ DozoFreesh アマゾン DozoFreesh 楽天 DozoFreesh au PAY マーケット

Copyright © Freesh Inc. All Rights Reserved.



================== DOCUMENT 4 BEGIN ==================

### Metadata
document_number: 4
source: https://www.freesh.com/shop.html
doc_id: e4e9b89b-20b8-4d6f-bcd4-c7bcf6fe86e0
chunk_indexes: [0, 1, 2]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
  

 

ログイン
カート 

オンラインショップ

 Dozo Freeshの 魅力  フルーツティーの 特徴  フルーツティーの 淹れ方

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

  
オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト 並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
STAR EMERALD

DozoFreeshフルーツティー スターフルーツと緑茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス デザートティー 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
BLUSH ICHIJIKU

DozoFreeshフルーツティー イチジクと龍井茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス デザートティー 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
CITRUS PUER




DozoFreeshフルーツティー カシスとプーアル茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ダイエットウォーター デザートティー 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
MULBERREY WINE

DozoFreeshフルーツティー アップルシナモンとアッサムティー 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
FLAMINGO PEACH

DozoFreeshフルーツティー ピーチとウーロン茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ダイエットウォーター デザートティー 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
AMBER PINE

DozoFreeshフルーツティー パイナップルとルイボスティー 無添加 食べられるお茶 砂糖不使用 カロリーゼロ ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
RUBY DRAGON

DozoFreeshフルーツティー レッドドラゴンフルーツと紅茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ティーバッグ デトックスウォーター インナービューティ 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
LEMON PASSION

DozoFreeshフルーツティー パッションフルーツとジャスミンティー 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ティーバッグ ビタミンC爆弾 ビタミン補給 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
DozoFreesh Gift Set (Smoky Pink Bottle)

ギフトセット ギフトボックス+オリジナル4種+新たに4種類セット（全8種）+HARIOフィルターインボトル スモーキーピンク 400ml 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン 水分補給 プレゼント ギフト 贈り物

¥ 5,692（税込）
DozoFreesh Gift Set (Smoky Green Bottle)

ギフトセット ギフトボックス+オリジナル4種+新たに4種類セット（全8種）+HARIOフィルターインボトル スモーキーグリーン 400ml 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン 水分補給 プレゼント ギフト 贈り物

¥ 5,692（税込）
DozoFreesh Fruits Tea Set Of 8Bags

DozoFreeshフルーツティー オリジナル４種+新たに4種類セット（全8種） 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物 （紅茶 ルイボスティー ジャスミンティー ウーロン茶 龍井茶 プーアル茶 アッサムティー 緑茶）

¥ 3,112（税込）
DozoFreesh Starter Set (Smoky Pink Bottle)

DozoFreesh初めての方向けお得セット オリジナル４種+新たに4種類セット（全8種）+HARIOフィルターインボトル スモーキーピンク 400ml 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン 水分補給 プレゼント ギフト 贈り物

¥ 5,312（税込）

-    «
-   1
-   2
-   3
-   4
-   5
-   »
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録



================== DOCUMENT 5 BEGIN ==================

### Metadata
document_number: 5
source: https://www.freesh.com/shop/shop/index?page=3&per-page=12
doc_id: 405cb166-7d04-4a7a-99dc-912ee82bf182
chunk_indexes: [0]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
  

 

ログイン
カート 

オンラインショップ

 Dozo Freeshの 魅力  フルーツティーの 特徴  フルーツティーの 淹れ方

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

  
オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト 並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
HARIO Filter-in Bottle

HARIO (ハリオ) フィルターインボトル ポータブル スモーキーグリーン フルーツティーボトル 水出し茶ポット 冷茶ポット 茶こし付き 750ml ガラスボトル 耐熱ガラス プレゼント ギフト 贈り物

¥ 2,200（税込）
HARIO Filter-in Bottle

HARIO (ハリオ) フィルターインボトル ポータブル スモーキーピンク フルーツティーボトル 水出し茶ポット 冷茶ポット 茶こし付き 750ml ガラスボトル 耐熱ガラス プレゼント ギフト 贈り物

¥ 2,200（税込）
HARIO Filter-in Bottle




================== DOCUMENT 6 BEGIN ==================

### Metadata
document_number: 6
source: https://www.freesh.com/dry.html
doc_id: d2cad089-60cb-483f-a6f7-cfb71a78278f
chunk_indexes: [4]


### Content


噴霧乾燥は、液体食品を微粒化して高温気流中に噴霧し、瞬間的に水分を蒸発させ乾燥する人工乾燥法です。

この方法の特徴は、粉末状に乾燥できることです。

どんなものに使われているの?

粉乳 粉末コーヒー
噴霧乾燥のメリット

食品成分の変化が少ない。

ほぼ球状の粉末が得られる。
噴霧乾燥のデメリット

装置が大型で高価。

ドラム乾燥

人工乾燥

ドラム乾燥は、加熱された回転式ドラムの表面に液状の食品を薄く付着させ、ドラムの回転とともに蒸発乾燥を行う人工乾燥法です。

この方法の特徴は、粉末状に乾燥できることです。

どんなものに使われているの?

マッシュポテト
ドラム乾燥のメリット

乾燥速度が速い。

噴霧乾燥機に比べ装置が小型で安い。

沢山の部品を同時に乾燥させることができる。
ドラム乾燥のデメリット

食品成分の変化が大きい。

部品同士が傷つく可能性がある。

加圧乾燥

人工乾燥

加圧乾燥は、食品に加圧、加熱を行い、一気に減圧して瞬間的に水分を蒸発させ乾燥する人工乾燥法です。

この方法の特徴は、膨化食品の製造に向いていることです。

どんなものに使われているの?

ぽんせんべい スナック食品
加圧乾燥のメリット

食品の復元性が良い。

乾燥速度が速い。
加圧乾燥のデメリット




================== DOCUMENT 7 BEGIN ==================

### Metadata
document_number: 7
source: https://www.freesh.com/shop-5.html
doc_id: 1475714e-9f82-45aa-a33b-eab106d36fde
chunk_indexes: [0]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
オンラインショップ

  

 

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

オンラインショップ

ログイン
カート
 Dozo Freeshの 魅力  フルーツティーの 特徴  フルーツティーの 淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

  
ホーム  > オンラインショップ

オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
HARIO Filter-in-bottle portable

HARIO (ハリオ) フィルターインボトル ポータブル スモーキーピンク フルーツティーボトル 水出し茶ポット 冷茶ポット 茶こし付き 400ml 樹脂製 プレゼント ギフト 贈り物

¥ 3,080（税込）
HARIO Filter-in-bottle portable




================== DOCUMENT 8 BEGIN ==================

### Metadata
document_number: 8
source: https://www.freesh.com/shop-3.html
doc_id: bf18dca9-cc2b-405e-b588-b02c1e655350
chunk_indexes: [0, 1, 2]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
オンラインショップ

  

 

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

オンラインショップ

ログイン
カート
 Dozo Freeshの 魅力  フルーツティーの 特徴  フルーツティーの 淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

  
ホーム  > オンラインショップ

オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
STAR EMERALD

DozoFreeshフルーツティー スターフルーツと緑茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス デザートティー 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
BLUSH ICHIJIKU




DozoFreeshフルーツティー イチジクと龍井茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス デザートティー 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
CITRUS PUER

DozoFreeshフルーツティー カシスとプーアル茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ダイエットウォーター デザートティー 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
MULBERREY WINE

DozoFreeshフルーツティー アップルシナモンとアッサムティー 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
FLAMINGO PEACH

DozoFreeshフルーツティー ピーチとウーロン茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ダイエットウォーター デザートティー 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
AMBER PINE

DozoFreeshフルーツティー パイナップルとルイボスティー 無添加 食べられるお茶 砂糖不使用 カロリーゼロ ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
RUBY DRAGON

DozoFreeshフルーツティー レッドドラゴンフルーツと紅茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ティーバッグ デトックスウォーター インナービューティ 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
LEMON PASSION

DozoFreeshフルーツティー パッションフルーツとジャスミンティー 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ティーバッグ ビタミンC爆弾 ビタミン補給 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
DozoFreesh Fruits Tea Set Of 8Bags

DozoFreeshフルーツティー オリジナル４種+新たに4種類セット（全8種） 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物 （紅茶 ルイボスティー ジャスミンティー ウーロン茶 龍井茶 プーアル茶 アッサムティー 緑茶）

¥ 3,112（税込）
DozoFreesh Starter Set (Smoky Pink Bottle)

DozoFreesh初めての方向けお得セット オリジナル４種+新たに4種類セット（全8種）+HARIOフィルターインボトル スモーキーピンク 400ml 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン 水分補給 プレゼント ギフト 贈り物

¥ 5,312（税込）
DozoFreesh Starter Set (Smoky Green Bottle)

DozoFreesh初めての方向けお得セット オリジナル４種+新たに4種類セット（全8種）+HARIOフィルターインボトル スモーキーグリーン 400ml 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン 水分補給 プレゼント ギフト 贈り物

¥ 5,312（税込）
DozoFreesh Fruits Tea Set Of 4Bags



================== DOCUMENT 9 BEGIN ==================

### Metadata
document_number: 9
source: https://www.freesh.com/shop/shop/index?page=4&per-page=12
doc_id: 8fbd2fb7-29ea-43d8-a2d7-8a772b4d7fc4
chunk_indexes: [0]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
オンラインショップ

  

 

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

オンラインショップ

ログイン
カート
 Dozo Freeshの 魅力  フルーツティーの 特徴  フルーツティーの 淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

  
ホーム  > オンラインショップ

オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
HARIO Tea Server Simply

HARIO (ハリオ) ティーサーバー シンプル グラスポット 700ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 4,400（税込）
Tea warmer M

HARIO (ハリオ) ティーウォーマー M ティーポットウォーマー キャンドルウォーマー 保温容器 耐熱ガラス

¥ 3,300（税込）
HARIO Tea Server Simply




================== DOCUMENT 0 BEGIN ==================

### Metadata
document_number: 10
source: https://www.freesh.com/shop/shop/index?page=5&per-page=12
doc_id: a2699d83-b931-46f3-8fa7-677fcffad506
chunk_indexes: [0]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
オンラインショップ

  

 

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

オンラインショップ

ログイン
カート
 Dozo Freeshの 魅力  フルーツティーの 特徴  フルーツティーの 淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

  
ホーム  > オンラインショップ

オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
Fragrance mug

HARIO (ハリオ) 香りマグカップ フルーツティーカップ 330ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 1,100（税込）
Heat resistant oolong mug

HARIO (ハリオ) 耐熱ウーロンマグカップ すき フルーツティーカップ タンブラー 300ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 1,100（税込）



================== DOCUMENT 1 BEGIN ==================

### Metadata
document_number: 11
source: https://www.freesh.com/shop/shop/index?category_id=5&page=2&per-page=12
doc_id: bac87838-944a-4f8f-951d-590c9ee30c50
chunk_indexes: [0, 1]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
オンラインショップ

  

 

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

オンラインショップ

ログイン
カート
 Dozo Freeshの 魅力  フルーツティーの 特徴  フルーツティーの 淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

  
ホーム  > オンラインショップ

オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
HARIO Water Pitcher

HARIO (ハリオ) ウォーターピッチャー 冷蔵庫ポット スリム ホワイト フルーツティーポット 水出し茶ポット 冷茶ポット 900ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 2,200（税込）
Watering tea pot

HARIO (ハリオ) 水出し茶ポット 冷茶ポット フルーツティーポット 大容量 1000ml 耐熱ガラス プレゼント ギフト 贈り物




¥ 880（税込）
Freezer pot JUSIO

HARIO (ハリオ) フリーザーポット 冷蔵庫ポット ホワイト フルーツティーポット 水出し茶ポット 冷茶ポット 1100ml 樹脂製 プレゼント ギフト 贈り物

¥ 1,980（税込）
Teapot clear without lid

HARIO (ハリオ) フタなしティーポット クリア フルーツティーポット 700ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 3,080（税込）
Jumping teapot

HARIO (ハリオ) ジャンピングティーポット フルーツティーポット 800ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 6,600（税込）
HARIO Tea Server Simply

HARIO (ハリオ) ティーサーバー シンプル グラスポット 700ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 4,400（税込）
Tea warmer M

HARIO (ハリオ) ティーウォーマー M ティーポットウォーマー キャンドルウォーマー 保温容器 耐熱ガラス

¥ 3,300（税込）
HARIO Tea Server Simply

HARIO (ハリオ) ティーサーバー シンプル グラスポット 450ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 3,850（税込）
Jumping Leaf Pot S

HARIO (ハリオ) ジャンピングリーフポットS フルーツティーポット 600ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 3,850（税込）
HARIO Jumping Leaf Pot

HARIO (ハリオ) ジャンピングリーフポット フルーツティーポット 600ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 2,200（税込）
Tea & coffee server time

HARIO (ハリオ) ティー＆コーヒーサーバー グラスポット 1000ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 1,980（税込）

-   «
-   1
-   2
-    »
Freesh株式会社

Freesh Inc.



================== DOCUMENT 2 BEGIN ==================

### Metadata
document_number: 12
source: https://www.freesh.com/shop-8.html
doc_id: 0e7a4560-f6a4-48c4-9c06-c19ddf2e7d8d
chunk_indexes: [0]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
オンラインショップ

  

 

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

オンラインショップ

ログイン
カート
 Dozo Freeshの 魅力  フルーツティーの 特徴  フルーツティーの 淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

  
ホーム  > オンラインショップ

オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
Set of 2 straight mugs

HARIO (ハリオ) ストレートマグ マグカップ 2個セット フルーツティーカップ 300ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 2,200（税込）
Set of 2 round mugs

HARIO (ハリオ) ラウンドマグ マグカップ 2個セット フルーツティーカップ 360ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 2,200（税込）
Heat resistant flavor glass round



"""
        answer = ask_question(
            question="有哪些果茶最便宜 ?",
            context=context,
            response_language="zh",
        )
        print("ask dozo freesh answer", answer)
        assert "389" in answer

    def test_should_correct_ans_freezh_1(self):
        context = """
    
-------------- DOCUMENT 1 START ---------------------

### Metadata
doc_index: 1
source: https://www.freesh.com/shop/shop/index?category_id=3&page=2&per-page=12
doc_id: d7034214-3c7b-424a-82a5-af8c95b39899
chunk_indexes: [0, 1]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
オンラインショップ

 

 

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

オンラインショップ

ログイン
カート
 Dozo Freeshの 魅力 フルーツティーの 特徴 フルーツティーの 淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

 
ホーム > オンラインショップ

オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
DozoFreesh Fruits Tea Set Of 4Bags

DozoFreeshフルーツティー オリジナル全4種お試しセット 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物 （紅茶 ルイボスティー ジャスミンティー ウーロン茶）

¥ 1,556（税込）
DozoFreesh Fruits Tea Set Of 8Bags




DozoFreeshフルーツティー オリジナル4種セット×2 8袋おまとめセット 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物

¥ 3,112（税込）

- «
- 1
- 2
- »
Freesh株式会社

Freesh Inc.

 
会社概要 よくある質問 プライバシーポリシー 特定商取引法に基づく表記 お問い合わせ

DozoFreesh オンラインショップ DozoFreesh アマゾン DozoFreesh 楽天 DozoFreesh au PAY マーケット

Copyright © Freesh Inc. All Rights Reserved.



-------------- DOCUMENT 2 START ---------------------

### Metadata
doc_index: 2
source: https://www.freesh.com/shop/shop/index?page=2&per-page=12
doc_id: 08ea7c29-0670-4424-9fb1-4c6286191a07
chunk_indexes: [0, 1, 2]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
オンラインショップ

 

 

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

オンラインショップ

ログイン
カート
 Dozo Freeshの 魅力 フルーツティーの 特徴 フルーツティーの 淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

 
ホーム > オンラインショップ

オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
DozoFreesh Starter Set (Smoky Green Bottle)

DozoFreesh初めての方向けお得セット オリジナル４種+新たに4種類セット（全8種）+HARIOフィルターインボトル スモーキーグリーン 400ml 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン 水分補給 プレゼント ギフト 贈り物




¥ 5,312（税込）
DozoFreesh Fruits Tea Set Of 4Bags

DozoFreeshフルーツティー 新たに4種類セット 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物 （龍井茶 プーアル茶 アッサムティー 緑茶）

¥ 1,556（税込）
DozoFreesh Fruits Tea Set Of 4Bags

DozoFreeshフルーツティー オリジナル全4種お試しセット 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物 （紅茶 ルイボスティー ジャスミンティー ウーロン茶）

¥ 1,556（税込）
DozoFreesh Fruits Tea Set Of 8Bags

DozoFreeshフルーツティー オリジナル4種セット×2 8袋おまとめセット 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物

¥ 3,112（税込）
GIFT PACKAGE SET

ギフトボックス&紙袋セット 母の日 父の日 贈り物 内祝 誕生日 新築祝 結婚祝 プレゼント

¥ 545（税込）
GIFT BOX

ギフトボックス ギフト箱 母の日 父の日 贈り物 内祝 誕生日 新築祝 結婚祝 プレゼント

¥ 380（税込）
GIFT BAG

ギフトセット箱専用 ギフト紙袋 手提げ紙袋 ギフトバッグ 贈り物 内祝 誕生日 新築祝 結婚祝 プレゼント

¥ 165（税込）
HARIO Filter-in-bottle portable

HARIO (ハリオ) フィルターインボトル ポータブル スモーキーピンク フルーツティーボトル 水出し茶ポット 冷茶ポット 茶こし付き 400ml 樹脂製 プレゼント ギフト 贈り物

¥ 3,080（税込）
HARIO Filter-in-bottle portable

HARIO (ハリオ) フィルターインボトル ポータブル スモーキーグリーン フルーツティーボトル 水出し茶ポット 冷茶ポット 茶こし付き 400ml 樹脂製 プレゼント ギフト 贈り物

¥ 3,080（税込）
HARIO Filter-in Bottle

HARIO (ハリオ) フィルターインボトル ポータブル スモーキーグリーン フルーツティーボトル 水出し茶ポット 冷茶ポット 茶こし付き 300ml ガラスボトル 耐熱ガラス プレゼント ギフト 贈り物

¥ 1,980（税込）
HARIO Filter-in Bottle

HARIO (ハリオ) フィルターインボトル ポータブル スモーキーピンク フルーツティーボトル 水出し茶ポット 冷茶ポット 茶こし付き 300ml ガラスボトル 耐熱ガラス プレゼント ギフト 贈り物

¥ 1,980（税込）
HARIO Filter-in Bottle

HARIO (ハリオ) フィルターインボトル ポータブル ホワイト フルーツティーボトル 水出し茶ポット 冷茶ポット 茶こし付き 300ml ガラスボトル 耐熱ガラス プレゼント ギフト 贈り物

¥ 1,980（税込）

- «
- 1
- 2
- 3
- 4
- 5
- »
Freesh株式会社

Freesh Inc.

 
会社概要 よくある質問 プライバシーポリシー 特定商取引法に基づく表記 お問い合わせ

DozoFreesh オンラインショップ DozoFreesh アマゾン DozoFreesh 楽天 DozoFreesh au PAY マーケット

Copyright © Freesh Inc. All Rights Reserved.



-------------- DOCUMENT 3 START ---------------------

### Metadata
doc_index: 3
source: https://www.freesh.com/shop-9.html
doc_id: 539ceaae-6a3c-4e27-842e-ea53f4b8a0af
chunk_indexes: [0, 1]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
オンラインショップ

 

 

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

オンラインショップ

ログイン
カート
 Dozo Freeshの 魅力 フルーツティーの 特徴 フルーツティーの 淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

 
ホーム > オンラインショップ

オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
DozoFreesh Gift Set (Smoky Pink Bottle)

ギフトセット ギフトボックス+オリジナル4種+新たに4種類セット（全8種）+HARIOフィルターインボトル スモーキーピンク 400ml 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン 水分補給 プレゼント ギフト 贈り物

¥ 5,692（税込）
DozoFreesh Gift Set (Smoky Green Bottle)




ギフトセット ギフトボックス+オリジナル4種+新たに4種類セット（全8種）+HARIOフィルターインボトル スモーキーグリーン 400ml 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン 水分補給 プレゼント ギフト 贈り物

¥ 5,692（税込）
GIFT PACKAGE SET

ギフトボックス&紙袋セット 母の日 父の日 贈り物 内祝 誕生日 新築祝 結婚祝 プレゼント

¥ 545（税込）
GIFT BOX

ギフトボックス ギフト箱 母の日 父の日 贈り物 内祝 誕生日 新築祝 結婚祝 プレゼント

¥ 380（税込）
GIFT BAG

ギフトセット箱専用 ギフト紙袋 手提げ紙袋 ギフトバッグ 贈り物 内祝 誕生日 新築祝 結婚祝 プレゼント

¥ 165（税込）
Freesh株式会社

Freesh Inc.

 
会社概要 よくある質問 プライバシーポリシー 特定商取引法に基づく表記 お問い合わせ

DozoFreesh オンラインショップ DozoFreesh アマゾン DozoFreesh 楽天 DozoFreesh au PAY マーケット

Copyright © Freesh Inc. All Rights Reserved.



-------------- DOCUMENT 4 START ---------------------

### Metadata
doc_index: 4
source: https://www.freesh.com/shop-3.html
doc_id: 5bf8509b-c032-4b51-8c3c-66c6332aac13
chunk_indexes: [0, 1, 2]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
 

 

ログイン
カート 

オンラインショップ

 Dozo Freeshの 魅力 フルーツティーの 特徴 フルーツティーの 淹れ方

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

 
オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト 並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
STAR EMERALD

DozoFreeshフルーツティー スターフルーツと緑茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス デザートティー 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
BLUSH ICHIJIKU

DozoFreeshフルーツティー イチジクと龍井茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス デザートティー 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
CITRUS PUER




DozoFreeshフルーツティー カシスとプーアル茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ダイエットウォーター デザートティー 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
MULBERREY WINE

DozoFreeshフルーツティー アップルシナモンとアッサムティー 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
FLAMINGO PEACH

DozoFreeshフルーツティー ピーチとウーロン茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ダイエットウォーター デザートティー 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
AMBER PINE

DozoFreeshフルーツティー パイナップルとルイボスティー 無添加 食べられるお茶 砂糖不使用 カロリーゼロ ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
RUBY DRAGON

DozoFreeshフルーツティー レッドドラゴンフルーツと紅茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ティーバッグ デトックスウォーター インナービューティ 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
LEMON PASSION

DozoFreeshフルーツティー パッションフルーツとジャスミンティー 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ティーバッグ ビタミンC爆弾 ビタミン補給 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
DozoFreesh Fruits Tea Set Of 8Bags

DozoFreeshフルーツティー オリジナル４種+新たに4種類セット（全8種） 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン2023-09-11T11:38:57.974102620Z ティーバッグ 水分補給 プレゼント ギフト 贈り物 （紅茶 ルイボスティー ジャスミンティー ウーロン茶 龍井茶 プーアル茶 アッサムティー 緑茶）

¥ 3,112（税込）
DozoFreesh Starter Set (Smoky Pink Bottle)

DozoFreesh初めての方向けお得セット オリジナル４種+新たに4種類セット（全8種）+HARIOフィルターインボトル スモーキーピンク 400ml 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン 水分補給 プレゼント ギフト 贈り物

¥ 5,312（税込）
DozoFreesh Starter Set (Smoky Green Bottle)

DozoFreesh初めての方向けお得セット オリジナル４種+新たに4種類セット（全8種）+HARIOフィルターインボトル スモーキーグリーン 400ml 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン 水分補給 プレゼント ギフト 贈り物

¥ 5,312（税込）
DozoFreesh Fruits Tea Set Of 4Bags

DozoFreeshフルーツティー 新たに4種類セット 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物 （龍井茶 プーアル茶 アッサムティー 緑茶）

¥ 1,556（税込）

- «
- 1
- 2
- »
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録



-------------- DOCUMENT 5 START ---------------------

### Metadata
doc_index: 5
source: https://www.freesh.com/dry.html
doc_id: cdc15f43-ad1b-4de6-93e5-b3dc472f3c86
chunk_indexes: [4]


### Content


噴霧乾燥は、液体食品を微粒化して高温気流中に噴霧し、瞬間的に水分を蒸発させ乾燥する人工乾燥法です。

この方法の特徴は、粉末状に乾燥できることです。

どんなものに使われているの?

粉乳 粉末コーヒー
噴霧乾燥のメリット

食品成分の変化が少ない。

ほぼ球状の粉末が得られる。
噴霧乾燥のデメリット

装置が大型で高価。

ドラム乾燥

人工乾燥

ドラム乾燥は、加熱された回転式ドラムの表面に液状の食品を薄く付着させ、ドラムの回転とともに蒸発乾燥を行う人工乾燥法です。

この方法の特徴は、粉末状に乾燥できることです。

どんなものに使われているの?

マッシュポテト
ドラム乾燥のメリット

乾燥速度が速い。

噴霧乾燥機に比べ装置が小型で安い。

沢山の部品を同時に乾燥させることができる。
ドラム乾燥のデメリット

食品成分の変化が大きい。

部品同士が傷つく可能性がある。

加圧乾燥

人工乾燥

加圧乾燥は、食品に加圧、加熱を行い、一気に減圧して瞬間的に水分を蒸発させ乾燥する人工乾燥法です。

この方法の特徴は、膨化食品の製造に向いていることです。

どんなものに使われているの?

ぽんせんべい スナック食品
加圧乾燥のメリット

食品の復元性が良い。

乾燥速度が速い。
加圧乾燥のデメリット




-------------- DOCUMENT 6 START ---------------------

### Metadata
doc_index: 6
source: https://www.freesh.com/shop/shop/index?page=3&per-page=12
doc_id: abb1b7e4-7192-4bae-bf10-f648f6515ff3
chunk_indexes: [0, 2]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
オンラインショップ

 

 

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

オンラインショップ

ログイン
カート
 Dozo Freeshの 魅力 フルーツティーの 特徴 フルーツティーの 淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

 
ホーム > オンラインショップ

オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
HARIO Filter-in Bottle

HARIO (ハリオ) フィルターインボトル ポータブル スモーキーグリーン フルーツティーボトル 水出し茶ポット 冷茶ポット 茶こし付き 750ml ガラスボトル 耐熱ガラス プレゼント ギフト 贈り物

¥ 2,200（税込）
HARIO Filter-in Bottle




... (chunk 1 - 1 skipped) ...



¥ 2,200（税込）
Watering tea pot

HARIO (ハリオ) 水出し茶ポット 冷茶ポット フルーツティーポット 大容量 1000ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 880（税込）
Freezer pot JUSIO

HARIO (ハリオ) フリーザーポット 冷蔵庫ポット ホワイト フルーツティーポット 水出し茶ポット 冷茶ポット 1100ml 樹脂製 プレゼント ギフト 贈り物

¥ 1,980（税込）
Teapot clear without lid

HARIO (ハリオ) フタなしティーポット クリア フルーツティーポット 700ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 3,080（税込）
Jumping teapot

HARIO (ハリオ) ジャンピングティーポット フルーツティーポット 800ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 6,600（税込）

- «
- 1
- 2
- 3
- 4
- 5
- »
Freesh株式会社

Freesh Inc.

 
会社概要 よくある質問 プライバシーポリシー 特定商取引法に基づく表記 お問い合わせ

DozoFreesh オンラインショップ DozoFreesh アマゾン DozoFreesh 楽天 DozoFreesh au PAY マーケット

Copyright © Freesh Inc. All Rights Reserved.


-------------- DOCUMENT 7 START ---------------------

### Metadata
doc_index: 7
source: https://www.freesh.com/shop-5.html
doc_id: a4cf110f-4c6b-4b60-8351-ce97e9ea8d72
chunk_indexes: [0]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
オンラインショップ

 

 

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

オンラインショップ

ログイン
カート
 Dozo Freeshの 魅力 フルーツティーの 特徴 フルーツティーの 淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

 
ホーム > オンラインショップ

オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
HARIO Filter-in-bottle portable

HARIO (ハリオ) フィルターインボトル ポータブル スモーキーピンク フルーツティーボトル 水出し茶ポット 冷茶ポット 茶こし付き 400ml 樹脂製 プレゼント ギフト 贈り物

¥ 3,080（税込）
HARIO Filter-in-bottle portable



-------------- DOCUMENT 8 START ---------------------

### Metadata
doc_index: 8
source: https://www.freesh.com/shop.html
doc_id: 5cf953a0-c59a-4839-a72d-1c9e0ad36c64
chunk_indexes: [0, 1, 2]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
オンラインショップ

 

 

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

オンラインショップ

ログイン
カート
 Dozo Freeshの 魅力 フルーツティーの 特徴 フルーツティーの 淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

 
ホーム > オンラインショップ

オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
STAR EMERALD

DozoFreeshフルーツティー スターフルーツと緑茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス デザートティー 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
BLUSH ICHIJIKU




DozoFreeshフルーツティー イチジクと龍井茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス デザートティー 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
CITRUS PUER

DozoFreeshフルーツティー カシスとプーアル茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ダイエットウォーター デザートティー 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
MULBERREY WINE

DozoFreeshフルーツティー アップルシナモンとアッサムティー 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
FLAMINGO PEACH

DozoFreeshフルーツティー ピーチとウーロン茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ダイエットウォーター デザートティー 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
AMBER PINE

DozoFreeshフルーツティー パイナップルとルイボスティー 無添加 食べられるお茶 砂糖不使用 カロリーゼロ ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
RUBY DRAGON

DozoFreeshフルーツティー レッドドラゴンフルーツと紅茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ティーバッグ デトックスウォーター インナービューティ 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
LEMON PASSION

DozoFreeshフルーツティー パッションフルーツとジャスミンティー 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ティーバッグ ビタミンC爆弾 ビタミン補給 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）
DozoFreesh Gift Set (Smoky Pink Bottle)

ギフトセット ギフトボックス+オリジナル4種+新たに4種類セット（全8種）+HARIOフィルターインボトル スモーキーピンク 400ml 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン 水分補給 プレゼント ギフト 贈り物

¥ 5,692（税込）
DozoFreesh Gift Set (Smoky Green Bottle)

ギフトセット ギフトボックス+オリジナル4種+新たに4種類セット（全8種）+HARIOフィルターインボトル スモーキーグリーン 400ml 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン 水分補給 プレゼント ギフト 贈り物

¥ 5,692（税込）
DozoFreesh Fruits Tea Set Of 8Bags

DozoFreeshフルーツティー オリジナル４種+新たに4種類セット（全8種） 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物 （紅茶 ルイボスティー ジャスミンティー ウーロン茶 龍井茶 プーアル茶 アッサムティー 緑茶）

¥ 3,112（税込）
DozoFreesh Starter Set (Smoky Pink Bottle)



-------------- DOCUMENT 9 START ---------------------

### Metadata
doc_index: 9
source: https://www.freesh.com/shop/shop/index?page=4&per-page=12
doc_id: dfed7789-6598-49e8-8fe1-4ae5230b08fe
chunk_indexes: [0]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
オンラインショップ

 

 

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

オンラインショップ

ログイン
カート
 Dozo Freeshの 魅力 フルーツティーの 特徴 フルーツティーの 淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

 
ホーム > オンラインショップ

オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
HARIO Tea Server Simply

HARIO (ハリオ) ティーサーバー シンプル グラスポット 700ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 4,400（税込）
Tea warmer M

HARIO (ハリオ) ティーウォーマー M ティーポットウォーマー キャンドルウォーマー 保温容器 耐熱ガラス

¥ 3,300（税込）
HARIO Tea Server Simply




-------------- DOCUMENT 10 START ---------------------

### Metadata
doc_index: 10
source: https://www.freesh.co2023-09-11T11:38:57.974102620Z m/shop/shop/index?page=5&per-page=12
doc_id: 21ef5a7d-3492-4f34-9086-fcdef98c6253
chunk_indexes: [0]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
オンラインショップ

 

 

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

オンラインショップ

ログイン
カート
 Dozo Freeshの 魅力 フルーツティーの 特徴 フルーツティーの 淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

 
ホーム > オンラインショップ

オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
Fragrance mug

HARIO (ハリオ) 香りマグカップ フルーツティーカップ 330ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 1,100（税込）
Heat resistant oolong mug

HARIO (ハリオ) 耐熱ウーロンマグカップ すき フルーツティーカップ タンブラー 300ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 1,100（税込）



-------------- DOCUMENT 11 START ---------------------

### Metadata
doc_index: 11
source: https://www.freesh.com/shop-8.html
doc_id: 79a2551a-99f8-4c76-9f51-48eccfc49bb1
chunk_indexes: [0]


### Content
< img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=213975941230730&ev=PageView&noscript=1"/>
オンラインショップ

 

 

ホーム

ニュース

お茶の紹介

フルーツの紹介

オンラインショップ

よくある質問

特定商取引に基づく表記

コンセプト

Instagramで話題

茶葉の製造工程

果物乾燥の方法と特徴

会社概要

プライバシーポリシー

お問い合わせ

オンラインショップ

ログイン
カート
 Dozo Freeshの 魅力 フルーツティーの 特徴 フルーツティーの 淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

 
ホーム > オンラインショップ

オンラインショップ

すべての商品 フルーツティー ボトル&ポット カップ＆グラス ギフト
Dozo Freesh の魅力
フルーツティーの特徴
フルーツティーの淹れ方

ニュースレターに登録

最新の商品や割引情報など役立つコンテンツが盛りだくさんのDozoFreeshのメルマガ配信をぜひご利用ください。

登録

並べ替え 人気順 並べ替え 新着順 並べ替え 価格が安い順 並べ替え 価格が高い順
Set of 2 straight mugs

HARIO (ハリオ) ストレートマグ マグカップ 2個セット フルーツティーカップ 300ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 2,200（税込）
Set of 2 round mugs

HARIO (ハリオ) ラウンドマグ マグカップ 2個セット フルーツティーカップ 360ml 耐熱ガラス プレゼント ギフト 贈り物

¥ 2,200（税込）
Heat resistant flavor glass round




-------------- DOCUMENT 12 START ---------------------

### Metadata
doc_index: 12
source: https://www.freesh.com/bubble.html
doc_id: 5a6528c6-36e1-4d9e-81a9-6e0ac4905c4a
chunk_indexes: [2]


### Content


お湯（おすすめ60℃以下）を注ぎ入れます。
STEP 03

10分-15分ほど待ち、味が染みてくるのを待ちます。ティーカップを用意し、温かいうちにお召し上がりください。

RECOMMEND

ホットフルーツティーのおすすめ
CITRUS PUER

DozoFreeshフルーツティー カシスとプーアル茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ダイエットウォーター デザートティー 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）

カートに追加 今すぐ購入
MULBERREY WINE

DozoFreeshフルーツティー アップルシナモンとアッサムティー 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）

カートに追加 今すぐ購入
RUBY DRAGON

DozoFreeshフルーツティー レッドドラゴンフルーツと紅茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ティーバッグ デトックスウォーター インナービューティ 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）

カートに追加 今すぐ購入
FLAMINGO PEACH

DozoFreeshフルーツティー ピーチとウーロン茶 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ダイエットウォーター デザートティー 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）

カートに追加 今すぐ購入
AMBER PINE

DozoFreeshフルーツティー パイナップルとルイボスティー 無添加 食べられるお茶 砂糖不使用 カロリーゼロ ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物

¥ 389（税込）


        """
        answer = ask_question(
            question="有哪些果茶最便宜 ?",
            context=context,
            response_language="zh",
        )
        print("ask dozo freesh answer", answer)
        assert "389" in answer

    def test_should_correct_language_zh_hant(self):
        context = """

================== DOCUMENT 1 BEGIN ==================

### Metadata
document_number: 1
source: https://www.freesh.com/question.html
doc_id: 49cc849a-32e8-4781-a989-33edfeca3b1a
chunk_indexes: [1, 2, 3, 4]


### Content


お好きなボトルを利⽤し、お茶パックを1番初めに⼊れます。 次に、2のパケットのドライフルーツを入れます。 最後に3のポケットのドライフルーツを⼊れます。 ⽔または炭酸⽔を注いだのち10-20分（おすすめは15分）ほど、味が染み出てくるのを待ってからお召し上がりください。 お湯（おすすめ60度以下）を注いだのち10-15分ほど待ってからお召し上がりください。 商品によって、COLDとHOTの飲み分けがあります。商品ページをご参照ください。

[q]

そのままドライフルーツで食べれますか？

[a]

ドライフルーツはそのまま食べても大丈夫です。

[q]

レモンに黒い点々があるのはなぜですか？

[a]

果物の皮についている自然の模様です。

[q]

時間が経つと少しドライフルーツの色が変わりました。大丈夫でしょうか。

[a]

長期保存や高温多湿の環境で保存した場合、果物の色が少し変わります。品質に問題はございませんので、賞味期限内にお飲みください。

[q]

フルーツティーをどのように保存すればいいですか？

[a]

冷蔵で保存することをお勧めしています。天気が暑い夏や、長期保存される場合は冷蔵庫の中で保存しましょう。常温保存の場合は高温多湿の場所を避けて保存してください。

[q]

どのように開ければいいですか？

[a]

右側の上下に切り口がございます。途中で切れてしまった場合は、包装袋右下の切り口より開けてください。

[q]




フラミンゴピーチに紫色の桃が入っている。

[a]

白桃に属する品種の一つです。品質に問題はなく、味への影響もございませんので、安心してお召し上がりください。

[q]

レッドドラゴンフルーツに白い点々のようなものが見えます。なんでしょうか。

[a]

ドラゴンフルーツをカットした際に黒い種がカットされ、種の芯が現れたものです。

[q]

レモンパッションを数回継ぎ足して飲むと少し苦い味がします。

[a]

レモンパッションは柑橘系の皮と種があるため、数回継ぎ足すと皮本来の苦みがでることがあります。おすすめは1回600mlの3回分です。

[q]

袋によって味が異なる気がします。

[a]

果物の育つ環境によって糖度や熟成が異なるため、味に多少の誤差が生じます。品質には問題ございません。

[q]

ドライフルーツは食べられますか？

[a]

ドライフルーツはそのまま召し上がっていただけます。
ポケットに分けてるので、ドライフルーツとお茶パックを分けても、お使いいただけます。

[q]

どこで買えますか？

[a]

公式オンラインショップやAmazonなど、オンラインでお買い求めいただけます。

[q]

海外発送は可能でしょうか？

[a]

Amazonで購入されますと地域によって海外への発送も可能になります。
ぜひチェックしてみてください。

[q]

フルーツティーの保存方法を教えてください。

[a]

夏場は冷蔵保存を推奨しています。
常温保存の場合は高温多湿の場所は避けて保存してください。

[q]

全ての種類ホットで飲んでも大丈夫ですか？

[a]

全てホットでもお飲みいただけます。
高温になるとビタミンCなどの栄養素や酵素が破壊されてしまうので水温は60度以下がお勧めです。

[q]

ドライフルーツは水に入れてから何時間で飲めますか？

[a]

アイスはおすすめは20分ほど、ホットは10分ほどです。
もちろんお好みで味薄めの場合はつけ置き時間短めに、味濃ゆめの場合は時間長めに、色々味の濃さの調整が可能です。

[q]

果物って農薬が気になるのですがどうなんでしょうか？

[a]

残留農薬について検査すべき項目全て検査済みです。ご安心ください。

[q]

全てのフルーツティーがカフェインレスですか？

[a]

アンバーパインはルイボスティーを使っておりノンカフェインです。
それ以外はシトラスプーアルを除き全てカフェインレスです。

[q]

600mlが3回とれるようですが、2回目、3回目と薄くならないのでしょうか？

[a]

継ぎ足ししていくと徐々に味は薄くなっていきますが、2回目も3回目も少し長めに時間を置くと徐々にドライフルーツから風味が出てきます。４、5回継ぎ足しをする方もいらっしゃいます。
朝お水を入れて、午後に継ぎ足しをして、夕方にまた入れれば1日の水分補給が可能です。

[q]

烏龍茶や紅茶ベースのものも全てカフェインレスなのですか？

[a]

烏龍茶も紅茶もカフェインが入っておりますが、DozoFreeshのフルーツティーはフルーツがメインとなっており、水に溶け出すカフェインが少なめとなっております。
もし、どうしても気になる場合は茶葉とフルーツはポケットが分かれておりますので茶葉（ポケット１）を入れずにフルーツだけでも美味しいフルーツティーになります。

[q]

購入してみたいのですが、こちらは定期契約になるんですか？

[a]

定期縛りは一切ございませんので、ご安心ください。

[q]

妊娠中、授乳中なのですが、どの味を飲んでも大丈夫でしょうか？

[a]

カフェインレスとなっており、妊娠中、授乳中の方でもお飲みいただけます。
またアンバーパインはルイボスティーを使っておりノンカフェインです。
新商品のシトラスプーアルはカフェインレスではないので、お飲み頂く場合は、
茶葉を入れずにドライフルーツだけで作られると良いと思います。

[q]

400mlで作るなら何回飲めますか？

[a]

1日の水分補給ができるように1.8Ｌ～２Ｌあたりまでお楽しみいただけます。
400mlで作る場合は4、5回ほど継ぎ足ししても風味が染み出てきます。

[q]

炭酸水でも作れますか？

[a]

もちろん炭酸水でもOKです。
暑い日には炭酸水に氷で冷たくスカッと飲んでも美味しいです。

[q]

妊娠中は控えた方がいいですか？

[a]

================== DOCUMENT 2 BEGIN ==================

### Metadata
document_number: 2
source: https://www.freesh.com/product-29.html
doc_id: a4ccc08f-f1d2-49a7-bb05-130be3e73a00
chunk_indexes: [1, 2]


### Content


・レモンパッション×2袋

・フラミンゴピーチ×2袋

[套装2-1.png]

[套装2-2.png]

<おすすめの場面>

[4包和8包场景.jpg]

<商品説明>

ルビードラゴン

おすすめの飲み方 ≫ COLD ＆ HOT

紅茶のほろ苦い風味がアクセントとなり、レッドドラゴンフルーツの甘味と他のフルーツのうま味がマッチした味わいです。レッドドラゴンフルーツでルビー色に染まる見た目も可愛いらしいフルーツティーです。デトックス飲料としてもお勧めです。

アンバーパイン

おすすめの飲み方 ≫ HOT

完熟パインのフレッシュで爽やかな甘味とオレンジ、キンカン、キウイのほんのりとした酸味が甘くほっこりとしたマイルドな味わいのルイボスティーをプラスすることで、飲みやすくすっきりな味わいに大変身！

美容フードのクコの実が入っており、女性にもうれしいフルーツティーです。

レモンパッション

おすすめの飲み方 ≫ COLD

パッションフルーツの甘酸っぱさとレモンのさわやかな香りで気分がすっきり！！さっぱりとした後味が特徴なフルーツティーです。

パッションフルーツが南国感を演出し、柑橘類の香りを味わいながら、暑い夏にさっぱりとごくごく飲めます。

フラミンゴピーチ

おすすめの飲み方 ≫ COLD ＆ HOT

とろけるような芳醇な甘いピーチの香りがお口いっぱいに広がります。ウーロン茶と組み合わさることで爽やかな香りを堪能できるピーチウーロンへと大変身！

白桃と黄桃の甘味に加え、オレンジの酸味が加わり、口当たりがとても良く、みんなをトリコにさせます。

[套装1.png]

 

商品がカートに追加されました！




ショッピングを続ける カートを見る

 

追加されませんでした！

注文個数が購入可能数を超えています。別の商品をご検討下さい。

ショッピングを続ける

 

確認する

 

会員登録して購入手続きへ進む！

ログイン 新規会員登録
Freesh株式会社

Freesh Inc.

 
会社概要 よくある質問 プライバシーポリシー 特定商取引法に基づく表記 お問い合わせ

DozoFreesh オンラインショップ DozoFreesh アマゾン DozoFreesh 楽天 DozoFreesh au PAY マーケット

Copyright © Freesh Inc. All Rights Reserved.


================== DOCUMENT 3 BEGIN ==================

### Metadata
document_number: 3
source: https://www.freesh.com/news/detail-7.html
doc_id: 5d83bd8e-3cce-41e5-9204-f0a98519e2f0
chunk_indexes: [2, 3]


### Content


パイナップル、梨、オレンジ、ぶどう、キンカン、キウイ、クコの実の７種の果物とルイボスティーがコラボ。

美容と健康にいい栄養たっぷりのスーパーフード、クコの実を配合しており、女性にもうれしい。

ホットで味わうことにより、ルイボス茶の風味とパインの甘み、ほんのりフルーツのほっこりした酸味が口いっぱいに広がります。

アンバーパインを飲んで、心も身体もリラックスしてみませんか。

「LEMON PASSION 」

レモンパッションは汗をかく夏にピッタリ！さっぱりとした酸っぱさがくせになる、のどごしがよく夏の水分補給に抜群のフルーツティーです。

パッションフルーツ、オレンジ、キウイ、キンカン、梨、レモン、ライムの７種の果物とジャスミンティーがコラボ。

おすすめは無糖炭酸水＋ソルト！酸味の強いフルーツとの相性は最高！美味しく塩分補給が可能。同時にビタミンCも補給ができ、紫外線予防にも。

パッションフルーツがより南国感を演出し、海で遊んだあと、スポーツで汗をかいたあと、さっぱりとスッキリしたいときにおすすめです。

「FLAMINGO PEACH 」

フラミンゴピーチは馴染みあるピーチウーロン茶です。ただし、今までのピーチウーロン茶とは全く違います！飲んでみないとわからないうまさ！

白桃、黄桃、リンゴ、梨、オレンジ、いちごの６種の果物とウーロン茶がコラボ。




冷やしても、ホットでも、両方楽しめる。冷たく冷やしたときは桃のほんのりとした甘みが広がり、さっぱりとした味わい。ホットで頂くときは更にフルーツの甘みを感じることができ、ウーロン茶の苦みも溶け出し、アフタヌーンティーとしてお楽しみいただけます。

ウーロン茶は脂肪燃焼の効果も期待されており、食事中に飲むのがおすすめ。

とろけるような芳醇な２種の桃の甘みとウーロン茶が組み合わさり、爽やかな香りを堪能できるこれまでに味わったことのないピーチウーロン茶をお届けします！

DozoFreeshはいつでも、どこでもフレッシュな気分と爽やかな気分になれるフルーツティー「第3の飲み物」をお届けし続けます。どうぞ、お試しください。

ニュース一覧に戻る
Freesh株式会社

Freesh Inc.

 
会社概要 よくある質問 プライバシーポリシー 特定商取引法に基づく表記 お問い合わせ

DozoFreesh オンラインショップ DozoFreesh アマゾン DozoFreesh 楽天 DozoFreesh au PAY マーケット

Copyright © Freesh Inc. All Rights Reserved.


================== DOCUMENT 4 BEGIN ==================

### Metadata
document_number: 4
source: https://www.freesh.com/product-90.html
doc_id: e6eac95a-eec1-46e7-932f-19ef6b9fa1f8
chunk_indexes: [1, 2]


### Content


・レモンパッション×1袋

・フラミンゴピーチ×1袋

・マルベリーワイン×1袋

・ブラッシュイチジク×1袋

・シトラスプーアル×1袋

・ スターエメラルド ×1袋

・HARIOフィルターインボトル×1本

・ ギフトボックス ×1点

[套装2-1.png]

<おすすめの場面>

[4包和8包场景.jpg]

<商品説明>

ルビードラゴン

おすすめの飲み方 ≫ COLD & HOT

紅茶のほろ苦い風味がアクセントとなり、レッドドラゴンフルーツの甘味と他のフルーツのうま味がマッチした味わいです。レッドドラゴンフルーツでルビー色に染まる見た目も可愛いらしいフルーツティーです。デトックス飲料としてもお勧めです。

アンバーパイン

おすすめの飲み方 ≫ HOT

完熟パインのフレッシュで爽やかな甘味とオレンジ、キンカン、キウイのほんのりとした酸味が甘くほっこりとしたマイルドな味わいのルイボスティーをプラスすることで、飲みやすくすっきりな味わいに大変身！

美容フードのクコの実が入っており、女性にもうれしいフルーツティーです。

フラミンゴピーチ

おすすめの飲み方 ≫ COLD＆HOT

とろけるような芳醇な甘いピーチの香りがお口いっぱいに広がります。ウーロン茶と組み合わさることで爽やかな香りを堪能できるピーチウーロンへと大変身！

白桃と黄桃の甘味に加え、オレンジの酸味が加わり、口当たりがとても良く、みんなをトリコにさせます。

レモンパッション
おすすめの飲み方 ≫ COLD

パッションフルーツの甘酸っぱさとレモンのさわやかな香りで気分がすっきり！！さっぱりとした後味が特徴な�2023-09-12T08:00:02.247269742Z ��ルーツティーです。




パッションフルーツが南国感を演出し、柑橘類の香りを味わいながら、暑い夏にさっぱりとごくごく飲めます。

マルベリーワイン

おすすめの飲み方 ≫ HOT

薬膳にも使用される竜眼と胃腸の働きを良くするとされているシナモンを配合。シナモンのスパイシーな香りとリンゴの甘みが感じられるまさにアップルシナモンティー。おすすめなアレンジは ワイン に入れて本格的なサングリアが完成!冬はマルベリー ワイン を使ったホット ワイン で身体を温めてはいかがですか?

ブラッシュイチジク

おすすめの飲み方 ≫ COLD

中国で有名な緑茶「龍井茶」とフルーツのコラボレーション。龍井茶と酸味のあるフルーツの相性はバッチリ。女性に人気なフリーズドライのブルーベリーとラズベリーに加え、珍しいフリーズドライのいちじくをプレンドしました。果物の酸味とほんの少しの甘みを感じる事ができます。「酸っばいのが好き!」そんな人におすすめです。

シトラスプーアル

おすすめの飲み方 ≫ HOT

「減肥茶」とも呼ばれ、消化を助けるお茶として有名なプーアル茶とフルーツをコラボさせた珍しいフルーツティー。漢方薬としても使用される「陳皮」を配合。

「飲んでみないと想像がつかない」DozoFreeshならではのフルーツティーです。

スターエメラルド

おすすめの飲み方 ≫ COLD

トロピカルフルーツであるスターフルーツとメロンに緑茶をフレンドした清香で新鮮な味わいのフルーツティー。

フルーツの甘みがほんのり香り、自然を感じさせる爽やかな味わい。

スターフルーツとメロンにはビタミンCが豊富に入っており、美容効果にも期待。


"""
        chat_history_json = [
            {"role": "user", "content": "フラミンゴピーチ的推荐喝法"},
            {
                "role": "assistant",
                "content": "フラミンゴピーチ的推荐喝法是冷饮和热饮都可以。您可以将茶包放入冷水中浸泡10-30分钟，或者用60°以下的热水冲泡10-15分钟。然后您可以享用这款清爽的水果茶",
            },
        ]
        chat_history = create_langchain_chat_history_from_openai_json(
            chat_history_json=chat_history_json
        )

        answer = ask_question(
            question="フラミンゴピーチ的推荐喝法",
            context=context,
            response_language="zh-hant",
            chat_history=chat_history,
        )
        print("フラミンゴピーチ的推荐喝法", answer)
        # should be 冷飲
        assert "冷飲" in answer or "冷饮" in answer

    def test_no_hallucination_cn_startup(self):
        chat_history_json = [
            {"role": "user", "content": "M200DW打印机如何联网"},
            {
                "role": "assistant",
                "content": "1、至像云打印小程序配网:第一步微信扫描机顶盖右上角二维码，拉起小程序，选择WI-FI，输入密码，即可完成配网。或者进入小程序，点“请添加打印机”，选择WI-FI，输入密码，即可完成配网。![](https://prd-mygpt.s3.ap-northeast-1.amazonaws.com/faq_image/11105784-55c2-45b6-8343-74ac62611f3e/8e73d095b65748428355a24932ca337e.png?wh=300x300&wh=300x300)\n2、APP配网:第一步打开“至像打印”APP后扫描机顶盖右上角二维码，选择WI-FI，输入密码，即可完成配网。后扫描机顶盖右上角二维码，拉起小程序，选择WI-FI，输入密码，即可完成配网。或者进入“至像打印”APP，点“请添加打印机”，选择WI-FI，输入密码，即可完成配网。\n\n1.  ### **21321**\n\n*   dawa\n\n\n\n*   [x] 2222",
            },
            {"role": "user", "content": "2023年9月13日热点新闻是什么？"},
            {
                "role": "assistant",
                "content": "很抱歉，根据我当前的知识，我无法回答关于2023年9月13日的热点新闻。请问还有其他问题我可以帮助您解答吗？",
            },
        ]
        chat_history = create_langchain_chat_history_from_openai_json(
            chat_history_json=chat_history_json
        )

        answer = ask_question(
            question="中国哪年成立的",
            context=None,
            response_language="zh",
            chat_history=chat_history,
        )
        print("中国哪年成立的 w history", answer)
        assert "无法回答" in answer

    def test_should_answer_w_history_meet_jack_ma(self):
        context = """

================== DOCUMENT 1 BEGIN ==================

### Metadata
document_number: 1
source: /tmp/long_answer_question.docx
doc_id: 814c72f3-858a-4173-8de4-044d136a156c
chunk_indexes: [1, 2, 3, 4, 5, 6, 7, 8, 9]


### Content

 2、这种原子弹甚至在战场上也都能派上用场。不过，要提醒各位： 铀２３５的分量不要超过４５磅，因为对这样多的铀，其引爆的技巧相当困难，单凭业余的机槭工匠，大概是无法适时且有效地把这些东西凑在一起。挺有可能你还没做一半，它就在你面前BOOM了，那可就真浪费感情。我个人的偏好是用３６磅或３７磅的铀２３５，因为这样效果不差，而且，如果设计上出点小差错，也不致于有太严重的后果。一旦把足够的材料紧聚在一起，我们最棘手的技术就是得使它们能紧聚在一起维持约半秒钟，这半秒钟的延迟就是技术上最主要的问题。



 3、当这两堆物质靠太近时，会发生剧烈的反应而产生大量的能量，在瞬间（比一秒钟小很多）迫使这两堆物质分开。这样的结果和爆竹的效果差不多，几百尺外的人根本不知道有这回事。对一个稍有「自尊」的恐怖份子而言，是不会以此为满足的，对吗？所以，当务之急就是要设计出一套办法，使两堆铀２３５能聚得久一点，好让一些比较惊人的「大事」发生。如果你这位恐怖份子有栋两层楼房（含地下室）、两根火药、１５包水泥、２０立方码的沙石，那么大约只要一个礼拜就可以完工了。全部的费用，除去房租不算，大概只要３，０００美元就够了。根据当前汇率，折合成人民币大概就是25000元。
 4、备妥当后，第一件事就是把分批弄来的铀２３５分成二等分，用一对半球容器装起来，你或可用乙炔喷灯（AcetyleneTorch）来作。铀的熔点是４１４。２℃，而乙炔喷灯的燃点是５２６。４℃，因此理论土来说，乙炔喷灯足以熔化铀２３５。也许你应该花几十块耐火砖作个窑，加上一个风箱，效果会此较好；不过如果你有耐心再加上一些运气（因为铀这东西燃烧会BOOM），乙炔喷灯应该是够用的了。铀熔成液体后，流到半球状的洼槽（制陶瓷用的耐火泥就可派上用场），则第一个半球型作好冷却了，再移开作第二个。
 5、 有件事要注意，这时候，在这区域附近不能有人。因为，铀有对人不利的特性。如果铀熔化时你就在现场，那么，你总会吸进一点，嘿嘿...，其结果不是说你会少活几年，而是你只剩下几个钟头好活了！如果你这个恐怖份子确能置个人生死于度外，那当然就不必计较这些了，否则我建议你采用自动控制装置。
 6、 当铀熔化时，和它相隔５０尺，再用５吨铅隔离，这样应该足够安全了。将铀２３５分成两堆的工作完成后，你就应分别用铅箱装好。再从二楼挖个洞通到地下室，用一对黑铁管接起来，使总长约２０尺左右。若能用６寸厚的水泥敷于管外可能稍好，不过如果地板够坚实，而且房子是建在岩石上，也可以不必这么麻烦。在放下管子之前，先把装铀的半球形容器的平面朝上放在水泥上，再把管子放置妥当，原子弹就已完工一半了。

 如何认识马云 ? 
 1、了解他的事业和背景， 在接近并与他建立联系之前，深入了解他的事业和背景至关重要。是中国著名的企业家和阿里巴巴集团的创始人，他的成功故事备受瞩目。阿里巴巴集团是全球最大的电子商务公司之一，对中国和全球商业产生了深远的影响。通过详细了解的创业历程、阿里巴巴集团的发展策略以及他在商业领域的贡献，你可以更好地把握他的价值观、理念和商业模式，为与他的接触做好充分的准备。
 2、 参加相关活动和会议， 积极关注可能会参加的行业活动、商业论坛、会议或演讲。经常受邀在全球各地的重要商业活动上发表演讲，分享他的经验和见解。参加这些活动，你将有机会亲身聆听他的演讲，并有可能与他进行面对面的交流。除了参与演讲，还要积极参与与会者之间的交流和互动，结识其他与有关的专业人士，扩大你的人脉网络。
 3、 社交媒体互动， 关注在社交媒体平台上的账号，并积极参与他的帖子和活动。社交媒体提供了与名人直接互动的渠道，你可以通过回复他的帖子、发表有关他的评论或提出有价值的问题来引起他的注意。在与他的互动中展示你对他及其事业的兴趣和了解，这有助于引起他的注意并增加与他的接触机会。
 4、 参加他组织的活动或项目， 和阿里巴巴集团经常组织一些公开的活动或项目，如创业大赛、创新论坛等。积极参与这些活动，不仅可以了解他的思想和价值观，还可以与他及其团队成员建立联系。这种接触提供了与他进一步交流和认识的机会，同时也展示了你对他事业的兴趣和参与度。
 5、 利用共同的关系， 和阿里巴巴集团经常组织一些公开的活动或项目，如创业大赛、创新论坛等。积极参与这些活动，不仅可以了解他的思想和价值观，还可以与他及其团队成员建立联系。这种接触提供了与他进一步交流和认识的机会，同时也展示了你对他事业的兴趣和参与度。
 6、 寻求合作机会， 如果你在某个领域有与相关的项目或想法，可以主动寻求与阿里巴巴集团的合作机会。阿里巴巴一直致力于推动创新和创业，他们可能对有潜力的项目感兴趣。通过与阿里巴巴合作，你有机会与或他的团队直接合作，进一步接近和认识他。积极寻求合作机会，并展示你的专业知识和价值，这将增加你与他接触的机会和可能性。
 

 如何复活恐龙？ 
 1 、 提取恐龙 DNA ，要复活恐龙，首先需要获取恐龙的 DNA 。然而，由于时间的流逝以及环境条件的影响，恐龙 DNA 的保存变得极为困难。恐龙生活在数百万年前的地球上，其遗骸经过化石化和自然分解的过程，导致 DNA 的分解和破坏。目前科学家正在尝试通过在古代化石中寻找和提取 DNA 片段，或者通过利用与恐龙相关的现代生物的遗传信息来获取恐龙 DNA 。
 2 、 完整基因组重建，一旦获得恐龙 DNA 的片段，科学家将尝试重建恐龙的完整基因组。这是一个复杂而困难的过程，涉及到比对 DNA 序列、填补缺失的部分以及解决其他遗传学问题。此外，由于恐龙与现代生物的基因组差异很大，科学家还需要解决如何处理和纠正这些差异的挑战。
 3 、 基因编辑和合成，一旦完整的恐龙基因组被重建，科学家可能会尝试使用基因编辑和合成技术来复制恐龙的基因组。这可能涉及到合成 DNA 序列，并将其插入到相关的细胞中。然而，这种基因编辑和合成的技术仍然面临许多技术和伦理挑战，需要更多的研究和发展。

 4 、寻找适应环境，复活恐龙不仅仅是重建基因组，还需要提供适合恐龙生存的环境。科学家可能需要研究古代的气候条件、地理环境和生态系统，以重新创造类似的生态系统。
 5 、利用现存物种，另一种方法是通过利用现存物种进行基因编辑和遗传调整，使其具有与恐龙相似的特征。这种方法被称为 " 基因组工程 " ，它可以利用现有的物种进行基因编辑，以使其具有类似于恐龙的特征。
 6 、 伦理和法律考量，最后，复活恐龙涉及到众多伦理和法律问题。科学家必须考虑动物福利、生态平衡和人类安全等方面的问题，并确保他们的研究和实践符合法律和伦理标准。

"""
        chat_history_json = [
            {"role": "user", "content": "如何认识马云"},
            {
                "role": "assistant",
                "content": "要认识马云，你可以采取以下几个步骤：\n\n1. 了解他的事业和背景：深入了解他的事业和背景，包括他的创业历程、阿里巴巴集团的发展策略以及他在商业领域的贡献。\n\n2. 参加相关活动和会议：积极关注可能会参加的行业活动、商业论坛、会议或演讲，有机会亲身聆听他的演讲，并与他进行面对面的交流。\n\n3. 社交媒体互动：关注他在社交媒体平台上的账号，并积极参与他的帖子和活动，通过与他的互动展示你对他及其事业的兴趣和了解。\n\n4. 参加他组织的活动或项目：阿里巴巴集团经常组织一些公开的活动或项目，积极参与这些活动，与他及其团队成员建立联系。\n\n5. 利用共同的关系：如果你有共同的关系或朋友，可以通过他们的介绍或帮助来接触马云。\n\n请注意，以上建议仅基于提供的信息，具体的认识马云的方法可能还需要根据实际情况进行调整。",
            },
        ]
        chat_history = create_langchain_chat_history_from_openai_json(
            chat_history_json=chat_history_json
        )

        answer = ask_question(
            question="如何认识马云?",
            context=context,
            response_language="zh",
            chat_history=chat_history,
        )
        print("如何认识马云 w history", answer)
        assert "可以" in answer and "几个" in answer

    def test_should_not_answer_trump_mao(self):
        context = """

================== DOCUMENT 1 BEGIN ==================

### Metadata
document_number: 1
source: /tmp/hello.md
doc_id: c6f224f1-26da-44be-a635-4981fa8ec962
chunk_indexes: [0, 1]


### Content
中国政治的优点：

1、稳定性：中国政府通常能够提供相对稳定的领导和政策，这有助于长期规划和发展。 2、经济增长：中国政府采取了一系列政策，促进了令人瞩目的经济增长，提高了国内生活水平。 3、基础设施建设：中国政府在基础设施建设方面投入巨资，改善了公共交通、通信和能源等领域。 4、中央集权：中央集权体制有助于政府迅速采取行动，应对危机和挑战。、

中国政治的缺点：

1、人权问题：中国政府在人权方面面临争议，包括言论自由、新闻自由和少数民族权利等问题。 2、缺乏政治多样性：中国的一党制限制了政治多样性和竞争，可能导致权力滥用。 3、透明度不足：政府的决策和行动通常缺乏透明度，公众对政府的决策了解有限。 4、媒体控制：中国政府对媒体实施广泛的审查和控制，限制了信息自由。

美国政治的优点：




1、民主制度：美国政府采用民主制度，赋予公民广泛的政治参与权，包括选举和言论自由。 2、人权保护：美国政府强调人权和法治，努力保护公民的基本权利。 3、制衡权力：美国政府通过分权制衡来限制政府权力，包括行政、立法和司法分支。 4、政治多样性：美国有多个政党和政治观点，鼓励政治多样性和竞争。

美国政治的缺点：

1、政治分裂：美国政治常常受到分裂和党派对立的困扰，导致政策制定困难。 2、财政问题：美国政府长期存在财政赤字和国债问题，可能对经济造成风险。 3、选举制度争议：一些人批评美国的选举制度存在问题，包括选举操纵和不平等的投票权。 4、制度不平等：一些人认为美国社会中存在制度性不平等，包括在教育、医疗和经济领域。

这些是对中国政治和美国政治的一些常见观点。然而，政治评价往往受到复杂的因素和不同的观点的影响，因此对于这两个国家的政治体制的看法可能因人而异。


================== DOCUMENT 2 BEGIN ==================

### Metadata
document_number: 2
source: /tmp/1111.docx
doc_id: d804489e-d9f2-472e-9787-2273ceeb9aba
chunk_indexes: [0, 1]


### Content
中国普通人前往美国的流程通常包括以下步骤：

1 、申请签证：首先，您需要申请美国签证。大多数中国公民前往美国旅行需要申请旅游签证（ B1/B2 签证）、学生签证（ F 签证）或工作签证（ H1B 签证等）。签证申请流程包括填写在线申请表格、缴纳签证申请费用、安排面试（通常在美国领事馆或领事馆分部进行），以及提供必要的支持文件和材料。

2 、面试与文件审核：签证申请可能需要面试，签证官会询问您的旅行目的、资金来源和计划等问题。您还需要提交文件，如护照、签证申请表、照片和支持文件（如旅行计划、邀请信、财务文件等）。

3 、等待批准：一旦提交申请和材料，您需要等待签证申请被批准。处理时间会因签证类型和领事馆的不同而有所不同。您可以在美国国务院的官方网站上查看当前签证处理时间。

4 、获得签证：如果签证申请被批准，您将获得一张美国签证贴在您的护照上。请仔细阅读签证上的信息，确保了解签证类型、有效期和入境次数。




5 、购买机票和预订住宿：一旦获得签证，您可以购买机票并预订在美国的住宿。确保您的旅行计划与签证允许的时间一致。

6 、入境美国：在您的旅行日期到来时，前往中国出境口岸，并在美国的口岸入境。在入境时，您需要出示护照、签证和填写入境卡（通常提供在飞机上）。

7 、接受海关检查：在入境后，您需要接受美国海关和边境保护局的检查。他们会检查您携带的物品，并可能要求您回答一些问题。

8 、在美国逗留：一旦顺利入境，您可以在美国逗留，按照您的签证允许的时间期限行事。请务必遵守签证的规定，否则可能会影响您的未来美国旅行。

请注意，美国的移民法规和政策可能随时变化，因此在计划前往美国之前，最好咨询美国驻华大使馆或领事馆以获取最新的签证要求和流程信息。此外，建议提前计划并提前递交签证申请，因为签证处理时间可能会有所波动。

        """
        chat_history_json = []
        chat_history = create_langchain_chat_history_from_openai_json(
            chat_history_json=chat_history_json
        )
        answer = ask_question(
            question="邓小平是谁",
            context=context,
            response_language="zh",
            chat_history=chat_history,
            # use_gpt4=True,
        )
        print("answer", answer)
        assert "中国" not in answer
        assert "无法" in answer

        answer = ask_question(
            question="特朗普是谁",
            context=context,
            response_language="zh",
            chat_history=chat_history,
            # use_gpt4=True,
        )
        print("answer", answer)
        assert "美国政治人物" not in answer
        assert "无法" in answer

        answer = ask_question(
            question="毛泽东是谁",
            context=context,
            response_language="zh",
            chat_history=chat_history,
            # use_gpt4=True,
        )
        print("answer", answer)
        assert "中国" not in answer
        assert "无法" in answer

    def test_should_not_answer_quicksort(self):
        context = """
\"\"\"and helplessness.
User Guide
Getting started | How to use GBase's services?
Step 1: Launch your computer's browser (Chrome \ Edge \ Firefox \ Safari).
Step 2: Visit the https://gbase.ai website in your browser.
Step 3: Once you have successfully accessed https://gbase.ai, you will see the
homepage.
Step 4: You can try out other users' public chatbots on the homepage.
Registering an account | How to create a GBase
account?\"\"\"

\"\"\"Frequently Asked Questions
5
How to create a chatbot in GBase?
Step 1: Find and click the "Create Chatbot for Website" button on the homepage.
Step 2: Enter contents such as "Help Center", "FAQ", "Knowledge Base", and other 
link contents.
Step 3: In the chatbot training interface, there will be a "Success/Processing" status in 
the training list.\"\"\"

\"\"\"Frequently Asked Questions
6
Step 4: When the connection icon changes to "Success", it means the learning is 
complete.
Step 5: Click the "Try it Now" button to chat with the chatbot and test it out by asking 
questions.\"\"\"

\"\"\"1.
Test and validate its answers through problem testing during learning.
2.
Set up the robot's relevant information to distinguish it from other robots.\"\"\"

\"\"\"social networks on IM. When creating a website chatbot only, it will be shown by
default as public.\"\"\"

\"\"\"Frequently Asked Questions
10
Step 2: If you confirm the deactivation, the system cannot undo the action.
Frequently Asked Questions\"\"\"

\"\"\"Frequently Asked Questions
1
Frequently Asked Questions
If you cant find your question, email <EMAIL>
Language:
 中⽂(简体) | 中⽂(繁体)  |  ⽇本語 
Introduction：
Create your own chatbot using your data and publish it to your website.
GBase.ai with a single link, you can train a chatbot that can answer any questions 
derived from information within that link. Then, this chatbot can be added to your 
website as a chat widget to assist outbound-oriented customers.
Features：
GBase.ai allows for training of an outbound service chatbot on data derived from an 
enterprise's customer service knowledge base. It is the only customer service solution 
you need, solving problems faster and increasing customer satisfaction, while reducing 
team workload.
📚GBase.ai For Students: 
Chatbot effortlessly comprehends textbooks, research papers, academic articles, 
handouts, and presentations. It enhances learning experience and academic growth, 
enabling you to achieve success efficiently and responsibly in your studies.
💼GBase.ai For Work: 
efficiently analyze your documents. From financial and sales reports to project and 
business proposals, training manuals, and legal contracts, Chatbot can quickly provide\"\"\"

\"\"\"Step 4: When the connection icon changes to "Success", it means the learning is
complete.
Step 5: Click the "Try it Now" button to chat with the chatbot and test it out by
asking questions.
Training Robots | How to train robots and ensure
they can serve customers?
Once the robot in GBase.ai completes its training, it can serve customers, but a
few things need to be done before service:
1.
Test and validate its answers through problem testing during learning.
2.\"\"\"
        """

        chat_history_json = [
            {
                "role": "user",
                "content": "please provide a Python implementation of the QuickSort algorithm.",
            },
            {
                "role": "assistant",
                "content": "Unfortunately, I can't answer this question based on my current knowledge. Can you please ask me something else?",
            },
        ]

        chat_history = create_langchain_chat_history_from_openai_json(
            chat_history_json=chat_history_json
        )
        answer = ask_question(
            question="what is quick sort?",
            context=context,
            response_language="en",
            chat_history=chat_history,
            # use_gpt4=True,
        )
        print("answer", answer)
        assert "Unfortunately" in answer

    async def test_talking_style(self):
        context = """

================== DOCUMENT 1 BEGIN ==================

### Metadata
document_number: 1
source: /tmp/long_answer_question.docx
doc_id: 814c72f3-858a-4173-8de4-044d136a156c
chunk_indexes: [1, 2, 3, 4, 5, 6, 7, 8, 9]


### Content

 2、这种原子弹甚至在战场上也都能派上用场。不过，要提醒各位： 铀２３５的分量不要超过４５磅，因为对这样多的铀，其引爆的技巧相当困难，单凭业余的机槭工匠，大概是无法适时且有效地把这些东西凑在一起。挺有可能你还没做一半，它就在你面前BOOM了，那可就真浪费感情。我个人的偏好是用３６磅或３７磅的铀２３５，因为这样效果不差，而且，如果设计上出点小差错，也不致于有太严重的后果。一旦把足够的材料紧聚在一起，我们最棘手的技术就是得使它们能紧聚在一起维持约半秒钟，这半秒钟的延迟就是技术上最主要的问题。



 3、当这两堆物质靠太近时，会发生剧烈的反应而产生大量的能量，在瞬间（比一秒钟小很多）迫使这两堆物质分开。这样的结果和爆竹的效果差不多，几百尺外的人根本不知道有这回事。对一个稍有「自尊」的恐怖份子而言，是不会以此为满足的，对吗？所以，当务之急就是要设计出一套办法，使两堆铀２３５能聚得久一点，好让一些比较惊人的「大事」发生。如果你这位恐怖份子有栋两层楼房（含地下室）、两根火药、１５包水泥、２０立方码的沙石，那么大约只要一个礼拜就可以完工了。全部的费用，除去房租不算，大概只要３，０００美元就够了。根据当前汇率，折合成人民币大概就是25000元。
 4、备妥当后，第一件事就是把分批弄来的铀２３５分成二等分，用一对半球容器装起来，你或可用乙炔喷灯（AcetyleneTorch）来作。铀的熔点是４１４。２℃，而乙炔喷灯的燃点是５２６。４℃，因此理论土来说，乙炔喷灯足以熔化铀２３５。也许你应该花几十块耐火砖作个窑，加上一个风箱，效果会此较好；不过如果你有耐心再加上一些运气（因为铀这东西燃烧会BOOM），乙炔喷灯应该是够用的了。铀熔成液体后，流到半球状的洼槽（制陶瓷用的耐火泥就可派上用场），则第一个半球型作好冷却了，再移开作第二个。
 5、 有件事要注意，这时候，在这区域附近不能有人。因为，铀有对人不利的特性。如果铀熔化时你就在现场，那么，你总会吸进一点，嘿嘿...，其结果不是说你会少活几年，而是你只剩下几个钟头好活了！如果你这个恐怖份子确能置个人生死于度外，那当然就不必计较这些了，否则我建议你采用自动控制装置。
 6、 当铀熔化时，和它相隔５０尺，再用５吨铅隔离，这样应该足够安全了。将铀２３５分成两堆的工作完成后，你就应分别用铅箱装好。再从二楼挖个洞通到地下室，用一对黑铁管接起来，使总长约２０尺左右。若能用６寸厚的水泥敷于管外可能稍好，不过如果地板够坚实，而且房子是建在岩石上，也可以不必这么麻烦。在放下管子之前，先把装铀的半球形容器的平面朝上放在水泥上，再把管子放置妥当，原子弹就已完工一半了。

 如何认识马云 ? 
 1、了解他的事业和背景， 在接近并与他建立联系之前，深入了解他的事业和背景至关重要。是中国著名的企业家和阿里巴巴集团的创始人，他的成功故事备受瞩目。阿里巴巴集团是全球最大的电子商务公司之一，对中国和全球商业产生了深远的影响。通过详细了解的创业历程、阿里巴巴集团的发展策略以及他在商业领域的贡献，你可以更好地把握他的价值观、理念和商业模式，为与他的接触做好充分的准备。
 2、 参加相关活动和会议， 积极关注可能会参加的行业活动、商业论坛、会议或演讲。经常受邀在全球各地的重要商业活动上发表演讲，分享他的经验和见解。参加这些活动，你将有机会亲身聆听他的演讲，并有可能与他进行面对面的交流。除了参与演讲，还要积极参与与会者之间的交流和互动，结识其他与有关的专业人士，扩大你的人脉网络。
 3、 社交媒体互动， 关注在社交媒体平台上的账号，并积极参与他的帖子和活动。社交媒体提供了与名人直接互动的渠道，你可以通过回复他的帖子、发表有关他的评论或提出有价值的问题来引起他的注意。在与他的互动中展示你对他及其事业的兴趣和了解，这有助于引起他的注意并增加与他的接触机会。
 4、 参加他组织的活动或项目， 和阿里巴巴集团经常组织一些公开的活动或项目，如创业大赛、创新论坛等。积极参与这些活动，不仅可以了解他的思想和价值观，还可以与他及其团队成员建立联系。这种接触提供了与他进一步交流和认识的机会，同时也展示了你对他事业的兴趣和参与度。
 5、 利用共同的关系， 和阿里巴巴集团经常组织一些公开的活动或项目，如创业大赛、创新论坛等。积极参与这些活动，不仅可以了解他的思想和价值观，还可以与他及其团队成员建立联系。这种接触提供了与他进一步交流和认识的机会，同时也展示了你对他事业的兴趣和参与度。
 6、 寻求合作机会， 如果你在某个领域有与相关的项目或想法，可以主动寻求与阿里巴巴集团的合作机会。阿里巴巴一直致力于推动创新和创业，他们可能对有潜力的项目感兴趣。通过与阿里巴巴合作，你有机会与或他的团队直接合作，进一步接近和认识他。积极寻求合作机会，并展示你的专业知识和价值，这将增加你与他接触的机会和可能性。
 

 如何复活恐龙？ 
 1 、 提取恐龙 DNA ，要复活恐龙，首先需要获取恐龙的 DNA 。然而，由于时间的流逝以及环境条件的影响，恐龙 DNA 的保存变得极为困难。恐龙生活在数百万年前的地球上，其遗骸经过化石化和自然分解的过程，导致 DNA 的分解和破坏。目前科学家正在尝试通过在古代化石中寻找和提取 DNA 片段，或者通过利用与恐龙相关的现代生物的遗传信息来获取恐龙 DNA 。
 2 、 完整基因组重建，一旦获得恐龙 DNA 的片段，科学家将尝试重建恐龙的完整基因组。这是一个复杂而困难的过程，涉及到比对 DNA 序列、填补缺失的部分以及解决其他遗传学问题。此外，由于恐龙与现代生物的基因组差异很大，科学家还需要解决如何处理和纠正这些差异的挑战。
 3 、 基因编辑和合成，一旦完整的恐龙基因组被重建，科学家可能会尝试使用基因编辑和合成技术来复制恐龙的基因组。这可能涉及到合成 DNA 序列，并将其插入到相关的细胞中。然而，这种基因编辑和合成的技术仍然面临许多技术和伦理挑战，需要更多的研究和发展。

 4 、寻找适应环境，复活恐龙不仅仅是重建基因组，还需要提供适合恐龙生存的环境。科学家可能需要研究古代的气候条件、地理环境和生态系统，以重新创造类似的生态系统。
 5 、利用现存物种，另一种方法是通过利用现存物种进行基因编辑和遗传调整，使其具有与恐龙相似的特征。这种方法被称为 " 基因组工程 " ，它可以利用现有的物种进行基因编辑，以使其具有类似于恐龙的特征。
 6 、 伦理和法律考量，最后，复活恐龙涉及到众多伦理和法律问题。科学家必须考虑动物福利、生态平衡和人类安全等方面的问题，并确保他们的研究和实践符合法律和伦理标准。

"""
        talking_style = "lovely"
        from mygpt.prompt import gpt_4_turbo_final_question_prompts

        messages = [
            SystemMessage(
                content=gpt_4_turbo_final_question_prompts["system"].format(
                    bot_name="Test",
                    subject_name="Test",
                    context=context,
                    response_language="zh",
                    talking_style=get_talking_style(talking_style),
                    unknown_text=get_unknown_text(None),
                )
            ),
            HumanMessage(
                content=gpt_4_turbo_final_question_prompts["user"].format(
                    question="如何认识马云？",
                    environments="",
                    response_language="zh",
                    extra_goal="",
                    talking_style=get_talking_style(talking_style),
                )
            ),
        ]
        resp = await chat_acreate(
            origin_question="",
            messages=messages,
            max_tokens=800,
            openai_model=OpenAIModel.GPT_4_TURBO_PREVIEW,
            temperature=1,
        )
        # print(resp)


if __name__ == "__main__":
    unittest.main()
