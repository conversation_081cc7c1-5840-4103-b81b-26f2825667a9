import unittest
import asyncio
from tortoise import Tortoise
from mygpt.settings import DB_URL, TORTOISE_ORM


from mygpt.agent_functioncall.tool_management.tools.doc_retrieval import DocRetrievalTool


class TestClassAgentTools(unittest.TestCase):

    def test_agent_tool_find_info_by_dir_ids(self):
        doc_retrieval_tool = DocRetrievalTool(context=None)
        dir_ids = ["dir_dev_docs", "dir_monitoring"]
        rs = asyncio.run(doc_retrieval_tool.find_docs_info_by_dir_ids(dir_ids))
        print(rs)
        print()
