import pytest

from mygpt.endpoints.faqs import faq_final_question_model_prompt
from mygpt.openai_utils import chat_acreate


@pytest.mark.asyncio
async def test_faq_final_question_model_prompt_chinese():
    answer = "This is a test answer."
    response_language = "Chinese"

    messages, openai_model = await faq_final_question_model_prompt(
        answer, response_language
    )

    response = await chat_acreate(openai_model=openai_model, messages=messages)

    assert isinstance(response.content, str)
    assert "测试" in response.content  # 检查是否包含中文字符


@pytest.mark.asyncio
async def test_faq_final_question_model_prompt_japanese():
    answer = "This is a test answer."
    response_language = "Japanese"

    messages, openai_model = await faq_final_question_model_prompt(
        answer, response_language
    )

    response = await chat_acreate(openai_model=openai_model, messages=messages)

    assert isinstance(response.content, str)
    assert "回答" in response.content


@pytest.mark.asyncio
async def test_faq_final_question_model_prompt_english():
    answer = "这是一个测试回答。"
    response_language = "English"

    messages, openai_model = await faq_final_question_model_prompt(
        answer, response_language
    )

    response = await chat_acreate(openai_model=openai_model, messages=messages)

    assert isinstance(response.content, str)
    assert "test" in response.content  # 检查是否包含英文字符


@pytest.mark.asyncio
async def test_faq_final_question_model_prompt_markdown():
    answer = """
# This is a test answer

- Point 1
- Point 2

```python
print("Hello, world!")
```

[Link](https://example.com)
    """
    response_language = "Chinese"

    messages, openai_model = await faq_final_question_model_prompt(
        answer, response_language
    )

    response = await chat_acreate(openai_model=openai_model, messages=messages)

    assert isinstance(response.content, str)
    assert "测试" in response.content  # 检查是否包含中文字符
    assert "#" in response.content  # 检查是否保留了Markdown格式
    assert "```python" in response.content  # 检查是否保留了代码块
    assert (
        """print("Hello, world!")""" in response.content
    )  # 检查是否保留了Markdown格式
    assert "[" in response.content and "]" in response.content  # 检查是否保留了链接格式


@pytest.mark.asyncio
async def test_faq_final_question_model_ja():
    answer = """・西庁舎10階「区民情報ひろば」で閲覧できます。※コピーはできません。
・庁舎外になりますが、練馬図書館で閲覧できます。※一部分のコピーはできます。詳しくは図書館へお聞きください。"""
    response_language = "Japanese"

    messages, openai_model = await faq_final_question_model_prompt(
        answer, response_language
    )

    response = await chat_acreate(openai_model=openai_model, messages=messages)

    assert isinstance(response.content, str)
    assert (
        "・西庁舎10階「区民情報ひろば」で閲覧できます。※コピーはできません。\n・庁舎外になりますが、練馬図書館で閲覧できます。※一部分のコピーはできます。詳しくは図書館へお聞きください。"
        == response.content
    )


@pytest.mark.asyncio
async def test_faq_final_question_model_ja_cn():
    answer = """短期滞在者で在留カードの申請ができない方は申込をすることはできません。

<br />

日本在住が確認できる書類をお持ちでしたら、ご契約可能でございます。

本人確認書類については下記をご確認ください。

<https://his-mobile.com/support/certificate>

<br />

SIM・eSIMのお申し込みは下記よりお願いいたします。

<https://his-mobile.com/entry>

"""
    response_language = "Chinese"

    messages, openai_model = await faq_final_question_model_prompt(
        answer, response_language
    )

    response = await chat_acreate(openai_model=openai_model, messages=messages)

    assert isinstance(response.content, str)
    assert "申请" in response.content  # 检查是否包含中文字符


@pytest.mark.asyncio
async def test_faq_final_question_model_ja_cn():
    answer = """
一番安価なデータ通信専用プランはビタッ！プランの上限設定100MBでございます。月額198円でございます。

<br />

プランの詳細は下記ページをご確認ください。

<https://his-mobile.com/domestic/service/vita>

"""
    response_language = "Japanese"

    messages, openai_model = await faq_final_question_model_prompt(
        answer, response_language
    )

    response = await chat_acreate(openai_model=openai_model, messages=messages)

    assert isinstance(response.content, str)
    assert "一番安価" in response.content  # 检查是否包含日文字符
