import unittest
import asyncio
from tortoise import Tortoise
from mygpt.settings import DB_URL, TORTOISE_ORM
from mygpt.dao.postgresql_dao.user_dao import user_dao


class TestClassUserDao(unittest.TestCase):

    async def init_db(self):
        await Tortoise.init(config=TORTOISE_ORM)

    async def close_db(self):
        await <PERSON>toise.close_connections()

    def setUp(self):
        asyncio.run(self.init_db())

    def tearDown(self):
        asyncio.run(self.close_db())

    def test_get_user_by_api_key(self):
        self.setUp()
        api_key = "ak-whjO05DbLMGO3aBK9gemRJOxY34ZQrcQwkMF2f397D3yO9Bx"
        rs = asyncio.run(user_dao.find_user_by_apikey(api_key))
        print(rs)
        self.assertTrue(rs is not None)
        self.tearDown()
