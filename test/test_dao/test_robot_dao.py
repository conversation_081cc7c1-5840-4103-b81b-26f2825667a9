import unittest
import asyncio
from tortoise import Tortoise
from mygpt.settings import DB_URL, TORTOISE_ORM
from mygpt.dao.postgresql_dao.robot_dao import robot_dao


class TestClassRobotDao(unittest.TestCase):

    async def init_db(self):
        await Tortoise.init(config=TORTOISE_ORM)

    async def close_db(self):
        await <PERSON>toise.close_connections()

    def setUp(self):
        asyncio.run(self.init_db())

    def tearDown(self):
        asyncio.run(self.close_db())

    def test_find_robot_by_user_id_and_name(self):
        self.setUp()
        # api_key = "ak-whjO05DbLMGO3aBK9gemRJOxY34ZQrcQwkMF2f397D3yO9Bx"
        user_id = "google-oauth2|104002370439909913399"
        robot_name = "Knowledge Project 知识库描述生成 Agent (内置)"

        rs = asyncio.run(robot_dao.find_robot_by_user_id_and_name(user_id=user_id, name=robot_name))
        print(rs)
        self.assertTrue(rs is not None)
        self.tearDown()
