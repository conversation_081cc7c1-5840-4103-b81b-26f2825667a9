import os

import pytest

from mygpt.openai_utils import language_detect

os.environ["ON_TEST_ENV"] = "1"


@pytest.mark.asyncio
async def test_language_detect_hi_zh():
    res = await language_detect("hi", ai_language="zh")
    print(res)
    assert res[0] == "en", f"Expected 'en', but got {res[0]}"


@pytest.mark.asyncio
async def test_language_detect_japanese():
    text = "一日千秋"
    res = await language_detect(text, ai_language="ja")
    print(res)
    assert res[0] == "ja", f"Expected 'ja', but got {res[0]}"

    text = "指定倉庫"
    res = await language_detect(text, ai_language="ja")
    print(res)
    assert res[0] == "ja", f"Expected 'ja', but got {res[0]}"


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "text", ["媒体名", "取消", "成功", "所在地", "分類", "過敏性腸症候群"]
)
async def test_language_detect_zh_ja(text):
    res_ja = await language_detect(text, ai_language="ja")
    print(f"Japanese: {res_ja}")
    assert res_ja[0] == "ja", f"Expected 'ja', but got {res_ja[0]}"

    res_zh = await language_detect(text, ai_language="zh")
    print(f"Chinese: {res_zh}")
    assert res_zh[0].startswith("zh"), f"Expected 'zh-*', but got {res_zh[0]}"


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "text",
    [
        "支持日语吗",
        "jp传播广告审查官的电话?",
        "在尝试在执行终端上确认运行时,包下载失败了。",
        "関東有哪些会员",
        "打印机",
    ],
)
async def test_language_detect_zh(text):
    res = await language_detect(text, ai_language="zh")
    print(res)
    assert res[0].startswith("zh"), f"Expected 'zh-*', but got {res[0]}"


@pytest.mark.asyncio
async def test_language_detect_xclaim():
    res = await language_detect("xclaim语法", ai_language="en")
    print(res)
    assert res[0].startswith("zh"), f"Expected 'zh-*', but got {res[0]}"
