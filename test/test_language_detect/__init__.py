# import asyncio
#
# import httpx
# from langchain.chains import <PERSON><PERSON><PERSON>n
# from langchain.chat_models import AzureChatOpenAI
# from langchain.prompts import (
#     HumanMessagePromptTemplate,
#     SystemMessagePromptTemplate,
#     ChatPromptTemplate,
# )
#
# from mygpt import settings
# from mygpt.openai_utils import _lang_detect_by_llm
#
# # text_both_zh_ja = ["一日千秋", "媒体名", "取消", "成功", "指定倉庫", "所在地", "分類", "過敏性腸症候群"]
# text_ja = ["一日千秋"]
#
# text_both_zh_ja = ["媒体名", "取消", "成功", "指定倉庫", "所在地", "分類", "過敏性腸症候群"]
#
# # davinci-003 fails at "支持日语吗"
# text_zh = ["支持日语吗", "jp传播广告审查官的电话?", "在尝试在执行终端上确认运行时,包下载失败了。", "関東有哪些会员", "打印机"]
#
# # list_of_dicts = [
# #     {"ai_language": t[0], "query": t[1], "expected_response_language": t[2]}
# #     for t in texts
# # ]
#
# # def _lang_detect():
#
# proxy_url = "http://127.0.0.1:10809"
#
# proxies = {
#     "http://": proxy_url,
#     "https://": proxy_url,
# }
#
# http_client = httpx.AsyncClient(http2=True, verify=False, proxies=proxies)
#
# # http_client = httpx.AsyncClient(http2=True, verify=False)
#
# # azure_davinci_003 = AzureOpenAI(
# #     temperature=0,
# #     max_tokens=400,
# #     deployment_name=settings.OPENAI_DIVINCI3_ENGINE_AZURE,
# #     openai_api_key=settings.OPENAI_API_KEY_AZURE,
# #     azure_endpoint=settings.OPENAI_API_BASE_AZURE,
# #     http_client=http_client,
# #     openai_api_version=settings.OPENAI_API_VERSION_AZURE,
# #     max_retries=1,
# #     request_timeout=10,
# # )
# #
# # import mygpt.prompt as prompt
# #
# # lang_detect_prompt = PromptTemplate.from_template(
# #     template=prompt.detect_language_system_template
# #     + "\n\n"
# #     + prompt.language_detect_user_template
# # )
# #
# #
# # response_lang_detect_chain = LLMChain(
# #     llm=azure_davinci_003, prompt=lang_detect_prompt, verbose=True
# # )
#
# # azure_gpt_4 = EmptyTextProofAzureChatOpenAI(
# azure_gpt_4 = AzureChatOpenAI(
#     temperature=0,
#     max_tokens=400,
#     deployment_name=settings.OPENAI_GPT_ENGINE_AZURE_4,
#     openai_api_key=settings.OPENAI_API_KEY_AZURE_4,
#     azure_endpoint=settings.OPENAI_API_BASE_AZURE_4,
#     http_client=http_client,
#     openai_api_version=settings.OPENAI_API_VERSION_AZURE,
#     max_retries=1,
#     request_timeout=10,
#     openai_api_type=settings.OPENAI_API_TYPE_AZURE,
#     model_name=settings.OPENAI_GPT_ENGINE_AZURE_4,
# )
#
# import mygpt.prompt as prompt
#
#
# lang_detect_prompt = ChatPromptTemplate.from_messages(
#     [
#         SystemMessagePromptTemplate.from_template(
#             template=prompt.detect_language_system_template,
#             input_variables=["ai_language", "query"],
#         ),
#         HumanMessagePromptTemplate.from_template(
#             template=prompt.language_detect_user_template,
#             input_variables=["ai_language", "query"],
#         ),
#     ]
# )
#
#
# response_lang_detect_chain = LLMChain(
#     llm=azure_gpt_4, prompt=lang_detect_prompt, verbose=True
# )
# # response_lang_detect_chain = LLMChain(
# #     llm=get_chat_model(settings.OPENAI_GPT_ENGINE_AZURE_4),
# #     prompt=chat_prompt,
# # )
#
#
# def test_response_language_detect(
#     chain: LLMChain, query: str, ai_lang: str, chat_history: str, user_lang: str
# ) -> str:
#     response_lang = _lang_detect_by_llm()
#     return
#
#
# import json
#
#
# async def run():
#     for item in text_both_zh_ja:
#         for lang in ["zh", "ja"]:
#             res = await response_lang_detect_chain.arun(query=item, ai_language=lang)
#             print(res)
#             assert json.loads(res)["language"].startswith(lang)
#     for item in text_zh:
#         for lang in ["zh", "ja"]:
#             res = await response_lang_detect_chain.arun(query=item, ai_language=lang)
#             print(item, res)
#             # assert res == "zh"
#             assert json.loads(res)["language"].startswith("zh")
#
#
# async def test1(item, ai_lang, expected_lang):
#     res = await response_lang_detect_chain.arun(query=item, ai_language=ai_lang)
#     print(res)
#     assert json.loads(res)["language"].startswith(expected_lang)
#
#
# if __name__ == "__main__":
#     # asyncio.run(run())
#     asyncio.run(test1("xclaim语法", "en", "en"))
#     # asyncio.run(test1("領収書", "ja", "ja"))
#     # asyncio.run(test1("H.I.S.Mobile株式会社所在地", "ja", "ja"))
#     # print(session.url)
