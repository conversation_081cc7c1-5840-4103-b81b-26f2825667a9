FROM python:3.10

LABEL maintainer="<EMAIL>"

ENV TZ=Asia/Tokyo
ENV DEBUG=0
ENV TIKTOKEN_CACHE_DIR=/backend/data-gym-cach

RUN apt-get update && \
    apt-get -y install gcc libpq-dev tzdata git wget

COPY . /backend
WORKDIR /backend

RUN cat /backend/requirements.txt

# 安装Poetry
RUN pip install poetry
RUN poetry self add poetry-plugin-export

# 使用Poetry生成requirements.txt
RUN poetry export -f requirements.txt --output requirements.txt --without-hashes

# Use system pip
RUN pip install  --isolated  -r requirements.txt
RUN pip install pip_system_certs
RUN pip install zstandard aioredis psutil ijson aiofiles matplotlib

RUN python -m compileall -b . 
RUN chmod +x build_pyc.sh
RUN ./build_pyc.sh
# Pre-download NLTK data
RUN python -m nltk.downloader -d /usr/local/lib/python3.10/site-packages/llama_index/core/_static/nltk_cache punkt_tab

# soffice
RUN apt-get update
RUN apt-get -y install libreoffice fontconfig fonts-wqy-zenhei vim
RUN fc-cache -fv
# 先合并切片文件
RUN cat resources/LibreOffice_25.2.3_Linux_x86-64_deb.tar.gz* > resources/LibreOffice_25.2.3_Linux_x86-64_deb.tar.gz
RUN tar -xvzf resources/LibreOffice_25.2.3_Linux_x86-64_deb.tar.gz
RUN dpkg -i LibreOffice_25.2.3.2_Linux_x86-64_deb/DEBS/*.deb
RUN rm -rf LibreOffice_25.2.3.2_Linux_x86-64_deb
RUN rm resources/LibreOffice_25.2.3_Linux_x86-64_deb.tar.gz
RUN rm /usr/bin/soffice
RUN ln -s /opt/libreoffice25.2/program/soffice /usr/bin/soffice

EXPOSE 8000

RUN chmod +x ./startup_customer.sh
CMD ["/bin/sh", "-c", "./startup_customer.sh"]