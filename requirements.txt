aerich==0.7.1 ; python_version >= "3.10" and python_version < "3.12"
aioboto3==12.3.0 ; python_version >= "3.10" and python_version < "3.12"
aiobotocore==2.11.2 ; python_version >= "3.10" and python_version < "3.12"
aiobotocore[boto3]==2.11.2 ; python_version >= "3.10" and python_version < "3.12"
aiohttp==3.9.4 ; python_version >= "3.10" and python_version < "3.12"
aioitertools==0.11.0 ; python_version >= "3.10" and python_version < "3.12"
aiosignal==1.3.1 ; python_version >= "3.10" and python_version < "3.12"
aiosqlite==0.17.0 ; python_version >= "3.10" and python_version < "3.12"
amqp==5.2.0 ; python_version >= "3.10" and python_version < "3.12"
anyio==4.3.0 ; python_version >= "3.10" and python_version < "3.12"
arize-phoenix==1.9.0 ; python_version >= "3.10" and python_version < "3.12"
asgiref==3.7.2 ; python_version >= "3.10" and python_version < "3.12"
async-timeout==4.0.3 ; python_version >= "3.10" and python_full_version < "3.11.3"
asyncio==3.4.3 ; python_version >= "3.10" and python_version < "3.12"
asyncpg==0.27.0 ; python_version >= "3.10" and python_version < "3.12"
attrs==23.2.0 ; python_version >= "3.10" and python_version < "3.12"
auth0-python==4.5.0 ; python_version >= "3.10" and python_version < "3.12"
backoff==2.2.1 ; python_version >= "3.10" and python_version < "3.12"
bcrypt==4.1.2 ; python_version >= "3.10" and python_version < "3.12"
beautifulsoup4==4.12.3 ; python_version >= "3.10" and python_version < "3.12"
billiard==4.2.0 ; python_version >= "3.10" and python_version < "3.12"
black==23.12.1 ; python_version >= "3.10" and python_version < "3.12"
boto3==1.34.34 ; python_version >= "3.10" and python_version < "3.12"
botocore==1.34.34 ; python_version >= "3.10" and python_version < "3.12"
bs4==0.0.2 ; python_version >= "3.10" and python_version < "3.12"
build==1.1.1 ; python_version >= "3.10" and python_version < "3.12"
cachetools==5.3.3 ; python_version >= "3.10" and python_version < "3.12"
celery[redis]==5.3.6 ; python_version >= "3.10" and python_version < "3.12"
certifi==2024.2.2 ; python_version >= "3.10" and python_version < "3.12"
cffi==1.16.0 ; python_version >= "3.10" and python_version < "3.12"
chardet==5.2.0 ; python_version >= "3.10" and python_version < "3.12"
charset-normalizer==3.3.2 ; python_version >= "3.10" and python_version < "3.12"
chroma-hnswlib==0.7.3 ; python_version >= "3.10" and python_version < "3.12"
chromadb==0.4.24 ; python_version >= "3.10" and python_version < "3.12"
click-didyoumean==0.3.0 ; python_version >= "3.10" and python_version < "3.12"
click-plugins==1.1.1 ; python_version >= "3.10" and python_version < "3.12"
click-repl==0.3.0 ; python_version >= "3.10" and python_version < "3.12"
click==8.1.7 ; python_version >= "3.10" and python_version < "3.12"
cohere==4.53 ; python_version >= "3.10" and python_version < "3.12"
colorama==0.4.6 ; python_version >= "3.10" and python_version < "3.12" and (platform_system == "Windows" or sys_platform == "win32" or os_name == "nt")
coloredlogs==15.0.1 ; python_version >= "3.10" and python_version < "3.12"
cryptography==41.0.7 ; python_version >= "3.10" and python_version < "3.12"
cssselect==1.2.0 ; python_version >= "3.10" and python_version < "3.12"
cssutils==2.9.0 ; python_version >= "3.10" and python_version < "3.12"
cython==0.29.37 ; python_version >= "3.10" and python_version < "3.12"
dataclasses-json==0.6.4 ; python_version >= "3.10" and python_version < "3.12"
dateparser==1.2.0 ; python_version >= "3.10" and python_version < "3.12"
ddsketch==2.0.4 ; python_version >= "3.10" and python_version < "3.12"
deprecated==1.2.14 ; python_version >= "3.10" and python_version < "3.12"
dictdiffer==0.9.0 ; python_version >= "3.10" and python_version < "3.12"
dirtyjson==1.0.8 ; python_version >= "3.10" and python_version < "3.12"
distro==1.9.0 ; python_version >= "3.10" and python_version < "3.12"
dnspython==2.6.1 ; python_version >= "3.10" and python_version < "3.12"
ecdsa==0.18.0 ; python_version >= "3.10" and python_version < "3.12"
email-validator==2.2.0 ; python_version >= "3.10" and python_version < "3.12"
et-xmlfile==1.1.0 ; python_version >= "3.10" and python_version < "3.12"
exceptiongroup==1.2.0 ; python_version >= "3.10" and python_version < "3.11"
fake-useragent==1.5.0 ; python_version >= "3.10" and python_version < "3.12"
fastapi-auth0==0.3.2 ; python_version >= "3.10" and python_version < "3.12"
fastapi-pagination==0.11.4 ; python_version >= "3.10" and python_version < "3.12"
fastapi==0.110.0 ; python_version >= "3.10" and python_version < "3.12"
fastavro==1.9.4 ; python_version >= "3.10" and python_version < "3.12"
filelock==3.13.1 ; python_version >= "3.10" and python_version < "3.12"
flatbuffers==24.3.7 ; python_version >= "3.10" and python_version < "3.12"
frozenlist==1.4.1 ; python_version >= "3.10" and python_version < "3.12"
fsspec==2024.2.0 ; python_version >= "3.10" and python_version < "3.12"
google-api-core==2.17.1 ; python_version >= "3.10" and python_version < "3.12"
google-api-python-client==2.121.0 ; python_version >= "3.10" and python_version < "3.12"
google-auth-httplib2==0.2.0 ; python_version >= "3.10" and python_version < "3.12"
google-auth==2.28.2 ; python_version >= "3.10" and python_version < "3.12"
googleapis-common-protos==1.62.0 ; python_version >= "3.10" and python_version < "3.12"
graphql-core==3.2.3 ; python_version >= "3.10" and python_version < "3.12"
greenlet==3.0.3 ; python_version >= "3.10" and python_version < "3.12"
grpcio-tools==1.48.2 ; python_version >= "3.10" and python_version < "3.12"
grpcio==1.62.1 ; python_version >= "3.10" and python_version < "3.12"
h11==0.14.0 ; python_version >= "3.10" and python_version < "3.12"
h2==4.1.0 ; python_version >= "3.10" and python_version < "3.12"
hdbscan==0.8.33 ; python_version >= "3.10" and python_version < "3.12"
hiredis==2.3.2 ; python_version >= "3.10" and python_version < "3.12"
hpack==4.0.0 ; python_version >= "3.10" and python_version < "3.12"
httpcore==1.0.4 ; python_version >= "3.10" and python_version < "3.12"
httplib2==0.22.0 ; python_version >= "3.10" and python_version < "3.12"
httptools==0.6.1 ; python_version >= "3.10" and python_version < "3.12"
httpx==0.25.2 ; python_version >= "3.10" and python_version < "3.12"
httpx[http2]==0.25.2 ; python_version >= "3.10" and python_version < "3.12"
huggingface-hub==0.21.4 ; python_version >= "3.10" and python_version < "3.12"
humanfriendly==10.0 ; python_version >= "3.10" and python_version < "3.12"
hyperframe==6.0.1 ; python_version >= "3.10" and python_version < "3.12"
idna==3.6 ; python_version >= "3.10" and python_version < "3.12"
importlib-metadata==6.11.0 ; python_version >= "3.10" and python_version < "3.12"
importlib-resources==6.1.3 ; python_version >= "3.10" and python_version < "3.12"
iniconfig==2.0.0 ; python_version >= "3.10" and python_version < "3.12"
iso8601==1.1.0 ; python_version >= "3.10" and python_version < "3.12"
itsdangerous==2.1.2 ; python_version >= "3.10" and python_version < "3.12"
jinja2==3.1.3 ; python_version >= "3.10" and python_version < "3.12"
jmespath==1.0.1 ; python_version >= "3.10" and python_version < "3.12"
joblib==1.3.2 ; python_version >= "3.10" and python_version < "3.12"
jsonpatch==1.33 ; python_version >= "3.10" and python_version < "3.12"
jsonpointer==2.4 ; python_version >= "3.10" and python_version < "3.12"
kombu==5.3.5 ; python_version >= "3.10" and python_version < "3.12"
kubernetes==29.0.0 ; python_version >= "3.10" and python_version < "3.12"
langchain-community==0.0.27 ; python_version >= "3.10" and python_version < "3.12"
langchain-core==0.1.30 ; python_version >= "3.10" and python_version < "3.12"
langchain-text-splitters==0.0.1 ; python_version >= "3.10" and python_version < "3.12"
langchain==0.1.11 ; python_version >= "3.10" and python_version < "3.12"
langsmith==0.1.23 ; python_version >= "3.10" and python_version < "3.12"
lark-oapi==1.3.4 ; python_version >= "3.10" and python_version < "3.12"
llama-index-agent-openai==0.1.5 ; python_version >= "3.10" and python_version < "3.12"
llama-index-cli==0.1.8 ; python_version >= "3.10" and python_version < "3.12"
llama-index-core==0.10.29 ; python_version >= "3.10" and python_version < "3.12"
llama-index-embeddings-openai==0.1.6 ; python_version >= "3.10" and python_version < "3.12"
llama-index-indices-managed-llama-cloud==0.1.3 ; python_version >= "3.10" and python_version < "3.12"
llama-index-legacy==0.9.48 ; python_version >= "3.10" and python_version < "3.12"
llama-index-llms-openai==0.1.15 ; python_version >= "3.10" and python_version < "3.12"
llama-index-multi-modal-llms-openai==0.1.4 ; python_version >= "3.10" and python_version < "3.12"
llama-index-program-openai==0.1.4 ; python_version >= "3.10" and python_version < "3.12"
llama-index-question-gen-openai==0.1.3 ; python_version >= "3.10" and python_version < "3.12"
llama-index-readers-file==0.1.9 ; python_version >= "3.10" and python_version < "3.12"
llama-index-readers-llama-parse==0.1.3 ; python_version >= "3.10" and python_version < "3.12"
llama-index-vector-stores-chroma==0.1.6 ; python_version >= "3.10" and python_version < "3.12"
llama-index==0.10.29 ; python_version >= "3.10" and python_version < "3.12"
llama-parse==0.3.8 ; python_version >= "3.10" and python_version < "3.12"
llamaindex-py-client==0.1.18 ; python_version >= "3.10" and python_version < "3.12"
llvmlite==0.42.0 ; python_version >= "3.10" and python_version < "3.12"
loader==2017.9.11 ; python_version >= "3.10" and python_version < "3.12"
loguru==0.7.2 ; python_version >= "3.10" and python_version < "3.12"
lxml==5.1.0 ; python_version >= "3.10" and python_version < "3.12"
markupsafe==2.1.5 ; python_version >= "3.10" and python_version < "3.12"
marshmallow==3.21.1 ; python_version >= "3.10" and python_version < "3.12"
mmh3==4.1.0 ; python_version >= "3.10" and python_version < "3.12"
monotonic==1.6 ; python_version >= "3.10" and python_version < "3.12"
mpmath==1.3.0 ; python_version >= "3.10" and python_version < "3.12"
multidict==6.0.5 ; python_version >= "3.10" and python_version < "3.12"
mypy-extensions==1.0.0 ; python_version >= "3.10" and python_version < "3.12"
nest-asyncio==1.6.0 ; python_version >= "3.10" and python_version < "3.12"
networkx==3.2.1 ; python_version >= "3.10" and python_version < "3.12"
nltk==3.8.1 ; python_version >= "3.10" and python_version < "3.12"
numba==0.59.0 ; python_version >= "3.10" and python_version < "3.12"
numpy==1.26.4 ; python_version >= "3.10" and python_version < "3.12"
oauthlib==3.2.2 ; python_version >= "3.10" and python_version < "3.12"
onnxruntime==1.17.1 ; python_version >= "3.10" and python_version < "3.12"
openai==1.11.0 ; python_version >= "3.10" and python_version < "3.12"
openpyxl==3.1.5 ; python_version >= "3.10" and python_version < "3.12"
opensearch-py[async]==2.4.2 ; python_version >= "3.10" and python_version < "3.12"
opentelemetry-api==1.23.0 ; python_version >= "3.10" and python_version < "3.12"
opentelemetry-exporter-otlp-proto-common==1.23.0 ; python_version >= "3.10" and python_version < "3.12"
opentelemetry-exporter-otlp-proto-grpc==1.23.0 ; python_version >= "3.10" and python_version < "3.12"
opentelemetry-instrumentation-asgi==0.44b0 ; python_version >= "3.10" and python_version < "3.12"
opentelemetry-instrumentation-fastapi==0.44b0 ; python_version >= "3.10" and python_version < "3.12"
opentelemetry-instrumentation==0.44b0 ; python_version >= "3.10" and python_version < "3.12"
opentelemetry-proto==1.23.0 ; python_version >= "3.10" and python_version < "3.12"
opentelemetry-sdk==1.23.0 ; python_version >= "3.10" and python_version < "3.12"
opentelemetry-semantic-conventions==0.44b0 ; python_version >= "3.10" and python_version < "3.12"
opentelemetry-util-http==0.44b0 ; python_version >= "3.10" and python_version < "3.12"
orjson==3.9.15 ; python_version >= "3.10" and python_version < "3.12"
overrides==7.7.0 ; python_version >= "3.10" and python_version < "3.12"
packaging==23.2 ; python_version >= "3.10" and python_version < "3.12"
pandas==2.2.2 ; python_version >= "3.10" and python_version < "3.12"
pathspec==0.12.1 ; python_version >= "3.10" and python_version < "3.12"
pexpect==4.9.0 ; python_version >= "3.10" and python_version < "3.12"
pillow==10.2.0 ; python_version >= "3.10" and python_version < "3.12"
pinecone-client==2.2.4 ; python_version >= "3.10" and python_version < "3.12"
pip==24.0 ; python_version >= "3.10" and python_version < "3.12"
platformdirs==4.2.0 ; python_version >= "3.10" and python_version < "3.12"
pluggy==1.4.0 ; python_version >= "3.10" and python_version < "3.12"
portalocker==2.8.2 ; python_version >= "3.10" and python_version < "3.12"
posthog==3.5.0 ; python_version >= "3.10" and python_version < "3.12"
prompt-toolkit==3.0.43 ; python_version >= "3.10" and python_version < "3.12"
protobuf==3.20.3 ; python_version >= "3.10" and python_version < "3.12"
psutil==5.9.8 ; python_version >= "3.10" and python_version < "3.12"
ptyprocess==0.7.0 ; python_version >= "3.10" and python_version < "3.12"
pulsar-client==3.4.0 ; python_version >= "3.10" and python_version < "3.12"
py3langid==0.2.2 ; python_version >= "3.10" and python_version < "3.12"
pyarrow==15.0.1 ; python_version >= "3.10" and python_version < "3.12"
pyasn1-modules==0.3.0 ; python_version >= "3.10" and python_version < "3.12"
pyasn1==0.5.1 ; python_version >= "3.10" and python_version < "3.12"
pycparser==2.21 ; python_version >= "3.10" and python_version < "3.12"
pycryptodome==3.20.0 ; python_version >= "3.10" and python_version < "3.12"
pydantic==1.10.14 ; python_version >= "3.10" and python_version < "3.12"
pyjwt==2.8.0 ; python_version >= "3.10" and python_version < "3.12"
pymupdf==1.23.26 ; python_version >= "3.10" and python_version < "3.12"
pymupdfb==1.23.22 ; python_version >= "3.10" and python_version < "3.12"
pynndescent==0.5.11 ; python_version >= "3.10" and python_version < "3.12"
pyopenssl==23.3.0 ; python_version >= "3.10" and python_version < "3.12"
pyparsing==3.1.2 ; python_version >= "3.10" and python_version < "3.12"
pypdf==4.3.1 ; python_version >= "3.10" and python_version < "3.12"
pypika-tortoise==0.1.6 ; python_version >= "3.10" and python_version < "3.12"
pypika==0.48.9 ; python_version >= "3.10" and python_version < "3.12"
pyproject-hooks==1.0.0 ; python_version >= "3.10" and python_version < "3.12"
pyreadline3==3.4.1 ; sys_platform == "win32" and python_version >= "3.10" and python_version < "3.12"
pytest-asyncio==0.23.5.post1 ; python_version >= "3.10" and python_version < "3.12"
pytest==8.1.1 ; python_version >= "3.10" and python_version < "3.12"
python-dateutil==2.9.0.post0 ; python_version >= "3.10" and python_version < "3.12"
python-docx==0.8.11 ; python_version >= "3.10" and python_version < "3.12"
python-dotenv==1.0.1 ; python_version >= "3.10" and python_version < "3.12"
python-jose==3.3.0 ; python_version >= "3.10" and python_version < "3.12"
python-multipart==0.0.5 ; python_version >= "3.10" and python_version < "3.12"
pytz==2024.1 ; python_version >= "3.10" and python_version < "3.12"
pywin32==306 ; python_version >= "3.10" and python_version < "3.12" and platform_system == "Windows"
pyyaml==6.0.1 ; python_version >= "3.10" and python_version < "3.12"
qdrant-client==1.10.1 ; python_version >= "3.10" and python_version < "3.12"
readability-lxml==0.8.1 ; python_version >= "3.10" and python_version < "3.12"
redis==5.0.3 ; python_version >= "3.10" and python_version < "3.12"
redis[hiredis]==5.0.3 ; python_version >= "3.10" and python_version < "3.12"
regex==2023.12.25 ; python_version >= "3.10" and python_version < "3.12"
requests-oauthlib==1.4.0 ; python_version >= "3.10" and python_version < "3.12"
requests-toolbelt==1.0.0 ; python_version >= "3.10" and python_version < "3.12"
requests==2.31.0 ; python_version >= "3.10" and python_version < "3.12"
rsa==4.9 ; python_version >= "3.10" and python_version < "3.12"
s3transfer==0.10.1 ; python_version >= "3.10" and python_version < "3.12"
scikit-learn==1.2.2 ; python_version >= "3.10" and python_version < "3.12"
scipy==1.12.0 ; python_version >= "3.10" and python_version < "3.12"
sentry-sdk[fastapi]==1.41.0 ; python_version >= "3.10" and python_version < "3.12"
setuptools==69.1.1 ; python_version >= "3.10" and python_version < "3.12"
six==1.16.0 ; python_version >= "3.10" and python_version < "3.12"
sniffio==1.3.1 ; python_version >= "3.10" and python_version < "3.12"
sortedcontainers==2.4.0 ; python_version >= "3.10" and python_version < "3.12"
soupsieve==2.5 ; python_version >= "3.10" and python_version < "3.12"
sparticleinc-skywalking==1.0.4 ; python_version >= "3.10" and python_version < "3.12"
sqlalchemy==2.0.12 ; python_version >= "3.10" and python_version < "3.12"
sqlalchemy[asyncio]==2.0.12 ; python_version >= "3.10" and python_version < "3.12"
sse-starlette==2.0.0 ; python_version >= "3.10" and python_version < "3.12"
starlette==0.36.3 ; python_version >= "3.10" and python_version < "3.12"
strawberry-graphql==0.208.2 ; python_version >= "3.10" and python_version < "3.12"
stripe==5.5.0 ; python_version >= "3.10" and python_version < "3.12"
striprtf==0.0.26 ; python_version >= "3.10" and python_version < "3.12"
sympy==1.12 ; python_version >= "3.10" and python_version < "3.12"
tenacity==8.2.3 ; python_version >= "3.10" and python_version < "3.12"
threadpoolctl==3.3.0 ; python_version >= "3.10" and python_version < "3.12"
tiktoken==0.6.0 ; python_version >= "3.10" and python_version < "3.12"
tokenizers==0.15.2 ; python_version >= "3.10" and python_version < "3.12"
tomli==2.0.1 ; python_version >= "3.10" and python_version < "3.11"
tomlkit==0.12.4 ; python_version >= "3.10" and python_version < "3.12"
tortoise-orm==0.19.3 ; python_version >= "3.10" and python_version < "3.12"
tqdm==4.66.2 ; python_version >= "3.10" and python_version < "3.12"
typer==0.9.0 ; python_version >= "3.10" and python_version < "3.12"
types-aiobotocore-s3==2.12.1 ; python_version >= "3.10" and python_version < "3.12"
typing-extensions==4.10.0 ; python_version >= "3.10" and python_version < "3.12"
typing-inspect==0.9.0 ; python_version >= "3.10" and python_version < "3.12"
tzdata==2024.1 ; python_version >= "3.10" and python_version < "3.12"
tzlocal==5.2 ; python_version >= "3.10" and python_version < "3.12"
umap-learn==0.5.5 ; python_version >= "3.10" and python_version < "3.12"
uritemplate==4.1.1 ; python_version >= "3.10" and python_version < "3.12"
urllib3==1.26.18 ; python_version >= "3.10" and python_version < "3.12"
uvicorn==0.27.0 ; python_version >= "3.10" and python_version < "3.12"
uvicorn[standard]==0.27.0 ; python_version >= "3.10" and python_version < "3.12"
uvloop==0.19.0 ; (sys_platform != "win32" and sys_platform != "cygwin") and platform_python_implementation != "PyPy" and python_version >= "3.10" and python_version < "3.12"
vine==5.1.0 ; python_version >= "3.10" and python_version < "3.12"
watchfiles==0.21.0 ; python_version >= "3.10" and python_version < "3.12"
wcwidth==0.2.13 ; python_version >= "3.10" and python_version < "3.12"
websocket-client==1.7.0 ; python_version >= "3.10" and python_version < "3.12"
websockets==12.0 ; python_version >= "3.10" and python_version < "3.12"
win32-setctime==1.1.0 ; python_version >= "3.10" and python_version < "3.12" and sys_platform == "win32"
wrapt==1.16.0 ; python_version >= "3.10" and python_version < "3.12"
xlrd==2.0.1 ; python_version >= "3.10" and python_version < "3.12"
yarl==1.9.4 ; python_version >= "3.10" and python_version < "3.12"
zipp==3.17.0 ; python_version >= "3.10" and python_version < "3.12"
