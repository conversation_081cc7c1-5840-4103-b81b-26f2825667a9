#!/bin/sh

#set -a
echo 'startup celery'
# 检查 .env 文件是否存在
#if [ -f .env ]; then
#    # 加载 .env 文件中的环境变量
#    . ./.env
#    echo "get env FILE_PROCESSING_CONCURRENCY: ${FILE_PROCESSING_CONCURRENCY}"
#else
#    echo ".env not found, using default configuration"
#fi

#set +a

#echo "Initial FILE_PROCESSING_CONCURRENCY: $FILE_PROCESSING_CONCURRENCY"
#: "${FILE_PROCESSING_CONCURRENCY:=1}"
#echo "After default setting FILE_PROCESSING_CONCURRENCY: $FILE_PROCESSING_CONCURRENCY"

#echo "force FILE_PROCESSING_CONCURRENCY to be 2"
#FILE_PROCESSING_CONCURRENCY=2
#echo "force FILE_PROCESSING_CONCURRENCY to be 2: $FILE_PROCESSING_CONCURRENCY"

celery -A mygpt.tasks.lark_integration worker \
    --pool=prefork \
    --loglevel=info \
    --autoscale=20,2 \
    --queues=lark_integration_rule \
    -n celery@lark_integration_rule_worker \
    --max-tasks-per-child=1 \
    -E &

#celery -A mygpt.tasks.lark_integration worker \
#    --pool=prefork \
#    --loglevel=info \
#    --concurrency=${FILE_PROCESSING_CONCURRENCY} \
#    --queues=lark_file_default_no_use \
#    -n celery@lark_file_worker \
#    --max-tasks-per-child=1 \
#    -O fair \
#    -E &
if [ -f "reset_celery_file_status.py" ]; then
    echo "reset_celery_file_status.py found"
    python3 reset_celery_file_status.py || true
else
    echo "reset_celery_file_status.py not found, using reset_celery_file_status.pyc"
    python3 reset_celery_file_status.pyc || true
fi

if [ -f "start_lark_file_worker.py" ]; then
    echo "start_lark_file_worker.py found"
    python3 start_lark_file_worker.py &
else
    echo "start_lark_file_worker.py not found, using start_lark_file_worker.pyc"
    python3 start_lark_file_worker.pyc &
fi

# flower
celery -A mygpt.tasks.lark_integration flower --port=8000 &

wait