#!/usr/bin/env python3
import logging
import os
import redis
from mygpt import settings
from mygpt.tasks.lark_integration import celery_app


def get_queue_names():
    logging.info("Connecting to Redis...")
    redis_client = redis.Redis.from_url(settings.CELERY_BROKER_URL)

    cursor = 0
    queue_pattern = "lark_file.*"
    queue_names = set()

    logging.info("Starting SCAN for queues...")
    while True:
        cursor, keys = redis_client.scan(cursor=cursor, match=queue_pattern, count=100)
        for key in keys:
            key_str = key.decode("utf-8")
            queue_length = redis_client.llen(key_str)
            logging.info(f"Queue {key_str} length: {queue_length}")
            if queue_length > 0:
                queue_names.add(key_str)
        if cursor == 0:
            logging.info("Completed SCAN for queues.")
            break

    redis_client.close()
    logging.info("Redis connection closed.")
    return queue_names


def start_worker(queue_list):
    FILE_PROCESSING_CONCURRENCY = settings.FILE_PROCESSING_CONCURRENCY
    logging.info(
        f"Starting Celery Worker with concurrency. FILE_PROCESSING_CONCURRENCY: {FILE_PROCESSING_CONCURRENCY}"
    )

    if queue_list:
        queue_arg = queue_list.split(",")
    else:
        queue_arg = ["default_placeholder"]

    # 构建命令行参数
    argv = [
        "-A",
        "mygpt.tasks.lark_integration",
        "worker",
        "--loglevel",
        "INFO",
        "--pool",
        "prefork",
        "--concurrency",
        str(FILE_PROCESSING_CONCURRENCY),
        "--queues",
        ",".join(queue_arg),
        "--hostname",
        "celery@lark_file_worker",
        "--max-tasks-per-child",
        "1",
        "--optimization",
        "fair",
        "--events",
    ]

    logging.info(f"Starting Celery Worker with arguments: {argv}")
    celery_app.worker_main(argv=argv)


def main():
    logging.basicConfig(level=logging.INFO)

    queue_names = get_queue_names()
    if not queue_names:
        logging.info("No queues found.")

    queue_list = ",".join(queue_names)
    start_worker(queue_list)


if __name__ == "__main__":
    main()
