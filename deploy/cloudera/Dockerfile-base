FROM qdrant/qdrant:v1.6.1 as qdrant_builder

FROM ubuntu:22.04
USER root

# ADD Qrant
COPY --from=qdrant_builder /qdrant /qdrant

ARG DEBIAN_FRONTEND=noninteractive

# 安装nginx redis postgresql
RUN apt-get update && apt-get install -y nginx redis libunwind8 postgresql

COPY nginx.conf /etc/nginx/nginx.conf

# Associate uid and gid 8536 with username cdsw
RUN \
  addgroup --gid 8536 cdsw && \
  adduser --disabled-password --gecos "CDSW User" --uid 8536 --gid 8536 cdsw

# Relax permissions to facilitate installation of Cloudera
# client files at startup
RUN for i in /bin /opt /usr /usr/share/java  /var/lib/nginx  /run; do \
   mkdir -p ${i}; \
   chown cdsw ${i}; \
   chmod +rw ${i}; \
   for subfolder in `find ${i} -type d` ; do \
      chown cdsw ${subfolder}; \
      chmod +rw ${subfolder}; \
   done \
 done

RUN for i in /etc /etc/alternatives /var/log/nginx /run /gptbase;  do \
mkdir -p ${i}; \
chmod 777 ${i}; \
done

RUN rm /var/log/nginx/access.log
RUN rm /var/log/nginx/error.log

RUN chown -R cdsw:cdsw /var/lib/postgresql && \
   chown -R cdsw:cdsw /var/log/postgresql && \
   chown -R cdsw:cdsw /etc/ssl/private && \
   chmod 0600 /etc/ssl/private/ssl-cert-snakeoil.key && \
   chown -R cdsw:cdsw /etc/postgresql && \
   chown -R cdsw:cdsw /var/run/postgresql && \
   chown -R cdsw:cdsw /qdrant && \
   chown -R cdsw:cdsw /etc/redis && \
   chown -R cdsw:cdsw /var/log/redis && \
   chown -R cdsw:cdsw /var/lib/redis && \
   chown -R cdsw:cdsw /gptbase


ARG BRANCH_NAME=prd

COPY qdrant_production.yaml /qdrant/config/production.yaml


# Install Python
# Note that the package python-is-python3 will alias python3 as python
RUN apt-get install -y --no-install-recommends \
   krb5-user python3.10 python3-pip python-is-python3 ssh xz-utils python3.10-venv

# Configure pip to install packages under /usr/local
# when building the Runtime image
RUN pip3 config set install.user false

# Install the Jupyter kernel gateway.
# The IPython kernel is automatically installed 
# under the name python3,
# so below we set the kernel name to python3.
RUN pip3 install "jupyter-kernel-gateway==2.5.2"

RUN apt-get -y install lsb-release ca-certificates curl gnupg2 curl 
RUN echo "deb [signed-by=/usr/share/keyrings/opensearch-keyring] https://artifacts.opensearch.org/releases/bundle/opensearch/2.x/apt stable main" | tee /etc/apt/sources.list.d/opensearch-2.x.list
RUN curl -o- https://artifacts.opensearch.org/publickeys/opensearch.pgp | gpg --dearmor --batch --yes -o /usr/share/keyrings/opensearch-keyring
RUN apt-get update && apt-get install -y --no-install-recommends  opensearch=2.11.1; apt-get autoclean; rm -rf /var/lib/apt/lists/*
RUN echo "vm.max_map_count=262144" | tee -a /etc/sysctl.conf
RUN mkdir -p /gptbase/opensearch
RUN chown -R cdsw:cdsw /etc/opensearch && \
    chown -R cdsw:cdsw /gptbase/opensearch && \
    chown -R cdsw:cdsw /var/log/opensearch
COPY opensearch.yml /etc/opensearch/opensearch.yml


RUN /usr/share/opensearch/bin/opensearch-plugin install analysis-kuromoji
RUN /usr/share/opensearch/bin/opensearch-plugin install analysis-smartcn
RUN /usr/share/opensearch/bin/opensearch-plugin install repository-s3 --batch
