#!/bin/bash
set -e

if [ ! -d "/home/<USER>/gptbase/logs/supervisor" ]; then
  echo "Create log directory /home/<USER>/gptbase/logs/supervisor"
  mkdir -p /home/<USER>/gptbase/logs/supervisor
fi

PA_DATA="/home/<USER>/gptbase/postgresql/data"

POSTGRESQL_CONF="$PA_DATA/postgresql.conf"
PG_HBA="$PA_DATA/pg_hba.conf"
PID_FILE="$PA_DATA/postmaster.pid"

# check file exists
if [ ! -f "$PA_DATA/PG_VERSION" ]; then
  echo "Need init postgresql data"
  cd /usr/lib/postgresql/14/bin
  # init db
  ./initdb -D $PA_DATA -U cdsw -E utf8 --pwfile=<(printf "%s\n" "pwd123456")
  sed -i "s/#listen_addresses = 'localhost'/listen_addresses = '*'/g" $POSTGRESQL_CONF
  echo "host all all 0.0.0.0/0 md5" >> $PG_HBA
else
  echo "Directory $PA_DATA already exists"
  if [ -f "/run/postgresql/.s.PGSQL.5432.lock" ]; then
    rm /var/run/postgresql/.s.PGSQL.5432.lock
    echo "Remove /run/postgresql/.s.PGSQL.5432.lock"
  fi
  if [ -f "$PID_FILE" ]; then
    rm $PID_FILE
    echo "Remove $PID_FILE"
  fi
fi

printf "PostgreSQL init process complete; ready for start up.\n"


