
# error_log /etc/alternatives/logs/nginx/error.log;
# access_log /etc/alternatives/logs/nginx/access.log;

server {
    listen 127.0.0.1:8100 default_server;
    server_name _;
    root /usr/share/nginx/html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://localhost:10100/;
    }

    location = /index.html {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        expires 0;
    }
}