[supervisord]
nodaemon=true
logfile=/home/<USER>/gptbase/logs/supervisor/supervisord.log
pidfile=/run/supervisord.pid
[program:nginx]
command=/usr/sbin/nginx -g "daemon off;"
stdout_logfile = /home/<USER>/gptbase/logs/supervisor/nginx.log
priority=999
[program:opensearch]
directory=/gptbase
command=sh opensearch.sh
stdout_logfile = /home/<USER>/gptbase/logs/supervisor/opensearch.log
priority=1
[program:postgresql]
directory=/usr/lib/postgresql/14/bin
user=cdsw
command=/usr/lib/postgresql/14/bin/postgres -D /home/<USER>/gptbase/postgresql/data
priority=5
startsecs=0
stopwaitsecs=10
stdout_logfile = /home/<USER>/gptbase/logs/supervisor/postgresql.log
[program:qdrant]
directory=/qdrant
command=sh entrypoint.sh
stdout_logfile = /home/<USER>/gptbase/logs/supervisor/qdrant.log
priority=1
[program:redis]
command=/usr/bin/redis-server /etc/redis/redis.conf --daemonize no
stdout_logfile = /home/<USER>/gptbase/logs/supervisor/redis.log
priority=1
[program:backend]
directory=/gptbase/backend
command=sh startup.sh
priority=998
startsecs=0
stdout_logfile = /home/<USER>/gptbase/logs/supervisor/gptbase.log
