FROM qdrant/qdrant:v1.6.1 as qdrant_builder

FROM ubuntu:22.04
USER root

# ADD Qrant
COPY --from=qdrant_builder /qdrant /qdrant

# Install Python
# Note that the package python-is-python3 will alias python3 as python
RUN apt-get update && apt-get install -y --no-install-recommends \
   krb5-user python3.10 python3-pip python-is-python3 ssh xz-utils

# Configure pip to install packages under /usr/local
# when building the Runtime image
RUN pip3 config set install.user false

# Install the Jupyter kernel gateway.
# The IPython kernel is automatically installed 
# under the name python3,
# so below we set the kernel name to python3.
RUN pip3 install "jupyter-kernel-gateway==2.5.2"


# 安装nginx
RUN apt-get install -y nginx redis

# 安装supervisor
RUN apt-get install -y supervisor
RUN mkdir -p /var/log/supervisor

COPY default.conf /etc/nginx/conf.d/default.conf
COPY nginx.conf /etc/nginx/nginx.conf




# Associate uid and gid 8536 with username cdsw
RUN \
  addgroup --gid 8536 cdsw && \
  adduser --disabled-password --gecos "CDSW User" --uid 8536 --gid 8536 cdsw


# Relax permissions to facilitate installation of Cloudera
# client files at startup
RUN for i in /bin /opt /usr /usr/share/java  /var/lib/nginx  /run; do \
   mkdir -p ${i}; \
   chown cdsw ${i}; \
   chmod +rw ${i}; \
   for subfolder in `find ${i} -type d` ; do \
      chown cdsw ${subfolder}; \
      chmod +rw ${subfolder}; \
   done \
 done

RUN for i in /etc /etc/alternatives /var/log/supervisor /var/log/nginx /run; do \
mkdir -p ${i}; \
chmod 777 ${i}; \
done

RUN rm /var/log/nginx/access.log
RUN rm /var/log/nginx/error.log

# Install any additional packages.
# apt-get install ...
# pip install ...

ARG DEBIAN_FRONTEND=noninteractive

RUN apt-get install -y postgresql

ENV POSTGRES_PASSWORD=pwd123456

RUN chown -R cdsw:cdsw /var/lib/postgresql
RUN chown -R cdsw:cdsw /var/log/postgresql
RUN chown -R cdsw:cdsw /etc/ssl/private
RUN chmod 0600 /etc/ssl/private/ssl-cert-snakeoil.key
RUN chown -R cdsw:cdsw /etc/postgresql
RUN chown -R cdsw:cdsw /var/run/postgresql
RUN chown -R cdsw:cdsw /qdrant
RUN chown -R cdsw:cdsw /etc/redis
RUN chown -R cdsw:cdsw /var/log/redis
RUN chown -R cdsw:cdsw /var/lib/redis


RUN apt-get install -y libunwind8

COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY entrypoint.sh /home/<USER>/entrypoint.sh

COPY startup.sh /home/<USER>/startup.sh
RUN chmod +x /home/<USER>/startup.sh

# Final touches are done by the cdsw user to avoid
# permission issues in CML
USER cdsw

# Set up Python symlink to /usr/local/bin/python3
RUN ln -s $(which python) /usr/local/bin/python3

# configure pip to install packages to /home/<USER>
# once the Runtime image is loaded into CML
RUN /bin/bash -c "echo -e '[install]\nuser = true'" > /etc/pip.conf

# Set Runtime label and environment variables metadata
#ML_RUNTIME_EDITOR and ML_RUNTIME_METADATA_VERSION must not be changed.
ENV ML_RUNTIME_EDITOR="PBJ Workbench" \
    ML_RUNTIME_METADATA_VERSION="2" \
    ML_RUNTIME_KERNEL="Python 3.10" \
    ML_RUNTIME_EDITION="Custom Edition" \
    ML_RUNTIME_SHORT_VERSION="1.0" \
    ML_RUNTIME_MAINTENANCE_VERSION="1" \
    ML_RUNTIME_JUPYTER_KERNEL_GATEWAY_CMD="/usr/local/bin/jupyter kernelgateway" \
    ML_RUNTIME_JUPYTER_KERNEL_NAME="python3" \
    ML_RUNTIME_DESCRIPTION="My first Custom PBJ Runtime"
          

ENV ML_RUNTIME_FULL_VERSION="$ML_RUNTIME_SHORT_VERSION.$ML_RUNTIME_MAINTENANCE_VERSION" 

LABEL com.cloudera.ml.runtime.editor=$ML_RUNTIME_EDITOR \
	    com.cloudera.ml.runtime.kernel=$ML_RUNTIME_KERNEL \
	    com.cloudera.ml.runtime.edition=$ML_RUNTIME_EDITION \
	    com.cloudera.ml.runtime.full-version=$ML_RUNTIME_FULL_VERSION \
      com.cloudera.ml.runtime.short-version=$ML_RUNTIME_SHORT_VERSION \
      com.cloudera.ml.runtime.maintenance-version=$ML_RUNTIME_MAINTENANCE_VERSION \
      com.cloudera.ml.runtime.description=$ML_RUNTIME_DESCRIPTION \
      com.cloudera.ml.runtime.runtime-metadata-version=$ML_RUNTIME_METADATA_VERSION

ARG BRANCH_NAME=prd

ENV PGDATA="/home/<USER>/postgresql/data"



WORKDIR /home/<USER>

EXPOSE 80

# 使用CMD指令来运行start.sh脚本
CMD ["/home/<USER>/startup.sh"]