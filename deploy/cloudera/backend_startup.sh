#!/bin/sh
set -e

if [ -f /gptbase/postgresql/data/postmaster.pid ]; then
    # 休眠2秒，等待postgresql启动
    sleep 2
fi

# 初始化DB
DB_NAME=gptbase
DB_EXISTS=$(psql -U cdsw -tAc "SELECT 1 FROM pg_database WHERE datname='$DB_NAME'" postgres) || { echo "Database query failed, exiting."; sleep 2; exit 1; }

if [ "$DB_EXISTS" = '1' ]; then
    echo "Database $DB_NAME already exists, skipping creation."
else
    echo "Database $DB_NAME does not exist, creating."
    psql -U cdsw -c "CREATE DATABASE $DB_NAME WITH TEMPLATE = template0 ENCODING = 'UTF8' LC_COLLATE = 'en_US.UTF-8' LC_CTYPE = 'en_US.UTF-8';" postgres
fi

cd /gptbase/backend
. venv/bin/activate
aerich upgrade
uvicorn mygpt.app:app --host 0.0.0.0 --port 10100