
DOMAIN=http://localhost
LOG_PATH=/home/<USER>/gptbase/logs/gptbase.log

AUTHORIZATION_TYPE=jwt
DEFAULT_USER_PASSWORD=gptbase123

# db config
DB_HOST=localhost
DB_PORT=5432
DB_USER=cdsw
DB_PASSWORD=pwd123456
DB_NAME=gptbase
DB_CHARSET=utf8


DEFAULT_VECTOR_STORAGE=qdrant_one_collection
DEFAULT_FAQ_VECTOR_STORAGE=qdrant_one_collection

UNLIMITED_VERSION=True

VECTOR_STORAGE_QDRANT_URL=http://localhost:6333
VECTOR_STORAGE_QDRANT_HOST=localhost
VECTOR_STORAGE_QDRANT_GRPC_PORT=6334

REDIS_URL=redis://localhost:6379

OPENSEARCH_URL=***********************************

# OPENAI_API_KEY=sk-xxx

# AZURE_OPENAI_ENDPOINT=sk-xxx

# CO_API_KEY=xxx