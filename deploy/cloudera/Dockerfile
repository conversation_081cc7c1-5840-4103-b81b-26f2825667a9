FROM gptbasesparticle/gb_backend as backend_builder
FROM gptbasesparticle/gb_admin as admin_builder
FROM gptbasesparticle/gb_backend:cloudera-base

ARG IMAGE_VERISON

ENV OPENSEARCH_JAVA_HOME=/usr/share/opensearch/jdk
ENV RUN_MODE=production

# 安装supervisor
RUN apt-get update && apt-get install -y supervisor gcc python3-dev
RUN mkdir -p /var/log/supervisor

# 安装GPTBase
COPY --from=backend_builder /backend /gptbase/backend
COPY --from=admin_builder /usr/share/nginx/html /usr/share/nginx/html
# RUN mkdir -p /gptbase/backend/logs && chown -R cdsw:cdsw /gptbase/backend/logs

# 设置python虚拟环境
RUN cd /gptbase/backend && python3 -m venv venv && . venv/bin/activate && pip3 install --isolated -r requirements.txt
COPY backend_startup.sh /gptbase/backend/startup.sh

COPY default.conf /etc/nginx/sites-available/default
COPY nginx.conf /etc/nginx/nginx.conf

COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY entrypoint.sh /gptbase/entrypoint.sh
COPY env /gptbase/backend/.env

# RUN mkdir -p /gptbase/postgresql/data
# RUN chown -R cdsw:cdsw /gptbase/postgresql/data

RUN mkdir -p /var/run/postgresql
RUN chown -R cdsw:cdsw /var/run/postgresql

COPY startup.sh /gptbase/startup.sh
COPY opensearch.sh /gptbase/opensearch.sh
COPY startup.py /gptbase/startup.py
RUN chmod +x /gptbase/startup.sh
RUN chmod +x /gptbase/opensearch.sh
RUN chmod +x /gptbase/entrypoint.sh

RUN for i in /var/log/supervisor; do \
mkdir -p ${i}; \
chmod 777 ${i}; \
done

RUN chmod 755 /usr/sbin/nginx && chmod u+s /usr/sbin/nginx

RUN locale-gen en_US.UTF-8

USER cdsw

# Set up Python symlink to /usr/local/bin/python3
RUN ln -s $(which python) /usr/local/bin/python3

# configure pip to install packages to /home/<USER>
# once the Runtime image is loaded into CML
RUN /bin/bash -c "echo -e '[install]\nuser = true'" > /etc/pip.conf

# Set Runtime label and environment variables metadata
#ML_RUNTIME_EDITOR and ML_RUNTIME_METADATA_VERSION must not be changed.
ENV ML_RUNTIME_EDITOR="PBJ Workbench" \
    ML_RUNTIME_METADATA_VERSION="2" \
    ML_RUNTIME_KERNEL="Python 3.10" \
    ML_RUNTIME_EDITION="Custom Edition" \
    ML_RUNTIME_SHORT_VERSION="1.0" \
    ML_RUNTIME_MAINTENANCE_VERSION=${IMAGE_VERISON} \
    ML_RUNTIME_JUPYTER_KERNEL_GATEWAY_CMD="/usr/local/bin/jupyter kernelgateway" \
    ML_RUNTIME_JUPYTER_KERNEL_NAME="python3" \
    ML_RUNTIME_DESCRIPTION="My first Custom PBJ Runtime"
          

ENV ML_RUNTIME_FULL_VERSION="$ML_RUNTIME_SHORT_VERSION.$ML_RUNTIME_MAINTENANCE_VERSION" 

LABEL com.cloudera.ml.runtime.editor=$ML_RUNTIME_EDITOR \
	  com.cloudera.ml.runtime.kernel=$ML_RUNTIME_KERNEL \
	  com.cloudera.ml.runtime.edition=$ML_RUNTIME_EDITION \
	  com.cloudera.ml.runtime.full-version=$ML_RUNTIME_FULL_VERSION \
      com.cloudera.ml.runtime.short-version=$ML_RUNTIME_SHORT_VERSION \
      com.cloudera.ml.runtime.maintenance-version=$ML_RUNTIME_MAINTENANCE_VERSION \
      com.cloudera.ml.runtime.description=$ML_RUNTIME_DESCRIPTION \
      com.cloudera.ml.runtime.runtime-metadata-version=$ML_RUNTIME_METADATA_VERSION

ARG BRANCH_NAME=prd

ENV TZ="Asia/Tokyo"

WORKDIR /gptbase
EXPOSE 80

# CMD ["bash", "-c", "/home/<USER>/startup.sh"]