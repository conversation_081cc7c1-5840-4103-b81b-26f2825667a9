from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "plans" ADD "multi_modal_parsing_quota_grant_type" VARCHAR(7) NOT NULL  DEFAULT 'monthly';
        ALTER TABLE "plans" ADD "web_page_quota_grant_type" VARCHAR(7) NOT NULL  DEFAULT 'monthly';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "plans" DROP COLUMN "multi_modal_parsing_quota_grant_type";
        ALTER TABLE "plans" DROP COLUMN "web_page_quota_grant_type";"""
