from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "apikey" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "user_id" VARCHAR(100) NOT NULL,
    "api_key" VARCHAR(50) NOT NULL
);
CREATE TABLE IF NOT EXISTS "robot" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "name" VARCHAR(100) NOT NULL,
    "discrible" TEXT,
    "user_id" VARCHAR(100) NOT NULL,
    "prompt" TEXT,
    "robot_type" VARCHAR(20) NOT NULL  DEFAULT 'demo'
);
COMMENT ON COLUMN "robot"."robot_type" IS 'DEMO: demo';
CREATE TABLE IF NOT EXISTS "vector" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "vector_text" TEXT,
    "index_id" VARCHAR(100),
    "text_type" VARCHAR(20) NOT NULL,
    "robot_id" UUID NOT NULL REFERENCES "robot" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "vector"."text_type" IS 'ORIGINAL: original\nEN: en\nUP_ORIGINAL: up_original\nUP_EN: up_en';
CREATE TABLE IF NOT EXISTS "vectorfile" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "key" VARCHAR(100),
    "filename" VARCHAR(100),
    "file_type" VARCHAR(20) NOT NULL,
    "robot_id" UUID NOT NULL REFERENCES "robot" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "vectorfile"."file_type" IS 'UPLOAD: upload\nCONVERT: convert';
CREATE TABLE IF NOT EXISTS "aerich" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "version" VARCHAR(255) NOT NULL,
    "app" VARCHAR(100) NOT NULL,
    "content" JSONB NOT NULL
);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
