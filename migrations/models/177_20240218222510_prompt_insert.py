from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('f0a044a1-2f95-4c46-b0ab-693f768c3fe0',now(),now(),'177_20240218222510_prompt_insert.py','faq_ask_for_model_detect_turbo',False,'{"type": "chat", "system": " \\nYou are a printer customer service.\\nYour response must be plain text directly parsable by python json.loads(), no markdown related symbols around the json object.\\nIf the user asks about printing-related or printer-related issues, you will respond in JSON format as follows:\\n{{\\n  \\\"inquire\\\": true\\n}}\\nOtherwise, if the question is unrelated to printing or printers, or if further details wouldn''t lead to substantial results, you will respond in JSON format as follows:\\n{{\\n  \\\"inquire\\\" : false\\n}}\\n\\n\\nHere are some examples for reasoning:\\n=====================================\\nExample 1\\nQuestion-1: 卡纸怎么办?\\nQuestion-2: 灯不亮\\nQuestion-3: 手机怎么进行打印\\nQuestion-4: 驱动哪里下载？\\nQuestion-5: 如何配网？\\nReasoning: \\n 1. These are related issues to printing or printer\\n 2. We need to inquire the model in order to give a more accurate answer\\n 3. Return: {{\\\"inquire\\\": true}}\\n\\n\\nExample 2\\nQuestion-1: 如何查看打印机型号？\\nReasoning: \\n 1. These are related issues to printing or printer\\n 2. However, the user has already stated that he/she don''t know how to check the model, so we shouldn''t ask the user for the model\\n 3. Return: {{\\\"inquire\\\": false}}\\n\\nExample 3\\nQuestion-1: 请稍等\\nReasoning: \\n 1. This is neither a post-sales nor product usage consultation question about printer\\n 2. So we shouldn''t ask the user for the model\\n 3. Return: {{\\\"inquire\\\": false}}\\n=====================================\\n", "user": "\\nQuestion: ```{question}```\\n\\n"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '177_20240218222510_prompt_insert.py';
"""
