from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "exceltesttask" ADD "is_scheduled" BOOL NOT NULL  DEFAULT False;
        CREATE TABLE IF NOT EXISTS "timedexceltestfile" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "name" VARCHAR(1024) NOT NULL,
    "unique_name" VARCHAR(1124) NOT NULL,
    "s3_file_url" VARCHAR(2000) NOT NULL
);;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "exceltesttask" DROP COLUMN "is_scheduled";
        DROP TABLE IF EXISTS "timedexceltestfile";"""
