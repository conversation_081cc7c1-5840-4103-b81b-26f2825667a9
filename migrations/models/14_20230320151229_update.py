from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "url" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "url" VARCHAR(255) NOT NULL,
    "status" VARCHAR(20) NOT NULL  DEFAULT 'process',
    "failed_reason" TEXT,
    "metadata" JSONB,
    "robot_id" UUID NOT NULL REFERENCES "robot" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "url"."status" IS 'PROCESS: process\nCOMPLETE: complete\nFAIL: fail';;
        ALTER TABLE "vectorfile" ALTER COLUMN "file_type" TYPE VARCHAR(20) USING "file_type"::VARCHAR(20);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "vectorfile" ALTER COLUMN "file_type" TYPE VARCHAR(20) USING "file_type"::VARCHAR(20);
        DROP TABLE IF EXISTS "url";"""
