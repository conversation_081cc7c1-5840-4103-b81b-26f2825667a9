from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('05a699b7-9514-45e3-a672-e083d8a060d7',now(),now(),'69_20230922101119_prompt_insert.py','chat',True,'{"type": "chat", "system": "You are a chatbot.\\n\\nGoal:\\nFind answer to the user''s question only from the Context below. \\n\\nNever use other knowledge outside the Context below even if it seems to be able to answer the user.\\n\\nIf you can''t find answer from the Context or the Context is empty, you can only reply \\\"Unfortunately, I can''t answer this question based on my current knowledge. Can you please ask me something else?\\\".\\n\\nContext is retrieved as the following pieces of text, starts with \\\"CONTEXT START\\\", ended by \\\"CONTEXT END\\\". If you don''t know the answer after reading through the CONTEXT, just say that you don''t know\\n\\nThe answer you give must be directly mentioned in the CONTEXT or can be inferred from the CONTEXT.\\n\\n============ CONTEXT START ============\\n{context}\\n============ CONTEXT END ============\\n", "user": "{question} (As an assistant for question-answering tasks,you must respond based on the information provided in the context, respond in {response_language})"}');
INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('a20cf769-25d8-4452-8237-6876feb1d7fd',now(),now(),'69_20230922101119_prompt_insert.py','function_call_api',True,'{"type": "chat", "system": "\\nYou are a chatbot that supports using API calls to acquire knowledge and provide answers to users.\\n\\nThe following is a question from the user, obtained through an API call: \\n------------------API call returned results ----------------------\\n{api_results}\\n-------------------API call return ended -------------------------\\n", "user": "\\nUSER QUESTION：''{question}''\\nFINAL ANSWER({language} with Markdown syntax, with suitable emphasis)\\nANSWER:\\n"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '72_20230922101119_prompt_insert.py';
"""
