from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "insiderpreviewuser" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "robot_id" UUID REFERENCES "robot" ("id") ON DELETE CASCADE,
    "user_id" VARCHAR(100) REFERENCES "user" ("user_id") ON DELETE CASCADE
);;
        CREATE TABLE IF NOT EXISTS "prompt" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMES<PERSON>MP,
    "deleted_at" TIMESTAMPTZ,
    "version" VARCHAR(255),
    "prompt_type" VARCHAR(100) NOT NULL,
    "is_insider" BOOL NOT NULL  DEFAULT True,
    "content" JSONB NOT NULL
);
COMMENT ON COLUMN "prompt"."prompt_type" IS 'INTENT: intent\nCHAT: chat\nLANUAGE_DETECT: language_detect\nFAQ_ANSWER_TURBO: faq_answer_turbo\nFAQ_ASK_USER_TO_PROVIDE_MODEL_TURBO: ask_user_to_provide_model_turbo\nFAQ_ASK_FOR_MODEL_DETECT_TURBO: faq_ask_for_model_detect_turbo\nFAQ_MODEL_DETECT_TURBO: faq_model_detect_turbo\nFUNCTION_CALL: function_call\nFUNCTION_CALL_API: function_call_api';;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "insiderpreviewuser";
        DROP TABLE IF EXISTS "prompt";"""
