from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "addon_subscriptions" DROP COLUMN "status";
        ALTER TABLE "addon_subscriptions" ALTER COLUMN "expires_at" DROP DEFAULT;
        ALTER TABLE "plan_subscriptions" DROP COLUMN "status";
        ALTER TABLE "plan_subscriptions" ALTER COLUMN "expires_at" DROP DEFAULT;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "plan_subscriptions" ADD "status" VARCHAR(9) NOT NULL;
        ALTER TABLE "plan_subscriptions" ALTER COLUMN "expires_at" SET DEFAULT '2025-04-11 11:58:42.520522';
        ALTER TABLE "addon_subscriptions" ADD "status" VARCHAR(9) NOT NULL;
        ALTER TABLE "addon_subscriptions" ALTER COLUMN "expires_at" SET DEFAULT '2025-04-11 11:58:42.557331';"""
