from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "dataset" ADD "collection_name" VARCHAR(36);
        ALTER TABLE "dataset" ADD "descirption" TEXT;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "dataset" DROP COLUMN "collection_name";
        ALTER TABLE "dataset" DROP COLUMN "descirption";"""
