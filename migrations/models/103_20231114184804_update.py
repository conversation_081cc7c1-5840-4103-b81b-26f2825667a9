from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "sessionmessage" DROP COLUMN "feedback_metadata";
        CREATE TABLE IF NOT EXISTS "sessionmessagemetadata" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "key" VARCHAR(100) NOT NULL,
    "value" VARCHAR(100) NOT NULL,
    "session_message_id" UUID REFERENCES "sessionmessage" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_sessionmess_session_8cc344" ON "sessionmessagemetadata" ("session_message_id");;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "sessionmessage" ADD "feedback_metadata" JSONB;
        DROP TABLE IF EXISTS "sessionmessagemetadata";"""
