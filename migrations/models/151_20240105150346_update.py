from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "robotprompt" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "prompt" TEXT,
    "robot_id" UUID NOT NULL REFERENCES "robot" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_robotprompt_robot_i_d4f0ef" ON "robotprompt" ("robot_id");;
        CREATE TABLE IF NOT EXISTS "robotpromptenvrionment" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "envrionment_key" VARCHAR(100) NOT NULL,
    "envrionment_value" TEXT NOT NULL,
    "prompt_id" UUID NOT NULL REFERENCES "robotprompt" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_robotprompt_prompt__218869" ON "robotpromptenvrionment" ("prompt_id");;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "robotprompt";
        DROP TABLE IF EXISTS "robotpromptenvrionment";"""
