from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        -- 删除旧的外键约束
        ALTER TABLE "vectorfile" DROP CONSTRAINT IF EXISTS "fk_vectorfi_integrat_2206c649";

        -- 删除旧的索引
        DROP INDEX IF EXISTS "idx_vectorfile_integra_5ea079";

        -- 删除旧的字段
        ALTER TABLE "vectorfile" DROP COLUMN IF EXISTS "integration_rule_id";
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        -- 恢复旧的字段
    ALTER TABLE "vectorfile" ADD COLUMN "integration_rule_id" UUID;

    -- 恢复旧的索引
    CREATE INDEX IF NOT EXISTS "idx_vectorfile_integra_5ea079" ON "vectorfile" ("integration_rule_id");

    -- 恢复旧的外键约束
    ALTER TABLE "vectorfile" ADD CONSTRAINT "fk_vectorfi_integrat_2206c649" FOREIGN KEY ("integration_rule_id") REFERENCES "integration_rule" ("id") ON DELETE CASCADE;
        ;"""
