from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "imageattachment" ADD "data_type" VARCHAR(20) NOT NULL  DEFAULT 'robot';
        ALTER TABLE "imageattachment" ADD "data_id" VARCHAR(255);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "imageattachment" DROP COLUMN "data_type";
        ALTER TABLE "imageattachment" DROP COLUMN "data_id";"""
