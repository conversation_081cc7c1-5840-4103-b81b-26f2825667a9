from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "datasetrobot";
        CREATE TABLE "dataset_robot" (
    "dataset_id" UUID NOT NULL REFERENCES "dataset" ("id") ON DELETE CASCADE,
    "robot_id" UUID NOT NULL REFERENCES "robot" ("id") ON DELETE CASCADE
);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "dataset_robot";"""
