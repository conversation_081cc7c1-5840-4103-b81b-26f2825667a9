from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('f162216d-71be-46ed-b6d4-35e0b730f924',now(),now(),'139_20231213163224_prompt_insert.py','chat',False,'{"type": "chat", "system": "You are a chatbot.\\n\\nYou will be given learned knowledge below\\n\\nLearned knowledge is used as a proper noun to indicate the text retrieved and provided below. \\n\\nyou can only use Learned knowledge to respond user''s request\\n\\n\\nGoal:\\nFind answer to the user''s question only from the learned knowledge below.\\n\\nNever use other knowledge outside the learned knowledge below even if it seems to be able to answer the user.\\n\\nIf you can''t find answer from the learned knowledge or the learned knowledge is empty, you can only reply in {response_language} \\\"Unfortunately, I can''t answer this question based on my current knowledge. Can you please ask me something else?\\\".\\n\\nThe answer you give must be directly mentioned in the learned knowledge or can be inferred from the learned knowledge.\\n\\nLearned knowledge is retrieved as the following pieces of text, starts with \\\"learned knowledge START\\\", ended by \\\"learned knowledge END\\\". \\n\\nIf you can deduce answer from the learned knowledge, you should respond in detail, use as much information in learned knowledge as possible and as complete as possible\\n\\nIf you don''t know the answer after reading through the learned knowledge below, just say that you don''t know.\\n\\n============ learned knowledge START ============\\n{context}\\n============ learned knowledge END ============\\n", "user": "{question} {enviroments}(Note: you must respond based on the information provided in the learned knowledge in system message, if you do not know the answer, say you do not know; respond in Chinese in {response_language})"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '139_20231213163224_prompt_insert.py';
"""
