from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
    DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_name = 'vectorfile' AND column_name = 'integration_rule_id'
        ) THEN
            ALTER TABLE "vectorfile" ADD "integration_rule_id" UUID;
        END IF;
    END $$;

    DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1 FROM pg_indexes
            WHERE tablename = 'vectorfile' AND indexname = 'idx_vectorfile_integra_5ea079'
        ) THEN
            CREATE INDEX "idx_vectorfile_integra_5ea079" ON "vectorfile" ("integration_rule_id");
        END IF;
    END $$;

    DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints
            WHERE constraint_name = 'fk_vectorfi_integrat_2206c649'
        ) THEN
            ALTER TABLE "vectorfile" ADD CONSTRAINT "fk_vectorfi_integrat_2206c649" FOREIGN KEY ("integration_rule_id") REFERENCES "integration_rule" ("id") ON DELETE CASCADE;
        END IF;
    END $$;
    """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
    ALTER TABLE "vectorfile" DROP CONSTRAINT IF EXISTS "fk_vectorfi_integrat_2206c649";
    DROP INDEX IF EXISTS "idx_vectorfile_integra_5ea079";
    ALTER TABLE "vectorfile" DROP COLUMN IF EXISTS "integration_rule_id";
    """
