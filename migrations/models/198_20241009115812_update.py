from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "integration_rule" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "type" VARCHAR(100) NOT NULL,
    "dataset_id" UUID NOT NULL REFERENCES "dataset" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "integration_rule"."type" IS 'SIMPLE_URL: simple_url\nGITBOOK: gitbook\nSITEMAP: sitemap\nLARK: lark';
        CREATE TABLE IF NOT EXISTS "lark_file" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "name" VARCHAR(1024) NOT NULL,
    "type" VARCHAR(100) NOT NULL,
    "token" VARCHAR(255) NOT NULL,
    "parent_token" VARCHAR(255),
    "created_time" TIMESTAMPTZ NOT NULL,
    "modified_time" TIMESTAMPTZ NOT NULL,
    "owner_id" VARCHAR(255),
    "url" VARCHAR(2048) NOT NULL,
    "vector_file_id" UUID NOT NULL UNIQUE REFERENCES "vectorfile" ("id") ON DELETE CASCADE
);
        CREATE TABLE IF NOT EXISTS "lark_integration_rule" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "app_id" VARCHAR(100) NOT NULL,
    "app_secret" VARCHAR(100) NOT NULL,
    "notification_emails" JSONB,
    "integration_rule_id" UUID NOT NULL UNIQUE REFERENCES "integration_rule" ("id") ON DELETE CASCADE
);
        CREATE TABLE IF NOT EXISTS "lark_share_url" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "url" VARCHAR(2048) NOT NULL,
    "recursive" BOOL NOT NULL,
    "integration_rule_id" UUID NOT NULL REFERENCES "lark_integration_rule" ("id") ON DELETE CASCADE
);
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "sessionmessage" ALTER COLUMN "model" TYPE VARCHAR(100) USING "model"::VARCHAR(100);
        ALTER TABLE "userconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "vectorfile" ADD "source_type" VARCHAR(100);
        ALTER TABLE "vectorfile" ALTER COLUMN "filename" TYPE VARCHAR(2048) USING "filename"::VARCHAR(2048);
        ALTER TABLE "vectorfile" ALTER COLUMN "file_type" TYPE VARCHAR(20) USING "file_type"::VARCHAR(20);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "userconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "vectorfile" DROP COLUMN "source_type";
        ALTER TABLE "vectorfile" ALTER COLUMN "filename" TYPE VARCHAR(2048) USING "filename"::VARCHAR(2048);
        ALTER TABLE "vectorfile" ALTER COLUMN "file_type" TYPE VARCHAR(20) USING "file_type"::VARCHAR(20);
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "sessionmessage" ALTER COLUMN "model" TYPE VARCHAR(100) USING "model"::VARCHAR(100);
        DROP TABLE IF EXISTS "integration_rule";
        DROP TABLE IF EXISTS "lark_file";
        DROP TABLE IF EXISTS "lark_integration_rule";
        DROP TABLE IF EXISTS "lark_share_url";"""
