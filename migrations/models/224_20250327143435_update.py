from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "plans" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "name" VARCHAR(50) NOT NULL UNIQUE,
    "plan_type" VARCHAR(14) NOT NULL,
    "monthly_price" INT,
    "description" TEXT,
    "storage_limit" INT NOT NULL  DEFAULT -1,
    "bot_limit" INT NOT NULL  DEFAULT -1,
    "monthly_message_limit" INT NOT NULL  DEFAULT -1,
    "web_page_limit" INT NOT NULL  DEFAULT -1,
    "multi_modal_parsing_limit" INT NOT NULL  DEFAULT -1,
    "enable_agent" BOOL NOT NULL  DEFAULT False,
    "enable_multi_modal_parsing" BOOL NOT NULL  DEFAULT False,
    "enable_voice_chat" BOOL NOT NULL  DEFAULT False,
    "enable_large_screen_ui" BOOL NOT NULL  DEFAULT False,
    "enable_canvas" BOOL NOT NULL  DEFAULT False,
    "enable_collaboration" BOOL NOT NULL  DEFAULT False,
    "enable_widget_customization" BOOL NOT NULL  DEFAULT False,
    "enable_chat_history_analysis" BOOL NOT NULL  DEFAULT False,
    "enable_2d_digital_human" BOOL NOT NULL  DEFAULT False,
    "available_llm_models" JSONB NOT NULL
);
COMMENT ON COLUMN "plans"."plan_type" IS 'TRIAL: trial\nSTARTER: starter\nPRO: pro\nBUSINESS: business\nBUSINESS_ELITE: business_elite';
COMMENT ON TABLE "plans" IS '套餐模型定义：用于定义各种功能的开启状态，以及各种操作的额度';;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "plans";"""
