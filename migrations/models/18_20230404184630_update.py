from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "openaiapikey" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "api_key" VARCHAR(60) NOT NULL UNIQUE,
    "balance" DOUBLE PRECISION NOT NULL  DEFAULT 18,
    "status" VARCHAR(20) NOT NULL  DEFAULT 'nomarl',
    "in_use" BOOL NOT NULL  DEFAULT True,
    "user_id" VARCHAR(100) NOT NULL REFERENCES "user" ("user_id") ON DELETE CASCADE
);
COMMENT ON COLUMN "openaiapikey"."status" IS 'NORMAl: nomarl\nINSUFFICIENT_BALANCE: insufficient_balance\nERROR: error';;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "openaiapikey";"""
