from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "session" ADD "created_ip" VARCHAR(100);
        ALTER TABLE "session" ADD "created_by" VARCHAR(100);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "session" DROP COLUMN "created_ip";
        ALTER TABLE "session" DROP COLUMN "created_by";"""
