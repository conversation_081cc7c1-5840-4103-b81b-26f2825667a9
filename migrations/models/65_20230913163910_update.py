from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "imageattachment" DROP CONSTRAINT "fk_imageatt_dataset_db34a75b";
        ALTER TABLE "imageattachment" DROP COLUMN "dataset_id";"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "imageattachment" ADD "dataset_id" UUID;
        ALTER TABLE "imageattachment" ADD CONSTRAINT "fk_imageatt_dataset_db34a75b" FOREIGN KEY ("dataset_id") REFERENCES "dataset" ("id") ON DELETE CASCADE;"""
