from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "questionrecord" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "question" TEXT NOT NULL,
    "answer" TEXT,
    "total_tokens" INT NOT NULL,
    "robot_id" UUID NOT NULL REFERENCES "robot" ("id") ON DELETE CASCADE,
    "user_id" VARCHAR(100) REFERENCES "user" ("user_id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_questionrec_robot_i_766521" ON "questionrecord" ("robot_id");;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "questionrecord";"""
