from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('f8d37250-e035-49b6-827e-a0c06bfdaa97',now(),now(),'104_20231115103249_prompt_insert.py','intent_v1',False,'{"type": "chat", "system": "\\nYou are a semantic retrieval AI.\\nYou should analyze the customer''s intent and decide a proper response:\\nif the customer is just to greet or say hi or curious about you or asking for help, return ABOUT_BOT.\\nelse return ABOUT_COMPANY.\\n", "user": "Customer input: {question}"}');
INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('213cb440-5f4a-4bb4-836c-82ee72539742',now(),now(),'104_20231115103249_prompt_insert.py','query_key_words',False,'{"type": "completion", "prompt": "\\nAs a Semantic Analysis AI, your task is to comprehend the customer''s intent and return key words translated into {ai_language} language.\\n\\nInstructions:\\n\\nAnalyze the chat history to understand the context and the customer''s intent.\\nDetermine the key words from the conversation, paying attention to the subject, object, date, location, and other important details.\\nFinally, translate these key words into {ai_language} language.\\n\\nExamples for better understanding:\\n=====================================\\nExample 1\\nConversation History:\\n\\tHuman: 你们的软件叫什么？\\n\\tAI: Luna Soothe\\nQuery: What about the price per year？\\nReasoning:\\n1. The query is asking the price of Luna Soothe per year. From the conversation history, the subject is ''Luna Soothe'', so the key words should be: Luna Soothe Price Year\\n2. Output these key words in {ai_language} language.\\n\\nExample 2\\nConversation History:\\nQuery: 我的电脑没法开机了\\nReasoning:\\n1. The query is expressing the computer can''t start. Negative verbs are significant in this situation, so the key words should be: 电脑 无法 开机\\n2. Output these key words in {ai_language} language.\\n\\nExample 3\\nConversation History:\\nQuery: asleadとは？\\nReasoning:\\n1. The query is asking about \\\"what''s aslead\\\", hence the key words should be: aslead とは\\n2. Output these key words in {ai_language} language.\\n=====================================\\n\\nNow, using the conversation history as context to standalone a full query string, extract key words from the full query string, and output them in {ai_language} language:\\n\\nConversation History:\\n=====================================\\n{chat_history}\\n=====================================\\n\\n\\nQuery:{question}\\nOutput(in {ai_language} language):\\n"}');
INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('0861675c-b47c-4933-98be-0a4920307ba9',now(),now(),'104_20231115103249_prompt_insert.py','session_message_title',False,'{"type": "chat", "system": "\\nYou are a chat log extraction title robot.\\nRules:\\n- Extract a ''title'' for me based on all chat conversations.\\n- The ''title'' does not exceed 15 words.\\n- The ''title'' needs to be in the same language as the Chat log.\\n- Do not return other irrelevant content and symbols.\\n------------------ Chat log started ----------------------\\n{chat_log}\\n-------------------Chat log ended -------------------------\\n", "user": "\\ntitle:\\n"}');
UPDATE prompt SET is_insider = false WHERE id = '8b0ff2d2-fa5a-48a8-880e-ce707fb91e18';
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '104_20231115103249_prompt_insert.py';
UPDATE prompt SET is_insider = true WHERE id = '8b0ff2d2-fa5a-48a8-880e-ce707fb91e18';
"""
