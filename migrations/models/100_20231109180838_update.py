from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "faqs" ADD "source_type" VARCHAR(100) NOT NULL  DEFAULT 'user_input';
        ALTER TABLE "sessionmessage" ADD "feedback_metadata" JSONB;
        ALTER TABLE "sessionmessage" ADD "feedback_content" TEXT;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "faqs" DROP COLUMN "source_type";
        ALTER TABLE "sessionmessage" DROP COLUMN "feedback_metadata";
        ALTER TABLE "sessionmessage" DROP COLUMN "feedback_content";"""
