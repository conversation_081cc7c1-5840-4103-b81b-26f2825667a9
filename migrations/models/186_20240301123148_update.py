from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "url" ALTER COLUMN "status" SET DEFAULT 'ready';
        ALTER TABLE "url" ALTER COLUMN "status" TYPE VARCHAR(20) USING "status"::VARCHAR(20);
        ALTER TABLE "vectorfile" ALTER COLUMN "file_status" SET DEFAULT 'ready';
        ALTER TABLE "vectorfile" ALTER COLUMN "file_status" TYPE VARCHAR(20) USING "file_status"::VARCHAR(20);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "url" ALTER COLUMN "status" SET DEFAULT 'process';
        ALTER TABLE "url" ALTER COLUMN "status" TYPE VARCHAR(20) USING "status"::VARCHAR(20);
        ALTER TABLE "vectorfile" ALTER COLUMN "file_status" SET DEFAULT 'process';
        ALTER TABLE "vectorfile" ALTER COLUMN "file_status" TYPE VARCHAR(20) USING "file_status"::VARCHAR(20);"""
