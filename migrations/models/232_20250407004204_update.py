from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "plan_subscriptions" ADD "is_cancelled" BOOL NOT NULL  DEFAULT False;
        ALTER TABLE "plan_subscriptions" DROP COLUMN "operation_status";"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "plan_subscriptions" ADD "operation_status" VARCHAR(9) NOT NULL;
        ALTER TABLE "plan_subscriptions" DROP COLUMN "is_cancelled";"""
