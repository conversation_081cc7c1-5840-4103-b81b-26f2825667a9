from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "stripeproducts" RENAME COLUMN "max_number" TO "max_questions";
        ALTER TABLE "stripeproducts" ADD "max_upload_file" INT   DEFAULT 0;
        ALTER TABLE "stripeproducts" ADD "param_ext" JSONB;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "stripeproducts" RENAME COLUMN "max_questions" TO "max_number";
        ALTER TABLE "stripeproducts" ADD "max_number" INT   DEFAULT 0;
        ALTER TABLE "stripeproducts" DROP COLUMN "max_upload_file";
        ALTER TABLE "stripeproducts" DROP COLUMN "param_ext";"""
