from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "robotconfig" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "key" VARCHAR(100) NOT NULL,
    "value" TEXT,
    "robot_id" UUID NOT NULL REFERENCES "robot" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_robotconfig_robot_i_3e5d80" ON "robotconfig" ("robot_id");
COMMENT ON COLUMN "robotconfig"."key" IS 'SOURCE_LANG: source_lang\nTRANSLATE_QUESTION: translate_question';;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "robotconfig";"""
