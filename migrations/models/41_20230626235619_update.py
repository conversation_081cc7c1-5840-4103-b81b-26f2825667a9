from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "exceltesttask" ADD "pass_count" INT   DEFAULT 0;
        ALTER TABLE "exceltesttask" ADD "fail_count" INT   DEFAULT 0;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "exceltesttask" DROP COLUMN "pass_count";
        ALTER TABLE "exceltesttask" DROP COLUMN "fail_count";"""
