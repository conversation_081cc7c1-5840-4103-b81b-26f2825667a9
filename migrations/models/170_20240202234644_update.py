from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "sessionmetricshourly" ADD "unanswered_count" INT   DEFAULT 0;
        ALTER TABLE "sessionmetricshourly" ADD "evaluation_answer_count" INT   DEFAULT 0;
        ALTER TABLE "sessionmetricshourly" ADD "evaluation_source_count" INT   DEFAULT 0;
        ALTER TABLE "sessionmetricshourly" ADD "evaluation_score_count" INT   DEFAULT 0;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "sessionmetricshourly" DROP COLUMN "unanswered_count";
        ALTER TABLE "sessionmetricshourly" DROP COLUMN "evaluation_answer_count";
        ALTER TABLE "sessionmetricshourly" DROP COLUMN "evaluation_source_count";
        ALTER TABLE "sessionmetricshourly" DROP COLUMN "evaluation_score_count";"""
