from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "robotaccessstatistics" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "type" VARCHAR(100) NOT NULL  DEFAULT 'pv',
    "date" DATE,
    "count" INT   DEFAULT 0,
    "robot_id" UUID NOT NULL REFERENCES "robot" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_robotaccess_robot_i_c2a174" ON "robotaccessstatistics" ("robot_id");
COMMENT ON COLUMN "robotaccessstatistics"."type" IS 'PV: pv';;
     """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "robotaccessstatistics";"""
