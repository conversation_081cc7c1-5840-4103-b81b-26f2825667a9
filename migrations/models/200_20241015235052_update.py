from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "vectorfile" ADD "integration_rule_id" UUID;
        ALTER TABLE "vectorfile" ADD CONSTRAINT "fk_vectorfi_integrat_2206c649" FOREIGN KEY ("integration_rule_id") REFERENCES "integration_rule" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "vectorfile" DROP CONSTRAINT "fk_vectorfi_integrat_2206c649";
        ALTER TABLE "vectorfile" DROP COLUMN "integration_rule_id";"""
