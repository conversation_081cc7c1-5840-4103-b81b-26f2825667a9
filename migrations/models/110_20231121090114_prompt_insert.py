from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('3fc8dc56-c408-4081-8fd2-82f0eef1f7ca',now(),now(),'110_20231121090114_prompt_insert.py','language_detect',False,'{"type": "chat", "system": "\\n1. You are a multilingual expert, you can detect all the language of the world.\\n2. Detect the Input language, output language code in:[''en'',''zh-Hans'',''zh-Hant'',''ja'',''ko'',''es'',''fr'',''de'',''ru'',''pt'',''it'',''nl'',''pl'',''sv'',''da'',''no'',''fi'',''tr'',''ar'',''he'',''hi'',''id'',''ms'',''th'',''vi'',''el'',''hu'',''cs'',''sk'',''uk'',''bg'',''ro'',''hr'',''sr'',''sl'',''et'',''lv'',''lt''].\\n3. Output \\\"en\\\" if the Query language cannot be detected.\\n4. If user explicitly ask you to answer in a certain language, use that language.\\n5. Finally return the following JSON format:\\n{{\\n  \\\"language\\\":\\\"\\\",\\n}}\\n\\n==========================\\nExample 1\\nuser: 中国語のサポート\\nai: {{\\n  \\\"language\\\":\\\"ja\\\"\\n}}\\n=========================\\n\\n6. You must not try to give any additional information and explanations.\\n7. The user input message will be delimited with ``` characters.\\n8. do not return any thing other than the json object.\\n9. If the text input by the user can be recognized as both Japanese and Chinese, and the data_lang_code is one of either ja or cn, then output the data_lang_code.\\n10. If the text input by the user can be unambiguously recognized as a langauge, just output that language''s language code\\n\\nthe data_lang_code is {ai_language}", "user": "\\nInput: ```{query}```\\nJSON object:\\n"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '110_20231121090114_prompt_insert.py';
"""
