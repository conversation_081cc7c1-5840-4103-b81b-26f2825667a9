from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "add_ons" RENAME COLUMN "effect_data" TO "value";
        ALTER TABLE "add_ons" ALTER COLUMN "target_field" TYPE VARCHAR(25) USING "target_field"::VARCHAR(25);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "add_ons" RENAME COLUMN "value" TO "effect_data";
        ALTER TABLE "add_ons" ALTER COLUMN "target_field" TYPE VARCHAR(25) USING "target_field"::VARCHAR(25);"""
