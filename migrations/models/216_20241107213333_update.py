from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "agentthoughtmessage" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "robot_id" UUID,
    "session_id" VARCHAR(100),
    "message_id" UUID,
    "user_input" TEXT NOT NULL,
    "iteration" SMALLINT NOT NULL  DEFAULT 0,
    "thought_message" TEXT,
    "action_name" VARCHAR(255),
    "action_args" JSONB,
    "is_interface_call" BOOL NOT NULL  DEFAULT False,
    "agent_function_call_api_id" UUID,
    "agent_function_call_request" JSONB,
    "agent_function_call_return" TEXT,
    "agent_function_call_duration" DECIMAL(7,3),
    "tool_return" TEXT,
    "iteration_duration" DECIMAL(7,3)
);
CREATE INDEX IF NOT EXISTS "idx_agentthough_robot_i_837d55" ON "agentthoughtmessage" ("robot_id");
CREATE INDEX IF NOT EXISTS "idx_agentthough_session_913f19" ON "agentthoughtmessage" ("session_id");
COMMENT ON TABLE "agentthoughtmessage" IS 'Agent的思考消息表, 记录Agent的思考过程, 工具调用结果, 模型最终的生辰结果. 主要应用于问题的排查分析';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """SELECT 1 WHERE FALSE;"""
