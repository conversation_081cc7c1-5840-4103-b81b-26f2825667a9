from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "sessionevaluate" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "status" VARCHAR(20) NOT NULL  DEFAULT 'pending',
    "satisfaction" DOUBLE PRECISION   DEFAULT 0,
    "session_id" VARCHAR(100) NOT NULL UNIQUE REFERENCES "session" ("session_id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_sessioneval_session_80b7b7" ON "sessionevaluate" ("session_id");
COMMENT ON COLUMN "sessionevaluate"."status" IS 'PENDING: pending\nAMENDED: amended';
COMMENT ON TABLE "sessionevaluate" IS 'Session 评分';;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "sessionevaluate";"""
