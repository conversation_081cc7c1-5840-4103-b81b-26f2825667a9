from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "questionrecommended" ALTER COLUMN "question" TYPE VARCHAR(1500) USING "question"::VARCHAR(1500);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "questionrecommended" ALTER COLUMN "question" TYPE VARCHAR(100) USING "question"::VARCHAR(100);"""
