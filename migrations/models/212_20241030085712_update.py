from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE INDEX "idx_integration_dataset_0782cb" ON "integration_rule" ("dataset_id");
        CREATE INDEX "idx_lark_file_parent__646251" ON "lark_file" ("parent_token");
        CREATE INDEX "idx_lark_file_token_e8f5fd" ON "lark_file" ("token");"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX "idx_integration_dataset_0782cb";
        DROP INDEX "idx_lark_file_token_e8f5fd";
        DROP INDEX "idx_lark_file_parent__646251";"""
