from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
CREATE TABLE IF NOT EXISTS "agentfunctioncallapi_robot" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "group_id" UUID,
    "related_status" VARCHAR(50),
    "agentfunctioncall_id" UUID NOT NULL REFERENCES "agentfunctioncallapi" ("id") ON DELETE CASCADE,
    "robot_id" UUID NOT NULL REFERENCES "robot" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_agentfuncti_agentfu_ccb4af" UNIQUE ("agentfunctioncall_id", "robot_id")
);
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """SELECT 1 WHERE FALSE;"""
