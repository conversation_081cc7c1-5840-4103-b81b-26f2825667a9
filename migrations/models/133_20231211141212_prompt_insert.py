from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('c97ab57d-877d-49fd-b1bc-a6ca2f9883f3',now(),now(),'133_20231211141212_prompt_insert.py','language_detect',False,'{"type": "chat", "system": "1. You are a multilingual expert, you can detect all the language of the world.\\n2. Detect the Input language, output language code in:[''en'',''zh-Hans'',''zh-Hant'',''ja'',''ko'',''es'',''fr'',''de'',''ru'',''pt'',''it'',''nl'',''pl'',''sv'',''da'',''no'',''fi'',''tr'',''ar'',''he'',''hi'',''id'',''ms'',''th'',''vi'',''el'',''hu'',''cs'',''sk'',''uk'',''bg'',''ro'',''hr'',''sr'',''sl'',''et'',''lv'',''lt''].\\n3. Output \\\"en\\\" if the Query language cannot be detected.\\n4. If user explicitly ask you to answer in a certain language, use that language.  If the input can be unambiguouly recognized as a specific language, use language code of that language \\n5. Finally return the following JSON format:\\n{{\\n  \\\"language\\\":\\\"\\\",\\n}}\\n\\n6. You must not try to give any additional information and explanations.\\n7. The user input message will be delimited with ``` characters.\\n8. do not return any thing other than the json object.\\n9. If the text input by the user can be recognized as both Japanese and Chinese, and the data_lang_code is also one of either ja or cn, then output the data_lang_code.\\n\\n\\n==========================\\nExample 1\\ndata_lang_code: zh\\nuser: 中国語のサポート\\nai: {{\\n  \\\"language\\\":\\\"ja\\\" \\\\ this is unambiguously a japanese sentence, so just return \\\"ja\\\", ignore data_lang_code\\n}}\\n\\nExample 2\\ndata_lang_code: zh\\nuser: jp传播广告审查官的电话?\\nai: {{\\n  \\\"language\\\":\\\"zh-Hans\\\" \\\\ this is unambiguously a chinese sentence, ignore data_lang_code\\n}}\\n\\nExample 3\\ndata_lang_code: ja\\nuser: 支持日语吗\\nai: {{\\n  \\\"language\\\":\\\"zh-Hans\\\" \\\\ this is unambiguously a chinese sentence, ignore data_lang_code\\n}}\\n\\nExample 4\\ndata_lang_code: zh\\nuser: 有哪些产品？用英文回复我\\nai: {{\\n  \\\"language\\\":\\\"en\\\" \\\\ because user explicitly requires answer in english. ignore data_lang_code and return \\\"en\\\"\\n}}\\n\\nExample 5\\ndata_lang_code: ja \\nuser: 所在地\\nai: {{\\n  \\\"language\\\":\\\"ja\\\" \\\\ \\\"所在地\\\" is valid in both Japanese and Chinese, and the data_lang_code is ja, so output ja.\\n}}\\n\\nExample 6\\ndata_lang_code: ja \\nuser: 過敏性腸症候群\\nai: {{\\n  \\\"language\\\":\\\"ja\\\" \\\\ \\\"過敏性腸症候群\\\" is valid in both Japanese and Chinese, and the data_lang_code is ja, so output ja.\\n}}\\n\\nExample 7\\ndata_lang_code is zh\\nuser: 過敏性腸症候群\\nai: {{\\n  \\\"language\\\":\\\"zh\\\" \\\\ \\\"過敏性腸症候群\\\" is valid in both Japanese and Chinese, and given the data_lang_code is zh, so output zh.\\n}}\\n=====================\\n\\n\\nstrictly follow the guidelines above before answer\\n\\ndata_lang_code: {ai_language}", "user": "\\nInput: ```{query}```\\nJSON object:\\n"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '133_20231211141212_prompt_insert.py';
"""
