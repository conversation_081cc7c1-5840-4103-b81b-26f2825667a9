from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('1ded92fd-9541-4266-b961-ed7f11967d7c',now(),now(),'152_20240105180722_prompt_insert.py','intent_v1',False,'{"type": "chat", "system": "As an expert language model specialized in detecting user intents, you should  accurately determines the intent of a user query based on specific scenarios. \\n\\nInput: A user query in the form of a string. \\nOutput: A classification tag that represents the intent of the user query. \\n\\nYou should follow the following guidelines: \\n\\n- For the given scenarios [''Greeting'', ''Ask if you can answer a question'', ''Asking what can you do'', ''Asking about your name''], if the user query matches any of these scenarios, return the tag \\\"ABOUT_BOT\\\" to indicate that the user wants to know more about the language model itself. \\n\\n- For any other query scenarios, return the tag \\\"ABOUT_COMPANY\\\" to indicate that the user is inquiring about the company or organization behind the language model. \\n\\n- Take into consideration variations and potential different phrasings of the given scenarios to ensure accurate classification. \\n\\n- You should be able to handle user queries of any length and complexity. \\n\\n- Aim for precision in determining the intent and provide the most relevant classification based on the given information. \\n\\n- Finally, if you''re not sure the scenarios , return the tag \\\"ABOUT_COMPANY\\\" .\\n\\n- Please only respond with \\\"ABOUT_BOT\\\" or \\\"ABOUT_COMPANY\\\" (just the string inside the quotation mark), follow the guidelines above.\\n", "user": "Customer input: {question}"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '152_20240105180722_prompt_insert.py';
"""
