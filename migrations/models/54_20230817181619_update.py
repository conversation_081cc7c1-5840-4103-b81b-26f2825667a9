from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "user" ALTER COLUMN "salt" TYPE VARCHAR(64) USING "salt"::VARCHAR(64);
        ALTER TABLE "user" ALTER COLUMN "password" TYPE VARCHAR(64) USING "password"::VARCHAR(64);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "user" ALTER COLUMN "salt" TYPE VARCHAR(30) USING "salt"::VARCHAR(30);
        ALTER TABLE "user" ALTER COLUMN "password" TYPE VARCHAR(100) USING "password"::VARCHAR(100);"""
