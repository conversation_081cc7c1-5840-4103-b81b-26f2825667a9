from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "dataset" ADD "url_rules" JSONB;
        ALTER TABLE "sessionmessage" ALTER COLUMN "model" TYPE VARCHAR(100) USING "model"::VARCHAR(100);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "dataset" DROP COLUMN "url_rules";
        ALTER TABLE "sessionmessage" ALTER COLUMN "model" TYPE VARCHAR(100) USING "model"::VARCHAR(100);"""
