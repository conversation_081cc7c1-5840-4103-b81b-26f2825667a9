from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "imageattachment" ALTER COLUMN "file_type" TYPE VARCHAR(20) USING "file_type"::VARCHAR(20);
        ALTER TABLE "vectorfile" ADD "resources" JSONB;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "vectorfile" DROP COLUMN "resources";
        ALTER TABLE "imageattachment" ALTER COLUMN "file_type" TYPE VARCHAR(20) USING "file_type"::VARCHAR(20);"""
