from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "add_ons" ALTER COLUMN "target_field" TYPE VARCHAR(25) USING "target_field"::VARCHAR(25);
        ALTER TABLE "addon_subscriptions" RENAME TO "add_on_subscriptions";
        ALTER TABLE "sessionmessage" ALTER COLUMN "model" TYPE VARCHAR(100) USING "model"::VARCHAR(100);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "add_ons" ALTER COLUMN "target_field" TYPE VARCHAR(25) USING "target_field"::VARCHAR(25);
        ALTER TABLE "sessionmessage" ALTER COLUMN "model" TYPE VARCHAR(100) USING "model"::VARCHAR(100);
        ALTER TABLE "add_on_subscriptions" RENAME TO "addon_subscriptions";"""
