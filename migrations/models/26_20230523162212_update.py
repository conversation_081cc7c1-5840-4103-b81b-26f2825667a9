from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "imageattachment" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "key" VARCHAR(255) NOT NULL UNIQUE,
    "filename" VARCHAR(255),
    "file_type" VARCHAR(20) NOT NULL  DEFAULT 'original'
);
COMMENT ON COLUMN "imageattachment"."file_type" IS 'AVATAR: avatar\nORIGINAL: original';;
        CREATE TABLE IF NOT EXISTS "stripeproducts" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "name" VARCHA<PERSON>(255),
    "max_number" INT   DEFAULT 0,
    "max_tokens" INT   DEFAULT 0,
    "sys_token" BOOL   DEFAULT False,
    "api_id" VARCHAR(255),
    "order" INT   DEFAULT 0,
    "month" INT   DEFAULT 0,
    "product" VARCHAR(255)
);;
        CREATE TABLE IF NOT EXISTS "stripewebhooklog" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "type" VARCHAR(255),
    "data" JSONB,
    "run_sta" VARCHAR(255)
);;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "imageattachment";
        DROP TABLE IF EXISTS "stripeproducts";
        DROP TABLE IF EXISTS "stripewebhooklog";"""
