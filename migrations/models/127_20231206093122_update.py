from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "sessionmessage" ADD "source" VARCHAR(100)   DEFAULT 'playground';
        ALTER TABLE "sessionmessage" ADD "title" VARCHAR(1024);
        ALTER TABLE "sessionmessage" ADD "url" VARCHAR(1024);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "sessionmessage" DROP COLUMN "source";
        ALTER TABLE "sessionmessage" DROP COLUMN "title";
        ALTER TABLE "sessionmessage" DROP COLUMN "url";"""
