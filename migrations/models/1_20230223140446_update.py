from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "apikey" ALTER COLUMN "api_key" TYPE VARCHAR(60) USING "api_key"::VARCHAR(60);
        CREATE UNIQUE INDEX "uid_apikey_api_key_539f6f" ON "apikey" ("api_key");"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX "idx_apikey_api_key_539f6f";
        ALTER TABLE "apikey" ALTER COLUMN "api_key" TYPE VARCHAR(50) USING "api_key"::VARCHAR(50);"""
