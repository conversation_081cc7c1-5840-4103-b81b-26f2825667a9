from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "accountmember" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "resource_id" VARCHAR(100) NOT NULL,
    "resource_type" VARCHAR(100) NOT NULL  DEFAULT 'robot',
    "member_id" VARCHAR(100) NOT NULL REFERENCES "user" ("user_id") ON DELETE CASCADE,
    "user_id" VARCHAR(100) NOT NULL REFERENCES "user" ("user_id") ON DELETE CASCADE
);
COMMENT ON COLUMN "accountmember"."resource_type" IS 'ROBOT: robot\nDATASET: dataset';
COMMENT ON TABLE "accountmember" IS 'user_id,resource_id,resouce_type,member,';;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "accountmember";"""
