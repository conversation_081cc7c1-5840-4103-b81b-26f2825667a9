from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "plans" ADD "duration_unit" VARCHAR(5) NOT NULL;
        ALTER TABLE "plans" ADD "trial" BOOL NOT NULL  DEFAULT False;
        ALTER TABLE "plans" ADD "total_message_limit" INT;
        ALTER TABLE "plans" ADD "duration_length" INT NOT NULL;
        ALTER TABLE "plans" DROP COLUMN "plan_type";
        ALTER TABLE "plans" DROP COLUMN "duration_days";
        ALTER TABLE "plans" ALTER COLUMN "storage_limit" DROP DEFAULT;
        ALTER TABLE "plans" ALTER COLUMN "storage_limit" DROP NOT NULL;
        ALTER TABLE "plans" ALTER COLUMN "monthly_message_limit" DROP DEFAULT;
        ALTER TABLE "plans" ALTER COLUMN "monthly_message_limit" DROP NOT NULL;
        ALTER TABLE "plans" ALTER COLUMN "web_page_limit" DROP DEFAULT;
        ALTER TABLE "plans" ALTER COLUMN "web_page_limit" DROP NOT NULL;
        ALTER TABLE "plans" ALTER COLUMN "bot_limit" DROP DEFAULT;
        ALTER TABLE "plans" ALTER COLUMN "bot_limit" DROP NOT NULL;
        ALTER TABLE "plans" ALTER COLUMN "multi_modal_parsing_limit" DROP DEFAULT;
        ALTER TABLE "plans" ALTER COLUMN "multi_modal_parsing_limit" DROP NOT NULL;
        ALTER TABLE "plan_subscriptions" ADD "end_time" TIMESTAMPTZ NOT NULL;
        CREATE TABLE IF NOT EXISTS "usagelog" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "usage_type" VARCHAR(19) NOT NULL,
    "amount" INT NOT NULL  DEFAULT 1,
    "source_subscription_id" UUID,
    "source_subscription_type" VARCHAR(20),
    "user_id" UUID NOT NULL REFERENCES "user" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_usagelog_source__4d41a3" ON "usagelog" ("source_subscription_id");
CREATE INDEX IF NOT EXISTS "idx_usagelog_user_id_5785f6" ON "usagelog" ("user_id", "usage_type", "created_at");
CREATE INDEX IF NOT EXISTS "idx_usagelog_source__91803c" ON "usagelog" ("source_subscription_id", "source_subscription_type");
COMMENT ON COLUMN "plans"."duration_unit" IS 'DAY: day\nMONTH: month';
COMMENT ON COLUMN "usagelog"."usage_type" IS 'FILE_LEARNING: file_learning\nMULTI_MODAL_PARSING: multi_modal_parsing\nMESSAGE_SENT: message_sent\nBOT_CREATED: bot_created\nSTORAGE_USED: storage_used\nWEB_PAGE_LEARNED: web_page_learned';
COMMENT ON COLUMN "usagelog"."amount" IS 'Amount consumed (e.g., messages=1, storage=bytes)';
COMMENT ON COLUMN "usagelog"."source_subscription_id" IS 'ID of the PlanSubscription or AddOnSubscription debited';
COMMENT ON COLUMN "usagelog"."source_subscription_type" IS '''plan'' or ''addon''';
COMMENT ON TABLE "usagelog" IS '记录用户的各类功能使用情况 (Resource consumption events).';;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "plans" ADD "plan_type" VARCHAR(14) NOT NULL;
        ALTER TABLE "plans" ADD "duration_days" INT NOT NULL  DEFAULT 30;
        ALTER TABLE "plans" DROP COLUMN "duration_unit";
        ALTER TABLE "plans" DROP COLUMN "trial";
        ALTER TABLE "plans" DROP COLUMN "total_message_limit";
        ALTER TABLE "plans" DROP COLUMN "duration_length";
        ALTER TABLE "plans" ALTER COLUMN "storage_limit" SET NOT NULL;
        ALTER TABLE "plans" ALTER COLUMN "storage_limit" SET DEFAULT -1;
        ALTER TABLE "plans" ALTER COLUMN "monthly_message_limit" SET NOT NULL;
        ALTER TABLE "plans" ALTER COLUMN "monthly_message_limit" SET DEFAULT -1;
        ALTER TABLE "plans" ALTER COLUMN "web_page_limit" SET NOT NULL;
        ALTER TABLE "plans" ALTER COLUMN "web_page_limit" SET DEFAULT -1;
        ALTER TABLE "plans" ALTER COLUMN "bot_limit" SET NOT NULL;
        ALTER TABLE "plans" ALTER COLUMN "bot_limit" SET DEFAULT -1;
        ALTER TABLE "plans" ALTER COLUMN "multi_modal_parsing_limit" SET NOT NULL;
        ALTER TABLE "plans" ALTER COLUMN "multi_modal_parsing_limit" SET DEFAULT -1;
        ALTER TABLE "plan_subscriptions" DROP COLUMN "end_time";
        DROP TABLE IF EXISTS "usagelog";"""
