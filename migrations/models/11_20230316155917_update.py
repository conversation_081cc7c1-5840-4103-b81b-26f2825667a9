from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "vectorfile" ADD "failed_reason" TEXT;
        ALTER TABLE "robot" ADD CONSTRAINT "fk_robot_user_f98c4957" FOREIGN KEY ("user_id") REFERENCES "user" ("user_id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "robot" DROP CONSTRAINT "fk_robot_user_f98c4957";
        ALTER TABLE "vectorfile" DROP COLUMN "failed_reason";"""
