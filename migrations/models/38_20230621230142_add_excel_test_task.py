from tortoise import BaseDBAsyncClient

# "input_file_url": upload to s3
# https://prd-mygpt.s3.ap-northeast-1.amazonaws.com/input_30f5e1b1-411c-4b5d-9d58-9849965d4e21_full-2023-06-20-testing.xlsx

# "output_file_url": upload to s3
# https://prd-mygpt.s3.ap-northeast-1.amazonaws.com/output_30f5e1b1-411c-4b5d-9d58-9849965d4e21_full-2023-06-20-testing.xlsx


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "exceltesttask" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "filename" VARCHAR(255),
    "status" VARCHAR(10),
    "total_count" INT   DEFAULT 0,
    "processed_count" INT   DEFAULT 0,
    "input_file_url" VARCHAR(1024),
    "output_file_url" VARCHAR(1024),
    "user_id" VARCHAR(100) REFERENCES "user" ("user_id") ON DELETE CASCADE
);
COMMENT ON COLUMN "exceltesttask"."status" IS 'ACCEPTED: accepted\nPROCESSING: processing\nFINISHED: finished\nFAILED: failed\nCANCELED: canceled';;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "exceltesttask";"""
