from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "integrationrule_vectorfile";
        CREATE TABLE IF NOT EXISTS "integrationrule_vectorfile" (
    "integration_rule_id" UUID NOT NULL REFERENCES "integration_rule" ("id") ON DELETE CASCADE,
    "vectorfile_id" UUID NOT NULL REFERENCES "vectorfile" ("id") ON DELETE CASCADE
);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """SELECT 1 WHERE FALSE;"""
