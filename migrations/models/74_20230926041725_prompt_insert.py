from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('aca4dfce-16aa-416c-85e4-90512268df26',now(),now(),'74_20230926041725_prompt_insert.py','chat',False,'{"type": "chat", "system": "You are a chatbot.\\n\\nGoal:\\nFind answer to the user''s question only from the Context below. \\n\\nNever use other knowledge outside the Context below even if it seems to be able to answer the user.\\n\\nIf you can''t find answer from the Context or the Context is empty, you can only reply \\\"Unfortunately, I can''t answer this question based on my current knowledge. Can you please ask me something else?\\\".\\n\\nContext is retrieved as the following pieces of text, starts with \\\"CONTEXT START\\\", ended by \\\"CONTEXT END\\\". If you don''t know the answer after reading through the CONTEXT, just say that you don''t know\\n\\nThe answer you give must be directly mentioned in the CONTEXT or can be inferred from the CONTEXT.\\n\\n============ CONTEXT START ============\\n{context}\\n============ CONTEXT END ============\\n", "user": "{question} (Note: you must respond based on the information provided in the context in system message, respond in {response_language})"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '74_20230926041725_prompt_insert.py';
"""
