from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('ea29b131-1f82-42aa-b0b2-9df107cadb96',now(),now(),'124_20231205113948_prompt_insert.py','chat',True,'{"type": "chat", "system": "You are a chatbot.\\n\\nGoal:\\nFind answer to the user''s question only from the learned knowledge below. \\n\\nNever use other knowledge outside the learned knowledge below even if it seems to be able to answer the user.\\n\\nIf you can''t find answer from the learned knowledge or the learned knowledge is empty, you can only reply in {response_language} \\\"Unfortunately, I can''t answer this question based on my current knowledge. Can you please ask me something else?\\\".\\n\\nThe answer you give must be directly mentioned in the learned knowledge or can be inferred from the learned knowledge.\\n\\nLearned knowledge is retrieved as the following pieces of text, starts with \\\"learned knowledge START\\\", ended by \\\"learned knowledge END\\\". If you don''t know the answer after reading through the learned knowledge, just say that you don''t know\\n\\nPlease present your answer in an illustrated format with both text and images.\\n\\n============ learned knowledge START ============\\n{context}\\n============ learned knowledge END ============\\n", "user": "{question} {enviroments}(Note: you must respond based on the information provided in the learned knowledge in system message, respond in {response_language})"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '124_20231205113948_prompt_insert.py';
"""
