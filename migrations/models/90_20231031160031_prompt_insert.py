from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('8b0ff2d2-fa5a-48a8-880e-ce707fb91e18',now(),now(),'90_20231031160031_prompt_insert.py','faq_answer_turbo',True,'{"type": "chat", "system": "\\nAct as an expert FAQ chatbot, your goal is to find out FAQ entries that can answer user''s question. Here''s rule how you can achieve this:\\nRules:\\n- You will be provided with a list of FAQs, separated by \\\"=====\\\". Each FAQ entry consists of a Question and its UUID.\\n- When a user asks a question, your task is to find out if there is a FAQ with a Question that may answer user''s question.\\n- Respond with a JSON object in the following format: {{\\\"UUID\\\": \\\"$uuid\\\", \\\"confidence\\\": $confidence_level}}, the \\\"UUID\\\" field is the UUID of the FAQ entry, and the \\\"confidence\\\" field indicates how sure you are that the FAQ can answer the user''s question, range [0, 100].\\n- To answer user''s question with a faq entry, that entry should have same entity, meaning. If the entry is about different things, it should not be chose. \\nfor example:\\ngiven User: \\\"北京在哪\\\", FAQ entry: \\\"南京在哪\\\" is about different cities, so this FAQ entry should not be be chosen\\ngiven User: \\\"北京面积\\\", FAQ entry: \\\"北京有多少人\\\" is about different properties about a city, so this FAQ entry should NOT be be chosen\\ngiven User: \\\"北京有多少人\\\" FAQ entry: \\\"南京有多少人\\\" is about same properties of different cities, so this FAQ entry should NOT be be chosen\\n- If none of the FAQ entries can answer user''s question, do not make up an response, just respond with: {{\\\"UUID\\\": \\\"\\\", \\\"confidence\\\": 100}}\\n{possible_answers}\\n", "user": "USER：''{question}''\\nANSWER:\\n"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '90_20231031160031_prompt_insert.py';
"""
