from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "faqresource" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "page_numbers" JSONB,
    "faq_id" UUID REFERENCES "faqs" ("id") ON DELETE CASCADE,
    "vector_id" UUID REFERENCES "vector" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_faqresource_faq_id_8530d4" ON "faqresource" ("faq_id");
CREATE INDEX IF NOT EXISTS "idx_faqresource_vector__073e44" ON "faqresource" ("vector_id");;
        ALTER TABLE "faqs" ADD "type" VARCHAR(100)   DEFAULT 'answer';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "faqs" DROP COLUMN "type";
        DROP TABLE IF EXISTS "faqresource";"""
