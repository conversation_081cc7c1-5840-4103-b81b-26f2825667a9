from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "imageattachment" DROP COLUMN "data_id";
        ALTER TABLE "imageattachment" DROP COLUMN "data_type";
        ALTER TABLE "sessionmessage" ADD "reference_list" JSONB;
        ALTER TABLE "sessionmessage" ADD "reference_indexes" JSONB;
        ALTER TABLE "sessionmessage" ADD "reference_context" TEXT;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "sessionmessage" DROP COLUMN "reference_list";
        ALTER TABLE "sessionmessage" DROP COLUMN "reference_indexes";
        ALTER TABLE "sessionmessage" DROP COLUMN "reference_context";
        ALTER TABLE "imageattachment" ADD "data_id" VARCHAR(255);
        ALTER TABLE "imageattachment" ADD "data_type" VARCHAR(20) NOT NULL  DEFAULT 'robot';"""
