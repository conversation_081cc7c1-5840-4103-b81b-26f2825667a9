from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "integration_rule" ADD "file_list_syncing_status" VARCHAR(100) NOT NULL  DEFAULT 'syncing';
        ALTER TABLE "integration_rule" DROP COLUMN "status";"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "integration_rule" ADD "status" VARCHAR(100) NOT NULL  DEFAULT 'syncing';
        ALTER TABLE "integration_rule" DROP COLUMN "file_list_syncing_status";"""
