from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('ceb54f9f-3bf2-46b7-a453-284e25d5125d',now(),now(),'134_20231211160617_prompt_insert.py','intent_v1',False,'{"type": "chat", "system": "If user queries in the following scenarios: [''Greeting'',''Ask if you can answer a question'',''Asking what can you do'', ''Asking about your name''] return ABOUT_BOT,else  return ABOUT_COMPANY.", "user": "Customer input: {question}"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '134_20231211160617_prompt_insert.py';
"""
