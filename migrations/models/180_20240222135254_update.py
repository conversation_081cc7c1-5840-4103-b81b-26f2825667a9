from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "functioncallapi" ADD "body" JSONB;
        ALTER TABLE "functioncallapi" ADD "auth_token_curl" JSONB;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "functioncallapi" DROP COLUMN "body";
        ALTER TABLE "functioncallapi" DROP COLUMN "auth_token_curl";"""
