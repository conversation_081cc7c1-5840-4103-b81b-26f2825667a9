from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "sessionmetricshourly" ADD "old_users" JSONB;
        ALTER TABLE "sessionmetricshourly" ADD "new_users" JSONB;
        ALTER TABLE "sessionmetricshourly" DROP COLUMN "new_user_count";
        ALTER TABLE "sessionmetricshourly" DROP COLUMN "old_user_count";"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "sessionmetricshourly" ADD "new_user_count" INT;
        ALTER TABLE "sessionmetricshourly" ADD "old_user_count" INT;
        ALTER TABLE "sessionmetricshourly" DROP COLUMN "old_users";
        ALTER TABLE "sessionmetricshourly" DROP COLUMN "new_users";"""
