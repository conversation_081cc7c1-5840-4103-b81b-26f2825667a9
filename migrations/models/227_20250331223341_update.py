from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "plans" RENAME COLUMN "monthly_price" TO "price";
        ALTER TABLE "plans" ADD "duration_days" INT NOT NULL  DEFAULT 30;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "plans" RENAME COLUMN "price" TO "monthly_price";
        ALTER TABLE "plans" DROP COLUMN "duration_days";"""
