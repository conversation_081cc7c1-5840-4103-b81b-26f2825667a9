from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "faqs" ADD "answer_characters_count" INT   DEFAULT 0;
        ALTER TABLE "faqs" ADD "has_image" BOOL NOT NULL  DEFAULT False;
        ALTER TABLE "faqs" ADD "question_token_count" INT   DEFAULT 0;
        ALTER TABLE "faqs" ADD "answer_token_count" INT   DEFAULT 0;
        ALTER TABLE "faqs" ADD "question_characters_count" INT   DEFAULT 0;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "faqs" DROP COLUMN "answer_characters_count";
        ALTER TABLE "faqs" DROP COLUMN "has_image";
        ALTER TABLE "faqs" DROP COLUMN "question_token_count";
        ALTER TABLE "faqs" DROP COLUMN "answer_token_count";
        ALTER TABLE "faqs" DROP COLUMN "question_characters_count";"""
