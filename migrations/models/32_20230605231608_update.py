from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "faqs" ADD "excel_id" VARCHAR(255);
        ALTER TABLE "faqs" ADD "sources" VARCHAR(255);
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "stripepayments" ADD "mode" VARCHAR(12)   DEFAULT 'subscription';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "faqs" DROP COLUMN "excel_id";
        ALTER TABLE "faqs" DROP COLUMN "sources";
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "stripepayments" DROP COLUMN "mode";"""
