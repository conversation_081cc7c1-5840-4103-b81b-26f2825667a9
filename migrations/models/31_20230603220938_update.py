from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "faqs" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "question" TEXT,
    "answer" TEXT,
    "robot_id" UUID REFERENCES "robot" ("id") ON DELETE CASCADE,
    "user_id" VARCHAR(100) REFERENCES "user" ("user_id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_faqs_robot_i_326ea9" ON "faqs" ("robot_id");;
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "stripepayments" ADD "canceled_at" TIMESTAMPTZ;
        ALTER TABLE "stripepayments" ADD "end_at" TIMESTAMPTZ;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "stripepayments" DROP COLUMN "canceled_at";
        ALTER TABLE "stripepayments" DROP COLUMN "end_at";
        DROP TABLE IF EXISTS "faqs";"""
