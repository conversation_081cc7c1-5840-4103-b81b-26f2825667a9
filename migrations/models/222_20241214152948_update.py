from tortoise import BaseDBAsync<PERSON><PERSON>


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "integration_rule" ADD "name" VARCHAR(255) NOT NULL  DEFAULT '';
        ALTER TABLE "integration_rule" ADD "notification_emails" JSONB;
        ALTER TABLE "integration_rule" ADD "sync_interval" INT;
        ALTER TABLE "integration_rule" ADD "created_by_id" UUID;
        ALTER TABLE "integration_rule" ADD "last_synced_at" TIMESTAMPTZ;
        ALTER TABLE "integration_rule" ALTER COLUMN "type" TYPE VARCHAR(100) USING "type"::VARCHAR(100);
        CREATE TABLE IF NOT EXISTS "integration_sync_log" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "operation" VARCHAR(6) NOT NULL,
    "trigger_type" VARCHAR(9) NOT NULL,
    "sync_status" VARCHAR(20),
    "failure_reason" TEXT,
    "integration_rule_id" UUID NOT NULL REFERENCES "integration_rule" ("id") ON DELETE CASCADE,
    "trigger_user_id" VARCHAR(100) REFERENCES "user" ("user_id") ON DELETE CASCADE,
    "vectorfile_id" UUID NOT NULL REFERENCES "vectorfile" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "integration_sync_log"."operation" IS 'CREATE: create\nUPDATE: update\nDELETE: delete';
COMMENT ON COLUMN "integration_sync_log"."trigger_type" IS 'SCHEDULED: scheduled\nMANUAL: manual';
COMMENT ON COLUMN "integration_sync_log"."sync_status" IS 'SUCCESS: success\nFAILURE: failure\nRUNNING: running';;
        ALTER TABLE "lark_integration_rule" DROP COLUMN "notification_emails";
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "sessionmessage" ALTER COLUMN "model" TYPE VARCHAR(100) USING "model"::VARCHAR(100);
        ALTER TABLE "integration_rule" ADD CONSTRAINT "fk_integrat_user_d04118b7" FOREIGN KEY ("created_by_id") REFERENCES "user" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "integration_rule" DROP CONSTRAINT "fk_integrat_user_d04118b7";
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "sessionmessage" ALTER COLUMN "model" TYPE VARCHAR(100) USING "model"::VARCHAR(100);
        ALTER TABLE "integration_rule" DROP COLUMN "name";
        ALTER TABLE "integration_rule" DROP COLUMN "notification_emails";
        ALTER TABLE "integration_rule" DROP COLUMN "sync_interval";
        ALTER TABLE "integration_rule" DROP COLUMN "created_by_id";
        ALTER TABLE "integration_rule" DROP COLUMN "last_synced_at";
        ALTER TABLE "integration_rule" ALTER COLUMN "type" TYPE VARCHAR(100) USING "type"::VARCHAR(100);
        ALTER TABLE "lark_integration_rule" ADD "notification_emails" JSONB;
        DROP TABLE IF EXISTS "integration_sync_log";"""
