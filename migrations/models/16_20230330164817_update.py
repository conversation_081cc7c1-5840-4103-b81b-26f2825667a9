from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "url" ALTER COLUMN "status" TYPE VARCHAR(20) USING "status"::VARCHAR(20);
        ALTER TABLE "vector" ADD "fingerprint" VARCHAR(100);
        ALTER TABLE "vectorfile" ADD "metadata" JSONB;
        ALTER TABLE "vectorfile" ALTER COLUMN "file_status" TYPE VARCHAR(20) USING "file_status"::VARCHAR(20);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "url" ALTER COLUMN "status" TYPE VARCHAR(20) USING "status"::VARCHAR(20);
        ALTER TABLE "vector" DROP COLUMN "fingerprint";
        ALTER TABLE "vectorfile" DROP COLUMN "metadata";
        ALTER TABLE "vectorfile" ALTER COLUMN "file_status" TYPE VARCHAR(20) USING "file_status"::VA<PERSON><PERSON><PERSON>(20);"""
