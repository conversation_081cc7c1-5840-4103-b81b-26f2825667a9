from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "question" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "question" TEXT NOT NULL,
    "question_type" VARCHAR(40) NOT NULL  DEFAULT 'generator',
    "robot_id" UUID NOT NULL REFERENCES "robot" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "question"."question_type" IS 'Generator: generator';;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "question";"""
