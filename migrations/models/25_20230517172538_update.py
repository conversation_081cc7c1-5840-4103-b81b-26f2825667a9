from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "imageattachment" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "key" VARCHAR(255) NOT NULL UNIQUE,
    "filename" VARCHAR(255),
    "file_type" VARCHAR(20) NOT NULL  DEFAULT 'original'
);
COMMENT ON COLUMN "imageattachment"."file_type" IS 'AVATAR: avatar\nORIGINAL: original';;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "imageattachment";"""
