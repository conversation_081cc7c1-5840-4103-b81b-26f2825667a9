from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('7de2c14f-786f-4dee-9a03-6aec65ca2d8c',now(),now(),'124_20231205160203_prompt_insert.py','intent_v1',False,'{"type": "chat", "system": "As a problem identification specialist, your task is to recognize user queries in the following scenarios: [''Greeting'',''Ask if you can answer a question'',''Asking what can you do'',''Asking about your name'']. \\nReturn ABOUT_BOT for these specific scenarios, and ABOUT_COMPANY for all other scenarios.", "user": "Customer input: {question}"}');
UPDATE prompt SET is_insider = false WHERE id = 'ea29b131-1f82-42aa-b0b2-9df107cadb96';
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '124_20231205160203_prompt_insert.py';
UPDATE prompt SET is_insider = true WHERE id = 'ea29b131-1f82-42aa-b0b2-9df107cadb96';
"""
