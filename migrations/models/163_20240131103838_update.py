from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "faqs" ADD "hierarchy_level" INT   DEFAULT 0;
        ALTER TABLE "faqs" ADD "hierarchy_parent_id" VARCHAR(255);
        ALTER TABLE "faqs" ADD "hierarchy_path" VARCHAR(255);
        ALTER TABLE "faqs" ADD "recommended_question" JSONB;
        ALTER TABLE "faqs" ADD "similar_questions" JSONB;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "faqs" DROP COLUMN "hierarchy_level";
        ALTER TABLE "faqs" DROP COLUMN "hierarchy_parent_id";
        ALTER TABLE "faqs" DROP COLUMN "hierarchy_path";
        ALTER TABLE "faqs" DROP COLUMN "recommended_question";
        ALTER TABLE "faqs" DROP COLUMN "similar_questions";"""
