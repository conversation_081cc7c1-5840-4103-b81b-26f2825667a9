from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "robot" ADD "robot_type" VARCHAR(100) NOT NULL  DEFAULT 'rag';
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "sessionmessage" ALTER COLUMN "model" TYPE VARCHAR(100) USING "model"::VARCHAR(100);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "robot" DROP COLUMN "robot_type";
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "sessionmessage" ALTER COLUMN "model" TYPE VARCHAR(100) USING "model"::VARCHAR(100);"""
