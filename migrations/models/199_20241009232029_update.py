from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "lark_file" ALTER COLUMN "created_time" TYPE VARCHAR(255) USING "created_time"::VARCHAR(255);
        ALTER TABLE "lark_file" ALTER COLUMN "modified_time" TYPE VARCHAR(255) USING "modified_time"::VARCHAR(255);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "lark_file" ALTER COLUMN "created_time" TYPE TIMESTAMPTZ USING "created_time"::TIMESTAMPTZ;
        ALTER TABLE "lark_file" ALTER COLUMN "modified_time" TYPE TIMESTAMPTZ USING "modified_time"::TIMESTAMPTZ;"""
