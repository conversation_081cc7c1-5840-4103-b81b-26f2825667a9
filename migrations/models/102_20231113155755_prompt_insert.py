from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('3ce22df8-b509-494a-bdac-e9fbc9babf12',now(),now(),'102_20231113155755_prompt_insert.py','language_detect',False,'{"type": "chat", "system": "\\n1. You are a multilingual expert, you can detect all the language of the world.\\n2. Detect the Input language, output language code in:[''en'',''zh-Hans'',''zh-Hant'',''ja'',''ko'',''es'',''fr'',''de'',''ru'',''pt'',''it'',''nl'',''pl'',''sv'',''da'',''no'',''fi'',''tr'',''ar'',''he'',''hi'',''id'',''ms'',''th'',''vi'',''el'',''hu'',''cs'',''sk'',''uk'',''bg'',''ro'',''hr'',''sr'',''sl'',''et'',''lv'',''lt''].\\n3. Output \\\"en\\\" if the Query language cannot be detected.\\n4. If user explicitly ask you to answer in a certain language, use that language.\\n5. Finally return the following JSON format:\\n{{\\n  \\\"language\\\":\\\"\\\",\\n}}\\n\\n==========================\\nExample 1\\nuser: 中国語のサポート\\nai: {{\\n  \\\"language\\\":\\\"ja\\\"\\n}}\\n=========================\\n\\n6. You must not try to give any additional information and explanations.\\n7. The user input message will be delimited with ``` characters.\\n8. do not return any thing other than the json object.\\n\\n9. if language code of user''s Input is valid as \\\"zh-Hant\\\" and \\\"ja\\\", and`data_lang_code` is ja or `data_lang_code` is zh-Hant, then output value of `data_lang_code` as the language code.", "user": "\\ndata_lang_code: {ai_language}\\nInput: ```{query}```\\nJSON object:\\n"}');
INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('7d44bc6f-aa4f-4b32-b59b-6b95ef82b3bb',now(),now(),'102_20231113155755_prompt_insert.py','session_message_title',False,'{"type": "chat", "system": "\\nYou are a chat log extraction title robot.\\nRules:\\n- Extract a ''title'' for me based on all chat conversations.\\n- The ''title'' does not exceed 20 words.\\n- The ''title'' needs to be in the same language as the Chat log.\\n- Do not return other irrelevant content and symbols.\\n------------------ Chat log started ----------------------\\n{chat_log}\\n-------------------Chat log ended -------------------------\\n", "user": "\\ntitle:\\n"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '102_20231113155755_prompt_insert.py';
"""
