from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX "idx_usagelog_source__91803c";
        ALTER TABLE "add_ons" RENAME COLUMN "duration_days" TO "duration_length";
        ALTER TABLE "add_ons" ADD "duration_unit" VARCHAR(5) NOT NULL;
        ALTER TABLE "add_ons" ALTER COLUMN "effect_data" TYPE JSONB USING "effect_data"::JSONB;
        ALTER TABLE "add_ons" ALTER COLUMN "target_field" TYPE VARCHAR(25) USING "target_field"::VARCHAR(25);
        ALTER TABLE "add_ons" ALTER COLUMN "price" TYPE DECIMAL(20,10) USING "price"::DECIMAL(20,10);
        ALTER TABLE "addon_subscriptions" ADD "status" VARCHAR(10) NOT NULL  DEFAULT 'active';
        ALTER TABLE "addon_subscriptions" RENAME COLUMN "start_time" TO "start_at";
        ALTER TABLE "addon_subscriptions" DROP COLUMN "operation_status";
        ALTER TABLE "plans" RENAME COLUMN "storage_limit" TO "storage_quota";
        ALTER TABLE "plans" RENAME COLUMN "web_page_limit" TO "web_page_quota";
        ALTER TABLE "plans" ADD "plan_type" VARCHAR(5) NOT NULL  DEFAULT 'basic';
        ALTER TABLE "plans" ADD "message_quota" INT;
        UPDATE "plans" SET "message_quota" = COALESCE("total_message_limit", "monthly_message_limit");
        ALTER TABLE "plans" DROP COLUMN "total_message_limit";
        ALTER TABLE "plans" DROP COLUMN "monthly_message_limit";
        ALTER TABLE "plans" RENAME COLUMN "multi_modal_parsing_limit" TO "multi_modal_parsing_quota";
        ALTER TABLE "plans" RENAME COLUMN "bot_limit" TO "bot_quota";
        ALTER TABLE "plans" ADD "message_quota_grant_type" VARCHAR(7) NOT NULL  DEFAULT 'monthly';
        ALTER TABLE "plans" DROP COLUMN "trial";
        ALTER TABLE "plan_subscriptions" RENAME COLUMN "end_time" TO "expires_at";
        ALTER TABLE "plan_subscriptions" ADD "status" VARCHAR(10) NOT NULL  DEFAULT 'active';
        ALTER TABLE "plan_subscriptions" RENAME COLUMN "start_time" TO "start_at";
        ALTER TABLE "usagelog" ALTER COLUMN "amount" TYPE BIGINT USING "amount"::BIGINT;
        ALTER TABLE "usagelog" ALTER COLUMN "usage_type" TYPE VARCHAR(19) USING "usage_type"::VARCHAR(19);
        CREATE UNIQUE INDEX "uid_add_ons_name_128256" ON "add_ons" ("name");
        CREATE INDEX "idx_addon_subsc_status_db2eb1" ON "addon_subscriptions" ("status");
        CREATE INDEX "idx_plan_subscr_status_a5b259" ON "plan_subscriptions" ("status");
        CREATE INDEX "idx_usagelog_source__c97faf" ON "usagelog" ("source_subscription_id", "source_subscription_type", "usage_type", "created_at");"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX "idx_addon_subsc_status_db2eb1";
        DROP INDEX "idx_plan_subscr_status_a5b259";
        DROP INDEX "idx_usagelog_source__c97faf";
        DROP INDEX "idx_add_ons_name_128256";
        ALTER TABLE "plans" RENAME COLUMN "message_quota" TO "monthly_message_limit";
        ALTER TABLE "plans" RENAME COLUMN "storage_quota" TO "monthly_message_limit";
        ALTER TABLE "plans" RENAME COLUMN "bot_quota" TO "monthly_message_limit";
        ALTER TABLE "plans" RENAME COLUMN "web_page_quota" TO "monthly_message_limit";
        ALTER TABLE "plans" RENAME COLUMN "multi_modal_parsing_quota" TO "monthly_message_limit";
        ALTER TABLE "plans" RENAME COLUMN "message_quota" TO "multi_modal_parsing_limit";
        ALTER TABLE "plans" RENAME COLUMN "storage_quota" TO "multi_modal_parsing_limit";
        ALTER TABLE "plans" RENAME COLUMN "bot_quota" TO "multi_modal_parsing_limit";
        ALTER TABLE "plans" RENAME COLUMN "web_page_quota" TO "multi_modal_parsing_limit";
        ALTER TABLE "plans" RENAME COLUMN "multi_modal_parsing_quota" TO "multi_modal_parsing_limit";
        ALTER TABLE "plans" RENAME COLUMN "message_quota" TO "total_message_limit";
        ALTER TABLE "plans" RENAME COLUMN "storage_quota" TO "total_message_limit";
        ALTER TABLE "plans" RENAME COLUMN "bot_quota" TO "total_message_limit";
        ALTER TABLE "plans" RENAME COLUMN "web_page_quota" TO "total_message_limit";
        ALTER TABLE "plans" RENAME COLUMN "multi_modal_parsing_quota" TO "total_message_limit";
        ALTER TABLE "plans" RENAME COLUMN "message_quota" TO "web_page_limit";
        ALTER TABLE "plans" RENAME COLUMN "storage_quota" TO "web_page_limit";
        ALTER TABLE "plans" RENAME COLUMN "bot_quota" TO "web_page_limit";
        ALTER TABLE "plans" RENAME COLUMN "web_page_quota" TO "web_page_limit";
        ALTER TABLE "plans" RENAME COLUMN "multi_modal_parsing_quota" TO "web_page_limit";
        ALTER TABLE "plans" RENAME COLUMN "message_quota" TO "storage_limit";
        ALTER TABLE "plans" RENAME COLUMN "storage_quota" TO "storage_limit";
        ALTER TABLE "plans" RENAME COLUMN "bot_quota" TO "storage_limit";
        ALTER TABLE "plans" RENAME COLUMN "web_page_quota" TO "storage_limit";
        ALTER TABLE "plans" RENAME COLUMN "multi_modal_parsing_quota" TO "storage_limit";
        ALTER TABLE "plans" ADD "trial" BOOL NOT NULL  DEFAULT False;
        ALTER TABLE "plans" RENAME COLUMN "message_quota" TO "bot_limit";
        ALTER TABLE "plans" RENAME COLUMN "storage_quota" TO "bot_limit";
        ALTER TABLE "plans" RENAME COLUMN "bot_quota" TO "bot_limit";
        ALTER TABLE "plans" RENAME COLUMN "web_page_quota" TO "bot_limit";
        ALTER TABLE "plans" RENAME COLUMN "multi_modal_parsing_quota" TO "bot_limit";
        ALTER TABLE "plans" DROP COLUMN "plan_type";
        ALTER TABLE "plans" DROP COLUMN "message_quota_grant_type";
        ALTER TABLE "add_ons" RENAME COLUMN "duration_length" TO "duration_days";
        ALTER TABLE "add_ons" DROP COLUMN "duration_unit";
        ALTER TABLE "add_ons" ALTER COLUMN "effect_data" TYPE JSONB USING "effect_data"::JSONB;
        ALTER TABLE "add_ons" ALTER COLUMN "target_field" TYPE VARCHAR(25) USING "target_field"::VARCHAR(25);
        ALTER TABLE "add_ons" ALTER COLUMN "price" TYPE INT USING "price"::INT;
        ALTER TABLE "add_ons" ALTER COLUMN "price" TYPE INT USING "price"::INT;
        ALTER TABLE "add_ons" ALTER COLUMN "price" TYPE INT USING "price"::INT;
        ALTER TABLE "add_ons" ALTER COLUMN "price" TYPE INT USING "price"::INT;
        ALTER TABLE "usagelog" ALTER COLUMN "amount" TYPE INT USING "amount"::INT;
        ALTER TABLE "usagelog" ALTER COLUMN "amount" TYPE INT USING "amount"::INT;
        ALTER TABLE "usagelog" ALTER COLUMN "amount" TYPE INT USING "amount"::INT;
        ALTER TABLE "usagelog" ALTER COLUMN "amount" TYPE INT USING "amount"::INT;
        ALTER TABLE "usagelog" ALTER COLUMN "usage_type" TYPE VARCHAR(19) USING "usage_type"::VARCHAR(19);
        ALTER TABLE "plan_subscriptions" RENAME COLUMN "start_at" TO "start_time";
        ALTER TABLE "plan_subscriptions" RENAME COLUMN "expires_at" TO "start_time";
        ALTER TABLE "plan_subscriptions" RENAME COLUMN "start_at" TO "end_time";
        ALTER TABLE "plan_subscriptions" RENAME COLUMN "expires_at" TO "end_time";
        ALTER TABLE "plan_subscriptions" DROP COLUMN "status";
        ALTER TABLE "addon_subscriptions" ADD "operation_status" VARCHAR(9) NOT NULL;
        ALTER TABLE "addon_subscriptions" RENAME COLUMN "start_at" TO "start_time";
        ALTER TABLE "addon_subscriptions" DROP COLUMN "status";
        CREATE INDEX "idx_usagelog_source__91803c" ON "usagelog" ("source_subscription_id", "source_subscription_type");"""
