from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "sessionmessage" ADD "question_metadata" JSONB;
        ALTER TABLE "sessionmessage" ADD "model" VARCHAR(100);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "sessionmessage" DROP COLUMN "question_metadata";
        ALTER TABLE "sessionmessage" DROP COLUMN "model";"""
