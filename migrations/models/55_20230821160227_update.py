from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "faqs" ADD "config_id" UUID;
        CREATE TABLE IF NOT EXISTS "faqsconfig" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "min_score" DOUBLE PRECISION
);;
        ALTER TABLE "faqs" ADD CONSTRAINT "fk_faqs_faqsconf_66c0e9aa" FOREIGN KEY ("config_id") REFERENCES "faqsconfig" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "faqs" DROP CONSTRAINT "fk_faqs_faqsconf_66c0e9aa";
        ALTER TABLE "faqs" DROP COLUMN "config_id";
        DROP TABLE IF EXISTS "faqsconfig";"""
