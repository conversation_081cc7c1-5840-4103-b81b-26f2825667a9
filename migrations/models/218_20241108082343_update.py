from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "integrationrule_vectorfile" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "integration_rule_id" UUID NOT NULL REFERENCES "integration_rule" ("id") ON DELETE CASCADE,
    "vector_file_id" UUID NOT NULL REFERENCES "vectorfile" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_integration_integra_1e2602" UNIQUE ("integration_rule_id", "vector_file_id")
);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "integrationrule_vectorfile";"""
