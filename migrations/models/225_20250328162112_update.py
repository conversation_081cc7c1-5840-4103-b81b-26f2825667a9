from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "add_ons" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "name" VARCHAR(100) NOT NULL,
    "description" TEXT,
    "price" INT,
    "target_field" VARCHAR(28) NOT NULL,
    "effect_data" JSONB NOT NULL,
    "duration_days" INT NOT NULL
);
COMMENT ON COLUMN "add_ons"."target_field" IS 'MONTHLY_MESSAGE_LIMIT: monthly_message_limit\nSTORAGE_LIMIT: storage_limit\nBOT_LIMIT: bot_limit\nWEB_PAGE_LIMIT: web_page_limit\nMULTI_MODAL_PARSING_LIMIT: multi_modal_parsing_limit\nENABLE_AGENT: enable_agent\nENABLE_MULTI_MODAL_PARSING: enable_multi_modal_parsing\nENABLE_VOICE_CHAT: enable_voice_chat\nENABLE_LARGE_SCREEN_UI: enable_large_screen_ui\nENABLE_CANVAS: enable_canvas\nENABLE_COLLABORATION: enable_collaboration\nENABLE_WIDGET_CUSTOMIZATION: enable_widget_customization\nENABLE_CHAT_HISTORY_ANALYSIS: enable_chat_history_analysis\nENABLE_2D_DIGITAL_HUMAN: enable_2d_digital_human\nAVAILABLE_LLM_MODELS: available_llm_models';
COMMENT ON TABLE "add_ons" IS '附加功能模型：用于定义额外购买的功能或容量。';;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "add_ons";"""
