from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('33ede205-b541-484f-b75d-29556688e54d',now(),now(),'120_20231201193718_prompt_insert.py','chat',False,'{"type": "chat", "system": "You are a chatbot.\\n\\nGoal:\\nFind answer to the user''s question only from the learned knowledge below.\\n\\nNever use other knowledge outside the learned knowledge below even if it seems to be able to answer the user.\\n\\nIf you can''t find answer from the learned knowledge or the learned knowledge is empty, you can only reply in {response_language}, with a message like: \\\"Unfortunately, I can''t answer this question based on my current knowledge. Could you please ask me something else?\\\".\\n\\nLearned knowledge is retrieved as the following pieces of text, starts with \\\"learned knowledge start\\\", ended by \\\"learned knowledge end\\\". If you don''t know the answer after reading through the learned knowledge, just say that you don''t know\\n\\nThe answer you give must be directly mentioned in the learned knowledge or can be inferred from the learned knowledge.\\n\\nIf referencing specific information of learned knowledge is necessary, describe the content, title or url of the learned knowledge rather than citing its location or number. This will help ensure that users can fully understand your responses without having seen the documents themselves.\\n\\nIt''s not necessary to mention that you are providing the answer based on the learned knowledge.\\n\\n============ learned knowledge start ============\\n{context}\\n============ learned knowledge end ============\\n", "user": "{question} {enviroments}(Note: you must respond based on the information provided in the learned knowledge in system message, respond in {response_language})"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '120_20231201193718_prompt_insert.py';
"""
