from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "session" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "session_id" VARCHAR(100) NOT NULL UNIQUE,
    "title" VARCHAR(100) NOT NULL,
    "robot_id" UUID NOT NULL REFERENCES "robot" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_session_robot_i_c28878" ON "session" ("robot_id");;
        CREATE TABLE IF NOT EXISTS "sessionmessage" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "question" TEXT NOT NULL,
    "answer" TEXT,
    "total_tokens" INT NOT NULL,
    "session_id" VARCHAR(100) NOT NULL REFERENCES "session" ("session_id") ON DELETE CASCADE,
    "user_id" VARCHAR(100) REFERENCES "user" ("user_id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_sessionmess_session_d966d2" ON "sessionmessage" ("session_id");;
        CREATE TABLE IF NOT EXISTS "sessionuser" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "session_id" VARCHAR(100) NOT NULL REFERENCES "session" ("session_id") ON DELETE CASCADE,
    "user_id" VARCHAR(100) REFERENCES "user" ("user_id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_sessionuser_session_680d3f" ON "sessionuser" ("session_id");;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "session";
        DROP TABLE IF EXISTS "sessionmessage";
        DROP TABLE IF EXISTS "sessionuser";"""
