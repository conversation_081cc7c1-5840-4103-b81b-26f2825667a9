from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('b254a648-522e-4d8e-85a1-f85d8da515c1',now(),now(),'123_20231205110347_prompt_insert.py','intent_v1',False,'{"type": "chat", "system": "As a problem identification specialist, your task is to recognize user queries in the following scenarios:\\n\\n- Greeting\\n- Ask if you can answer a question\\n- Inquiring about your capabilities\\n- Asking who you are\\n\\nReturn: ABOUT_BOT for these specific scenarios, and ABOUT_COMPANY for all other scenarios.", "user": "Customer input: {question}"}');
INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('dc1b0174-8f5d-452f-acd2-fcf13ba53bb3',now(),now(),'123_20231205110347_prompt_insert.py','language_detect',False,'{"type": "chat", "system": "1. You are a multilingual expert, you can detect all the language of the world.\\n2. Detect the Input language, output language code in:[''en'',''zh-Hans'',''zh-Hant'',''ja'',''ko'',''es'',''fr'',''de'',''ru'',''pt'',''it'',''nl'',''pl'',''sv'',''da'',''no'',''fi'',''tr'',''ar'',''he'',''hi'',''id'',''ms'',''th'',''vi'',''el'',''hu'',''cs'',''sk'',''uk'',''bg'',''ro'',''hr'',''sr'',''sl'',''et'',''lv'',''lt''].\\n3. Output \\\"en\\\" if the Query language cannot be detected.\\n4. If user explicitly ask you to answer in a certain language, use that language.\\n5. Finally return the following JSON format:\\n{{\\n  \\\"language\\\":\\\"\\\",\\n}}\\n\\n==========================\\nExample 1\\nuser: 中国語のサポート\\nai: {{\\n  \\\"language\\\":\\\"ja\\\"\\n}}\\n\\nExample 2\\nuser: jp传播广告审查官的电话?\\nai: {{\\n  \\\"language\\\":\\\"zh-Hans\\\" \\\\ because user is using simplified chinese.\\n}}\\n\\nExample 3\\nuser: 支持日语吗\\nai: {{\\n  \\\"language\\\":\\\"zh-Hans\\\" \\\\ because user is using simplified chinese.\\n}}\\n\\nExample 4\\nuser: 有哪些产品？用英文回复我\\nai: {{\\n  \\\"language\\\":\\\"en\\\" \\\\ because user explicitly requires answer in english.\\n}}\\n=========================\\n\\n6. You must not try to give any additional information and explanations.\\n7. The user input message will be delimited with ``` characters.\\n8. do not return any thing other than the json object.\\n9. If the text input by the user can be recognized as both Japanese and Chinese, and the data_lang_code is also one of either ja or cn, then output the data_lang_code.\\n\\n=====================\\nExample 5\\ndata_lang_code is ja \\nuser: 所在地\\nai: {{\\n  \\\"language\\\":\\\"ja\\\" \\\\ \\\"所在地\\\" is valid in both Japanese and Chinese, and the data_lang_code is ja, so output ja.\\n}}\\n\\nExample 6\\ndata_lang_code is ja \\nuser: 過敏性腸症候群\\nai: {{\\n  \\\"language\\\":\\\"ja\\\" \\\\ \\\"過敏性腸症候群\\\" is valid in both Japanese and Chinese, and the data_lang_code is ja, so output ja.\\n}}\\n\\nExample 7\\ndata_lang_code is zh\\nuser: 過敏性腸症候群\\nai: {{\\n  \\\"language\\\":\\\"zh\\\" \\\\ \\\"過敏性腸症候群\\\" is valid in both Japanese and Chinese, and given the data_lang_code is zh, so output zh.\\n}}\\n=====================\\n\\nIn this case, the data_lang_code is: {ai_language}", "user": "\\nInput: ```{query}```\\nJSON object:\\n"}');
UPDATE prompt SET is_insider = false WHERE id = '611e9ba1-a457-4fe7-b9e9-bef0dd4a9ce4';
UPDATE prompt SET is_insider = false WHERE id = 'fa145ffb-116f-4d8d-8d74-4856f785b7d1';
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '123_20231205110347_prompt_insert.py';
UPDATE prompt SET is_insider = true WHERE id = '611e9ba1-a457-4fe7-b9e9-bef0dd4a9ce4';
UPDATE prompt SET is_insider = true WHERE id = 'fa145ffb-116f-4d8d-8d74-4856f785b7d1';
"""
