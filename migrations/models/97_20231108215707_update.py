from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "signaturestatistics" ADD "icon" VARCHAR(100);
        ALTER TABLE "signaturestatistics" ADD "content" VARCHAR(100);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "signaturestatistics" DROP COLUMN "icon";
        ALTER TABLE "signaturestatistics" DROP COLUMN "content";"""
