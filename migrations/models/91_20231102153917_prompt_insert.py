from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('336b86d9-4003-481a-b3a3-a73471c65408',now(),now(),'91_20231102153917_prompt_insert.py','session_message_title',True,'{"type": "chat", "system": "\\nYou are a chat log extraction title robot.\\nRules:\\n- The following is a chat record content, help me extract a theme (word limit not exceeding 20)\\n------------------ Chat log started ----------------------\\n{chat_log}\\n-------------------Chat log ended -------------------------\\n", "user": "\\nTHEME:\\n"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '91_20231102153917_prompt_insert.py';
"""
