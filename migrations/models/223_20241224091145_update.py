from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """        
-- 1. 删除旧外键
ALTER TABLE integration_sync_log
DROP CONSTRAINT IF EXISTS integration_sync_log_trigger_user_id_fkey;

-- 2. 修改列类型为 uuid
ALTER TABLE integration_sync_log
  ALTER COLUMN trigger_user_id TYPE uuid
  USING trigger_user_id::uuid;

-- 3. 建立新的外键，引用 user(id)
ALTER TABLE integration_sync_log
  ADD CONSTRAINT integration_sync_log_trigger_user_id_fkey
    FOREIGN KEY (trigger_user_id)
    REFERENCES "user" (id)
    ON DELETE CASCADE;
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """SELECT 1 WHERE FALSE;"""
