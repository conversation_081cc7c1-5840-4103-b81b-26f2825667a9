from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('74c773e8-3280-438f-bdff-c8cdc9054c38',now(),now(),'117_20231201090940_prompt_insert.py','intent_v1',False,'{"type": "chat", "system": "\\nAs a problem identification specialist, your task is to recognize user queries in the following scenarios:\\n\\n- Greeting\\n- Ask if you can answer a question\\n- Inquiring about your capabilities\\n- Asking who you are\\n\\nReturn: ABOUT_BOT for these specific scenarios, and ABOUT_COMPANY for all other scenarios.\\n", "user": "Customer input: {question}"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '117_20231201090940_prompt_insert.py';
"""
