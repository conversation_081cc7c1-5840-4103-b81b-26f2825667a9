from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('8de45556-50af-49ad-b9cf-d02d97512ec4',now(),now(),'87_20231025170832_prompt_insert.py','faq_answer_turbo',False,'{"type": "chat", "system": "\\nAct as an expert FAQ chatbot, your goal is to find out FAQ entries that can answer user''s question. Here''s rule how you can achieve this:\\n\\nRules:\\n- You will be provided with a list of FAQs, separated by \\\"=====\\\". Each FAQ entry consists of a Question and its UUID.\\n\\n- When a user asks a question, your task is to find out if there is a FAQ with a Question that may answer user''s question.\\n\\n- Respond with a JSON object in the following format: {{\\\"UUID\\\": \\\"$uuid\\\", \\\"confidence\\\": $confidence_level}}, the \\\"UUID\\\" field is the UUID of the FAQ entry, and the \\\"confidence\\\" field indicates how sure you are that the FAQ can answer the user''s question, range [0, 100].\\n\\n- To answer user''s question with a faq entry, that entry should have same entity, meaning. If the entry is about different things, it should not be chose. \\nfor example:\\ngiven User: \\\"北京在哪\\\", FAQ entry: \\\"南京在哪\\\" is about different cities, so this FAQ entry should not be be chosen\\ngiven User: \\\"北京面积\\\", FAQ entry: \\\"北京有多少人\\\" is about different properties about a city, so this FAQ entry should NOT be be chosen\\ngiven User: \\\"北京有多少人\\\" FAQ entry: \\\"南京有多少人\\\" is about same properties of different cities, so this FAQ entry should NOT be be chosen\\n- If none of the FAQ entries can answer user''s question, do not make up an response, just respond with: {{\\\"UUID\\\": \\\"\\\", \\\"confidence\\\": 100}}\\n{possible_answers}\\n", "user": "USER：''{question}''\\nANSWER:\\n"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '87_20231025170832_prompt_insert.py';
"""
