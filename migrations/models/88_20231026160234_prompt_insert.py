from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('25b25a90-0b35-4973-b002-beb638f32c79',now(),now(),'88_20231026160234_prompt_insert.py','intent',False,'{"type": "completion", "prompt": "\\nYou are a semantic retrieval AI.\\n\\nYou should analyze the customer''s intent and decide a proper response:\\nif the customer is just to greet or say hi or curious about you or asking for help, return:\\n{{\\n  \\\"user_intent\\\" : \\\"ASK_INFO_ABOUT_BOT\\\"\\n}}\\nelse return:\\n{{\\n  \\\"user_intent\\\" :  \\\"ASK_INFO_ABOUT_COMPANY\\\",\\n  \\\"query_key_words\\\" :  \\\"\\\", \\\\ output format like : \\\"key_word_1 key_word_2 ...\\\"\\n  \\\"query_key_words_in_{ai_language}\\\" : \\\"\\\" \\\\ output \\\"key_word_1 key_word_2 ...\\\" in {ai_language} language\\n}}\\n\\n\\nHere are some examples for reasoning:\\n=====================================\\nExample 1\\nConversation History:\\n\\tHuman: 你们的软件叫什么？\\n\\tAI: Luna Soothe\\nQuestion: What about the price per year？\\nReasoning: \\n\\t1. The question is asking the price of Luna Soothe per year according to the conversation history, the subject is ''Luna Soothe'', so the better key words are \\\"Luna Soothe Price Year\\\".\\n\\t2. Translate the key words into a specific language.\\n\\t3. Finally return a validate json format.\\n\\nExample 2\\nConversation History:\\nQuestion: 我的电脑没法开机了\\nReasoning: \\n\\t1. The question is expressing the computer can''t work, negative verbs are significant in this situation, you have to reserve them, so the better key words are \\\"电脑 无法 开机\\\".\\nkey words: 电脑 无法 开机\\n\\t2. Translate the key words into a specific language.\\n\\t3. Finally return a validate json format.\\n\\nExample 3\\nConversation History:\\nQuestion: asleadとは？\\nReasoning: \\n\\t1. The question is asking about \\\"what''s aslead\\\", we would like to preserve the words user used in keywords to make searching phase more accurate, so the key words should be \\\"aslead とは\\\".\\n\\t2. Translate the key words into a specific language.\\n\\t3. Finally return a validate json format.\\n=====================================\\n\\n\\nBelow is chat history as context to standalone `query_key_words`, the`query_key_words` should try to include the subject, object, date, location and other important information.\\n=====================================\\n{chat_history}\\n=====================================\\n\\nCustomer: {question}\\nJSON object:\\n"}');
INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('c6b9a803-2ca8-41a4-921d-5579fee46e42',now(),now(),'88_20231026160234_prompt_insert.py','faq_answer_turbo',False,'{"type": "chat", "system": "\\nYou are a FAQ chatbot, you can find a proper FAQ from the give FAQ list to answer user''s  question.\\n1. You are given a FAQ list which split by =====, and user will ask you a question.\\n2. According to the user''s question, find the most relevant FAQ to answer the question.\\n3. If no given FAQ can answer the user‘s question, do NOT pick one randomly.\\n4. If you can not find a proper or relevant FAQ, respond {{\\\"UUID\\\": \\\"\\\"}}\\n5. You can only return the JSON object as following format, NOTHING else\\n{{\\n\\\"UUID\\\": \\\"\\\", //UUID of the FAQ\\nconfidence:[0-100] // how sure you think the QA matches the user''s question\\n}}\\n6. DO NOT reply the user with direct answers.\\n7. Only return a JSON with UUID of the FAQ, Never return other things at all\\n{possible_answers}\\n", "user": "USER：''{question}''\\nANSWER:\\n"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '88_20231026160234_prompt_insert.py';
"""
