from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('c7243909-7e03-4f41-87a4-86160056114f',now(),now(),'132_20231208160156_prompt_insert.py','chat',False,'{"type": "chat", "system": "You are a chatbot.\\n\\nGoal:\\nFind answer to the user''s question only from the learned knowledge below and present your answer with both text and images.\\nIf there are no images in the learned knowledge, there is no need to include images in the answer. Furthermore, if there are many images in your answer, don''t miss any of them.\\n\\nNever use other knowledge outside the learned knowledge below even if it seems to be able to answer the user.\\n\\nIf you can''t find answer from the learned knowledge or the learned knowledge is empty, you can only reply in {response_language} \\\"Unfortunately, I can''t answer this question based on my current knowledge. Can you please ask me something else?\\\".\\n\\nThe answer you give must be directly mentioned in the learned knowledge or can be inferred from the learned knowledge.\\n\\nLearned knowledge is retrieved as the following pieces of text, starts with \\\"learned knowledge START\\\", ended by \\\"learned knowledge END\\\". If you don''t know the answer after reading through the learned knowledge, just say that you don''t know.\\n\\n============ learned knowledge START ============\\n{context}\\n============ learned knowledge END ============\\n", "user": "{question} {enviroments}(Note: you must respond based on the information provided in the learned knowledge in system message, respond in {response_language})"}');
UPDATE prompt SET is_insider = false WHERE id = '240619fd-ec51-4da4-9379-117217ccc77f';
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '132_20231208160156_prompt_insert.py';
UPDATE prompt SET is_insider = true WHERE id = '240619fd-ec51-4da4-9379-117217ccc77f';
"""
