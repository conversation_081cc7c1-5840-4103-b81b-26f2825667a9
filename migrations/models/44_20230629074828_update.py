from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "faqproperty_faqs";"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE "faqproperty_faqs" (
    "faqs_id" UUID NOT NULL REFERENCES "faqs" ("id") ON DELETE CASCADE,
    "faqproperty_id" UUID NOT NULL REFERENCES "faqproperty" ("id") ON DELETE CASCADE
);"""
