from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "plan_subscriptions" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "start_time" TIMESTAMPTZ NOT NULL,
    "operation_status" VARCHAR(9) NOT NULL,
    "plan_id" UUID NOT NULL REFERENCES "plans" ("id") ON DELETE CASCADE,
    "user_id" UUID NOT NULL REFERENCES "user" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "plan_subscriptions"."operation_status" IS 'CANCELLED: cancelled\nACTIVE: active\nEXPIRED: expired';
COMMENT ON TABLE "plan_subscriptions" IS 'Model for user subscriptions to specific Plans.';;
        CREATE TABLE IF NOT EXISTS "addon_subscriptions" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "start_time" TIMESTAMPTZ NOT NULL,
    "operation_status" VARCHAR(9) NOT NULL,
    "add_on_id" UUID NOT NULL REFERENCES "add_ons" ("id") ON DELETE CASCADE,
    "user_id" UUID NOT NULL REFERENCES "user" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "addon_subscriptions"."operation_status" IS 'CANCELLED: cancelled\nACTIVE: active\nEXPIRED: expired';
COMMENT ON TABLE "addon_subscriptions" IS 'Model for user subscriptions to specific AddOns.';;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "plan_subscriptions";
        DROP TABLE IF EXISTS "addon_subscriptions";"""
