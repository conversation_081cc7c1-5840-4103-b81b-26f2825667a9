from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "faqresource" DROP CONSTRAINT "faqresource_vector_id_fkey";
        DROP INDEX IF EXISTS "idx_faqresource_vector__073e44";
        ALTER TABLE "faqresource" RENAME COLUMN "vector_id" TO "vector_file_id";
        ALTER TABLE "faqresource" ADD CONSTRAINT "fk_faqresou_vectorfi_19c067a9" FOREIGN KEY ("vector_file_id") REFERENCES "vectorfile" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "faqresource" DROP CONSTRAINT "faqresource_vector_id_fkey";
        ALTER TABLE "faqresource" RENAME COLUMN "vector_file_id" TO "vector_id";
        ALTER TABLE "faqresource" ADD CONSTRAINT "fk_faqresou_vector_99091941" FOREIGN KEY ("vector_id") REFERENCES "vector" ("id") ON DELETE CASCADE;"""
