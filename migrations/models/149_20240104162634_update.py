from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "vectorfile" ADD "file_size" INT NOT NULL  DEFAULT 0;
        ALTER TABLE "vectorfile" ADD "token_count" INT NOT NULL  DEFAULT 0;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "vectorfile" DROP COLUMN "file_size";
        ALTER TABLE "vectorfile" DROP COLUMN "token_count";"""
