from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "integrationrule_vectorfile";
        CREATE TABLE IF NOT EXISTS "integrationrule_vectorfile" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "integration_rule_id" UUID NOT NULL REFERENCES "integration_rule" ("id") ON DELETE CASCADE,
    "vectorfile_id" UUID NOT NULL REFERENCES "vectorfile" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_integration_integra_0165fa" UNIQUE ("integration_rule_id", "vectorfile_id")
);
CREATE INDEX IF NOT EXISTS "idx_integration_integra_09d3c8" ON "integrationrule_vectorfile" ("integration_rule_id");
CREATE INDEX IF NOT EXISTS "idx_integration_vectorf_faca71" ON "integrationrule_vectorfile" ("vectorfile_id");"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "integrationrule_vectorfile";"""
