from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "faqs" ADD "session_message_id" UUID;
        ALTER TABLE "faqs" ADD "citation" JSONB;
        ALTER TABLE "sessionmessage" ADD "faq_id" UUID;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "faqs" DROP COLUMN "session_message_id";
        ALTER TABLE "faqs" DROP COLUMN "citation";
        ALTER TABLE "sessionmessage" DROP COLUMN "faq_id";"""
