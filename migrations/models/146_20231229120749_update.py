from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "userconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        CREATE INDEX "idx_sessionmess_robot_i_afa039" ON "sessionmessagepageaccessinfo" ("robot_id");
        CREATE INDEX "idx_sessionmetr_robot_i_4a9fcf" ON "sessionmetricshourly" ("robot_id");
        CREATE INDEX "idx_signaturest_robot_i_617252" ON "signaturestatistics" ("robot_id");"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX "idx_sessionmess_robot_i_afa039";
        DROP INDEX "idx_sessionmetr_robot_i_4a9fcf";
        DROP INDEX "idx_signaturest_robot_i_617252";
        ALTER TABLE "userconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);"""
