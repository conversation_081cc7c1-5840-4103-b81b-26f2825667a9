from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX "idx_plan_subscr_status_a5b259";
        DROP INDEX "idx_addon_subsc_status_db2eb1";
        ALTER TABLE "addon_subscriptions" ADD "is_cancelled" BOOL NOT NULL  DEFAULT False;
        ALTER TABLE "addon_subscriptions" ADD "expires_at" TIMESTAMPTZ NOT NULL  DEFAULT '2025-04-11 11:56:55.603623';
        ALTER TABLE "addon_subscriptions" ALTER COLUMN "status" DROP DEFAULT;
        ALTER TABLE "addon_subscriptions" ALTER COLUMN "status" TYPE VARCHAR(9) USING "status"::VARCHAR(9);
        ALTER TABLE "addon_subscriptions" ALTER COLUMN "status" TYPE VARCHAR(9) USING "status"::VARCHAR(9);
        ALTER TABLE "plan_subscriptions" ALTER COLUMN "expires_at" SET DEFAULT '2025-04-11 11:56:55.567505';
        ALTER TABLE "plan_subscriptions" ALTER COLUMN "status" DROP DEFAULT;
        ALTER TABLE "plan_subscriptions" ALTER COLUMN "status" TYPE VARCHAR(9) USING "status"::VARCHAR(9);
        ALTER TABLE "plan_subscriptions" ALTER COLUMN "status" TYPE VARCHAR(9) USING "status"::VARCHAR(9);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "plan_subscriptions" ALTER COLUMN "expires_at" DROP DEFAULT;
        ALTER TABLE "plan_subscriptions" ALTER COLUMN "status" SET DEFAULT 'active';
        ALTER TABLE "plan_subscriptions" ALTER COLUMN "status" TYPE VARCHAR(10) USING "status"::VARCHAR(10);
        ALTER TABLE "plan_subscriptions" ALTER COLUMN "status" TYPE VARCHAR(10) USING "status"::VARCHAR(10);
        ALTER TABLE "addon_subscriptions" DROP COLUMN "is_cancelled";
        ALTER TABLE "addon_subscriptions" DROP COLUMN "expires_at";
        ALTER TABLE "addon_subscriptions" ALTER COLUMN "status" SET DEFAULT 'active';
        ALTER TABLE "addon_subscriptions" ALTER COLUMN "status" TYPE VARCHAR(10) USING "status"::VARCHAR(10);
        ALTER TABLE "addon_subscriptions" ALTER COLUMN "status" TYPE VARCHAR(10) USING "status"::VARCHAR(10);
        CREATE INDEX "idx_plan_subscr_status_a5b259" ON "plan_subscriptions" ("status");
        CREATE INDEX "idx_addon_subsc_status_db2eb1" ON "addon_subscriptions" ("status");"""
