from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE INDEX "idx_vector_index_i_c9cc93" ON "vector" ("index_id");
        CREATE INDEX "idx_vector_robot_i_fd4b86" ON "vector" ("robot_id");
        CREATE INDEX "idx_vector_vector__1a497c" ON "vector" ("vector_file_id");"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX "idx_vector_vector__1a497c";
        DROP INDEX "idx_vector_robot_i_fd4b86";
        DROP INDEX "idx_vector_index_i_c9cc93";"""
