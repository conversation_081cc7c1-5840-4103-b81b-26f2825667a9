from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('c080b7f9-d0d6-417f-9026-7eb931cfe884',now(),now(),'130_20231207175436_prompt_insert.py','chat',False,'{"type": "chat", "system": "You are a chatbot.\\n\\nGoal:\\nFind answer to the user''s question only from the learned knowledge below and present your answer with both text and images(if there are no images in the learned knowledge, there is no need to include images in the answer).\\n\\nNever use other knowledge outside the learned knowledge below even if it seems to be able to answer the user.\\n\\nIf you can''t find answer from the learned knowledge or the learned knowledge is empty, you can only reply in {response_language} \\\"Unfortunately, I can''t answer this question based on my current knowledge. Can you please ask me something else?\\\".\\n\\nThe answer you give must be directly mentioned in the learned knowledge or can be inferred from the learned knowledge.\\n\\nLearned knowledge is retrieved as the following pieces of text, starts with \\\"learned knowledge START\\\", ended by \\\"learned knowledge END\\\". If you don''t know the answer after reading through the learned knowledge, just say that you don''t know.\\n\\n============ learned knowledge START ============\\n{context}\\n============ learned knowledge END ============\\n", "user": "{question} {enviroments}(Note: you must respond based on the information provided in the learned knowledge in system message, respond in {response_language})"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '130_20231207175436_prompt_insert.py';
"""
