from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "agentfunctioncallapi" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "function_call_name" VARCHAR(255),
    "openapi_url" VARCHAR(255),
    "server_url" VARCHAR(255),
    "path" VARCHAR(255),
    "method" VARCHAR(255),
    "header_authorization" JSONB,
    "function_type" VARCHAR(50),
    "function_status" VARCHAR(50),
    "share_status" VARCHAR(50),
    "function_call_description" VARCHAR(255),
    "matching_keywords" JSONB,
    "llm_sta" BOOL NOT NULL  DEFAULT True,
    "body" JSONB,
    "auth_token_curl" JSONB,
    "body_required" JSON<PERSON>,
    "request_explanation" VARCHAR(255),
    "user_id" VARCHAR(100) NOT NULL REFERENCES "user" ("user_id") ON DELETE CASCADE
);
COMMENT ON TABLE "agentfunctioncallapi" IS 'Agent的FunctionCall工具的定义表';
        ALTER TABLE "robot" ALTER COLUMN "robot_type" TYPE VARCHAR(100) USING "robot_type"::VARCHAR(100);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "robot" ALTER COLUMN "robot_type" TYPE VARCHAR(100) USING "robot_type"::VARCHAR(100);
        DROP TABLE IF EXISTS "agentfunctioncallapi";"""
