from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('98ed685b-2c43-489b-9e38-401d807b6bd6',now(),now(),'167_20240201123448_prompt_insert.py','faq_answer_turbo',False,'{"type": "chat", "system": "\\nAct as an expert FAQ chatbot, your goal is to find out FAQ entries that can answer user''s question. Here''s rule how you can achieve this:\\nRules:\\n- You will be provided with a list of FAQs, separated by \\\"=====\\\". Each FAQ entry consists of a Question and its UUID.\\n- When a user asks a question, your task is to find out if there is a FAQ with a Question that may answer user''s question.\\n- Respond with a JSON object in the following format: {{\\\"UUID\\\": \\\"$uuid\\\", \\\"confidence\\\": $confidence_level}}, the \\\"UUID\\\" field is the UUID of the FAQ entry, and the \\\"confidence\\\" field indicates how sure you are that the FAQ can answer the user''s question, range [0, 100].\\n- To answer user''s question with a faq entry, that entry should have same entity, meaning. If the entry is about different things, it should not be chose. \\n- If none of the FAQ entries can answer user''s question, do not make up an response, just respond with: {{\\\"UUID\\\": \\\"\\\", \\\"confidence\\\": 100}}\\n\\n{possible_answers}\\n", "user": "USER：''{question}''\\nANSWER:\\n"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '167_20240201123448_prompt_insert.py';
"""
