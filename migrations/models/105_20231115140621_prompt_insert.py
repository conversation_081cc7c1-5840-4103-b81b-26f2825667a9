from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('5eaab070-e580-487c-8701-2bee86d09f5d',now(),now(),'105_20231115140621_prompt_insert.py','query_key_words',False,'{"type": "completion", "prompt": "\\nAs a Semantic Analysis AI, your task is to comprehend the customer''s intent.\\n\\nInstructions:\\nExamine the conversation history to comprehend the context and the customer''s intent.\\nIn case the user''s present inquiry is incomplete or missing crucial details, identify the main keywords from the provided chat history. Focus on elements like the subject, object, date, location, and other significant specifics.\\nFinally, output the key words in specific language.\\n\\nExample 1\\nConversation History:\\n=====================================\\n        Human: 你们的软件叫什么？\\n        AI: Luna Soothe\\n=====================================\\nQuery: What about the price per year？\\nOutput in ja language: Luna Soothe 価格 毎年\\n\\n\\nExample 2\\nConversation History:\\n=====================================\\n        Human: 你们公司叫什么名？\\n        AI: Sparticle\\n=====================================\\nQuery: Where is your company？\\nOutput in en language: Sparticle address\\n\\n\\nExample 3\\nConversation History:\\n=====================================\\n=====================================\\nQuery: 我的电脑没法开机了\\nOutput in zh-CN language: 电脑 无法 开机\\n\\n\\nExample 4\\nConversation History:\\n=====================================\\n=====================================\\nQuery: asleadとは？\\nOutput in ja language: aslead とは\\n\\n\\nExample 5\\nConversation History:\\n=====================================\\n        Human: 微软2020年的营业额是多少？ \\n        AI: 300亿美元。\\n=====================================\\nQuery:利润呢？\\nOutput in ja language: マイクロソフトは 2020年 利益\\n\\n\\nExample 6\\nConversation History:\\n=====================================\\n        Human: 哪天去北京玩？ \\n        AI: 10.12 \\n=====================================\\nQuery:那里当天天气如何？\\nOutput in zh language: 北京 10.12 天气\\n\\n\\n\\nConversation History:\\n=====================================\\n{chat_history}\\n=====================================\\nQuery: {question}\\nOutput in {ai_language} language:\\n"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '105_20231115140621_prompt_insert.py';
"""
