from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "sessionmessage" ADD "canvas" JSONB;
        ALTER TABLE "sessionmessage" DROP COLUMN "canvas_url";"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "sessionmessage" ADD "canvas_url" VARCHAR(1024);
        ALTER TABLE "sessionmessage" DROP COLUMN "canvas";"""
