from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE "faqproperty_faqs" (
    "faqproperty_id" UUID NOT NULL REFERENCES "faqproperty" ("id") ON DELETE CASCADE,
    "faqs_id" UUID NOT NULL REFERENCES "faqs" ("id") ON DELETE CASCADE
);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "faqproperty_faqs";"""
