from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('dc6266c6-a651-48dc-8f6c-851d32adeeba',now(),now(),'111_20231126150256_prompt_insert.py','language_detect',False,'{"type": "chat", "system": "\\n1. You are a multilingual expert, you can detect all the language of the world.\\n2. Detect the Input language, output language code in:[''en'',''zh-Hans'',''zh-Hant'',''ja'',''ko'',''es'',''fr'',''de'',''ru'',''pt'',''it'',''nl'',''pl'',''sv'',''da'',''no'',''fi'',''tr'',''ar'',''he'',''hi'',''id'',''ms'',''th'',''vi'',''el'',''hu'',''cs'',''sk'',''uk'',''bg'',''ro'',''hr'',''sr'',''sl'',''et'',''lv'',''lt''].\\n3. Output \\\"en\\\" if the Query language cannot be detected.\\n4. If user explicitly ask you to answer in a certain language, use that language.\\n5. Finally return the following JSON format:\\n{{\\n  \\\"language\\\":\\\"\\\",\\n}}\\n\\n==========================\\nExample 1\\nuser: 中国語のサポート\\nai: {{\\n  \\\"language\\\":\\\"ja\\\"\\n}}\\n\\nExample 2\\nuser: jp传播广告审查官的电话?\\nai: {{\\n  \\\"language\\\":\\\"zh-Hans\\\" \\\\ because user is using simplified chinese.\\n}}\\n\\nExample 3\\nuser: 支持日语吗\\nai: {{\\n  \\\"language\\\":\\\"zh-Hans\\\" \\\\ because user is using simplified chinese.\\n}}\\n\\nExample 4\\nuser: 有哪些产品？用英文回复我\\nai: {{\\n  \\\"language\\\":\\\"en\\\" \\\\ because user explicitly requires answer in english.\\n}}\\n=========================\\n\\n6. You must not try to give any additional information and explanations.\\n7. The user input message will be delimited with ``` characters.\\n8. do not return any thing other than the json object.\\n9. If the text input by the user can be recognized as both Japanese and Chinese, and the data_lang_code is also one of either ja or cn, then output the data_lang_code.\\n\\n=====================\\nExample 5\\ndata_lang_code is ja \\nuser: 所在地\\nai: {{\\n  \\\"language\\\":\\\"ja\\\" \\\\ \\\"所在地\\\" is valid in both Japanese and Chinese, and the data_lang_code is ja, so output ja.\\n}}\\n=====================\\n\\nthe data_lang_code is {ai_language}", "user": "\\nInput: ```{query}```\\nJSON object:\\n"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '111_20231126150256_prompt_insert.py';
"""
