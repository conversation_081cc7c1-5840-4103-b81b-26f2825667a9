from tortoise import BaseDBAsync<PERSON><PERSON>


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "dataset" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "data_status" VARCHAR(6) NOT NULL  DEFAULT 'init',
    "name" VARCHAR(255) NOT NULL,
    "metadata" JSONB,
    "user_id" VARCHAR(100) NOT NULL REFERENCES "user" ("user_id") ON DELETE CASCADE
);
COMMENT ON COLUMN "dataset"."data_status" IS 'INIT: init\nREADY: ready\nFROZEN: frozen';;
        CREATE TABLE IF NOT EXISTS "datasetrobot" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "dataset_id" UUID NOT NULL REFERENCES "dataset" ("id") ON DELETE CASCADE,
    "robot_id" UUID NOT NULL REFERENCES "robot" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_datasetrobo_dataset_925e71" ON "datasetrobot" ("dataset_id");
CREATE INDEX IF NOT EXISTS "idx_datasetrobo_robot_i_189942" ON "datasetrobot" ("robot_id");
COMMENT ON TABLE "datasetrobot" IS '# create Dataset muilt connection to robot model';;
        ALTER TABLE "faqproperty" ADD "dataset_id" UUID;
        ALTER TABLE "faqs" ADD "dataset_id" UUID;
        ALTER TABLE "functioncallapi" ADD "dataset_id" UUID;
        ALTER TABLE "robotmd5" ADD "dataset_id" UUID;
        ALTER TABLE "robotmd5" ALTER COLUMN "robot_id" DROP NOT NULL;
        ALTER TABLE "url" ALTER COLUMN "status" TYPE VARCHAR(20) USING "status"::VARCHAR(20);
        ALTER TABLE "vectorfile" ADD "dataset_id" UUID;
        ALTER TABLE "vectorfile" ALTER COLUMN "robot_id" DROP NOT NULL;
        ALTER TABLE "vectorfile" ALTER COLUMN "file_status" TYPE VARCHAR(20) USING "file_status"::VARCHAR(20);
        CREATE INDEX "idx_faqproperty_dataset_06c9d6" ON "faqproperty" ("dataset_id");
        ALTER TABLE "faqproperty" ADD CONSTRAINT "fk_faqprope_dataset_2680886c" FOREIGN KEY ("dataset_id") REFERENCES "dataset" ("id") ON DELETE CASCADE;
        CREATE INDEX "idx_faqs_dataset_5d6afb" ON "faqs" ("dataset_id");
        ALTER TABLE "faqs" ADD CONSTRAINT "fk_faqs_dataset_626bfe97" FOREIGN KEY ("dataset_id") REFERENCES "dataset" ("id") ON DELETE CASCADE;
        CREATE INDEX "idx_functioncal_dataset_13904e" ON "functioncallapi" ("dataset_id");
        ALTER TABLE "functioncallapi" ADD CONSTRAINT "fk_function_dataset_e0e2b9c6" FOREIGN KEY ("dataset_id") REFERENCES "dataset" ("id") ON DELETE CASCADE;
        CREATE INDEX "idx_robotmd5_dataset_83232d" ON "robotmd5" ("dataset_id");
        ALTER TABLE "robotmd5" ADD CONSTRAINT "fk_robotmd5_dataset_a50e073b" FOREIGN KEY ("dataset_id") REFERENCES "dataset" ("id") ON DELETE CASCADE;
        CREATE INDEX "idx_vectorfile_dataset_aed480" ON "vectorfile" ("dataset_id");
        ALTER TABLE "vectorfile" ADD CONSTRAINT "fk_vectorfi_dataset_e204321d" FOREIGN KEY ("dataset_id") REFERENCES "dataset" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "functioncallapi" DROP CONSTRAINT "fk_function_dataset_e0e2b9c6";
        DROP INDEX "idx_functioncal_dataset_13904e";
        ALTER TABLE "faqproperty" DROP CONSTRAINT "fk_faqprope_dataset_2680886c";
        DROP INDEX "idx_faqproperty_dataset_06c9d6";
        ALTER TABLE "vectorfile" DROP CONSTRAINT "fk_vectorfi_dataset_e204321d";
        DROP INDEX "idx_vectorfile_dataset_aed480";
        ALTER TABLE "robotmd5" DROP CONSTRAINT "fk_robotmd5_dataset_a50e073b";
        DROP INDEX "idx_robotmd5_dataset_83232d";
        ALTER TABLE "faqs" DROP CONSTRAINT "fk_faqs_dataset_626bfe97";
        DROP INDEX "idx_faqs_dataset_5d6afb";
        ALTER TABLE "url" ALTER COLUMN "status" TYPE VARCHAR(20) USING "status"::VARCHAR(20);
        ALTER TABLE "faqs" DROP COLUMN "dataset_id";
        ALTER TABLE "robotmd5" DROP COLUMN "dataset_id";
        ALTER TABLE "vectorfile" DROP COLUMN "dataset_id";
        ALTER TABLE "vectorfile" ALTER COLUMN "file_status" TYPE VARCHAR(20) USING "file_status"::VARCHAR(20);
        ALTER TABLE "faqproperty" DROP COLUMN "dataset_id";
        ALTER TABLE "functioncallapi" DROP COLUMN "dataset_id";
        DROP TABLE IF EXISTS "datasetrobot";
        DROP TABLE IF EXISTS "dataset";"""
