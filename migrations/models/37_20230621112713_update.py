from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "imageattachment" ADD "robot_id" UUID;
        ALTER TABLE "imageattachment" ADD CONSTRAINT "fk_imageatt_robot_075212c8" FOREIGN KEY ("robot_id") REFERENCES "robot" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "imageattachment" DROP CONSTRAINT "fk_imageatt_robot_075212c8";
        ALTER TABLE "imageattachment" DROP COLUMN "robot_id";"""
