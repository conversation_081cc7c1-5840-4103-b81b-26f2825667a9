from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        CREATE TABLE IF NOT EXISTS "sessionmessagepageaccessinfo" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "robot_id" UUID NOT NULL,
    "title" VARCHAR(1024),
    "url" VARCHAR(1024)
);;
        CREATE TABLE IF NOT EXISTS "sessionmessagepageaccessmetrics" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "page_access_info_id" UUID NOT NULL REFERENCES "sessionmessagepageaccessinfo" ("id") ON DELETE CASCADE
);;
        CREATE TABLE IF NOT EXISTS "sessionmetricshourly" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "robot_id" UUID NOT NULL,
    "hour_of_day" INT,
    "message_count" INT,
    "like_count" INT,
    "dislike_count" INT,
    "new_user_count" INT,
    "old_user_count" INT
);;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        DROP TABLE IF EXISTS "sessionmessagepageaccessinfo";
        DROP TABLE IF EXISTS "sessionmessagepageaccessmetrics";
        DROP TABLE IF EXISTS "sessionmetricshourly";"""
