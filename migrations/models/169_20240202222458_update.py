from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "sessionevaluate" ADD "message_count" INT NOT NULL  DEFAULT 0;
        ALTER TABLE "sessionevaluate" RENAME COLUMN "satisfaction" TO "score";
        ALTER TABLE "sessionevaluate" ADD "unanswerable_count" INT NOT NULL  DEFAULT 0;
        ALTER TABLE "sessionevaluate" ADD "correct_count" INT NOT NULL  DEFAULT 0;
        ALTER TABLE "sessionevaluate" DROP COLUMN "status";"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "sessionevaluate" ADD "status" VARCHAR(20) NOT NULL  DEFAULT 'pending';
        ALTER TABLE "sessionevaluate" RENAME COLUMN "score" TO "satisfaction";
        ALTER TABLE "sessionevaluate" DROP COLUMN "message_count";
        ALTER TABLE "sessionevaluate" DROP COLUMN "unanswerable_count";
        ALTER TABLE "sessionevaluate" DROP COLUMN "correct_count";"""
