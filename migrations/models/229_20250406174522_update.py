from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "plans" DROP COLUMN "enable_2d_digital_human";
        ALTER TABLE "plans" DROP COLUMN "enable_widget_customization";
        ALTER TABLE "plans" DROP COLUMN "enable_large_screen_ui";
        ALTER TABLE "plans" DROP COLUMN "available_llm_models";
        ALTER TABLE "plans" DROP COLUMN "enable_voice_chat";
        ALTER TABLE "plans" DROP COLUMN "enable_canvas";
        ALTER TABLE "plans" DROP COLUMN "enable_agent";
        ALTER TABLE "plans" DROP COLUMN "enable_multi_modal_parsing";
        ALTER TABLE "plans" DROP COLUMN "enable_chat_history_analysis";
        ALTER TABLE "plans" DROP COLUMN "enable_collaboration";"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "plans" ADD "enable_2d_digital_human" BOOL NOT NULL  DEFAULT False;
        ALTER TABLE "plans" ADD "enable_widget_customization" BOOL NOT NULL  DEFAULT False;
        ALTER TABLE "plans" ADD "enable_large_screen_ui" BOOL NOT NULL  DEFAULT False;
        ALTER TABLE "plans" ADD "available_llm_models" JSONB NOT NULL;
        ALTER TABLE "plans" ADD "enable_voice_chat" BOOL NOT NULL  DEFAULT False;
        ALTER TABLE "plans" ADD "enable_canvas" BOOL NOT NULL  DEFAULT False;
        ALTER TABLE "plans" ADD "enable_agent" BOOL NOT NULL  DEFAULT False;
        ALTER TABLE "plans" ADD "enable_multi_modal_parsing" BOOL NOT NULL  DEFAULT False;
        ALTER TABLE "plans" ADD "enable_chat_history_analysis" BOOL NOT NULL  DEFAULT False;
        ALTER TABLE "plans" ADD "enable_collaboration" BOOL NOT NULL  DEFAULT False;"""
