from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "sessionmessage" ADD "anonymous_username" VARCHAR(100);
        ALTER TABLE "sessionuser" ADD "anonymous_username" VARCHAR(100);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "sessionuser" DROP COLUMN "anonymous_username";
        ALTER TABLE "sessionmessage" DROP COLUMN "anonymous_username";"""
