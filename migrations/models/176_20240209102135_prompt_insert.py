from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
       INSERT INTO "prompt" ("id","created_at","updated_at","version","prompt_type","is_insider","content") VALUES ('69fc7124-cc2f-4755-bbc4-259d0bcc4b3e',now(),now(),'176_20240209102135_prompt_insert.py','ask_user_to_provide_model_turbo',False,'{"type": "chat", "system": "\\nYou are a translation expert. \\nThe following context is a content of markdown format or html format.\\nTranslate the given context into {language} language and keep the original format, and you should neither add any tags nor remove any tags.\\nDo not translate or add LINKs when you are translating the given context.\\nYou are not allowed to include any other information in the answer, even if it is related.\\nYou are not allowed to optimize the original words because some words are proper noun.\\n\\ncontext:\\n{answer}\\n\\n", "user": "\\nanswer:\\n"}');
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DELETE FROM prompt WHERE version = '176_20240209102135_prompt_insert.py';
"""
