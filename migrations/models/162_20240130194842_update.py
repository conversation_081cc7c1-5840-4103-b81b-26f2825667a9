from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "session" ADD "source" VARCHAR(100)   DEFAULT 'playground';
        ALTER TABLE "sessionmessage" DROP COLUMN "source";"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "session" DROP COLUMN "source";
        ALTER TABLE "robotconfig" ALTER COLUMN "key" TYPE VARCHAR(100) USING "key"::VARCHAR(100);
        ALTER TABLE "sessionmessage" ADD "source" VARCHAR(100)   DEFAULT 'playground';"""
