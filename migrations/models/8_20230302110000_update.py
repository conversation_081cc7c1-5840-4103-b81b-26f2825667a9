from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "vector" ADD "vector_file_id" UUID;
        ALTER TABLE "vector" ADD "text_origin" TEXT;
        ALTER TABLE "vector" ADD "status" VARCHAR(50) NOT NULL  DEFAULT 'init';
        ALTER TABLE "vector" ALTER COLUMN "text_type" SET DEFAULT 'en';
        ALTER TABLE "vectorfile" ADD "file_lang" VARCHAR(50);
        ALTER TABLE "vectorfile" ALTER COLUMN "file_status" SET DEFAULT 'process';
        ALTER TABLE "vector" ADD CONSTRAINT "fk_vector_vectorfi_24cfa43e" FOREIGN KEY ("vector_file_id") REFERENCES "vectorfile" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "vector" DROP CONSTRAINT "fk_vector_vectorfi_24cfa43e";
        ALTER TABLE "vector" DROP COLUMN "vector_file_id";
        ALTER TABLE "vector" DROP COLUMN "text_origin";
        ALTER TABLE "vector" DROP COLUMN "status";
        ALTER TABLE "vector" ALTER COLUMN "text_type" DROP DEFAULT;
        ALTER TABLE "vectorfile" DROP COLUMN "file_lang";
        ALTER TABLE "vectorfile" ALTER COLUMN "file_status" SET DEFAULT 'complete';"""
