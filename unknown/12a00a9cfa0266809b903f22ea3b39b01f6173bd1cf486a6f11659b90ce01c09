from abc import ABC, abstractmethod
from typing import Dict, List
from datetime import datetime
from loguru import logger as logging

from mygpt.agent_functioncall.types import Response, Result
from mygpt.enums import OpenAIModel, FunctionCallStyle, LLM_PROVIDER
from mygpt.openai_utils import question_answer_with_function_call


class LLMBaseClient(ABC):
    def __init__(
        self,
        meta_prompt: str,
        history: List,
        callbacks: List = None,
        call_style=FunctionCallStyle.OPENAI_STYLE,
        model: OpenAIModel = OpenAIModel.CLAUDE_35_SONNET,
    ):
        self.model = model
        self.meta_prompt = meta_prompt
        self.history = history
        self.callbacks = callbacks
        self.call_style = call_style
        self.messages = []
        # openai_history = convert_base_messages_to_openai_format(self.chat_history)
        # self.construct_request_messages()

    def construct_request_messages(self):
        system_message = {
            "role": "system",
            "content": self.meta_prompt
            + "The current time zone time:"
            + datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        }
        self.messages.append(system_message)
        self.messages.extend(self.history)

    @abstractmethod
    def add_llm_tool_calls_response(self, response: Response):
        pass

    @abstractmethod
    def add_tool_calls_result(self, tool_results: List[Result]):
        pass

    @abstractmethod
    async def request_model(self, tools: List, max_tokens=4096):
        pass


class OpenAILLMClient(LLMBaseClient):
    def __init__(
        self,
        meta_prompt: str,
        history: List,
        callbacks: List = None,
        call_style=FunctionCallStyle.OPENAI_STYLE,
        model: OpenAIModel = OpenAIModel.GPT_4_OMNI_2024_11_20,
    ):
        super().__init__(meta_prompt, history, callbacks, call_style, model=model)

    def add_llm_tool_calls_response(self, response: Response):
        if response.tool_calls:
            if response.turn_answer:
                content = response.turn_answer
            elif response.content:
                content = response.content
            else:
                content = ""
            message = {
                "content": content,
                "refusal": "",
                "role": response.role,
                "tool_calls": response.tool_calls,
            }
            self.messages.append(message)

    def add_tool_calls_result(self, tool_results: List[Result]):
        if tool_results:
            call_result_list = []
            for tool_call_result in tool_results:
                call_result_list.append(
                    {
                        "tool_call_id": tool_call_result.tool_call_id,
                        "role": "tool",
                        "name": tool_call_result.name,
                        "content": tool_call_result.value,
                    }
                )
            self.messages.extend(call_result_list)

    async def request_model(self, tools: List, max_tokens=4096):
        logging.info(
            f"【OpenAILLMClient.request_model】meta_prompt: {self.meta_prompt}"
        )
        logging.info(f"【OpenAILLMClient.request_model】messages: {self.messages}")
        gpt_response_iter = await question_answer_with_function_call(
            messages=self.messages,
            functions=tools,
            function_call="auto",
            max_tokens=max_tokens,
            request_timeout=60,
            stream=True,
            model_name=self.model,
            callbacks=self.callbacks,
            function_call_style=self.call_style,
        )
        return gpt_response_iter


class ClaudeLLMClient(LLMBaseClient):
    def __init__(
        self,
        meta_prompt: str,
        history: List,
        callbacks: List = None,
        call_style=FunctionCallStyle.OPENAI_STYLE,
        model: OpenAIModel = OpenAIModel.CLAUDE_35_SONNET,
    ):
        super().__init__(meta_prompt, history, callbacks, call_style, model=model)

    def add_llm_tool_calls_response(self, response: Response):
        if response.tool_calls:
            if response.turn_answer:
                content = response.turn_answer
            elif response.content:
                content = response.content
            else:
                content = "null"
            message = {
                "content": content,
                "refusal": None,
                "role": response.role,
                "tool_calls": response.tool_calls,
            }
            self.messages.append(message)

    def add_tool_calls_result(self, tool_results: List[Result]):
        if tool_results:
            call_result_list = []
            for tool_call_result in tool_results:
                call_result_list.append(
                    {
                        "tool_call_id": tool_call_result.tool_call_id,
                        "role": "tool",
                        "name": tool_call_result.name,
                        "content": tool_call_result.value,
                    }
                )
            self.messages.extend(call_result_list)

    async def request_model(self, tools: List, max_tokens=4096):
        logging.info(
            f"【ClaudeLLMClient.request_model】meta_prompt: {self.meta_prompt}"
        )
        logging.info(f"【ClaudeLLMClient.request_model】messages: {self.messages}")
        gpt_response_iter = await question_answer_with_function_call(
            messages=self.messages,
            functions=tools,
            function_call="auto",
            max_tokens=max_tokens,
            request_timeout=60,
            stream=True,
            model_name=self.model,
            callbacks=self.callbacks,
            function_call_style=self.call_style,
        )
        return gpt_response_iter


class GeminiLLMClient(LLMBaseClient):
    def __init__(
        self,
        meta_prompt: str,
        history: List,
        callbacks: List = None,
        call_style=FunctionCallStyle.OPENAI_STYLE,
        model: OpenAIModel = OpenAIModel.GEMINI_20_FLASH,
    ):
        super().__init__(meta_prompt, history, callbacks, call_style, model=model)

    def add_llm_tool_calls_response(self, response: Response):
        if response.tool_calls:
            if response.turn_answer:
                content = response.turn_answer
            elif response.content:
                content = response.content
            else:
                content = ""
            message = {
                "content": content,
                "refusal": "",
                "role": response.role,
                "tool_calls": response.tool_calls,
            }
            self.messages.append(message)

    def add_tool_calls_result(self, tool_results: List[Result]):
        if tool_results:
            call_result_list = []
            for tool_call_result in tool_results:
                call_result_list.append(
                    {
                        "tool_call_id": tool_call_result.tool_call_id,
                        "role": "user",
                        "name": tool_call_result.name,
                        "content": tool_call_result.value,
                    }
                )
            self.messages.extend(call_result_list)

    def _convert_message_to_gemini_format(self, message):
        if message["role"] == "user":
            message = {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": message["content"],
                    }
                ]
            }
        return message


    async def request_model(self, tools: List, max_tokens=4096):
        logging.info(
            f"【ClaudeLLMClient.request_model】meta_prompt: {self.meta_prompt}"
        )
        logging.info(f"【ClaudeLLMClient.request_model】messages: {self.messages}")
        gpt_response_iter = await question_answer_with_function_call(
            messages=self.messages,
            functions=tools,
            function_call="auto",
            max_tokens=max_tokens,
            request_timeout=600,
            stream=True,
            model_name=self.model,
            callbacks=self.callbacks,
            function_call_style=self.call_style,
            llm_provider=LLM_PROVIDER.GEMINI
        )
        return gpt_response_iter