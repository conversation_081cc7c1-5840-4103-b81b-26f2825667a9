from loguru import logger as logging
import contextvars
from functools import wraps
import contextlib

from mygpt.util.trace import get_trace_id
import os
import json
from datetime import datetime
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage

# 创建一个上下文变量来标记是否应该打印特定日志
conditional_logging_var = contextvars.ContextVar('conditional_logging', default=False)

def should_print_log():
    """检查是否应该打印条件日志"""
    return conditional_logging_var.get()

def set_log_condition(value):
    """设置日志打印条件"""
    conditional_logging_var.set(value)

@contextlib.contextmanager
def conditional_logging_context(should_log=False):
    """控制条件日志打印的上下文管理器"""
    original_value = conditional_logging_var.get()
    set_log_condition(should_log)
    try:
        yield
    finally:
        set_log_condition(original_value)


def __track_search_results(search_results, track_tag):
    """
    跟踪搜索结果
    """
    if should_print_log() and search_results and len(search_results) > 0:
        hits = search_results.get("hits", {}).get("hits", [])
        # 每200个结果打印一条日志
        for i in range(0, len(hits), 200):
            batch = hits[i:i+200]
            id_scores = [f"{hit['_id']},{hit['_score']:.2f}" for hit in batch]
            logging.info(f"question_tracker_{track_tag}: ids,scores#{'#'.join(id_scores)}")

def __track_embeddings_results(embeddings_results, track_tag):
    """
    跟踪向量结果
    """
    if should_print_log() and embeddings_results and len(embeddings_results) > 0:
        # 每200个结果打印一条日志
        for i in range(0, len(embeddings_results), 200):
            batch = embeddings_results[i:i+200]
            id_scores = [f"{hit.id},{hit.score:.2f}" for hit in batch]
            logging.info(f"question_tracker_{track_tag}: ids,scores#{'#'.join(id_scores)}")

def __track_reference_results(reference_results, track_tag):
    """
    跟踪向量结果
    """
    if should_print_log() and reference_results and len(reference_results) > 0:
        # 每200个结果打印一条日志
        for i in range(0, len(reference_results), 200):
            batch = reference_results[i:i+200]
            id_scores = [f"{hit['id']},{hit['score']:.2f}" for hit in batch]
            logging.info(f"question_tracker_{track_tag}: ids,scores#{'#'.join(id_scores)}")

def __trace_messages(messages, track_tag):
    """
    跟踪消息
    """
    if should_print_log() and messages:
        message_type_map = {
            HumanMessage: "HumanMessage",
            SystemMessage: "SystemMessage",
            AIMessage: "AIMessage"
        }
        for index, message in enumerate(messages):
            message_type = message_type_map.get(type(message), "UnknownMessage")
            logging.info(f"question_tracker_{track_tag}: {index}: {message_type}: {message.content}")