import asyncio
import base64
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

from auth0 import Auth0Error
from auth0.authentication import GetToken
from auth0.management import Auth0
from fastapi import Depends
from fastapi.security import SecurityScopes
from fastapi_auth0 import Auth0User, auth0_rule_namespace
from fastapi_auth0.auth import Auth0<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import jwt
from loguru import logger as logging

from mygpt.auth0.init import auth
from mygpt.auth0.key import get_or_create_auth_key
from mygpt.enums import UserRole
from mygpt.error import OperationFailedException, ServerErrorException
from mygpt.models import User
from mygpt import settings


async def get_user_info(user_id: str) -> dict:
    auth0_token = GetToken(
        settings.AUTH0_DOMAIN,
        settings.AUTH0_ADMIN_CLIENT_ID,
        settings.AUTH0_ADMIN_CLIENT_SRCRET,
    ).client_credentials(settings.AUTH0_ADMIN_API_AUDIENCE)
    access_token = auth0_token["access_token"]
    auth0 = Auth0(settings.AUTH0_DOMAIN, access_token)
    try:
        user_info = await auth0.users.get_async(id=user_id)
    except Auth0Error as e:
        raise ServerErrorException()

    return user_info


TEMP_AUTH0_TOKEN = None
AUTH0_EXPIRES_AT = 0


async def access_token() -> str:
    global TEMP_AUTH0_TOKEN
    global AUTH0_EXPIRES_AT
    now = datetime.now()
    # if TEMP_AUTH0_TOKEN and AUTH0_EXPIRES_AT > now:
    #     return TEMP_AUTH0_TOKEN
    logging.info("get new auth0 token")
    client = GetToken(
        settings.AUTH0_DOMAIN,
        settings.AUTH0_ADMIN_CLIENT_ID,
        settings.AUTH0_ADMIN_CLIENT_SRCRET,
    )
    auth0_token = await asyncio.to_thread(
        client.client_credentials, settings.AUTH0_ADMIN_API_AUDIENCE
    )
    TEMP_AUTH0_TOKEN = auth0_token["access_token"]
    AUTH0_EXPIRES_AT = now + timedelta(seconds=auth0_token["expires_in"])
    return TEMP_AUTH0_TOKEN


async def list_user_roles(user_id: str) -> list:
    # 本地jwt还没有角色概念，直接返回True
    if settings.AUTHORIZATION_TYPE == "jwt":
        return []
    auth0 = Auth0(settings.AUTH0_DOMAIN, await access_token())
    try:
        user_roles = await auth0.users.list_roles_async(user_id)
    except Auth0Error as e:
        logging.error(f"create client error:{e}")
        raise ServerErrorException(e)
    return user_roles["roles"]


async def check_user_role(user_id: str, role: UserRole) -> bool:
    # 本地jwt还没有角色概念，直接返回True
    if settings.AUTHORIZATION_TYPE == "jwt":
        return True
    # 后面使用数据库的角色，verify_super_admin_access
    user_roles = await list_user_roles(user_id)
    for user_role in user_roles:
        if user_role["name"] == role:
            return True
    return False


async def get_user_by_email(email: str) -> dict:
    auth0 = Auth0(settings.AUTH0_DOMAIN, await access_token())
    try:
        user = await auth0.users_by_email.search_users_by_email_async(email)
    except Auth0Error as e:
        logging.error(f"search_users_by_email_async with email{email} error:{e}")
        raise ServerErrorException(e)
    return user[0] if user else None


async def resend_email_verification(email: str) -> None:
    user = await get_user_by_email(email)
    if not user or user["email_verified"]:
        raise OperationFailedException("User not found or already verified")
    auth0 = Auth0(settings.AUTH0_DOMAIN, await access_token())
    try:
        await auth0.jobs.send_verification_email_async({"user_id": user["user_id"]})
    except Auth0Error as e:
        logging.error(f"create client error:{e}")
        raise ServerErrorException(e)


async def get_current_user(
    security_scopes: SecurityScopes,
    creds: Optional[HTTPAuthorizationCredentials] = Depends(
        Auth0HTTPBearer(auto_error=False)
    ),
) -> Auth0User | None:
    # Check if we have credentials and they look like a JWT token (longer than 100 chars)
    if creds and creds.credentials and len(creds.credentials) > 100:
        return await auth.get_user(security_scopes, creds)


def get_oauth_token(
    code: str,
    redirect_uri: str,
):
    return GetToken(
        settings.AUTH0_DOMAIN, settings.AUTH0_CLIENT_ID, settings.AUTH0_CLIENT_SECRET
    ).authorization_code(code, redirect_uri)


def refresh_oauth_token(
    refresh_token: str,
):
    return GetToken(
        settings.AUTH0_DOMAIN, settings.AUTH0_CLIENT_ID, settings.AUTH0_CLIENT_SECRET
    ).refresh_token(refresh_token)


async def create_jwt_token(payload: dict):
    private_key, _ = await get_or_create_auth_key()

    private_pem = base64.b64decode(private_key)

    # Define JWT claims (payload)
    email = payload.get("email")
    del payload["email"]
    jwt_payload = {
        f"{auth0_rule_namespace}/email": email,
        "aud": settings.AUTH0_API_AUDIENCE,
        "scope": "offline_access",
        "iss": f"https://{settings.AUTH0_DOMAIN}/",
    }
    jwt_payload.update(payload)
    # Generate JWT token
    jwt_token = jwt.encode(
        jwt_payload,
        private_pem,
        algorithm="RS256",
        headers={
            "kid": settings.JWT_HEADER_KID,
            "alg": "RS256",
        },
    )
    return jwt_token


async def create_jwt_token_for_user(user: User):
    now = datetime.utcnow()
    base_payload = {
        "sub": getattr(user, "user_id", getattr(user, "id")),
        "email": user.email,
        "iat": now,
        "nbf": now,
    }
    access_token_payload = {
        **base_payload,
        "exp": now + timedelta(days=1),
        "type": "access",
    }

    refresh_token_payload = {
        **base_payload,
        "exp": now + timedelta(weeks=1),
        "type": "refresh",
    }

    access_token = await create_jwt_token(access_token_payload)

    access_token_expires_in = int((access_token_payload["exp"] - now).total_seconds())

    refresh_token = await create_jwt_token(refresh_token_payload)

    return {
        "access_token": access_token,
        "token_type": "Bearer",
        "expires_in": access_token_expires_in,
        "refresh_token": refresh_token,
    }


async def refresh_jwt_token(refresh_token: str):
    user: Auth0User = await auth.get_user(
        SecurityScopes(scopes=[]),
        HTTPAuthorizationCredentials(scheme="Bearer", credentials=refresh_token),
    )
    if not user:
        raise ValueError("Invalid refresh token.")

    users = await User.filter(email=user.email, deleted_at__isnull=True)
    if not users:
        raise ValueError("Invalid refresh token.")

    return await create_jwt_token_for_user(user)
