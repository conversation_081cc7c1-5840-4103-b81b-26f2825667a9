import asyncio

from tortoise import Tortoise
from mygpt import settings

print(f"db url: {settings.DB_URL}")

# 从 migrations 导入特定的迁移模块
import importlib.util
import os

spec = importlib.util.spec_from_file_location(
    "seed_trial_plan",
    os.path.join("migrations", "models", "230_20250406192950_seed_trial_plan.py"),
)
module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(module)
upgrade = module.upgrade


async def run_migration():
    # 初始化数据库连接
    await Tortoise.init(
        db_url=settings.DB_URL,
        modules={"models": ["mygpt.models"]},
    )

    # 执行升级函数
    print("Running migration...")
    await upgrade(Tortoise.get_connection("default"))

    # 关闭连接
    await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(run_migration())
