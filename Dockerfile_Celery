FROM python:3.10

LABEL maintainer="<EMAIL>"

ENV TZ=Asia/Tokyo
ENV DEBUG=0

RUN apt-get update && \
    apt-get -y install gcc libpq-dev tzdata git wget

COPY . /backend
WORKDIR /backend

# 安装Poetry
RUN pip install poetry
RUN poetry self add poetry-plugin-export

# 使用Poetry生成requirements.txt
RUN poetry export -f requirements.txt --output requirements.txt --without-hashes

# Use system pip
RUN pip install  --isolated  -r requirements.txt
RUN pip install pip_system_certs

# Pre-download NLTK data
RUN python -m nltk.downloader -d /usr/local/lib/python3.10/site-packages/llama_index/core/_static/nltk_cache punkt_tab

EXPOSE 8000

RUN chmod +x ./startup_celery.sh
CMD ["/bin/sh", "-c", "./startup_celery.sh"]