IS_USE_LOCAL_VLLM=True
OPENAI_BASE_URL=http://localhost:9009/v1
#OPENAI_BASE_URL=http://**************:9009/v1
LOCAL_VLLM_MODEL=gpt-4-1106-preview

# openai embedding服务地址
#LOCAL_EMBEDDING_PROXY=https://one.felo.me/v1
#LOCAL_EMBEDDING_KEY=sk-kmBgabwBzdsBJEnX9187CcEf17C844B1964cF0D021085317

# 本地embedding
LOCAL_EMBEDDING_PROXY=http://127.0.0.1:9001
LOCAL_EMBEDDING_KEY=706d8bdf-209f-453b-ab17-1ae9c4eb9b8c
LOCAL_EMBEDDING_VECTOR_SIZE=768
LOCAL_EMBEDDING_MODEL_NAME=bce



UNLIMITED_VERSION=true

# auth0 config
AUTH0_DOMAIN=dev-w1oz7a1bpi7nbnl2.us.auth0.com
AUTH0_API_AUDIENCE=https://mygpt.felo.me
AUTH0_CLIENT_ID=3exrv8fRxO245KBeGRUe5PjYoTsaPhsQ
AUTH0_REDIRECT_URI=http://localhost:8000
AUTH0_LOGOUT_REDIRECT_URI=http://localhost:8000/docs/oauth2-redirect


# local jwt config
JWT_HEADER_KID = gptbase.ai
# 启用的验证类型 auth0 或者 本地jwt
AUTHORIZATION_TYPE = os.getenv('AUTHORIZATION_TYPE', 'auth0')

# auth0 or jwt
AUTHORIZATION_TYPE=jwt
DEFAULT_USER_PASSWORD=passwordjp

# 在本地jwt模式下，创建默认用户
# default user
DEFAULT_USER_EMAIL = <EMAIL>
# default random password
DEFAULT_USER_PASSWORD = passwordjp
# dapr client config



# dapr client config
DAPR_API_METHOD_INVOCATION_PROTOCOL=GRPC
DAPR_CLIENT_ADDRESS=************:30095
DAPR_APP_ID_AI=felo-ai.default
# db config
DB_USER=postgres
DB_PORT=5432
DB_NAME=gptbase
DB_CHARSET=utf8

#DB_PORT=15432
DB_HOST=0.0.0.0
DB_PASSWORD=yRhnjSnhufuxNcCxFtPctXnTbAKS2jT2

#DB_HOST=dev-circleo-pg.celp3nik7oaq.ap-northeast-1.rds.amazonaws.com
#DB_PASSWORD=AeEGDB0b7Z5GK0E2tblt


# AWS
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=Pn+GCQt5157uOxxkkusRyHoqvKQDUPBm7zGeotbk
AWS_S3_REGION=ap-northeast-1
AWS_S3_BUCKET_NAME=prd-mygpt

PINECONE_API_KEY=b68d1e6e-8d14-4516-9079-aeac00b49a64
PINECONE_ENVIRONMENT=us-east1-gcp
FAQS_PINECONE_API_KEY=706d8bdf-209f-453b-ab17-1ae9c4eb9b8c
FAQS_PINECONE_ENVIRONMENT=asia-southeast1-gcp-free

# AZURE
OPENAI_API_TYPE_AZURE=azure
OPENAI_API_KEY_AZURE=********************************
OPENAI_API_BASE_AZURE=https://gptbase-prd.openai.azure.com
OPENAI_API_VERSION_AZURE=2023-07-01-preview
OPENAI_CHAT_COMPLETION_ENGINE_AZURE=turbo-35
OPENAI_API_BASE_AZURE_EMBEDDINGS=https://gptbase-prd-ja-east.openai.azure.com
OPENAI_API_KEY_AZURE_EMBEDDINGS=********************************
OPENAI_EMBEDDING_ENGINE_AZURE=text-embedding-ada-002

# azure 16k
OPENAI_API_KEY_AZURE_16K=********************************
OPENAI_API_BASE_AZURE_16K=https://gptbase-prd-ja-east.openai.azure.com
OPENAI_CHAT_COMPLETION_ENGINE_AZURE_16K=gpt-35-turbo-16k


# PDF OCR tessdata
#TESSDATA_PREFIX /usr/share/tesseract-ocr/tessdata/

#DOMAIN=http://localhost:8000
#OPENAI_JUST_AZURE=True
# stripe
STRIPE_API_KEY=sk_test_51NAlpBABEbniSWSF6TY7WJAa5UaSZUk71KEkCXD50ObtRB6UEz0Kqk2rkQ0wsUNbdUh4822bHFJXW1UxbWD58BPG00WMzSzjZC

# transformer
#TRANSFORMERS_URL=https://transformers.gledis.info
#FAQS_STATUS=True


# website crawler
#WEBSITE_CRAWLER_URL=https://api-dev.gptbase.ai/sitef
#WEBSITE_CRAWLER_CALLBACK_URL=https://api-dev.gptbase.ai/web-crawler-callback

# parser
#PARSER_URL=http://************:8000
PARSER_URL=http://127.0.0.1:8001
PARSER_TOKEN=eyJhbGciOiJSUzI1NiIsImtpZCI6ImdwdGJhc2UuYWkiLCJ0eXAiOiJKV1QifQ.eyJodHRwczovL2dpdGh1Yi5jb20vZG9yaW5jbGlzdS9mYXN0YXBpLWF1dGgwL2VtYWlsIjpudWxsLCJhdWQiOiJodHRwczovL215Z3B0LmZlbG8ubWUiLCJzY29wZSI6Im9mZmxpbmVfYWNjZXNzIiwiaXNzIjoiaHR0cHM6Ly9kZXYtdzFvejdhMWJwaTduYm5sMi51cy5hdXRoMC5jb20vIiwic3ViIjoiZWNhMDdkMGQtNTczYS00MjdiLWI4MzQtMjRhYTBhMWUzMDYyIiwiaWF0IjoxNzEzODQ0NzUxLCJuYmYiOjE3MTM4NDQ3NTEsImV4cCI6Nzc2MTIzOTk1MX0.p005vgmQlFzWS6kzhBuT-FgWlzATWCsSaonwrN69ZIwNSK2jWq3s5g7mMDfQsVCdXJvHdgVzVayHtJB_Hxzh3_g9iWPuJVP8Vx183XOJuK6Expr9Su8yQ3xEapw77mrNnRWkpr3G2GleMCSlJZZ2r0eNSXUlY7eQupdWnf38nZxXvqPJwFTBdJnevXE4CQqifdocadML23vFAoK2ampVs3V0OmF6HnFLcOEsOeY1HbQRS3PQDfusq7QA4zG1NR_Qcgw41Ud90nzE7qkcroI6B-geGftEV7YWzYAQgUb96MysZeDhkXSotuN2s7TOAmJonfQWTnADxQ9VwATqqjbjwQ

# quality_assessment
QUALITY_ASSESSMENT_URL=https://quality-dev.gptbase.ai/api

REDIS_URL=redis://:eYVX7EwVmmxKPCDmwMtyKVge8oLd2t81@127.0.0.1:6379
REDIS_DB=1

# vector storage
DEFAULT_VECTOR_STORAGE=qdrant_one_collection
DEFAULT_FAQ_VECTOR_STORAGE=qdrant
#VECTOR_STORAGE_QDRANT_HOST=************
#VECTOR_STORAGE_QDRANT_GRPC_PORT=30102

#VECTOR_STORAGE_QDRANT_HOST=localhost
#VECTOR_STORAGE_QDRANT_GRPC_PORT=6334

VECTOR_STORAGE_QDRANT_URL=http://127.0.0.1:6333

OPENSEARCH_URL=**************************************


TIKTOKEN_CACHE_DIR=./data-gym-cach

#COHERE_API_KEY=8w5qcdFpJfvk2ri9yG6F6bSRpVeR5jprrfjjeT6H

PRIVATE_KEY_BASE64=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

#Azure 通用配置项

AZURE_MODEL_VERSION=2023-07-01-preview
AZURE_MODEL_ENDPOINTS=https://gptbase-prd-ja-east.openai.azure.com
AZURE_MODEL_IDENTITY_KEY=********************************

AZURE_MODEL_TEXT_EMBEDDING_ADA_002_DEPLOYMENT_NAME=text-embedding-ada-002
AZURE_MODEL_TEXT_EMBEDDING_ADA_002_VERSION=2023-05-15
AZURE_MODEL_TEXT_EMBEDDING_3_LARGE_DEPLOYMENT_NAME=text-embedding-3-large
# text-embedding-ada-002
AZURE_MODEL_GPT_35_TURBO_16K_DEPLOYMENT_NAME=gpt-35-turbo-16k

AZURE_MODEL_GPT_4_DEPLOYMENT_NAME=gpt-4
AZURE_MODEL_GPT_4_1106_PREVIEW_DEPLOYMENT_NAME=gpt-4-turbo
AZURE_MODEL_GPT_4_1106_PREVIEW_ENDPOINTS=https://gptbase-prd-south-india.openai.azure.com
AZURE_MODEL_GPT_4_1106_PREVIEW_IDENTITY_KEY=7bee4db6931c42fcb165adf7dbca60f5


#OPENAI_API_KEY=sk-kmBgabwBzdsBJEnX9187CcEf17C844B1964cF0D021085317
#OPENAI_BASE_URL=https://one.felo.me/v1


#OPENAI_API_KEY=***************************************************


OPENAI_JUST_AZURE=false
CONCURRENT_INTENT_DETECT=false



FUNCTION_CALL_AUTH_URL=https://iot-maks.lshenergy.com/api-auth/oauth/user/token
FUNCTION_CALL_AUTH_USERNAME=lshe_test2
STATISTICS_KEY=79H721EWNI8912BCEHUW21B312WENCKWJWWBKEWF


# SMTP
MAIL_SERVER=email-smtp.ap-northeast-1.amazonaws.com
MAIL_PORT=587
MAIL_USERNAME=AKIA5M4ISMZHTZEJVVNG
MAIL_PASSWORD=BIxBAl43FTLZryXeQoB3QxGyuKT2U/sgQzhz56xgj3uW
MAIL_FORM=<EMAIL>

ADMIN_DOMAIN=https://admin-dev.gptbase.ai

DEBUG=false

FILE_STORAGE=local
