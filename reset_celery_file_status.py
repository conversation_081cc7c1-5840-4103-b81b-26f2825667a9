import asyncio

from loguru import logger
from tortoise import Tortoise

from mygpt.enums import VectorFileStatus, VectorFileSourceType, VectorFileType
from mygpt.models import VectorFile
from mygpt.settings import TORTOISE_ORM


async def reset_status():
    await Tortoise.init(config=TORTOISE_ORM)

    try:
        # 先查询受影响的记录数
        count = await VectorFile.filter(
            file_type=VectorFileType.INTEGRATION,
            source_type=VectorFileSourceType.LARK,
            file_status=VectorFileStatus.PROCESS,
        ).count()

        logger.info(f"Will update {count} records.")
        if count == 0:
            logger.info("No records need to be updated")
            return

        updated = await VectorFile.filter(
            file_type=VectorFileType.INTEGRATION,
            source_type=VectorFileSourceType.LARK,
            file_status=VectorFileStatus.PROCESS,
        ).update(file_status=VectorFileStatus.FAIL)
        logger.info(f"Successfully updated {updated} records")

    except Exception as e:
        logger.error(f"Error updating status: {str(e)}")


if __name__ == "__main__":
    asyncio.run(reset_status())
