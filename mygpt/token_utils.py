import logging
from datetime import datetime, timedelta

from mygpt.models import User
from mygpt.settings import UNLIMITED_VERSION
from mygpt.utils_stripe import UsedRedisClient, get_package, get_user_token


async def verify_token_dataset(tokens: int, dataset_id: str):
    if UNLIMITED_VERSION:
        return True
    user = await User.get_or_none(datasets__id=dataset_id).prefetch_related(
        "userconfigs"
    )
    if user is None:
        logging.error(f"verify_token_dataset:{dataset_id} error: user is None")
        return False
    info = await get_user_token(user)
    key = f"token_used_{user.user_id}"
    info["used_tokens"] += tokens
    if info["used_tokens"] > info["max_tokens"]:
        return False
    # 会导致重复计算的问题
    # await UsedRedisClient.set(key, info)
    return True


async def sync_user_token(tokens: int, dataset_id: str):
    user = await User.get_or_none(datasets__id=dataset_id)
    if user is None:
        logging.error(f"sync_user_token_dataset:{dataset_id} error: user is None")
        return False
    key = f"token_used_{user.user_id}"
    info = await UsedRedisClient.get(key)
    if info:
        end_time = float(info["end_at"])
        if end_time < datetime.now().timestamp():
            await UsedRedisClient.unset(key)
        else:
            info["used_tokens"] += tokens
            await UsedRedisClient.set(key, info)
            return True

    package = await get_package(user)
    end_at = package.created_at + timedelta(days=package.month * 30)
    used_token = {
        "max_tokens": package.max_tokens,
        "used_tokens": tokens,
        "end_at": end_at.timestamp(),
    }
    await UsedRedisClient.set(key, used_token)
    return True
