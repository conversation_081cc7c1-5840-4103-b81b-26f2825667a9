import asyncio
from mygpt.grpc_client.client import TestClient
from loguru import logger as logging


async def test_client(client):
    try:
        # 连接服务器
        logging.info("开始连接服务器...")
        await client.connect()
        logging.info("服务器连接成功")
        # 测试Echo方法
        logging.info("测试 Echo 方法...")
        message, timestamp = await client.echo("Hello gRPC!")
        logging.info(f"Echo响应: {message}, 时间: {timestamp}")

        # 测试Concat方法
        result = await client.concat("Hello", "World")
        logging.info(f"Concat响应: {result}")

        # 测试流式Echo方法
        async for message, timestamp in client.stream_echo("This is a streaming test"):
            logging.info(f"流式响应: {message}, 时间: {timestamp}")

    except Exception as e:
        logging.error(f"测试出错: {e}")
    finally:
        # 关闭连接
        await client.close()


async def test_all_methods():
    # 本地测试
    # local_client = TestClient(env="local")
    # await test_client(local_client)

    # 开发环境测试
    dev_client = TestClient(env="dev")
    await test_client(dev_client)


if __name__ == "__main__":
    asyncio.run(test_all_methods())
