from typing import List

import asyncio
import grpc
from mygpt.grpc_client.protos import test_service_pb2, test_service_pb2_grpc
from mygpt.grpc_client.protos import chat_service_pb2, chat_service_pb2_grpc
from loguru import logger as logging


class TestClient:
    def __init__(self, env="local"):
        if env == "local":
            self.host = "localhost"
            self.port = "50051"
            self.use_ssl = False
        else:  # dev
            self.host = "grpc-api-dev.gbase.ai"
            self.port = "443"
            self.use_ssl = True

        self.server_address = f"{self.host}:{self.port}"
        self.channel = None
        self.stub = None
        # 添加统一的元数据
        self.metadata = [
            ("content-type", "application/grpc"),
            ("user-agent", "grpc-python/1.0"),
            # ('accept', 'application/grpc'),
            # ('te', 'trailers'),
            # ('x-grpc-path', '/grpc'),  # 通过header传递路径
        ]
        if env == "dev":
            # 对于开发环境，添加特殊的头部
            # self.metadata.extend(
            #     [
            #         ("x-forwarded-proto", "https"),
            #         ("x-forwarded-port", "443"),
            #         ("x-forwarded-path", "/grpc"),  # 确保这个路径与 nginx 配置匹配
            #     ]
            # )
            pass

    async def connect(self):
        try:
            logging.info(f"尝试连接到服务器: {self.server_address}")
            if self.use_ssl:
                # 开发环境配置
                credentials = grpc.ssl_channel_credentials()
                channel_options = [
                    ("grpc.max_send_message_length", 10 * 1024 * 1024),
                    ("grpc.max_receive_message_length", 10 * 1024 * 1024),
                    ("grpc.ssl_target_name_override", "grpc-api-dev.gbase.ai"),
                    ("grpc.default_authority", "grpc-api-dev.gbase.ai"),
                    # 添加 HTTP/2 特定选项
                    ("grpc.http2.min_time_between_pings_ms", 10000),
                    ("grpc.keepalive_timeout_ms", 20000),
                    ("grpc.keepalive_permit_without_calls", 1),
                    # 设置初始元数据
                    ("grpc.primary_user_agent", "grpc-python/1.0"),
                ]
                target = f"{self.server_address}"  # 添加 scheme 和路径
                logging.info(f"连接目标: {target}")

                self.channel = grpc.aio.secure_channel(
                    target, credentials, options=channel_options
                )
            else:
                # 本地测试配置
                self.channel = grpc.aio.insecure_channel(
                    self.server_address,
                    options=[
                        ("grpc.max_send_message_length", 10 * 1024 * 1024),
                        ("grpc.max_receive_message_length", 10 * 1024 * 1024),
                    ],
                )
            # 创建 stub
            self.stub = test_service_pb2_grpc.TestServiceStub(self.channel)
            logging.info("已创建服务存根")
        except Exception as e:
            logging.error(f"Failed to connect to gRPC server: {e}")
            raise

    async def close(self):
        """关闭连接"""
        if self.channel:
            await self.channel.close()
            logging.info("Closed gRPC connection")

    async def echo(self, message: str):
        """测试Echo方法"""
        try:
            request = test_service_pb2.EchoRequest(message=message)
            response = await self.stub.Echo(request, metadata=self.metadata)
            return response.message, response.timestamp
        except grpc.RpcError as e:
            logging.error(f"RPC错误: {e.code()}: {e.details()}")
            raise

    async def concat(self, text1: str, text2: str):
        """测试Concat方法"""
        try:
            request = test_service_pb2.ConcatRequest(text1=text1, text2=text2)
            response = await self.stub.Concat(request, metadata=self.metadata)
            return response.result
        except grpc.RpcError as e:
            logging.error(f"RPC错误: {e.code()}: {e.details()}")
            raise

    async def stream_echo(self, message: str):
        """测试流式Echo方法"""
        try:
            request = test_service_pb2.EchoRequest(message=message)
            async for response in self.stub.StreamEcho(request, metadata=self.metadata):
                yield response.message, response.timestamp
        except grpc.aio.AioRpcError as e:
            logging.error(f"RPC错误: {e.code()}: {e.details()}")
            raise


class ChatAgentClient:
    def __init__(self, env="local"):
        if env == "local":
            self.host = "localhost"
            self.port = "50051"
            self.use_ssl = False
        else:  # dev
            self.host = "grpc-api-dev.gbase.ai"
            self.port = "443"
            self.use_ssl = True

        self.server_address = f"{self.host}:{self.port}"
        self.channel = None
        self.stub = None
        # 添加统一的元数据
        self.metadata = [
            ("content-type", "application/grpc"),
            ("user-agent", "grpc-python/1.0"),
            # ('accept', 'application/grpc'),
            # ('te', 'trailers'),
            # ('x-grpc-path', '/grpc'),  # 通过header传递路径
        ]
        if env == "dev":
            # 对于开发环境，添加特殊的头部
            # self.metadata.extend(
            #     [
            #         ("x-forwarded-proto", "https"),
            #         ("x-forwarded-port", "443"),
            #         ("x-forwarded-path", "/grpc"),  # 确保这个路径与 nginx 配置匹配
            #     ]
            # )
            pass

    async def connect(self):
        try:
            logging.info(f"尝试连接到服务器: {self.server_address}")
            if self.use_ssl:
                # 开发环境配置
                credentials = grpc.ssl_channel_credentials()
                channel_options = [
                    ("grpc.max_send_message_length", 10 * 1024 * 1024),
                    ("grpc.max_receive_message_length", 10 * 1024 * 1024),
                    ("grpc.ssl_target_name_override", "grpc-api-dev.gbase.ai"),
                    ("grpc.default_authority", "grpc-api-dev.gbase.ai"),
                    # 添加 HTTP/2 特定选项
                    ("grpc.http2.min_time_between_pings_ms", 10000),
                    ("grpc.keepalive_timeout_ms", 20000),
                    ("grpc.keepalive_permit_without_calls", 1),
                    # 设置初始元数据
                    ("grpc.primary_user_agent", "grpc-python/1.0"),
                ]
                target = f"{self.server_address}"  # 添加 scheme 和路径
                logging.info(f"连接目标: {target}")

                self.channel = grpc.aio.secure_channel(
                    target, credentials, options=channel_options
                )
            else:
                # 本地测试配置
                self.channel = grpc.aio.insecure_channel(
                    self.server_address,
                    options=[
                        ("grpc.max_send_message_length", 10 * 1024 * 1024),
                        ("grpc.max_receive_message_length", 10 * 1024 * 1024),
                    ],
                )
            # 创建 stub
            self.stub = chat_service_pb2_grpc.ChatServiceStub(self.channel)
            logging.info("已创建服务存根")
        except Exception as e:
            logging.error(f"Failed to connect to gRPC server: {e}")
            raise

    async def close(self):
        """关闭连接"""
        if self.channel:
            await self.channel.close()
            logging.info("Closed gRPC connection")

    async def chat_agent_stream(
        self,
        user_id: str,
        robot_id: str,
        session_id: str,
        agent_mode: str,
        messages: List,
        parameters: dict,
    ):
        """测试流式的chat方法"""
        try:
            request = chat_service_pb2.ChatAgentStreamRequest(
                user_id=user_id,
                robot_id=robot_id,
                session_id=session_id,
                agent_mode=agent_mode,
                messages=messages,
                parameters=parameters,
            )
            # async for response in self.stub.ChatAgentStream(
            #         user_id=user_id,
            #         robot_id=robot_id,
            #         session_id=session_id,
            #         agent_mode=agent_mode,
            #         messages=messages,
            #         parameters=parameters,
            #         metadata=self.metadata):
            #     yield response.chunk
            async for response in self.stub.ChatAgentStream(
                request, metadata=self.metadata
            ):
                yield response.chunk
        except grpc.aio.AioRpcError as e:
            logging.error(f"RPC错误: {e.code()}: {e.details()}")
            raise
