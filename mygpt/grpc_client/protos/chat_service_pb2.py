# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: chat_service.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x12\x63hat_service.proto\x12\nmygpt.chat",\n\x0b\x43hatMessage\x12\x0c\n\x04role\x18\x01 \x01(\t\x12\x0f\n\x07\x63ontent\x18\x02 \x01(\t"\x89\x02\n\x16\x43hatAgentStreamRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x10\n\x08robot_id\x18\x02 \x01(\t\x12\x12\n\nsession_id\x18\x03 \x01(\t\x12\x12\n\nagent_mode\x18\x04 \x01(\t\x12)\n\x08messages\x18\x05 \x03(\x0b\x32\x17.mygpt.chat.ChatMessage\x12\x46\n\nparameters\x18\x06 \x03(\x0b\x32\x32.mygpt.chat.ChatAgentStreamRequest.ParametersEntry\x1a\x31\n\x0fParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01"(\n\x17\x43hatAgentStreamResponse\x12\r\n\x05\x63hunk\x18\x01 \x01(\t2m\n\x0b\x43hatService\x12^\n\x0f\x43hatAgentStream\x12".mygpt.chat.ChatAgentStreamRequest\x1a#.mygpt.chat.ChatAgentStreamResponse"\x00\x30\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "chat_service_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    _globals["_CHATAGENTSTREAMREQUEST_PARAMETERSENTRY"]._options = None
    _globals["_CHATAGENTSTREAMREQUEST_PARAMETERSENTRY"]._serialized_options = b"8\001"
    _globals["_CHATMESSAGE"]._serialized_start = 34
    _globals["_CHATMESSAGE"]._serialized_end = 78
    _globals["_CHATAGENTSTREAMREQUEST"]._serialized_start = 81
    _globals["_CHATAGENTSTREAMREQUEST"]._serialized_end = 346
    _globals["_CHATAGENTSTREAMREQUEST_PARAMETERSENTRY"]._serialized_start = 297
    _globals["_CHATAGENTSTREAMREQUEST_PARAMETERSENTRY"]._serialized_end = 346
    _globals["_CHATAGENTSTREAMRESPONSE"]._serialized_start = 348
    _globals["_CHATAGENTSTREAMRESPONSE"]._serialized_end = 388
    _globals["_CHATSERVICE"]._serialized_start = 390
    _globals["_CHATSERVICE"]._serialized_end = 499
# @@protoc_insertion_point(module_scope)
