syntax = "proto3";

package mygpt.test;

// 测试服务
service TestService {
    // 简单的字符串echo测试
    rpc Echo (EchoRequest) returns (EchoResponse) {}

    // 字符串拼接测试
    rpc Concat (ConcatRequest) returns (ConcatResponse) {}

    // 字符串流式返回测试
    rpc StreamEcho (EchoRequest) returns (stream EchoResponse) {}
}

// Echo请求消息
message EchoRequest {
    string message = 1;  // 输入消息
}

// Echo响应消息
message EchoResponse {
    string message = 1;  // 输出消息
    string timestamp = 2;  // 处理时间戳
}

// 拼接请求消息
message ConcatRequest {
    string text1 = 1;  // 第一个字符串
    string text2 = 2;  // 第二个字符串
}

// 拼接响应消息
message ConcatResponse {
    string result = 1;  // 拼接结果
}

