# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import mygpt.grpc_server.protos.v1.test_service_pb2 as test__service__pb2


class TestServiceStub(object):
    """测试服务"""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Echo = channel.unary_unary(
            "/mygpt.test.TestService/Echo",
            request_serializer=test__service__pb2.EchoRequest.SerializeToString,
            response_deserializer=test__service__pb2.EchoResponse.FromString,
        )
        self.Concat = channel.unary_unary(
            "/mygpt.test.TestService/Concat",
            request_serializer=test__service__pb2.ConcatRequest.SerializeToString,
            response_deserializer=test__service__pb2.ConcatResponse.FromString,
        )
        self.StreamEcho = channel.unary_stream(
            "/mygpt.test.TestService/StreamEcho",
            request_serializer=test__service__pb2.EchoRequest.SerializeToString,
            response_deserializer=test__service__pb2.EchoResponse.FromString,
        )


class TestServiceServicer(object):
    """测试服务"""

    def Echo(self, request, context):
        """简单的字符串echo测试"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def Concat(self, request, context):
        """字符串拼接测试"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def StreamEcho(self, request, context):
        """字符串流式返回测试"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")


def add_TestServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
        "Echo": grpc.unary_unary_rpc_method_handler(
            servicer.Echo,
            request_deserializer=test__service__pb2.EchoRequest.FromString,
            response_serializer=test__service__pb2.EchoResponse.SerializeToString,
        ),
        "Concat": grpc.unary_unary_rpc_method_handler(
            servicer.Concat,
            request_deserializer=test__service__pb2.ConcatRequest.FromString,
            response_serializer=test__service__pb2.ConcatResponse.SerializeToString,
        ),
        "StreamEcho": grpc.unary_stream_rpc_method_handler(
            servicer.StreamEcho,
            request_deserializer=test__service__pb2.EchoRequest.FromString,
            response_serializer=test__service__pb2.EchoResponse.SerializeToString,
        ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
        "mygpt.test.TestService", rpc_method_handlers
    )
    server.add_generic_rpc_handlers((generic_handler,))


# This class is part of an EXPERIMENTAL API.
class TestService(object):
    """测试服务"""

    @staticmethod
    def Echo(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/mygpt.test.TestService/Echo",
            test__service__pb2.EchoRequest.SerializeToString,
            test__service__pb2.EchoResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def Concat(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/mygpt.test.TestService/Concat",
            test__service__pb2.ConcatRequest.SerializeToString,
            test__service__pb2.ConcatResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def StreamEcho(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_stream(
            request,
            target,
            "/mygpt.test.TestService/StreamEcho",
            test__service__pb2.EchoRequest.SerializeToString,
            test__service__pb2.EchoResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )
