import asyncio
from mygpt.grpc_client.client import ChatAgentClient
from mygpt.grpc_client.protos import chat_service_pb2
from loguru import logger as logging


async def test_client(client):
    try:
        # 连接服务器
        logging.info("开始连接服务器...")
        await client.connect()
        logging.info("服务器连接成功")
        # 测试Echo方法
        logging.info("测试 ChatAgentStream 方法...")
        """
        user_message = chat_service_pb2.ChatMessage(
            role="user",
            content="你好啊"
        )
        request = chat_service_pb2.ChatAgentStreamRequest(
            user_id="auth0|6495aff96ee6f0aa8795de76",
            robot_id="36bb28f9-d5ef-4af0-bbff-466bd9e33756",
            session_id="grpc_test|20250218-1335",
            agent_mode="chat",
            messages=[user_message]
        )
        """
        user_id = "auth0|6495aff96ee6f0aa8795de76"
        robot_id = "36bb28f9-d5ef-4af0-bbff-466bd9e33756"
        session_id = "grpc_test|20250218-1335"
        agent_mode = "chat"
        messages = [chat_service_pb2.ChatMessage(role="user", content="你好啊")]
        parameters = {"model": "gpt_4o"}
        stream_response = client.chat_agent_stream(
            user_id=user_id,
            robot_id=robot_id,
            session_id=session_id,
            agent_mode=agent_mode,
            messages=messages,
            parameters=parameters,
        )
        async for message in stream_response:
            logging.info(f"流式响应: {message}")

    except Exception as e:
        logging.error(f"测试出错: {e}")
    finally:
        # 关闭连接
        await client.close()


async def test_all_methods():
    # 本地测试
    # local_client = TestClient(env="local")
    # await test_client(local_client)

    # 开发环境测试
    dev_client = ChatAgentClient(env="dev")
    await test_client(dev_client)


if __name__ == "__main__":
    asyncio.run(test_all_methods())
# mygpt.grpc_server.protos.
