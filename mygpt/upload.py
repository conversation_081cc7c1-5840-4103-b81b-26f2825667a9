import asyncio
import base64
import io
import os
import re
import uuid
from fastapi import UploadFile

from mygpt import settings
from mygpt.enums import FileSource, FileType, FileStorage
from mygpt.models import ImageAttachment
from mygpt.schemata import ImageAttachmentOut
from mygpt.utils import download_file, get_image_s3_key, upload_file_to_s3


async def upload_image_attachment(
    file_type: FileType, file: UploadFile, ai_id: str, file_source: FileSource.ROBOT
):
    attachment_id = uuid.uuid4()

    file_binary: bytes = await file.read()
    suffix = os.path.splitext(file.filename)[1][1:] or "binary"
    mimetype = file.content_type or "application/octet-stream"

    if settings.FILE_STORAGE == FileStorage.LOCATION.value:
        uploads_directory = "uploads"
        file_type_directory = os.path.join(uploads_directory, str(file_type.value))
        if not os.path.exists(file_type_directory):
            os.makedirs(file_type_directory)
        attachment_key = os.path.join(
            file_type_directory, str(attachment_id) + "." + suffix
        )
        with open(attachment_key, "wb") as f:
            f.write(file_binary)
    else:
        attachment_key = get_image_s3_key(attachment_id, suffix, file_type, ai_id)
        await upload_file_to_s3(
            blob_s3_key=attachment_key,
            binary=file_binary,
            content_type=mimetype,
        )

    attachment = await ImageAttachment.create(
        id=attachment_id,
        filename=file.filename,
        file_type=file_type,
        key=attachment_key,
        data_id=ai_id,
        data_type=file_source,
        file_storage=settings.FILE_STORAGE,
    )
    return await ImageAttachmentOut.from_tortoise_orm(attachment)


async def upload_resource_to_s3(
    ai_id: str,
    file_type: FileType,
    url: str,
    name: str,
    file_source: FileSource = FileSource.ROBOT,
):
    try:
        if url.startswith("data:"):
            # base64 image 上传到s3
            image_data = base64.b64decode(url.split(",")[1])
            file_object = io.BytesIO(image_data)
            file_name = f"{name}.png"  # 设置文件名

        elif url.startswith("file://"):
            # 本地文件上传到s3
            file_object = open(url[7:], "rb")
            file_name = os.path.basename(url[7:])
        else:
            if url.startswith("/"):
                raise Exception(status_code=400, detail="Invalid image url")
            data = await download_file(url)
            file_object = io.BytesIO(data)
            suffix = url.split(".")[-1]
            file_name = f"{name}.{suffix}"  # 设置文件名
        # 创建 UploadFile 对象
        upload_file = UploadFile(filename=file_name, file=file_object)
        image = await upload_image_attachment(
            file_type, upload_file, ai_id, file_source=file_source
        )
    except Exception as e:
        return {"url": url, "original_url": url}

    return {"url": image.url, "original_url": url}
