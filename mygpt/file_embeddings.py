import asyncio
import json
import os
import traceback
from asyncio import Task
from datetime import datetime
from uuid import uuid4

from langchain.docstore.document import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
from loguru import logger as logging, logger
from redis import Redis

from mygpt import settings
from mygpt.core.utils import get_collection_name
from mygpt.core.vector_storage import VectorStorageFactory
from mygpt.enums import (
    FileSource,
    VectorFileType,
    VectorStatus,
    VectorStorageType,
)
from mygpt.error import (
    InvalidParameterException,
    NotFoundException,
    ServerErrorException,
)
from mygpt.loader.service import split, split_by_file, word_to_pdf
from mygpt.loader.transcoder import DocumentTranscoder
from mygpt.models import Dataset, ImageAttachment, Robot, Vector, VectorFile
from mygpt.utils import (
    delete_file_from_s3,
    delete_files_from_s3,
    num_tokens_from_string,
    download_tmp_file_from_s3,
    get_file_tmp_location,
    upload_binary,
)

separators = [
    "\n\n",
    "\n",
    " ",
    ".",
    "。",
    "？",
    "",
]

short_text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=300,
    chunk_overlap=10,
    separators=separators,
    length_function=num_tokens_from_string,
)


async def convert_word_to_pdf(file_id: str, path: str = None):
    """
    异步word转换pdf文件
    """
    vector_file = await VectorFile.get_or_none(id=file_id, deleted_at__isnull=True)
    if not vector_file:
        raise NotFoundException()
    if (
        vector_file.file_type != VectorFileType.UPLOAD
        and vector_file.file_type != VectorFileType.INTEGRATION
    ):
        raise InvalidParameterException()
    if not vector_file.key:
        raise ServerErrorException()
    try:
        if not path:
            path = await download_tmp_file_from_s3(vector_file.key)
    except Exception as e:
        logging.error(f"fail to download file in error:{e}")
        raise ServerErrorException()

    base_name = os.path.basename(vector_file.filename)
    hash_key, _k_extension = os.path.splitext(vector_file.key)
    _, extension = os.path.splitext(base_name)
    filename = vector_file.filename
    upload_key = f"{hash_key}.pdf"
    file_location = get_file_tmp_location(f"{hash_key}.pdf")

    if extension.startswith(".doc"):
        try:
            content = await word_to_pdf(path)
            with open(file_location, "wb") as file_object:
                file_object.write(content)

            await upload_binary(upload_key, file_location)
        except Exception as e:
            logging.error(
                f"word_to_pdf failed, filename: {vector_file.filename}, fileid:{vector_file.id}, error:{e}"
            )
            raise Exception(e)
        await VectorFile.filter(id=vector_file.id).update(
            # key=upload_key,  #不应该改这个key，重学的源文件根据这个key从s3上取回来，更新这个会导致生成的预览文件替换了源文件
            updated_at=datetime.now(),
        )
    return file_location


def spliter_short_text(document: Document) -> list[Document]:
    # 查看源代码，没有相关影响，这里并不需要拷贝
    # document = document.copy(deep=True)
    # document.page_content = OutlineParser.delete_breadcrumb(
    #     document.page_content
    # )
    short_docs = short_text_splitter.split_documents([document])
    if len(short_docs) < 2:
        return short_docs
    # 对拆分出来的短段落进行处理，如果短段落的长度小于100，拼接到前后相关的更短段落中，并把该段落清理掉
    need_clean_idx = []
    for idx, short_doc in enumerate(short_docs):
        if num_tokens_from_string(short_doc.page_content) < 150:
            need_clean_idx.append(idx)
            if idx == 0:
                short_docs[idx + 1].page_content = (
                    short_doc.page_content + short_docs[idx + 1].page_content
                )
            elif idx == len(short_docs) - 1:
                short_docs[idx - 1].page_content = (
                    short_docs[idx - 1].page_content + short_doc.page_content
                )
            else:
                prev_len = num_tokens_from_string(short_docs[idx - 1].page_content)
                next_len = num_tokens_from_string(short_docs[idx + 1].page_content)
                if prev_len < next_len:
                    short_docs[idx - 1].page_content = (
                        short_docs[idx - 1].page_content + short_doc.page_content
                    )
                else:
                    short_docs[idx + 1].page_content = (
                        short_doc.page_content + short_docs[idx + 1].page_content
                    )
    for idx in reversed(need_clean_idx):
        short_docs.pop(idx)
    return short_docs


async def spliter_document(
    documents, _index=0, use_page_content_in_doc=False
) -> (list[int], list[Document]):
    """
    Split a document into chunks
    """
    # 删掉outlines，拆分也不会插入到chunk中了，2023.10.16
    parser_file_id = documents[0].metadata.get("parser_file_id")
    for doc in documents:
        if "parser_file_id" in doc.metadata:
            del doc.metadata["parser_file_id"]
    start_time = datetime.now()
    if parser_file_id and not use_page_content_in_doc:
        docs = await split_by_file(parser_file_id)
    else:
        docs = await split(documents)
    for doc in docs:
        if "outlines" in doc.metadata:
            del doc.metadata["outlines"]
        if "resources" in doc.metadata:
            del doc.metadata["resources"]

    start_time = datetime.now()

    ids = []  # save to vectorstore index_id,generate by uuid4
    docs_with_short_docs = []  # with short docs
    short_tasks = []

    def spliter_short_text_task(doc: Document, parent_id: str):
        short_docs = spliter_short_text(doc)
        if len(short_docs) < 2:
            # can not split to short docs
            return
        for short_doc in short_docs:
            short_id = str(uuid4())
            docs_with_short_docs.append(short_doc)
            ids.append(short_id)
            short_doc.metadata["parent_index"] = parent_id
            short_doc.metadata["short_chunk"] = True
            logging.debug(f"short_id:{short_id} documents:{short_doc.page_content}")

    for index, doc in enumerate(docs):
        id = str(uuid4())
        ids.append(id)
        docs_with_short_docs.append(doc)
        doc.metadata["chunk_index"] = index + _index
        try:
            short_tasks.append(asyncio.to_thread(spliter_short_text_task, doc, id))
        except Exception as e:
            logging.error(f"split short text error:{e}")

    await asyncio.gather(*short_tasks)
    logging.debug(f"spliter_document cost:{datetime.now() - start_time}")
    for doc in docs_with_short_docs:
        document_transcoder = DocumentTranscoder(doc.page_content)
        doc.page_content = document_transcoder.encode()
    logging.debug(
        f"encode cost:{datetime.now() - start_time}, spliter_short_text_task result: {len(docs_with_short_docs)}"
    )
    return ids, docs_with_short_docs


def task_callback(task: Task):
    try:
        result = task.result()  # 这会重新引发任何发生的异常
    except Exception as e:
        logger.error(
            f"Task failed with exception: {e}, Traceback: {traceback.format_exc()}"
        )
    else:
        logger.info(f"Task {task.get_name()} completed successfully, {result}")


# def log_function_call(func, *args, **kwargs):
#     func_name = func.__name__
#     args_repr = [repr(a) for a in args]
#     kwargs_repr = [f"{k}={v!r}" for k, v in kwargs.items()]
#     signature = ", ".join(args_repr + kwargs_repr)
#     return f"{func_name}({signature})"


def create_logged_task(func, *args, **kwargs):
    task_name = f"{func.__name__}({len(args)} args, {len(kwargs)} kwargs)"

    async def logged_wrapper():
        return await asyncio.to_thread(func, *args, **kwargs)

    task = asyncio.create_task(logged_wrapper(), name=task_name)
    task.add_done_callback(task_callback)


async def delete_dataset_file_embeddings(vectorfile: VectorFile, dataset_obj: Dataset):
    # if not vectorfile or vectorfile.file_status != VectorFileStatus.COMPLETE:
    #    return False
    if not vectorfile:
        return False
    try:
        await vectorfile.delete_search_index()
    except Exception as e:
        logging.warning(f"file_id:{vectorfile.id} not delete with error:{e}")
    resources = vectorfile.resources
    if resources:
        await delete_files_from_s3(resources)

    await vectorfile.soft_delete()


async def deleted_vector(vector_file_id: str, dataset_obj: Dataset):
    vectors = await Vector.filter(
        vector_file_id=vector_file_id, deleted_at__isnull=True
    )
    if len(vectors) == 0:
        return
    ids = []
    # 删除pinecone中的向量
    for vector in vectors:
        if not vector.index_id or not vector.status == VectorStatus.FINISHED:
            continue
        ids.append(vector.index_id)
    try:
        n = 500
        id_chunks = [ids[i : i + n] for i in range(0, len(ids), n)]
        embedding_params = dataset_obj.get_embedding_params()
        vector_storage = VectorStorageFactory.get_instance()
        collection_name = get_collection_name(
            embedding_params.model_name,
            embedding_params.dimensions,
        )
        for id_chunk in id_chunks:
            await vector_storage.index_delete(id_chunk, collection_name)
    except Exception as e:
        logging.error(
            f"fail to delete vector:{ids} namespace:{dataset_obj.collection_name} in pinecone error:{e}"
        )
        raise e
    [await vector.soft_delete() for vector in vectors]


file_embeddings_locks: dict[asyncio.Lock] = {}


async def task_delete_attachments(
    dataset_obj: Dataset = None,
    robot_obj: Robot = None,
):
    async def _delete(images):
        keys = [image.key for image in images]
        await delete_files_from_s3(keys)

    if dataset_obj:
        images = await ImageAttachment.filter(
            data_id=dataset_obj.id, data_type=FileSource.DATASET
        )
        await _delete(images)
        await ImageAttachment.filter(
            data_id=dataset_obj.id, data_type=FileSource.DATASET
        ).delete()
    elif robot_obj:
        images = await ImageAttachment.filter(
            robot_id=robot_obj.id, data_type=FileSource.ROBOT
        )
        await _delete(images)
        await ImageAttachment.filter(
            robot_id=robot_obj.id, data_type=FileSource.ROBOT
        ).delete()
        images = await ImageAttachment.filter(
            data_id=robot_obj.id, data_type=FileSource.ROBOT
        )
        await _delete(images)
        await ImageAttachment.filter(
            data_id=robot_obj.id, data_type=FileSource.ROBOT
        ).delete()
    else:
        return


async def delete_dataset_by_obj(dataset_obj: Dataset):
    vectorfiles = await VectorFile.filter(
        dataset_id=dataset_obj.id, deleted_at__isnull=True
    )

    await task_delete_files(vectorfiles, dataset_obj)
    # 删除对应的集合
    vector_storage_type = VectorStorageType(
        dataset_obj.metadata.get("vector_storage", VectorStorageType.PINECONE)
    )
    if vector_storage_type == VectorStorageType.QDRANT:
        vector_storage = VectorStorageFactory.get_instance()
        await vector_storage.delete_collection(dataset_obj.collection_name)


async def cancel_queued_tasks(vectorfile, user_id: str):
    """取消排队中的任务"""
    TASK_ID_KEY_PREFIX = f"lark_file_task:"
    queue_name = f"lark_file.{user_id}"
    logging.debug(
        f"Cancelling queued tasks for VectorFile {vectorfile.id} in queue {queue_name}"
    )
    redis_client = Redis.from_url(settings.CELERY_BROKER_URL)

    try:
        # 从 Redis 获取任务 ID
        key = f"{TASK_ID_KEY_PREFIX}{vectorfile.id}"
        task_id = redis_client.get(key)
        task_id_str = task_id.decode("utf-8") if task_id else None
        logging.debug(
            f"got task_id:{task_id}, {task_id_str} for VectorFile {vectorfile.id}"
        )

        if task_id:
            _remove_task_from_redis_queue(
                queue_name=queue_name, task_id=task_id_str, redis_client=redis_client
            )

            from mygpt.tasks.lark_integration import celery_app

            # 获取任务实例
            celery_app.control.revoke(task_id_str, terminate=True)

            logging.info(
                f"revoked task {task_id}, {task_id_str} for VectorFile {vectorfile.id}"
            )
    except Exception as e:
        logging.error(f"Error cancelling task for VectorFile {vectorfile.id}: {e}")


def _remove_task_from_redis_queue(queue_name: str, task_id: str, redis_client: Redis):
    logging.info(f"Removing task {task_id} from Redis queue {queue_name}")
    queue = redis_client.lrange(queue_name, 0, -1)
    for task_json in queue:
        task = json.loads(task_json)
        try:
            if task.get("headers").get("id") == task_id:
                redis_client.lrem(queue_name, 1, task_json)
                logging.info(f"Removed task {task_id} from Redis queue {queue_name}")
                break
        except AttributeError as e:
            logging.error(f"Error removing task from Redis queue: {e}")


async def task_delete_files(vectorfiles: list[VectorFile], dataset_obj: Dataset):
    if dataset_obj.id not in file_embeddings_locks:
        file_embeddings_locks[dataset_obj.id] = asyncio.Lock()
    lock = file_embeddings_locks[dataset_obj.id]
    if lock.locked():
        logging.info(f"Lock already acquired by another task,{dataset_obj.id}")
    await lock.acquire()
    try:
        await dataset_obj.fetch_related("user")
        user_id = str(dataset_obj.user.id)

        for vectorfile in vectorfiles:
            logging.info(f"Delete file:{vectorfile.id}")
            await deleted_vector(str(vectorfile.id), dataset_obj)
            await delete_dataset_file_embeddings(vectorfile, dataset_obj)
            await cancel_queued_tasks(vectorfile, user_id)
            if vectorfile.file_type == VectorFileType.UPLOAD:
                await delete_file_from_s3(vectorfile.key)
    except Exception as e:
        logging.error(f"fail to delete files error:{e}, {traceback.format_exc()}")
    finally:
        lock.release()
