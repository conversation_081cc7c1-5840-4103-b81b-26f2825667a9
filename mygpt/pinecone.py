import asyncio

from mygpt.settings import PINECONE_API_KEY, PINECONE_ENVIRONMENT


class PineconeIndex:
    def __init__(
        self,
        name="openai",
        dimension=1536,
        api_key=PINECONE_API_KEY,
        environment=PINECONE_ENVIRONMENT,
    ) -> None:
        # initialize connection to pinecone (get API key at app.pinecone.io)
        import pinecone

        pinecone.init(api_key=api_key, environment=environment)
        self.name = name
        self.index = pinecone.Index(name)

    async def index_query(self, **args):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, lambda: self.index.query(**args))

    async def index_fetch(self, **args):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, lambda: self.index.fetch(**args))

    async def query_by_vectorfile_id(self, namespace: str, vectorfile_id: str):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            lambda: self.index.query(
                namespace=namespace, filter={"vectorfile_id": vectorfile_id}
            ),
        )

    async def index_upsert(self, **args):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, lambda: self.index.upsert(**args))

    async def index_delete(self, **args):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, lambda: self.index.delete(**args))

    async def update(self, **args):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, lambda: self.index.update(**args))


pinecone_index = None


def get_pinecone_index():
    global pinecone_index
    if pinecone_index is None and PINECONE_API_KEY and PINECONE_ENVIRONMENT:
        pinecone_index = PineconeIndex()
        return pinecone_index
    else:
        return pinecone_index
