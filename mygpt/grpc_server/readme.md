# gRPC Server

这是一个基于 gRPC 的服务器实现，提供了一些基础的测试服务。项目使用 Poetry 进行依赖管理。

## 目录结构

```
grpc_server/
├── protos/                 # proto 文件目录
│   ├── test_service.proto  # 测试服务定义
│   ├── test_service_pb2.py      # 生成的消息类
│   └── test_service_pb2_grpc.py # 生成的服务类
├── services/               # 服务实现目录
│   └── test_service.py    # 测试服务实现
├── generate_proto.py      # proto 文件生成工具
├── server.py              # gRPC 服务器实现
└── README.md              # 本文档
```

## 环境配置

1. 安装 Poetry（如果还没有安装）
```bash
curl -sSL https://install.python-poetry.org | python3 -
```

2. 安装项目依赖
```bash
poetry install
```

## 开发指南

### 生成 Proto 代码

每次修改 `.proto` 文件后，需要重新生成代码：

```bash
poetry run python -m mygpt.grpc_server.generate_proto
```

### 添加新依赖

如果需要添加新的依赖：
```bash
poetry add grpcio-tools  # 添加生产依赖
poetry add --dev pytest  # 添加开发依赖
```

### 实现新服务

1. 在 `protos/` 目录下创建或修改 `.proto` 文件
2. 生成代码
3. 在 `services/` 目录下实现服务
4. 在 `server.py` 中注册新服务

### 当前可用服务

#### TestService

提供以下方法：
- `Echo`: 简单的消息回显
- `Concat`: 字符串拼接
- `StreamEcho`: 流式消息回显

示例：
```python
# 客户端调用示例
async with grpc.aio.insecure_channel('localhost:50051') as channel:
    stub = test_service_pb2_grpc.TestServiceStub(channel)
    
    # Echo 调用
    response = await stub.Echo(
        test_service_pb2.EchoRequest(message="Hello")
    )
```

## 配置说明

在 `settings.py` 中可以配置以下参数：
- `GRPC_PORT`: gRPC 服务端口（默认：50051）
- `GRPC_MAX_WORKERS`: 最大并发 RPC 数
- `GRPC_MAX_MESSAGE_LENGTH`: 最大消息长度

## 开发注意事项

1. 使用 Poetry 虚拟环境
```bash
poetry shell  # 激活虚拟环境
```

2. 代码生成
- 每次修改 `.proto` 文件后必须重新生成代码
- 不要手动修改生成的 `*_pb2.py` 和 `*_pb2_grpc.py` 文件

3. 测试
```bash
poetry run pytest tests/  # 运行测试
```

## 调试建议

1. 使用 Poetry 环境变量进行调试：
```bash
GRPC_VERBOSITY=DEBUG GRPC_TRACE=all poetry run python your_script.py
```

2. 使用调试工具：
- `grpcui`: 交互式 Web 界面
- `grpcurl`: 命令行工具

## 常见问题

1. Proto 生成失败
- 确保在正确的 Poetry 环境中
- 检查 `grpcio-tools` 是否正确安装
- 检查 proto 文件语法

2. 服务启动问题
- 检查端口是否被占用
- 确认环境变量配置
- 查看日志输出