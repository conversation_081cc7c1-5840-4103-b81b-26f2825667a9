import grpc
from grpc_reflection.v1alpha import reflection
from loguru import logger as logging

from mygpt.grpc_server.handlers.generation_handler import <PERSON><PERSON>andler
from mygpt.grpc_server.handlers.test_service import TestService
from mygpt.grpc_server.handlers.chat_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mygpt.grpc_server.handlers.document_handler import DocumentHandler
from mygpt.grpc_server.protos.v2 import (
    test_service_pb2,
    test_service_pb2_grpc,
    chat_service_pb2,
    chat_service_pb2_grpc,
    document_service_pb2,
    document_service_pb2_grpc,
    generation_service_pb2,
    generation_service_pb2_grpc,
)
from mygpt import settings


_server: grpc.aio.Server = None


async def serve_grpc():
    global _server
    _server = grpc.aio.server(
        maximum_concurrent_rpcs=settings.GRPC_MAX_WORKERS,  # 控制最大并发RPC数量
        options=[
            ("grpc.max_send_message_length", settings.GRPC_MAX_MESSAGE_LENGTH),
            ("grpc.max_receive_message_length", settings.GRPC_MAX_MESSAGE_LENGTH),
            # 添加更多选项以匹配客户端
            ("grpc.keepalive_time_ms", 300000),
            ("grpc.keepalive_timeout_ms", 100000),
            ("grpc.http2.min_time_between_pings_ms", 100000),
            ("grpc.keepalive_permit_without_calls", 1),
        ],
    )

    # 注册测试服务
    test_service_pb2_grpc.add_TestServiceServicer_to_server(
        TestService(),
        _server,
    )
    # 注册聊天服务
    chat_service_pb2_grpc.add_ChatServiceServicer_to_server(
        ChatHandler(),
        _server,
    )
    # 注册文档服务
    document_service_pb2_grpc.add_DocumentServiceServicer_to_server(
        DocumentHandler(),
        _server,
    )
    # 注册生成类型服务
    generation_service_pb2_grpc.add_GenerationServiceServicer_to_server(
        GenerationHandler(),
        _server,
    )

    # 添加服务反射
    SERVICE_NAMES = (
        test_service_pb2.DESCRIPTOR.services_by_name["TestService"].full_name,
        chat_service_pb2.DESCRIPTOR.services_by_name["ChatService"].full_name,
        document_service_pb2.DESCRIPTOR.services_by_name["DocumentService"].full_name,
        generation_service_pb2.DESCRIPTOR.services_by_name["GenerationService"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(SERVICE_NAMES, _server)

    port = settings.GRPC_SERVER_PORT
    addr = f"[::]:{port}"
    _server.add_insecure_port(addr)
    logging.info(f"正在启动gRPC服务器，监听地址: {addr}")
    logging.info(f"服务器配置: maximum_concurrent_rpcs={settings.GRPC_MAX_WORKERS}")
    await _server.start()
    logging.info(f"GRPC Server started at port {port}")

    try:
        await _server.wait_for_termination()
    except Exception as e:
        logging.error(f"GRPC Server error: {e}")
        await _server.stop(0)
    finally:
        logging.info("GRPC Server stopped")


async def stop_grpc():
    global _server
    if _server:
        await _server.stop(0)
        logging.info("GRPC Server stopped")
