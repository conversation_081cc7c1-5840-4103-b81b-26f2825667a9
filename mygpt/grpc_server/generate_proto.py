import os
import sys
import subprocess
from pathlib import Path


def generate_proto(file_name: str = None):
    # 获取当前脚本所在目录
    # current_dir = os.path.dirname(os.path.abspath(__file__))
    # proto_dir = os.path.join(current_dir, "protos")

    # 使用Path获取当前脚本所在目录
    current_dir = Path(__file__).parent
    # 获取项目根目录 (通过目录名称`felo-mygpt`寻找)
    project_dir = current_dir
    while project_dir.name != "felo-mygpt":
        project_dir = project_dir.parent
    # proto文件目录
    proto_dir = str(project_dir / "mygpt/grpc_server/protos/v2")

    # 获取当前Python解释器路径
    python_executable = sys.executable

    # 获取protos目录下所有的.proto文件
    proto_files = [f for f in os.listdir(proto_dir) if f.endswith(".proto")]

    for proto_file in proto_files:
        if file_name and file_name != proto_file:
            # 如果指定了文件名，只生成指定文件
            continue
        cmd = [
            python_executable,  # Python解释器路径
            "-m",
            "grpc_tools.protoc",
            f"-I{proto_dir}",
            f"--python_out={proto_dir}",
            f"--grpc_python_out={proto_dir}",
            os.path.join(proto_dir, proto_file),
        ]

        print(f"Generating {proto_file}...")
        print(f"Command: {' '.join(cmd)}")  # 打印完整命令以便调试
        subprocess.run(cmd, check=True)
        print(f"Generated {proto_file} successfully!")


if __name__ == "__main__":
    file_name = "generation_service.proto"
    # file_name = None
    generate_proto(file_name)
