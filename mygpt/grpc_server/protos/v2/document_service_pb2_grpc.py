# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import mygpt.grpc_server.protos.v2.document_service_pb2 as document__service__pb2


class DocumentServiceStub(object):
    """文档处理服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GenerateDocumentSummary = channel.unary_unary(
                '/mygpt.document.DocumentService/GenerateDocumentSummary',
                request_serializer=document__service__pb2.DocumentSummaryRequest.SerializeToString,
                response_deserializer=document__service__pb2.DocumentSummaryResponse.FromString,
                )
        self.GenerateProjectSummaryByTitles = channel.unary_unary(
                '/mygpt.document.DocumentService/GenerateProjectSummaryByTitles',
                request_serializer=document__service__pb2.GenerateProjectSummaryByTitlesRequest.SerializeToString,
                response_deserializer=document__service__pb2.GenerateProjectSummaryByTitlesResponse.FromString,
                )
        self.GenerateDocumentTree = channel.unary_unary(
                '/mygpt.document.DocumentService/GenerateDocumentTree',
                request_serializer=document__service__pb2.DocumentTreeRequest.SerializeToString,
                response_deserializer=document__service__pb2.DocumentTreeResponse.FromString,
                )


class DocumentServiceServicer(object):
    """文档处理服务
    """

    def GenerateDocumentSummary(self, request, context):
        """文档摘要提取服务
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GenerateProjectSummaryByTitles(self, request, context):
        """通过文章标题生成摘要信息
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GenerateDocumentTree(self, request, context):
        """文档目录树构建服务
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DocumentServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GenerateDocumentSummary': grpc.unary_unary_rpc_method_handler(
                    servicer.GenerateDocumentSummary,
                    request_deserializer=document__service__pb2.DocumentSummaryRequest.FromString,
                    response_serializer=document__service__pb2.DocumentSummaryResponse.SerializeToString,
            ),
            'GenerateProjectSummaryByTitles': grpc.unary_unary_rpc_method_handler(
                    servicer.GenerateProjectSummaryByTitles,
                    request_deserializer=document__service__pb2.GenerateProjectSummaryByTitlesRequest.FromString,
                    response_serializer=document__service__pb2.GenerateProjectSummaryByTitlesResponse.SerializeToString,
            ),
            'GenerateDocumentTree': grpc.unary_unary_rpc_method_handler(
                    servicer.GenerateDocumentTree,
                    request_deserializer=document__service__pb2.DocumentTreeRequest.FromString,
                    response_serializer=document__service__pb2.DocumentTreeResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'mygpt.document.DocumentService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class DocumentService(object):
    """文档处理服务
    """

    @staticmethod
    def GenerateDocumentSummary(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/mygpt.document.DocumentService/GenerateDocumentSummary',
            document__service__pb2.DocumentSummaryRequest.SerializeToString,
            document__service__pb2.DocumentSummaryResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GenerateProjectSummaryByTitles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/mygpt.document.DocumentService/GenerateProjectSummaryByTitles',
            document__service__pb2.GenerateProjectSummaryByTitlesRequest.SerializeToString,
            document__service__pb2.GenerateProjectSummaryByTitlesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GenerateDocumentTree(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/mygpt.document.DocumentService/GenerateDocumentTree',
            document__service__pb2.DocumentTreeRequest.SerializeToString,
            document__service__pb2.DocumentTreeResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
