syntax = "proto3";
import "google/protobuf/struct.proto";

package mygpt.generation;

// 文档处理服务
service GenerationService {
    // 文档摘要提取服务
    rpc GeneratePresentationStream (GeneratePresentationStreamRequest) returns (stream GeneratePresentationStreamResponse) {}
}

// 通过文章标题生成摘要信息请求
message GeneratePresentationStreamRequest {
    string api_key = 1; // API Key
    string article = 2; // 文章内容
    map<string, google.protobuf.Value> parameters = 3; // 可选参数
}

message GeneratePresentationStreamResponse {
    string chunk = 1; // 输出chunk
    string chunk_type = 2; // 输出类型 [text, image, video, audio]
    string error_details = 3; // 错误详情
}
