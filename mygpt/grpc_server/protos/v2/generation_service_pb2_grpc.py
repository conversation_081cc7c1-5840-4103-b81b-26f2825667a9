# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import mygpt.grpc_server.protos.v2.generation_service_pb2 as generation__service__pb2


class GenerationServiceStub(object):
    """文档处理服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GeneratePresentationStream = channel.unary_stream(
                '/mygpt.generation.GenerationService/GeneratePresentationStream',
                request_serializer=generation__service__pb2.GeneratePresentationStreamRequest.SerializeToString,
                response_deserializer=generation__service__pb2.GeneratePresentationStreamResponse.FromString,
                )


class GenerationServiceServicer(object):
    """文档处理服务
    """

    def GeneratePresentationStream(self, request, context):
        """文档摘要提取服务
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_GenerationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GeneratePresentationStream': grpc.unary_stream_rpc_method_handler(
                    servicer.GeneratePresentationStream,
                    request_deserializer=generation__service__pb2.GeneratePresentationStreamRequest.FromString,
                    response_serializer=generation__service__pb2.GeneratePresentationStreamResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'mygpt.generation.GenerationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class GenerationService(object):
    """文档处理服务
    """

    @staticmethod
    def GeneratePresentationStream(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/mygpt.generation.GenerationService/GeneratePresentationStream',
            generation__service__pb2.GeneratePresentationStreamRequest.SerializeToString,
            generation__service__pb2.GeneratePresentationStreamResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
