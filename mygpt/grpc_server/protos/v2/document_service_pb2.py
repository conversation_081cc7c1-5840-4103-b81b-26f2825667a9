# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: document_service.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x16\x64ocument_service.proto\x12\x0emygpt.document\x1a\x1cgoogle/protobuf/struct.proto\"\xee\x01\n%GenerateProjectSummaryByTitlesRequest\x12\x0f\n\x07\x61pi_key\x18\x01 \x01(\t\x12\x0e\n\x06titles\x18\x02 \x03(\t\x12Y\n\nparameters\x18\x03 \x03(\x0b\x32\x45.mygpt.document.GenerateProjectSummaryByTitlesRequest.ParametersEntry\x1aI\n\x0fParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01\"u\n&GenerateProjectSummaryByTitlesResponse\x12\x19\n\x11summary_by_titles\x18\x01 \x01(\t\x12\x19\n\x11processing_status\x18\x02 \x01(\t\x12\x15\n\rerror_details\x18\x03 \x01(\t\"\xf5\x02\n\x16\x44ocumentSummaryRequest\x12\x0f\n\x07\x61pi_key\x18\x01 \x01(\t\x12\x0e\n\x06\x64oc_id\x18\x02 \x01(\t\x12\x13\n\x0b\x64oc_content\x18\x03 \x01(\t\x12\x11\n\tdoc_title\x18\x04 \x01(\t\x12\x10\n\x08\x64oc_type\x18\x05 \x01(\t\x12\x15\n\rdoc_tree_path\x18\x06 \x01(\t\x12\x11\n\ts3_bucket\x18\x07 \x01(\t\x12\x0f\n\x07s3_path\x18\x08 \x01(\t\x12\x15\n\ropensearch_id\x18\t \x01(\t\x12\x17\n\x0freturn_original\x18\n \x01(\x08\x12J\n\nparameters\x18\x0b \x03(\x0b\x32\x36.mygpt.document.DocumentSummaryRequest.ParametersEntry\x1aI\n\x0fParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01\"\x98\x05\n\x17\x44ocumentSummaryResponse\x12\r\n\x05title\x18\x01 \x01(\t\x12\x0f\n\x07summary\x18\x02 \x01(\t\x12\x10\n\x08language\x18\x03 \x01(\t\x12\x0e\n\x06topics\x18\x04 \x03(\t\x12\x0c\n\x04tags\x18\x05 \x03(\t\x12\x46\n\nevaluation\x18\x06 \x01(\x0b\x32\x32.mygpt.document.DocumentSummaryResponse.Evaluation\x12I\n\x0ctoken_counts\x18\x07 \x01(\x0b\x32\x33.mygpt.document.DocumentSummaryResponse.TokenCounts\x12\x10\n\x08original\x18\x08 \x01(\t\x12\x19\n\x11processing_status\x18\t \x01(\t\x12\x15\n\rerror_details\x18\n \x01(\t\x12\x17\n\x0fprocessing_time\x18\x0b \x01(\x02\x1aG\n\nEvaluation\x12\x1c\n\x14information_coverage\x18\x01 \x01(\x02\x12\x1b\n\x13missing_key_aspects\x18\x02 \x03(\t\x1a\xf3\x01\n\x0bTokenCounts\x12\x17\n\x0foriginal_tokens\x18\x01 \x01(\x05\x12\x16\n\x0esummary_tokens\x18\x02 \x01(\x05\x12\x14\n\x0ctitle_tokens\x18\x03 \x01(\x05\x12\x17\n\x0flanguage_tokens\x18\x04 \x01(\x05\x12\x15\n\rtopics_tokens\x18\x05 \x01(\x05\x12\x13\n\x0btags_tokens\x18\x06 \x01(\x05\x12\"\n\x1amissing_key_aspects_tokens\x18\x07 \x01(\x05\x12\x19\n\x11\x63ompression_ratio\x18\x08 \x01(\x02\x12\x19\n\x11percent_reduction\x18\t \x01(\t\"\xd4\x01\n\x13\x44ocumentTreeRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x18\n\x10\x64ocument_content\x18\x02 \x01(\t\x12G\n\nparameters\x18\x03 \x03(\x0b\x32\x33.mygpt.document.DocumentTreeRequest.ParametersEntry\x1aI\n\x0fParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01\"\xe6\x01\n\x14\x44ocumentTreeResponse\x12;\n\x04root\x18\x01 \x01(\x0b\x32-.mygpt.document.DocumentTreeResponse.TreeNode\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x15\n\rerror_message\x18\x03 \x01(\t\x1ai\n\x08TreeNode\x12\r\n\x05title\x18\x01 \x01(\t\x12\r\n\x05level\x18\x02 \x01(\x05\x12?\n\x08\x63hildren\x18\x03 \x03(\x0b\x32-.mygpt.document.DocumentTreeResponse.TreeNode2\xf8\x02\n\x0f\x44ocumentService\x12l\n\x17GenerateDocumentSummary\x12&.mygpt.document.DocumentSummaryRequest\x1a\'.mygpt.document.DocumentSummaryResponse\"\x00\x12\x91\x01\n\x1eGenerateProjectSummaryByTitles\x12\x35.mygpt.document.GenerateProjectSummaryByTitlesRequest\x1a\x36.mygpt.document.GenerateProjectSummaryByTitlesResponse\"\x00\x12\x63\n\x14GenerateDocumentTree\x12#.mygpt.document.DocumentTreeRequest\x1a$.mygpt.document.DocumentTreeResponse\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'document_service_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_GENERATEPROJECTSUMMARYBYTITLESREQUEST_PARAMETERSENTRY']._options = None
  _globals['_GENERATEPROJECTSUMMARYBYTITLESREQUEST_PARAMETERSENTRY']._serialized_options = b'8\001'
  _globals['_DOCUMENTSUMMARYREQUEST_PARAMETERSENTRY']._options = None
  _globals['_DOCUMENTSUMMARYREQUEST_PARAMETERSENTRY']._serialized_options = b'8\001'
  _globals['_DOCUMENTTREEREQUEST_PARAMETERSENTRY']._options = None
  _globals['_DOCUMENTTREEREQUEST_PARAMETERSENTRY']._serialized_options = b'8\001'
  _globals['_GENERATEPROJECTSUMMARYBYTITLESREQUEST']._serialized_start=73
  _globals['_GENERATEPROJECTSUMMARYBYTITLESREQUEST']._serialized_end=311
  _globals['_GENERATEPROJECTSUMMARYBYTITLESREQUEST_PARAMETERSENTRY']._serialized_start=238
  _globals['_GENERATEPROJECTSUMMARYBYTITLESREQUEST_PARAMETERSENTRY']._serialized_end=311
  _globals['_GENERATEPROJECTSUMMARYBYTITLESRESPONSE']._serialized_start=313
  _globals['_GENERATEPROJECTSUMMARYBYTITLESRESPONSE']._serialized_end=430
  _globals['_DOCUMENTSUMMARYREQUEST']._serialized_start=433
  _globals['_DOCUMENTSUMMARYREQUEST']._serialized_end=806
  _globals['_DOCUMENTSUMMARYREQUEST_PARAMETERSENTRY']._serialized_start=238
  _globals['_DOCUMENTSUMMARYREQUEST_PARAMETERSENTRY']._serialized_end=311
  _globals['_DOCUMENTSUMMARYRESPONSE']._serialized_start=809
  _globals['_DOCUMENTSUMMARYRESPONSE']._serialized_end=1473
  _globals['_DOCUMENTSUMMARYRESPONSE_EVALUATION']._serialized_start=1156
  _globals['_DOCUMENTSUMMARYRESPONSE_EVALUATION']._serialized_end=1227
  _globals['_DOCUMENTSUMMARYRESPONSE_TOKENCOUNTS']._serialized_start=1230
  _globals['_DOCUMENTSUMMARYRESPONSE_TOKENCOUNTS']._serialized_end=1473
  _globals['_DOCUMENTTREEREQUEST']._serialized_start=1476
  _globals['_DOCUMENTTREEREQUEST']._serialized_end=1688
  _globals['_DOCUMENTTREEREQUEST_PARAMETERSENTRY']._serialized_start=238
  _globals['_DOCUMENTTREEREQUEST_PARAMETERSENTRY']._serialized_end=311
  _globals['_DOCUMENTTREERESPONSE']._serialized_start=1691
  _globals['_DOCUMENTTREERESPONSE']._serialized_end=1921
  _globals['_DOCUMENTTREERESPONSE_TREENODE']._serialized_start=1816
  _globals['_DOCUMENTTREERESPONSE_TREENODE']._serialized_end=1921
  _globals['_DOCUMENTSERVICE']._serialized_start=1924
  _globals['_DOCUMENTSERVICE']._serialized_end=2300
# @@protoc_insertion_point(module_scope)
