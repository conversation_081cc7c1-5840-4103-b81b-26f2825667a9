# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generation_service.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x18generation_service.proto\x12\x10mygpt.generation\x1a\x1cgoogle/protobuf/struct.proto\"\xe9\x01\n!GeneratePresentationStreamRequest\x12\x0f\n\x07\x61pi_key\x18\x01 \x01(\t\x12\x0f\n\x07\x61rticle\x18\x02 \x01(\t\x12W\n\nparameters\x18\x03 \x03(\x0b\x32\x43.mygpt.generation.GeneratePresentationStreamRequest.ParametersEntry\x1aI\n\x0fParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.Value:\x02\x38\x01\"^\n\"GeneratePresentationStreamResponse\x12\r\n\x05\x63hunk\x18\x01 \x01(\t\x12\x12\n\nchunk_type\x18\x02 \x01(\t\x12\x15\n\rerror_details\x18\x03 \x01(\t2\xa1\x01\n\x11GenerationService\x12\x8b\x01\n\x1aGeneratePresentationStream\x12\x33.mygpt.generation.GeneratePresentationStreamRequest\x1a\x34.mygpt.generation.GeneratePresentationStreamResponse\"\x00\x30\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'generation_service_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_GENERATEPRESENTATIONSTREAMREQUEST_PARAMETERSENTRY']._options = None
  _globals['_GENERATEPRESENTATIONSTREAMREQUEST_PARAMETERSENTRY']._serialized_options = b'8\001'
  _globals['_GENERATEPRESENTATIONSTREAMREQUEST']._serialized_start=77
  _globals['_GENERATEPRESENTATIONSTREAMREQUEST']._serialized_end=310
  _globals['_GENERATEPRESENTATIONSTREAMREQUEST_PARAMETERSENTRY']._serialized_start=237
  _globals['_GENERATEPRESENTATIONSTREAMREQUEST_PARAMETERSENTRY']._serialized_end=310
  _globals['_GENERATEPRESENTATIONSTREAMRESPONSE']._serialized_start=312
  _globals['_GENERATEPRESENTATIONSTREAMRESPONSE']._serialized_end=406
  _globals['_GENERATIONSERVICE']._serialized_start=409
  _globals['_GENERATIONSERVICE']._serialized_end=570
# @@protoc_insertion_point(module_scope)
