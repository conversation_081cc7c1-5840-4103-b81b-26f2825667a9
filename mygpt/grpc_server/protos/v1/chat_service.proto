syntax = "proto3";
import "google/protobuf/struct.proto";

package mygpt.chat;

// 测试服务
service ChatService {
    rpc ChatAgentStream (ChatAgentStreamRequest) returns (stream ChatAgentStreamResponse) {}
}

message ChatMessage {
    string role = 1;
    string content = 2;
}

// Chat请求消息
message ChatAgentStreamRequest {
    string user_id = 1;  // 用户ID
    string robot_id = 2;  // 机器人ID 或者 Project ID 需要是一个UUID
    string session_id = 3;  // 会话ID
    string agent_mode = 4;  // 机器人模式 [chat, tts, ...]
    repeated ChatMessage messages = 5;   // 消息列表  [role, content]
    map<string, google.protobuf.Value> parameters = 6;  // 可选参数 {model: [gpt_4o, o3_mini, claude_35_sonnet]}
}

// Chat响应消息
message ChatAgentStreamResponse {
    string chunk = 1;  // 输出chunk
}
