# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: test_service.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x12test_service.proto\x12\nmygpt.test"\x1e\n\x0b\x45\x63hoRequest\x12\x0f\n\x07message\x18\x01 \x01(\t"2\n\x0c\x45\x63hoResponse\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x11\n\ttimestamp\x18\x02 \x01(\t"-\n\rConcatRequest\x12\r\n\x05text1\x18\x01 \x01(\t\x12\r\n\x05text2\x18\x02 \x01(\t" \n\x0e\x43oncatResponse\x12\x0e\n\x06result\x18\x01 \x01(\t2\xd2\x01\n\x0bTestService\x12;\n\x04\x45\x63ho\x12\x17.mygpt.test.EchoRequest\x1a\x18.mygpt.test.EchoResponse"\x00\x12\x41\n\x06\x43oncat\x12\x19.mygpt.test.ConcatRequest\x1a\x1a.mygpt.test.ConcatResponse"\x00\x12\x43\n\nStreamEcho\x12\x17.mygpt.test.EchoRequest\x1a\x18.mygpt.test.EchoResponse"\x00\x30\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "test_service_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    _globals["_ECHOREQUEST"]._serialized_start = 34
    _globals["_ECHOREQUEST"]._serialized_end = 64
    _globals["_ECHORESPONSE"]._serialized_start = 66
    _globals["_ECHORESPONSE"]._serialized_end = 116
    _globals["_CONCATREQUEST"]._serialized_start = 118
    _globals["_CONCATREQUEST"]._serialized_end = 163
    _globals["_CONCATRESPONSE"]._serialized_start = 165
    _globals["_CONCATRESPONSE"]._serialized_end = 197
    _globals["_TESTSERVICE"]._serialized_start = 200
    _globals["_TESTSERVICE"]._serialized_end = 410
# @@protoc_insertion_point(module_scope)
