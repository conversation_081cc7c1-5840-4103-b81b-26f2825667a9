# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import mygpt.grpc_server.protos.v1.chat_service_pb2 as chat__service__pb2


class ChatServiceStub(object):
    """测试服务"""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ChatAgentStream = channel.unary_stream(
            "/mygpt.chat.ChatService/ChatAgentStream",
            request_serializer=chat__service__pb2.ChatAgentStreamRequest.SerializeToString,
            response_deserializer=chat__service__pb2.ChatAgentStreamResponse.FromString,
        )


class ChatServiceServicer(object):
    """测试服务"""

    def ChatAgentStream(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")


def add_ChatServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
        "ChatAgentStream": grpc.unary_stream_rpc_method_handler(
            servicer.ChatAgentStream,
            request_deserializer=chat__service__pb2.ChatAgentStreamRequest.FromString,
            response_serializer=chat__service__pb2.ChatAgentStreamResponse.SerializeToString,
        ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
        "mygpt.chat.ChatService", rpc_method_handlers
    )
    server.add_generic_rpc_handlers((generic_handler,))


# This class is part of an EXPERIMENTAL API.
class ChatService(object):
    """测试服务"""

    @staticmethod
    def ChatAgentStream(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_stream(
            request,
            target,
            "/mygpt.chat.ChatService/ChatAgentStream",
            chat__service__pb2.ChatAgentStreamRequest.SerializeToString,
            chat__service__pb2.ChatAgentStreamResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )
