syntax = "proto3";
import "google/protobuf/struct.proto";

package mygpt.document;

// 文档处理服务
service DocumentService {
    // 文档摘要提取服务
    rpc GenerateDocumentSummary (DocumentSummaryRequest) returns (DocumentSummaryResponse) {}
    
    // 文档目录树构建服务
    rpc GenerateDocumentTree (DocumentTreeRequest) returns (DocumentTreeResponse) {}
}

// 文档摘要请求
message DocumentSummaryRequest {
    string doc_id = 1;    // 文档ID
    string doc_content = 2;  // 文档内容
    string doc_title = 3; // 文档标题 (预留)
    string doc_type = 4;  // 文档类型 (预留)
    string doc_tree_path = 5; // 文档目录树路径 (预留)
    string s3_bucket = 6;      // 文档存储桶 (预留)
    string s3_path = 7;        // 文档存储路径 (预留)
    string opensearch_id = 8;  // OpenSearch ID (预留)
    bool return_original = 9;  // 是否返回原文
    map<string, google.protobuf.Value> parameters = 10;  // 可选参数，如模型选择等
}

// 文档摘要响应
message DocumentSummaryResponse {
    // 基本信息
    string title = 1;           // 文档标题
    string summary = 2;         // 生成的摘要内容
    string language = 3;        // 文档语言
    // 主题与标签
    repeated string topics = 4; // 主题
    repeated string tags = 5;   // 标签
    // 评估信息
    message Evaluation {
        float information_coverage = 1;          // 信息覆盖度评分 (1-10)
        repeated string missing_key_aspects = 2; // 缺失的关键方面
    }
    Evaluation evaluation = 6;  // 评估信息
    // Token统计
    message TokenCounts {
        int32 original_tokens = 1;     // 原文档tokens数量
        int32 summary_tokens = 2;      // 摘要tokens数量
        int32 title_tokens = 3;        // 标题tokens数量
        int32 language_tokens = 4;     // 语言tokens数量
        int32 topics_tokens = 5;       // 主题tokens数量
        int32 tags_tokens = 6;         // 标签tokens数量
        int32 missing_key_aspects_tokens = 7;  // 缺失关键方面tokens数量
        float compression_ratio = 8;   // 压缩比例
        string percent_reduction = 9;  // 压缩比例百分比表示
    }
    TokenCounts token_counts = 7;      // Token统计
    string original = 8; // 原文内容

    // 处理状态
    string processing_status = 9;     // 处理状态 [success, error]
    string error_details = 10;         // 错误详情
    float processing_time = 11;       // 处理时间（秒）
}

// 文档目录树请求
message DocumentTreeRequest {
    string user_id = 1;       // 用户ID
    string document_content = 2; // 文档内容
    map<string, google.protobuf.Value> parameters = 3; // 可选参数
}

// 文档目录树响应
message DocumentTreeResponse {
    message TreeNode {
        string title = 1;      // 标题
        int32 level = 2;       // 层级
        repeated TreeNode children = 3; // 子节点
    }
    
    TreeNode root = 1;        // 根节点
    bool success = 2;         // 是否成功
    string error_message = 3; // 错误信息（如果有）
} 