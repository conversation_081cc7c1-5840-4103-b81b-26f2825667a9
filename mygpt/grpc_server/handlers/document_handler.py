import grpc
import traceback

from loguru import logger as logging
from mygpt.grpc_server.protos.v2 import document_service_pb2_grpc, document_service_pb2
from mygpt.service.agent_service import AgentService
from mygpt.dao.postgresql_dao.user_dao import user_dao


class DocumentHandler(document_service_pb2_grpc.DocumentServiceServicer):
    """文档服务的gRPC控制器

    负责处理document_service.proto中定义的gRPC请求，
    并调用业务逻辑层的服务来执行实际操作。
    """

    def __init__(self):
        self.agent_service = AgentService()

    async def GenerateDocumentSummary(self, request, context):
        """处理文档摘要生成请求"""
        logging.info(
            f"[GenerateDocumentSummary] Received document summary for doc_id: {request.doc_id}"
        )
        """
        message DocumentSummaryRequest {
            string api_key = 1;     // API Key
            string doc_id = 1;    // 文档ID
            string doc_content = 4;  // 文档内容
            string doc_title = 3; // 文档标题 (预留)
            string doc_type = 2;  // 文档类型 (预留)
            string doc_tree_path = 5; // 文档目录树路径 (预留)
            string s3_bucket = 6;      // 文档存储桶 (预留)
            string s3_path = 7;        // 文档存储路径 (预留)
            map<string, google.protobuf.Value> parameters = 9;  // 可选参数，如模型选择等
        }
        """
        try:
            api_key = request.api_key
            # 通过api_key获取user对象
            user = await user_dao.find_user_by_apikey(api_key)
            if not user:
                logging.error(f"[GenerateDocumentSummary] api_key is an invalid key")
                context.set_code(grpc.StatusCode.UNAUTHENTICATED)
                context.set_details("Invalid api_key")
                return document_service_pb2.DocumentSummaryResponse(
                    processing_status="error", error_details="Invalid api_key"
                )
            # 调用业务逻辑层的服务
            summary_result = await self.agent_service.agent_summary_service(
                user=user,
                doc_content=request.doc_content,
                doc_title=request.doc_title,
                doc_type=request.doc_type,
                doc_tree_path=request.doc_tree_path,
            )
            # 构建评估信息
            evaluation = document_service_pb2.DocumentSummaryResponse.Evaluation(
                information_coverage=summary_result.evaluation.get(
                    "information_coverage", 0
                ),
                missing_key_aspects=summary_result.evaluation.get(
                    "missing_key_aspects", []
                ),
            )
            # 构建TokenCounts
            token_counts = document_service_pb2.DocumentSummaryResponse.TokenCounts(
                original_tokens=summary_result.token_counts.get("original_tokens", 0),
                summary_tokens=summary_result.token_counts.get("summary_tokens", 0),
                title_tokens=summary_result.token_counts.get("title_tokens", 0),
                language_tokens=summary_result.token_counts.get("language_tokens", 0),
                topics_tokens=summary_result.token_counts.get("topics_tokens", 0),
                tags_tokens=summary_result.token_counts.get("tags_tokens", 0),
                missing_key_aspects_tokens=summary_result.token_counts.get(
                    "missing_key_aspects_tokens", 0
                ),
                compression_ratio=summary_result.token_counts.get(
                    "compression_ratio", 0
                ),
                percent_reduction=summary_result.token_counts.get(
                    "percent_reduction", 0
                ),
            )
            return_original = (
                request.return_original if request.return_original else False
            )
            # 构建返回结果
            response = document_service_pb2.DocumentSummaryResponse(
                title=summary_result.title,
                summary=summary_result.summary,
                language=summary_result.language,
                topics=summary_result.topics,
                tags=summary_result.tags,
                evaluation=evaluation,
                token_counts=token_counts,
                original=summary_result.original if return_original else "",
                processing_status="success",
                processing_time=summary_result.processing_time,
            )
            return response
        except Exception as e:
            logging.error(f"[GenerateDocumentSummary] Error: {e}")
            # 打印堆栈信息
            traceback.print_exc()
            # 返回错误信息
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {e}")
            return document_service_pb2.DocumentSummaryResponse(
                processing_status="error", error_details=str(e)
            )

    async def GenerateProjectSummaryByTitles(self, request, context):
        logging.info(f"[GenerateProjectSummaryByTitles] Received project summary")
        titles = request.titles
        api_key = request.api_key
        # 通过api_key获取user对象
        user = await user_dao.find_user_by_apikey(api_key)
        if not user:
            logging.error(f"[GenerateProjectSummaryByTitles] api_key is an invalid key")
            context.set_code(grpc.StatusCode.UNAUTHENTICATED)
            context.set_details("Invalid api_key")
            return document_service_pb2.GenerateProjectSummaryByTitlesResponse(
                summary_by_titles="",
                processing_status="error",
                error_details="Invalid api_key",
            )
        try:
            # 调用业务逻辑层的服务
            summary_result = await self.agent_service.agent_summary_by_file_title_service(
                title_list=titles, user=user
            )
            return document_service_pb2.GenerateProjectSummaryByTitlesResponse(
                summary_by_titles=summary_result,
                processing_status="success",
                error_details=""
            )
        except Exception as e:
            logging.error(f"[GenerateProjectSummaryByTitles] Error: {e}")
            # 打印堆栈信息
            traceback.print_exc()
            # 返回错误信息
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {e}")
            return document_service_pb2.GenerateProjectSummaryByTitlesResponse(
                summary_by_titles="",
                processing_status="error",
                error_details=str(e),
            )
