import grpc
import traceback

from loguru import logger as logging
from mygpt.grpc_server.protos.v2 import generation_service_pb2_grpc, generation_service_pb2
from mygpt.grpc_server.utils.proto_utils import parse_parameters
from mygpt.service.agent.presentation_service import presentation_service
from mygpt.dao.postgresql_dao.user_dao import user_dao


class GenerationHandler(generation_service_pb2_grpc.GenerationServiceServicer):
    """文档服务的gRPC控制器

    负责处理document_service.proto中定义的gRPC请求，
    并调用业务逻辑层的服务来执行实际操作。
    """

    async def GeneratePresentationStream(self, request, context):
        """处理文档摘要生成请求"""
        logging.info(
            f"[GenerateWebPagesStream] run started"
        )
        article = request.article
        api_key = request.api_key
        proto_parameters = request.parameters
        parameters = parse_parameters(proto_parameters)

        # 通过api_key获取user对象
        user = await user_dao.find_user_by_apikey(api_key)
        if not user:
            logging.error(f"[GeneratePresentationStream] received api_key is an invalid key")
            context.set_code(grpc.StatusCode.UNAUTHENTICATED)
            context.set_details("Invalid api_key")
            raise ValueError("Invalid api_key")

        presentation_iter = await presentation_service.presentation_process_control(
            article=article,
            user=user,
        )
        try:
            async for response in presentation_iter:
                print(response)
                yield generation_service_pb2.GeneratePresentationStreamResponse(
                    chunk=response,
                )
        except Exception as e:
            logging.error(f"[GeneratePresentationStream] error: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Internal server error")
            raise e
        finally:
            logging.info(
                f"[GeneratePresentationStream] run finished"
            )
