import asyncio
from datetime import datetime
import grpc
from mygpt.grpc_server.protos.v2 import test_service_pb2_grpc, test_service_pb2
from loguru import logger as logging


class TestService(test_service_pb2_grpc.TestServiceServicer):
    """测试服务实现"""

    async def Echo(self, request, context):
        """简单的echo测试方法"""
        logging.info(f"Echo: {request.message}")
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            return test_service_pb2.EchoResponse(
                message=request.message, timestamp=timestamp
            )
        except Exception as e:
            await context.abort(grpc.StatusCode.INTERNAL, str(e))

    async def Concat(self, request, context):
        """字符串拼接测试方法"""
        logging.info(f"Concat: {request.text1} {request.text2}")
        try:
            result = f"{request.text1} {request.text2}"
            return test_service_pb2.ConcatResponse(result=result)
        except Exception as e:
            await context.abort(grpc.StatusCode.INTERNAL, str(e))

    async def StreamEcho(self, request, context):
        """流式echo测试方法"""
        logging.info(f"StreamEcho: {request.message}")
        try:
            # 模拟流式返回，将消息分成多份返回
            words = request.message.split()
            for word in words:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                yield test_service_pb2.EchoResponse(message=word, timestamp=timestamp)
                await asyncio.sleep(0.5)  # 模拟处理延迟
        except Exception as e:
            await context.abort(grpc.StatusCode.INTERNAL, str(e))
