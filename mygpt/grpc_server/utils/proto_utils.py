from google.protobuf.struct_pb2 import Value, ListValue, Struct


def value_to_dict(value):
    """将protobuf Value对象转换回Python数据类型"""
    which_value = value.WhichOneof("kind")

    if which_value == "null_value":
        return None
    elif which_value == "number_value":
        return value.number_value
    elif which_value == "string_value":
        return value.string_value
    elif which_value == "bool_value":
        return value.bool_value
    elif which_value == "struct_value":
        return struct_to_dict(value.struct_value)
    elif which_value == "list_value":
        return list_value_to_list(value.list_value)
    else:
        return None


def struct_to_dict(struct):
    """将protobuf Struct对象转换为Python字典"""
    result = {}
    for key, value in struct.fields.items():
        result[key] = value_to_dict(value)
    return result


def list_value_to_list(list_value):
    """将protobuf ListValue对象转换为Python列表"""
    return [value_to_dict(value) for value in list_value.values]


def parse_parameters(proto_parameters):
    """从gRPC请求中解析参数"""
    parameters = {}
    for key, value in proto_parameters.items():
        parameters[key] = value_to_dict(value)
    return parameters
