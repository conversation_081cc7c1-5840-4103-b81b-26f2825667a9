import tiktoken
from huggingface_hub import login
from transformers import AutoTokenizer

from mygpt import settings

# - HF_TOKEN = hf_UXnSLYr****nMJZMOdV
login(token=settings.HF_TOKEN)
# - HUGGING_FACE_HUB_TOKEN = hf_Vrwt****olzEbrV
# login(token="hf_VrwtKo****VolzEbrV")


class LocalModelManager:
    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(LocalModelManager, cls).__new__(cls)
        return cls._instance

    def __init__(self, model_name: str):
        if not hasattr(self, "_initialized"):
            self._initialized = True
            self.model_name = model_name
            self.tokenizer = self.load_tokenizer()

    def load_tokenizer(self):
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.model_name, trust_remote_code=True
        )
        return self.tokenizer

    def calculate_token_count(self, text):
        tokenized_text = self.tokenizer.encode(text)
        token_count = len(tokenized_text)
        return token_count


class LocalModelManagerFactory:
    @staticmethod
    def get_manager(model_name="Qwen/Qwen2.5-0.5B"):
        return LocalModelManager(model_name)


if __name__ == "__main__":
    model_manager = LocalModelManagerFactory.get_manager("Qwen/Qwen2.5-0.5B")
    print(id(model_manager))
    model_manager = LocalModelManagerFactory.get_manager("Qwen/Qwen2.5-0.5B")
    print(id(model_manager))
    s = """
    你好，12345，hello, bonjour
    Please activate your account by clicking the link in the welcome email we've just sent you.
Reset Password Successful

未來世界，全世界進入冰河時代，幸存的人類全部躲進了一輛火車 #美國 #高分電影 #電影解說 #影視解說 #電影推薦](javascript:void(0) "未來世界，全世界進入冰河時代，幸存的人類全部躲進了一輛火車 #美國 #高分電影 #電影解說 #影視解說 #電影推薦
We have sent you a password reset email.
    """
    token_num = model_manager.calculate_token_count(s)
    print("qwen", token_num)
    tokenizer = tiktoken.get_encoding("cl100k_base")
    print("oai", len(tokenizer.encode(s)))

    model_manager = LocalModelManager("meta-llama/Llama-3.2-1B")
    ll1 = model_manager.calculate_token_count(s)
    print("ll", ll1)

    try:
        tk1 = AutoTokenizer.from_pretrained(
            # "meta-llama/Llama-3.2-1B", trust_remote_code=True
            "Sparticle/llama3.1-70B-4bit",
            trust_remote_code=True,
        )
        tk1t = tk1.encode(s)
        print("ll2", len(tk1t))
    except Exception as e:
        print(f"加载模型时出错：{e}")

    # tokenizer = tiktoken.get_encoding("o200k_base")
    # print(len(tokenizer.encode(s)))
