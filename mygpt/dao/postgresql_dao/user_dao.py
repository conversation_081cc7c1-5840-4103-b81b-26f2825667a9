from typing import List
from mygpt.models import Robot, Dataset, User
from tortoise.connection import connections


class UserDao:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(UserDao, cls).__new__(cls)
        return cls._instance

    async def find_user_by_apikey(self, api_key: str) -> User | None:
        conn = connections.get("default")  # 使用默认连接或指定连接名
        results = await conn.execute_query("""
            SELECT u.* FROM "user" as u 
            JOIN apikey as ak ON u.user_id = ak.user_id
            WHERE ak.api_key = $1 AND ak.deleted_at IS NULL
        """, [api_key])

        if results[0] > 0:  # 如果有结果
            # 将结果转换为User对象
            user_data = results[1][0]
            user = User(**{k: v for k, v in user_data.items() if k in User._meta.fields_map})
            return user
        return None


user_dao = UserDao()

