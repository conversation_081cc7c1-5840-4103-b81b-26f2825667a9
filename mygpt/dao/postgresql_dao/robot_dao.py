from typing import List
from mygpt.models import Robot, Dataset


class RobotDao:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(RobotDao, cls).__new__(cls)
        return cls._instance

    async def find_robot_by_user_ids_and_name(self, user_ids: List[str], name) -> List[Robot]:
        robot = await Robot.filter(user_id__in=user_ids, name=name)
        return robot

    async def find_robot_by_user_id_and_name(self, user_id: str, name) -> List[Robot]:
        robots = await Robot.filter(user_id=user_id, name=name, deleted_at__isnull=True).prefetch_related(
            "robotconfigs", "dictionary", "apis", "datasets", "datasets__vectorfiles"
        )
        return robots


robot_dao = RobotDao()

