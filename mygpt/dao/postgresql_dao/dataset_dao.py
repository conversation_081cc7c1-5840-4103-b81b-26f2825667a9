from typing import List
from mygpt.models import Robot, Dataset


class DatasetDao:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DatasetDao, cls).__new__(cls)
        return cls._instance

    async def find_datasets_by_ids(self, dataset_ids: list) -> List[Dataset]:
        datasets = await Dataset.filter(id__in=dataset_ids).all()
        return datasets


dataset_dao = DatasetDao()

