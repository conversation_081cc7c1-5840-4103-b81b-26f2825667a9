from typing import List, Dict

from loguru import logger as logging
from mygpt.opensearch_knowledge import OpenSearchKnowledgeClient


class KnowledgeDocsDao:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(KnowledgeDocsDao, cls).__new__(cls)
            cls._instance.client = OpenSearchKnowledgeClient.get_instance()
        return cls._instance

    def __del__(self):
        if hasattr(self, "client") and self.client:
            self.client.close()

    async def find_docs_by_project_id(self, project_id: str, fields: list = None) -> Dict:
        logging.info(f"[KnowledgeDocsDao] find_docs_by_project_id: {project_id}")
        try:
            rs = await self.client.aget_by_project_id(
                project_id=project_id,
                fields=fields
            )
            return rs
        except Exception as e:
            logging.error(f"[KnowledgeDocsDao] find_docs_by_project_id error: {e}")
            return {}

    async def find_docs_by_doc_ids(self, doc_ids: List[str], fields: List[str] = None) -> Dict:
        """通过文档ID查找文档"""
        logging.info(f"[KnowledgeDocsDao] find_docs_by_doc_ids: {doc_ids}")
        try:
            rs = await self.client.aget_by_doc_ids(
                doc_ids=doc_ids,
                fields=fields
            )
            return rs
        except Exception as e:
            logging.error(f"[KnowledgeDocsDao] find_docs_by_doc_ids error: {e}")
            return {}

    async def find_docs_by_project_id_without_dir(self, project_id: str, fields: list = None, size=1000) -> Dict:
        """通过项目ID查找文档，不包含目录"""
        logging.info(f"[KnowledgeDocsDao] find_docs_by_project_id_without_dir: {project_id}")
        try:
            rs = await self.client.afind_docs_by_project_id_without_dir(
                project_id=project_id,
                fields=fields,
                size=size
            )
            return rs
        except Exception as e:
            logging.error(f"[KnowledgeDocsDao] find_docs_by_project_id_without_dir error: {e}")
            return {}

    async def find_docs_info_by_dir_ids(
        self,
        dir_ids: List[str],
        fields: List[str] = None,
        size=1000
    ) -> Dict:
        """通过多个目录ID查找目录下的文档summary相关信息 (非递归)"""
        if not fields:
            fields = [
                "project_name", "project_id", "doc_id", "title", "is_directory", "summary", "content_token_count",
                "summary_token_count", "information_coverage", "missing_key_aspects", "language", "tags", "topics",
                "path", "path_parts", "depth_level", "current_directory_id", "directory_ids"
            ]
        logging.info(f"[KnowledgeDocsDao] find_docs_info_by_dir_ids: {dir_ids}")
        try:
            rs = await self.client.afind_docs_info_by_dir_ids(
                dir_ids=dir_ids,
                fields=fields,
                size=size
            )
            return rs
        except Exception as e:
            logging.error(f"[KnowledgeDocsDao] find_info_by_dir_ids error: {e}")
            return {}

    async def find_dir_info_by_project_id(self, project_id: str, fields: list = None, size=500) -> Dict:
        """通过项目ID查找目录"""
        logging.info(f"[KnowledgeDocsDao] find_dir_info_by_project_id: {project_id}")
        if not fields:
            fields = [
                "dir_id", "dir_name", "is_directory", "directory_description", "is_directory",
                "path", "path_parts", "depth_level", "current_directory_id", "directory_ids",
            ]
        try:
            rs = await self.client.afind_dir_info_by_project_id(
                project_id=project_id,
                fields=fields,
                size=size
            )
            return rs
        except Exception as e:
            logging.error(f"[KnowledgeDocsDao] find_dir_info_by_project_id error: {e}")
            return {}


knowledge_docs_dao = KnowledgeDocsDao()
