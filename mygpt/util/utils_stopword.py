import re
import unicodedata

# 英文常见停用词
en_stopwords = {"the", "is", "in", "at", "which", "on", "and", "a", "an", "to", "of", "for", "with", "as", "by", "that",
                "from", "it", "this", "be", "are", "was", "were", "will", "would", "can", "could", "should", "has",
                "have", "had", "do", "does", "did", "but", "or", "so", "if", "then", "than", "about", "into", "over",
                "after", "before", "between", "out", "up", "down", "not", "no", "yes", "how"}

# 中文常见停用词
zh_stopwords = {"的", "了", "在", "是", "我", "有", "和", "就", "不", 
                "都", "一", "一个", "上", "也", "很", "到",
                "说", "要", "去", "你", "会", "着", "没有", "看", "好", "自己", "这"}

# 日语常见助词/语气词
japanese_particles = {"て", "ね", "よ", "の", "が", "は", "を", "に", "も", "へ", "から", "まで", "より", "や", "ぞ",
                      "ぜ", "さ", "な", "だ", "か", "し", "わ", "っと", "など", "です", "ます", "でした", "ました"}

def filter_japanese_particles(text: str) -> str:
    """
    过滤常见日语语气词/助词，保留主要内容。
    将语气词替换为空格而非直接删除，以保持词间距离和语义完整性。
    仅做简单分词和过滤，不影响关键词。
    特殊处理：
    - 保留常见疑问词组合如"なんですか"、"どうですか"等
    """
    preserved_phrases = [
        "なんですか", "どうですか", "いつですか", "どこですか", "だれですか", "なぜですか",
        "どのように", "どんなですか", "いかがですか", "なぜなら",
        "ください", "ましょう", "ませんか", "でしょう",
        "ありがとう", "すみません", "お願いします",
        "それとも", "そして", "だから", "ですが", "けれども",
        # 补充常见疑问短语
        "どう思いますか",
        "ですか"
    ]
    protected_text = text
    replacements = {}
    for i, phrase in enumerate(preserved_phrases):
        if phrase in protected_text:
            placeholder = f"__PROTECTED_{i}__"
            protected_text = protected_text.replace(phrase, placeholder)
            replacements[placeholder] = phrase
    for particle in japanese_particles:
        protected_text = protected_text.replace(particle, " ")
    while "  " in protected_text:
        protected_text = protected_text.replace("  ", " ")
    result_text = protected_text
    for placeholder, original in replacements.items():
        result_text = result_text.replace(placeholder, original)
    return result_text.strip()

def filter_stopwords(text: str, language: str = "auto") -> str:
    """
    轻量多语言（中/英/日）停用词过滤：
    - 英文：正则分词+停用词过滤
    - 中文：遍历字符+停用词过滤，保留空格
    - 日文：已有助词过滤方案，保留空格
    """
    # 先去除首尾空白
    text = text.strip()
    
    if language == "auto":
        if re.search(r"[\u3040-\u30ff]", text):
            language = "japanese"
        elif re.search(r"[\u4e00-\u9fff]", text):
            language = "chinese"
        elif re.search(r"[a-zA-Z]", text):
            language = "english"
        else:
            # 如果无法确定语言，默认按日文处理标点和空格
            language = "japanese"

    if language == "english":
        # Extract tokens while preserving original case
        original_tokens = re.findall(r"\w+", text)
        # Only lowercase for stopword comparison, but keep original tokens for output
        filtered = [token for token in original_tokens if token.lower() not in en_stopwords]
        return " ".join(filtered)
    elif language == "chinese":
        # 优先过滤多字停用词，再过滤单字
        filtered_text = text
        # 先处理多字停用词（长度>1）
        multi_char_stopwords = [w for w in zh_stopwords if len(w) > 1]
        for stopword in multi_char_stopwords:
            filtered_text = filtered_text.replace(stopword, " ")  # 替换为空格而非空字符串
        
        # 过滤单字停用词，但保留空格
        filtered = []
        for c in filtered_text:
            if c in zh_stopwords:
                filtered.append(" ")  # 替换为空格
            elif unicodedata.category(c).startswith('Z'):
                filtered.append(" ")  # 保留空格字符，但统一为普通空格
            else:
                filtered.append(c)
        
        # 合并并去除多余空格
        result = "".join(filtered)
        while "  " in result:
            result = result.replace("  ", " ")
        return result.strip()
    elif language == "japanese":
        return filter_japanese_particles(text)
    else:
        return text
