import orjson
from typing import Any, Callable, Dict, Union, Optional, TextIO, BinaryIO, List, Tuple, Iterator
from functools import lru_cache


class Json:
    """
    基于orjson的高性能JSON工具类，API设计与标准json库保持一致，
    同时提供更丰富的功能和更高的性能。
    """

    # TODO: 未验证
    @staticmethod
    def dumps(obj: Any,
              default: Optional[Callable[[Any], Any]] = None,
              indent: bool = False,
              sort_keys: bool = False,
              append_newline: bool = False,
              serialize_numpy: bool = False) -> str:
        """
        将Python对象序列化为JSON字符串。

        参数:
            obj: 要序列化的Python对象
            default: 处理无法序列化类型的函数
            indent: 是否缩进格式化输出 (True时使用2空格缩进)
            sort_keys: 是否按键排序
            append_newline: 是否在结尾添加换行符
            serialize_numpy: 是否序列化numpy数组

        返回:
            JSON字符串
        """
        options = 0

        if indent:
            options |= orjson.OPT_INDENT_2

        if sort_keys:
            options |= orjson.OPT_SORT_KEYS

        if append_newline:
            options |= orjson.OPT_APPEND_NEWLINE

        if serialize_numpy:
            options |= orjson.OPT_SERIALIZE_NUMPY

        # orjson.dumps返回bytes，转换为str
        if options or default:
            return orjson.dumps(obj, default=default, option=options).decode('utf-8')
        else:
            return orjson.dumps(obj).decode('utf-8')

    @staticmethod
    def loads(s: Union[str, bytes, bytearray, memoryview]) -> Any:
        """
        将JSON字符串解析为Python对象。

        参数:
            s: JSON字符串或bytes

        返回:
            解析后的Python对象

        异常:
            JSONDecodeError: 如果JSON格式无效
        """
        try:
            return orjson.loads(s)
        except orjson.JSONDecodeError as e:
            # 添加更多上下文信息
            s_preview = str(s)[:100] + "..." if len(str(s)) > 100 else str(s)
            raise orjson.JSONDecodeError(f"{str(e)} - 输入: {s_preview}", str(s), 0) from e

    # TODO: 未验证
    @staticmethod
    def dump(obj: Any,
             fp: Union[TextIO, BinaryIO],
             default: Optional[Callable[[Any], Any]] = None,
             indent: bool = False,
             sort_keys: bool = False,
             append_newline: bool = False,
             serialize_numpy: bool = False) -> None:
        """
        将Python对象序列化为JSON并写入文件。

        参数:
            obj: 要序列化的Python对象
            fp: 文件对象
            default: 处理无法序列化类型的函数
            indent: 是否缩进格式化输出 (True时使用2空格缩进)
            sort_keys: 是否按键排序
            append_newline: 是否在结尾添加换行符
            serialize_numpy: 是否序列化numpy数组
        """
        result = Json.dumps(
            obj,
            default=default,
            indent=indent,
            sort_keys=sort_keys,
            append_newline=append_newline,
            serialize_numpy=serialize_numpy
        )

        # 检查文件对象
        if not hasattr(fp, 'write'):
            raise TypeError("fp must be a file-like object with write() method")

        # 明确区分二进制模式和文本模式
        is_binary_mode = hasattr(fp, 'mode') and 'b' in fp.mode

        try:
            if is_binary_mode:
                # 二进制模式写入
                fp.write(result.encode('utf-8'))  # type: ignore
            else:
                # 文本模式写入
                fp.write(result)  # type: ignore
        except TypeError as e:
            if is_binary_mode:
                raise TypeError("Binary mode file object expects bytes, not str") from e
            else:
                raise TypeError("Text mode file object expects str, not bytes") from e

    # TODO: 未验证
    @staticmethod
    def load(fp: Union[TextIO, BinaryIO]) -> Any:
        """
        从文件读取JSON并解析为Python对象。

        参数:
            fp: 文件对象

        返回:
            解析后的Python对象
        """
        if not hasattr(fp, 'read'):
            raise TypeError("fp must be a file-like object with read() method")

        content = fp.read()
        if isinstance(content, bytes):
            return orjson.loads(content)
        else:
            return orjson.loads(content.encode('utf-8'))

    # ----- 扩展实用方法 -----

    # TODO: 未验证
    @staticmethod
    def prettify(json_str: Union[str, bytes]) -> str:
        """
        美化JSON字符串。

        参数:
            json_str: JSON字符串或bytes

        返回:
            美化后的JSON字符串
        """
        # 先解析再序列化以确保格式正确
        obj = Json.loads(json_str)
        return Json.dumps(obj, indent=2)

    # TODO: 未验证
    @staticmethod
    def is_valid(json_str: Union[str, bytes]) -> bool:
        """
        检查JSON字符串是否有效。

        参数:
            json_str: 要检查的JSON字符串或bytes

        返回:
            布尔值，表示JSON是否有效
        """
        try:
            Json.loads(json_str)
            return True
        except Exception:
            return False

    # TODO: 未验证
    @staticmethod
    def merge(obj1: Dict, obj2: Dict, deep: bool = False) -> Dict:
        """
        合并两个JSON对象。

        参数:
            obj1: 第一个字典
            obj2: 第二个字典
            deep: 是否执行深度合并（递归合并嵌套字典）

        返回:
            合并后的字典
        """
        if not isinstance(obj1, dict) or not isinstance(obj2, dict):
            raise TypeError("obj1 and obj2 must be dictionaries")

        if not deep:
            result = obj1.copy()
            result.update(obj2)
            return result

        # 深度合并
        result = obj1.copy()
        for key, value in obj2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = Json.merge(result[key], value, deep=True)
            else:
                result[key] = value
        return result

    # TODO: 未验证
    @staticmethod
    @lru_cache(maxsize=128)
    def _path_resolver(path: str) -> List[str]:
        """解析路径字符串为路径组件列表，并缓存结果"""
        return path.split(".")

    # TODO: 未验证
    @staticmethod
    def extract(obj: Dict, path: str, default: Any = None) -> Any:
        """
        从JSON对象中提取特定路径的值，支持点号表示法。

        参数:
            obj: 字典对象
            path: 路径，如"user.address.city"
            default: 如果路径不存在时的默认值

        返回:
            路径对应的值，如果路径不存在则返回默认值
        """
        if not obj:
            return default

        parts = Json._path_resolver(path)
        current = obj

        try:
            for part in parts:
                if isinstance(current, dict):
                    current = current.get(part)
                elif isinstance(current, list) and part.isdigit():
                    index = int(part)
                    if 0 <= index < len(current):
                        current = current[index]
                    else:
                        return default
                else:
                    return default

                if current is None:
                    return default
            return current
        except (KeyError, IndexError, TypeError):
            return default

    # TODO: 未验证
    @staticmethod
    def flatten(obj: Dict, prefix: str = "", separator: str = ".") -> Dict:
        """
        将嵌套的JSON对象扁平化为单层键值对。

        参数:
            obj: 要扁平化的字典
            prefix: 键前缀
            separator: 分隔符

        返回:
            扁平化后的字典
        """
        if not isinstance(obj, dict):
            raise TypeError("obj must be a dictionary")

        result = {}

        def _flatten(x, name=''):
            if isinstance(x, dict):
                for k, v in x.items():
                    _flatten(v, name + str(k) + separator if name else str(k) + separator)
            elif isinstance(x, list):
                for i, v in enumerate(x):
                    _flatten(v, name + str(i) + separator)
            else:
                # 移除末尾的分隔符
                if name.endswith(separator):
                    name = name[:-len(separator)]
                result[name] = x

        _flatten(obj, prefix)
        return result

    # TODO: 未验证
    @staticmethod
    def unflatten(obj: Dict, separator: str = ".") -> Dict:
        """
        将扁平化的字典转换回嵌套结构。

        参数:
            obj: 扁平化的字典
            separator: 键中使用的分隔符

        返回:
            嵌套结构的字典
        """
        if not isinstance(obj, dict):
            raise TypeError("obj must be a dictionary")

        result = {}

        for key, value in obj.items():
            parts = key.split(separator)
            current = result

            for i, part in enumerate(parts[:-1]):
                if part.isdigit() and isinstance(current, list):
                    idx = int(part)
                    # 确保列表足够长
                    while len(current) <= idx:
                        current.append({} if i < len(parts) - 2 else [])
                    current = current[idx]
                else:
                    if part not in current:
                        # 查看下一部分是否是数字，如果是则初始化为列表
                        if i + 1 < len(parts) and parts[i + 1].isdigit():
                            current[part] = []
                        else:
                            current[part] = {}
                    current = current[part]

            last_key = parts[-1]
            if last_key.isdigit() and isinstance(current, list):
                idx = int(last_key)
                # 确保列表足够长
                while len(current) <= idx:
                    current.append(None)
                current[idx] = value
            else:
                current[last_key] = value

        return result

    # TODO: 未验证
    @staticmethod
    def minify(json_str: Union[str, bytes]) -> str:
        """
        最小化JSON字符串，移除所有不必要的空白。

        参数:
            json_str: JSON字符串或bytes

        返回:
            最小化后的JSON字符串
        """
        obj = Json.loads(json_str)
        return Json.dumps(obj)

    # TODO: 未验证
    @staticmethod
    def diff(obj1: Dict, obj2: Dict, deep: bool = False) -> Dict:
        """
        比较两个JSON对象，返回差异信息。支持深度递归比较。

        参数:
            obj1: 第一个字典
            obj2: 第二个字典
            deep: 是否执行深度比较

        返回:
            包含添加、删除和修改的差异字典
        """
        if not isinstance(obj1, dict) or not isinstance(obj2, dict):
            raise TypeError("obj1 and obj2 must be dictionaries")

        result = {
            "added": {},
            "removed": {},
            "modified": {}
        }

        # 检查添加和修改的键
        for key, value in obj2.items():
            if key not in obj1:
                result["added"][key] = value
            elif obj1[key] != value:
                if deep and isinstance(obj1[key], dict) and isinstance(value, dict):
                    nested_diff = Json.diff(obj1[key], value, deep=True)
                    if any(nested_diff.values()):  # 如果有任何差异
                        result["modified"][key] = nested_diff
                else:
                    result["modified"][key] = {
                        "from": obj1[key],
                        "to": value
                    }

        # 检查删除的键
        for key, value in obj1.items():
            if key not in obj2:
                result["removed"][key] = value

        return result

    # TODO: 未验证
    @staticmethod
    def read_ndjson(file_path: str) -> Iterator[Any]:
        """
        读取NDJSON（换行分隔的JSON）文件，生成每行解析后的对象。

        参数:
            file_path: NDJSON文件路径

        返回:
            生成器，每次迭代返回一行解析后的JSON对象
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:  # 跳过空行
                    try:
                        yield Json.loads(line)
                    except orjson.JSONDecodeError as e:
                        raise orjson.JSONDecodeError(
                            f"Invalid JSON at line: {line[:50]}...", line, 0
                        ) from e

    # TODO: 未验证
    @staticmethod
    def write_ndjson(objs: List[Any], file_path: str, default: Optional[Callable] = None) -> None:
        """
        将对象列表写入NDJSON文件。

        参数:
            objs: 要写入的对象列表
            file_path: 输出文件路径
            default: 处理无法序列化类型的函数
        """
        with open(file_path, 'w', encoding='utf-8') as f:
            for i, obj in enumerate(objs):
                try:
                    json_line = Json.dumps(obj, default=default)
                    f.write(json_line + '\n')
                except Exception as e:
                    raise TypeError(f"Error serializing object at index {i}: {str(e)}") from e

    # TODO: 未验证
    @staticmethod
    def schema(obj: Any) -> Dict:
        """
        从JSON对象生成简单的schema描述。

        参数:
            obj: 要分析的对象

        返回:
            对象schema的描述
        """

        def _get_type(value):
            if value is None:
                return "null"
            elif isinstance(value, bool):
                return "boolean"
            elif isinstance(value, int):
                return "integer"
            elif isinstance(value, float):
                return "number"
            elif isinstance(value, str):
                return "string"
            elif isinstance(value, list):
                if not value:
                    return "array<unknown>"

                types = set()
                for item in value[:10]:  # 仅分析前10个元素
                    types.add(_get_type(item))
                if len(types) == 1:
                    return f"array<{next(iter(types))}>"
                else:
                    return "array<mixed>"
            elif isinstance(value, dict):
                return "object"
            else:
                return "unknown"

        def _analyze(value):
            if isinstance(value, dict):
                result = {
                    "type": "object",
                    "properties": {}
                }
                for k, v in value.items():
                    result["properties"][k] = _analyze(v)
                return result
            elif isinstance(value, list):
                if not value:
                    return {"type": "array", "items": {"type": "unknown"}}

                # 分析数组元素的类型
                types = set(_get_type(item) for item in value[:10])
                if len(types) == 1:
                    element_type = next(iter(types))
                    if element_type in ("object", "array<mixed>", "array<object>"):
                        # 对于对象数组，分析第一个元素的结构
                        return {
                            "type": "array",
                            "items": _analyze(value[0])
                        }
                    else:
                        return {
                            "type": "array",
                            "items": {"type": element_type}
                        }
                else:
                    # 处理混合类型数组
                    return {
                        "type": "array",
                        "items": {"type": "mixed", "possible_types": list(types)}
                    }
            else:
                return {"type": _get_type(value)}

        return _analyze(obj)

    # TODO: 未验证
    @staticmethod
    def validate(obj: Any, schema: Dict) -> Tuple[bool, Optional[str]]:
        """
        根据简单schema验证JSON对象。

        参数:
            obj: 要验证的对象
            schema: 由schema()方法生成的schema

        返回:
            (是否有效, 错误消息)
        """

        def _validate(value, schema_part):
            # 检查类型
            if "type" not in schema_part:
                return False, "Schema missing 'type' field"

            schema_type = schema_part["type"]

            # 处理null值
            if value is None:
                return schema_type == "null", f"Expected null but got {value}"

            # 处理基本类型
            if schema_type == "string":
                if not isinstance(value, str):
                    return False, f"Expected string but got {type(value).__name__}"
            elif schema_type == "integer":
                if not isinstance(value, int) or isinstance(value, bool):
                    return False, f"Expected integer but got {type(value).__name__}"
            elif schema_type == "number":
                if not isinstance(value, (int, float)) or isinstance(value, bool):
                    return False, f"Expected number but got {type(value).__name__}"
            elif schema_type == "boolean":
                if not isinstance(value, bool):
                    return False, f"Expected boolean but got {type(value).__name__}"
            elif schema_type == "array":
                if not isinstance(value, list):
                    return False, f"Expected array but got {type(value).__name__}"

                # 验证数组元素
                if "items" in schema_part and value:
                    for i, item in enumerate(value):
                        valid, error = _validate(item, schema_part["items"])
                        if not valid:
                            return False, f"Array item {i}: {error}"
            elif schema_type == "object":
                if not isinstance(value, dict):
                    return False, f"Expected object but got {type(value).__name__}"

                # 验证对象属性
                if "properties" in schema_part:
                    for prop_name, prop_schema in schema_part["properties"].items():
                        if prop_name in value:
                            valid, error = _validate(value[prop_name], prop_schema)
                            if not valid:
                                return False, f"Property '{prop_name}': {error}"

            return True, None

        return _validate(obj, schema)
