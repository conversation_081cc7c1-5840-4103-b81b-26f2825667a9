import logging
import os
import time
from typing import Dict, Tuple

from mygpt.send_email import send_admin_alert_email
from mygpt.settings import RedisClient

# 错误类型常量
ERROR_TYPE_AI_API = "error_ai_api"  # API 503错误，通常是API账号异常
ERROR_TYPE_VECTOR = "error_vector"  # 向量数据库连接错误
ERROR_TYPE_GENERAL = "error_general"  # 业务错误，不能恢复

# 错误类型匹配规则
ERROR_TYPE_PATTERNS = {
    ERROR_TYPE_AI_API: [
        "Error code: 401",
        "Error code: 503",
        "Error code: 429",
        "Error code: 529",
        "Service Unavailable",
        "Rate limit exceeded",
        "API quota exceeded",
    ],
    ERROR_TYPE_VECTOR: [
        "All connection attempts failed",
    ],
}

# 默认配置 (count, window(秒), cooldown(秒))
DEFAULT_THRESHOLD = (10, 60, 600)


def _parse_threshold_config(config_str: str) -> <PERSON><PERSON>[int, int, int]:
    """解析阈值配置字符串，格式：'count,window,cooldown'"""
    try:
        count, window, cooldown = map(int, config_str.strip().split(","))
        return count, window, cooldown
    except (ValueError, AttributeError):
        return DEFAULT_THRESHOLD


# 从环境变量读取每种错误类型的配置
ERROR_CONFIGS: Dict[str, Tuple[int, int, int]] = {
    ERROR_TYPE_AI_API: _parse_threshold_config(
        os.environ.get("NOTICE_AI_API_CONFIG", ",".join(map(str, DEFAULT_THRESHOLD)))
    ),
    ERROR_TYPE_VECTOR: _parse_threshold_config(
        os.environ.get("NOTICE_VECTOR_CONFIG", ",".join(map(str, DEFAULT_THRESHOLD)))
    ),
    ERROR_TYPE_GENERAL: DEFAULT_THRESHOLD,
}


def _get_redis_key(error_type: str, field: str) -> str:
    """生成Redis键名"""
    return f"error_notice:{error_type}:{field}"


def _get_error_type(error_msg: str) -> str:
    """根据错误消息确定错误类型"""
    error_msg = error_msg.lower()
    for error_type, patterns in ERROR_TYPE_PATTERNS.items():
        if any(pattern.lower() in error_msg for pattern in patterns):
            return error_type
    return ERROR_TYPE_GENERAL


async def notice(e: str):
    notice_list = os.environ.get("EMAIL_ADMIN_OUTGOING_NOTICE_LIST")
    if not notice_list:
        logging.error("EMAIL_ADMIN_OUTGOING_NOTICE_LIST 环境变量未设置")
        return

    try:
        error_msg = str(e)
        logging.warning(f"⚠️ 错误通知 | 收到错误消息: {error_msg}")

        # 使用新的错误类型判断逻辑
        error_type = _get_error_type(error_msg)
        logging.warning(f"⚠️ 错误通知 | 错误类型: {error_type}")

        current_time = int(time.time())
        threshold_count, time_window, cooldown = ERROR_CONFIGS[error_type]
        logging.warning(
            f"⚠️ 错误通知 | 配置 - 错误阈值: {threshold_count}次, 时间窗口: {time_window}秒, 冷却时间: {cooldown}秒"
        )

        redis_client = RedisClient.get_client()

        # 获取Redis中的计数器状态
        count_key = _get_redis_key(error_type, "count")
        last_sent_key = _get_redis_key(error_type, "last_sent")
        first_error_key = _get_redis_key(error_type, "first_error")

        # 获取当前状态
        last_sent = int(await redis_client.get(last_sent_key) or 0)
        first_error = int(await redis_client.get(first_error_key) or 0)

        # 转换时间戳为人类可读格式
        last_sent_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(last_sent)) if last_sent > 0 else "从未发送"
        first_error_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(first_error)) if first_error > 0 else "无首次错误"
        logging.warning(f"⚠️ 错误通知 | 历史记录 - 上次发送时间: {last_sent_time}, 首次错误时间: {first_error_time}")

        # 检查是否需要开始新的错误计数周期
        if (
            current_time - last_sent > cooldown
            and current_time - first_error > time_window  # 超过冷却时间
        ):  # 超过时间窗口
            # 只有同时满足冷却时间和时间窗口条件才重置计数
            await redis_client.set(count_key, 1)
            await redis_client.set(first_error_key, current_time)
            current_count = 1
            logging.warning("⚠️ 错误通知 | ▶️ 开始新的错误计数周期 - 已重置计数器")
        else:
            # 在时间窗口内继续累加计数
            current_count = int(await redis_client.incr(count_key))
            if current_count == 1:  # 如果是第一次错误，记录时间
                await redis_client.set(first_error_key, current_time)

        # 将时间差转换为更友好的格式
        def format_time_diff(seconds):
            if seconds < 60:
                return f"{seconds}秒"
            elif seconds < 3600:
                minutes = seconds // 60
                remain_seconds = seconds % 60
                if remain_seconds == 0:
                    return f"{minutes}分钟"
                else:
                    return f"{minutes}分钟{remain_seconds}秒"
            elif seconds < 86400:
                hours = seconds // 3600
                minutes = (seconds % 3600) // 60
                if minutes == 0:
                    return f"{hours}小时"
                else:
                    return f"{hours}小时{minutes}分钟"
            else:
                days = seconds // 86400
                hours = (seconds % 86400) // 3600
                if hours == 0:
                    return f"{days}天"
                else:
                    return f"{days}天{hours}小时"
                
        # 转换时间戳为人类可读格式
        current_time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(current_time))
        first_error_time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(first_error)) if first_error > 0 else "无首次错误"
        
        # 添加距离上次发送的时间信息
        last_sent_diff = ""
        if last_sent > 0:
            time_since_last = format_time_diff(current_time - last_sent)
            last_sent_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(last_sent))
            last_sent_diff = f", 上次发送: {last_sent_str} (距今 {time_since_last})"
        
        logging.warning(
            f"⚠️ 错误通知 | 计数状态 - 当前错误计数: {current_count}/{threshold_count}, 首次错误: {first_error_time_str}, 当前时间: {current_time_str}{last_sent_diff}"
        )

        # 检查是否需要发送邮件
        should_send = False
        if current_count >= threshold_count:  # 错误计数达到阈值
            if last_sent == 0:  # 从未发送过通知
                should_send = True
            elif current_time - last_sent > cooldown:  # 超过冷却时间
                should_send = True

        # 简化决策日志逻辑
        count_check = f"错误计数: {current_count}/{threshold_count} {'✓' if current_count >= threshold_count else '✗'}"
        
        if not should_send:
            # 不发送通知的情况
            if current_count < threshold_count:
                decision_reason = f"原因: 错误计数不足 ({current_count}/{threshold_count})"
            else:  # current_count >= threshold_count
                if last_sent > 0 and current_time - last_sent <= cooldown:
                    time_diff = current_time - last_sent  # 已经过去的时间
                    remain_time = cooldown - time_diff   # 还需等待的时间
                    formatted_diff = format_time_diff(time_diff)
                    formatted_remain = format_time_diff(remain_time)
                    decision_reason = f"原因: 还在冷却期内 (已过: {formatted_diff}, 还需等待: {formatted_remain})"
                else:
                    # 这种情况不应该发生，但为了完整性添加
                    decision_reason = "原因: 逻辑异常、请检查代码"
        else:
            # 发送通知的情况
            if last_sent == 0:
                decision_reason = f"原因: 错误计数足够 ({current_count}/{threshold_count}) 且为首次发送"
            else:
                time_diff = current_time - last_sent
                formatted_diff = format_time_diff(time_diff)
                formatted_cooldown = format_time_diff(cooldown)
                decision_reason = f"原因: 错误计数足够 ({current_count}/{threshold_count}) 且冷却时间已过 (已过: {formatted_diff}, 冷却时间: {formatted_cooldown})"
        
        logging.warning(
            f"⚠️ 错误通知 | 发送决策 - 是否发送: {'✓' if should_send else '✗'}, {decision_reason}"
        )

        if should_send:
            error_type_display = {
                ERROR_TYPE_AI_API: "API 503 429等 错误 (API账号/限流问题)",
                ERROR_TYPE_VECTOR: "向量数据库连接错误",
                ERROR_TYPE_GENERAL: "通用系统错误",
            }.get(error_type, "未分类错误")

            # 格式化错误信息
            error_details = (
                f"错误类型: {error_type_display}\n"
                f"错误次数: {current_count} (阈值: {threshold_count})\n"
                f"时间窗口: {time_window}秒\n"
                f"冷却时间: {cooldown}秒\n"
                f"原始错误: {error_msg}"
            )

            logging.warning(f"⚠️ 错误通知 | 📧 准备发送邮件到: {notice_list}")
            try:
                send_admin_alert_email(
                    to_email=notice_list,
                    title=f"错误报警: {error_type_display}",
                    cause=error_details,
                )
            except Exception as e:
                logging.error(f"发送邮件失败: {str(e)}", exc_info=True)
                pass
            logging.warning("⚠️ 错误通知 | ✅ 邮件发送完成")

            # 更新发送时间并重置计数
            await redis_client.set(last_sent_key, current_time)
            await redis_client.set(count_key, 0)
            await redis_client.set(first_error_key, 0)
            logging.warning("⚠️ 错误通知 | 🔄 重置计数器状态完成")

    except Exception as ex:
        logging.error(f"发送邮件失败: {str(ex)}", exc_info=True)
        pass
