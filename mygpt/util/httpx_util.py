import httpx
import asyncio
from loguru import logger as logging

class Httpx:
    _client: httpx.AsyncClient | None = None

    @classmethod
    async def initialize_client(cls):
        """在应用启动时调用，初始化客户端"""
        if cls._client is None or cls._client.is_closed: # 检查是否已关闭以允许重连
            logging.info(f"【httpx_util.Httpx】Initializing HTTPX client")
            cls._client = httpx.AsyncClient(timeout=httpx.Timeout(10.0, connect=5.0))  # 配置默认超时
        else:
            logging.info(f"【httpx_util.Httpx】HTTPX client already initialized")

    @classmethod
    async def get_client(cls):
        """获取HTTPX客户端实例"""
        if cls._client is None:
            await cls.initialize_client()
        return cls._client

    @classmethod
    async def close_client(cls):
        """关闭HTTPX客户端"""
        if cls._client is not None:
            logging.info(f"【httpx_util.Httpx】Closing HTTPX client")
            await cls._client.aclose()
            # 清理引用
            cls._client = None

    @classmethod
    async def check_url_accessible(cls, url: str, headers: dict = None, **kwargs) -> bool:
        """检查URL是否可访问"""
        client = cls.get_client()
        try:
            logging.info(f"【httpx_util.Httpx.check_url_accessible】Checking URL: {url}")
            response = await client.head(url, headers=headers, **kwargs)
            # 我们这里可以更灵活地只判断状态码
            if 200 <= response.status_code < 300:
                print(f"URL {url} is accessible, status: {response.status_code}")
                return True
            else:
                print(f"URL {url} returned status: {response.status_code}")
                return False
        except httpx.HTTPStatusError as e:  # 捕获由 raise_for_status() 或服务器返回的 4xx/5xx 错误
            logging.warning(f"【httpx_util.Httpx.check_url_accessible】HTTPStatusError: {e}")
            return False
        except httpx.RequestError as e:  # 网络相关的错误 (DNS, connection refused, timeout等)
            logging.warning(f"【httpx_util.Httpx.check_url_accessible】RequestError: {e}")
            return False
        except Exception as e:  # 其他意外错误
            logging.warning(f"【httpx_util.Httpx.check_url_accessible】Unexpected error: {e}")
            return False
