import asyncio
import base64
import io
import os
import shutil
import json
import ijson
import traceback
import uuid
from datetime import datetime
from typing import List, Optional
from urllib.parse import quote

from fastapi import APIRouter, Request, UploadFile
from fastapi.responses import HTMLResponse, JSONResponse, StreamingResponse

from mygpt import settings
from mygpt.core.embedding import EmbeddingFactory
from mygpt.core.vector_storage import VectorStorageFactory
from mygpt.enums import (
    VectorFileStatus,
    VectorFileType,
    DatasourceType,
    VectorStorageType,
    DATASET_STATUS,
    AIStatus,
)
from mygpt.file_embeddings import spliter_short_text
from mygpt.loader.enums import UnstructuredType, filename_to_unstructured_type
from mygpt.loader.transcoder import DocumentTranscoder
from mygpt.models import VectorFile, Dataset, Robot, UsageType
from mygpt.opensearch import get_open_search_client
from mygpt.schemata import VectorFileOut
import aiofiles
from datetime import datetime
from typing import List, Optional
from urllib.parse import quote
from fastapi import APIRouter, Request, UploadFile
from fastapi.responses import HTMLResponse, JSONResponse, StreamingResponse
from langchain.docstore.document import Document
from mygpt import settings
from mygpt.enums import (
    VectorFileStatus,
    VectorFileType,
    DatasourceType,
    VectorStorageType,
    DATASET_STATUS,
    AIStatus,
)
from mygpt.models import VectorFile, Dataset, Robot, UsageType
from mygpt.schemata import VectorFileOut
from mygpt.loader.enums import UnstructuredType, filename_to_unstructured_type
from mygpt.loader.transcoder import DocumentTranscoder
from mygpt.core.vector_storage import VectorStorageFactory
from mygpt.utils import num_tokens_from_string, compute_charactors_count
from mygpt.token_utils import verify_token_dataset, sync_user_token
from mygpt.core.embedding import EmbeddingFactory
from mygpt.opensearch import get_open_search_client
from mygpt.file_embeddings import spliter_short_text
from mygpt.train import logs, utils, core, monitor
from mygpt.services.quota_service import QuotaService

train_router = APIRouter(prefix="/train", tags=["Train"])


def train_init():
    logs.init()
    asyncio.create_task(vectorfile_check_daemon())
    asyncio.create_task(monitor.monitor_probe_daemon())


@train_router.api_route(
    "/monitor", methods=["GET", "POST"], response_class=HTMLResponse
)
async def train_monitor(request: Request):
    params: dict = dict(request.query_params)
    forms: dict = dict(await request.form())
    session: dict = request.session
    response: HTMLResponse = await monitor.monitor(params, forms, session)
    return response


@train_router.post("/spool", response_class=JSONResponse)
async def train_spool(req: dict) -> dict:
    res = await core.train_spool(req)
    return JSONResponse(res)


@train_router.get("/file", response_class=StreamingResponse)
async def train_file(file_key: str) -> StreamingResponse:
    async with utils.s3_get(file_key) as file:
        res_bytes = file.read()
    return StreamingResponse(content=iter([res_bytes]))


@train_router.post("/upload", response_class=JSONResponse)
async def train_upload(req: dict) -> dict:
    data = req["data"]
    data = base64.b85decode(data)
    data = io.BytesIO(data)
    filename = req["filename"]
    filename = os.path.basename(filename)
    ext = os.path.splitext(filename.lower())[1]
    uid = uuid.uuid4().hex
    file_key = f"parser_file/{uid}{ext}"
    file_src = await utils.s3_put(file_key=file_key, file=data)
    res = {
        "res": "ok",
        "filename": filename,
        "file_src": file_src,
        "file_key": file_key,
    }
    return JSONResponse(res)


@train_router.post("/finish", response_class=JSONResponse)
async def train_finish(req: Request):
    zstd_filepath = os.path.join(utils.backend_temp_dir(), f"{uuid.uuid4().hex}.zstd")
    try:
        params_len = -1
        params_chunk = b""
        async with aiofiles.open(zstd_filepath, "wb") as file:
            async for chunk in req.stream():
                if params_len < 0:
                    assert len(chunk) >= 4
                    params_len = int.from_bytes(chunk[:4], "little")
                    assert params_len >= 0
                    chunk = chunk[4:]
                assert len(params_chunk) <= params_len
                if len(params_chunk) < params_len:
                    length = min(params_len, len(chunk))
                    params_chunk += chunk[:length]
                    chunk = chunk[length:]
                if len(params_chunk) == params_len:
                    await file.write(chunk)
                else:
                    assert len(chunk) == 0
        res_filepath = os.path.join(utils.backend_temp_dir(), f"{uuid.uuid4().hex}.res")
        await utils.zstandard_decompress(zstd_filepath, res_filepath)
        params = json.loads(params_chunk.decode("utf-8"))
        res = await core.train_finish(params, res_filepath)
        return JSONResponse(res)
    finally:
        if os.path.exists(zstd_filepath):
            os.remove(zstd_filepath)


###########################################################


async def download_preview(vectorfile_id: str, to_pdf: bool) -> StreamingResponse:
    src_filepath = None
    des_filepath = None
    try:
        logs.info(
            f"train-v2: download_preview start, vectorfile_id={vectorfile_id}, to_pdf={to_pdf}"
        )
        vectorfile: VectorFile = await VectorFile.get_or_none(
            id=vectorfile_id, deleted_at__isnull=True
        )
        logs.asserts(vectorfile)
        logs.asserts(vectorfile.key)
        logs.asserts(
            vectorfile.file_type in [VectorFileType.UPLOAD, VectorFileType.INTEGRATION]
        )
        file_ext = os.path.splitext(vectorfile.filename)[1]
        key_name, key_ext = os.path.splitext(vectorfile.key)
        key = vectorfile.key
        if to_pdf and key_ext.lower() != ".pdf" and file_ext.lower() != ".pdf":
            des_key = f"{key_name}.pdf"
            try:
                async with utils.s3_get(des_key) as s3_file:
                    file_bytes = s3_file.read()
            except Exception as ex:
                logs.warning(
                    f"train-v2: download_preview, preveiew_key_not_existed, ex={repr(ex)}"
                )
                src_filepath = os.path.join(
                    utils.backend_temp_dir(), f"{uuid.uuid4().hex}{key_ext.lower()}"
                )
                async with utils.s3_get(vectorfile.key) as s3_file:
                    with open(src_filepath, "wb") as src_file:
                        await asyncio.to_thread(shutil.copyfileobj, s3_file, src_file)
                des_filepath = f"{os.path.splitext(src_filepath)[0]}.pdf"
                logs.asserts(os.path.exists(src_filepath))
                logs.info(
                    f"train-v2: download_preview, convert_to_pdf, src_filpath={src_filepath}, des_filepath={des_filepath}"
                )
                logs.asserts(await utils.convert_to_pdf(src_filepath, des_filepath))
                logs.asserts(os.path.exists(des_filepath))
                with open(des_filepath, "rb") as des_file:
                    await utils.s3_put(des_key, des_file)
                with open(des_filepath, "rb") as des_file:
                    file_bytes = des_file.read()
        else:
            async with utils.s3_get(key) as s3_file:
                file_bytes = s3_file.read()
        return StreamingResponse(
            content=iter([file_bytes]),
            headers={
                "Content-Disposition": f"attachment; filename={quote(os.path.basename(vectorfile.filename))}",
            },
            media_type="application/octet-stream",
        )

    except Exception as ex:
        logs.dump_exception(ex)
        raise ex
    finally:
        if src_filepath and os.path.exists(src_filepath):
            os.remove(src_filepath)
        if des_filepath and os.path.exists(des_filepath):
            os.remove(des_filepath)
        logs.info(
            f"train-v2: download_preview end, vectorfile_id={vectorfile_id}, to_pdf={to_pdf}, src_filepath={src_filepath}, des_filepath={des_filepath}"
        )


async def delete_list(dataset_id: str, file_id_list: list[str]):
    async with utils.global_lock(utils.LOCK_VECTOR_FILE):
        for vectorfile_id in file_id_list:
            try:
                await vectorfile_delete(vectorfile_id=vectorfile_id)
                await Dataset.filter(id=dataset_id).update(updated_at=datetime.now())
            except Exception as ex:
                logs.error(f"train-v2: delete_list, except, ex={repr(ex)}")
                logs.error(traceback.format_exc())


async def refresh_list(
    dataset_id: str | uuid.UUID,
    file_id_list: list[str] | list[uuid.UUID],
    datasource_type: DatasourceType,
    learn_type: Optional[int] = None,
):
    logs.asserts(datasource_type in [item for item in DatasourceType])
    dataset = await Dataset.get_or_none(id=dataset_id, deleted_at__isnull=True)
    logs.asserts(dataset)
    vector_storage_type = VectorStorageType(
        dataset.metadata.get("vector_storage", VectorStorageType.PINECONE)
    )
    logs.asserts(vector_storage_type == VectorStorageType.QDRANT_ONE_COLLECTION)
    for vectorfile_id in file_id_list:
        try:
            await core.task_clear_by_vectorfile_id(str(vectorfile_id))
            await vectorfile_reset(
                vectorfile_id=str(vectorfile_id),
                file_status=VectorFileStatus.READY,
                failed_reason="",
                learn_type=learn_type,
            )
            await Dataset.filter(id=dataset.id).update(updated_at=datetime.now())
        except Exception as ex:
            logs.error(f"train-v2: train_refresh_files, except, ex={repr(ex)}")
            logs.error(traceback.format_exc())
            await vectorfile_reset(
                vectorfile_id=vectorfile_id,
                file_status=VectorFileStatus.FAIL,
                failed_reason=repr(ex),
            )


async def add_files(dataset_id: str, learn_type: int, files: list[UploadFile]):
    logs.asserts(
        len(files) <= settings.MAX_FILES_ALLOWED,
        f"Too many files, please limit the number of files to {settings.MAX_FILES_ALLOWED}",
    )
    for file in files:
        logs.asserts(
            file.size <= settings.MAX_FILE_SIZE,
            f"File {file.filename} size {file.size} exceeds the limit of {settings.MAX_FILE_SIZE} bytes",
        )
        logs.asserts(file.size > 0, f"Empty file not allowed: {file.filename}")
        logs.asserts(
            filename_to_unstructured_type(file.filename) != UnstructuredType.UNKNOWN,
            f"File extension not supported for file {file.filename}",
        )
    desc_list: List[dict] = []
    try:
        for file in files:
            file_name = file.filename
            file_headers = dict(file.headers)
            from urllib.parse import unquote
            file_title = (f"user_define_title: {unquote(file_headers['title'])}" if "title" in file_headers else None)
            file_md5 = await utils.file_md5(file.file)
            file_size = file.size
            file_obj = await VectorFile.filter(
                dataset_id=dataset_id, content_hash=file_md5, deleted_at__isnull=True
            ).first()
            if file_obj:
                file_id = str(file_obj.id)
                file_key = file_obj.key
                duplicated_file_obj = file_obj
            else:
                file_id = str(uuid.uuid4())
                file_ext = os.path.splitext(file_name)[1].lower()
                file_key = f"{file_md5}_{uuid.UUID(file_id).hex}{file_ext}"
                await utils.s3_put(file_key, file.file)
                duplicated_file_obj = None
            desc_list.append(
                {
                    "file_id": file_id,
                    "file_name": file_name,
                    "file_title": file_title,
                    "file_md5": file_md5,
                    "file_key": file_key,
                    "file_size": file_size,
                    "duplicated_file_obj": duplicated_file_obj,
                }
            )
    except Exception as ex:
        logs.error(f"train-v2: add_files, except, ex={repr(ex)}")
        logs.error(traceback.format_exc())
        await utils.s3_del([desc["file_key"] for desc in desc_list])
        raise ValueError(f"File upload failed, ex={repr(ex)}")
    file_objs: List[VectorFile] = []
    async with utils.global_lock(utils.LOCK_VECTOR_FILE):
        for desc in desc_list:
            if desc["duplicated_file_obj"]:
                file_obj = desc["duplicated_file_obj"]
                file_obj.upload_status = "duplicated"
            else:
                file_obj = await VectorFile.create(
                    id=desc["file_id"],
                    dataset_id=dataset_id,
                    filename=desc["file_name"],
                    title=desc["file_title"],
                    learn_type=learn_type,
                    file_type=VectorFileType.UPLOAD,
                    file_status=VectorFileStatus.READY,
                    file_size=desc["file_size"],
                    content_hash=desc["file_md5"],
                    metadata={"spa": False, "max_images": 0},
                    key=desc["file_key"],
                )
                file_obj.upload_status = "added"
            file_objs.append(file_obj)
    result_objs = []
    for file_obj in file_objs:
        result_obj = await VectorFileOut.from_tortoise_orm(file_obj)
        result_dict = result_obj.dict()
        result_dict["upload_status"] = file_obj.upload_status
        result_objs.append(result_dict)
    await Dataset.filter(id=dataset_id).update(
        updated_at=datetime.now(), data_status=DATASET_STATUS.READY
    )
    await Robot.update_ai_status_from_dataset(dataset_id, AIStatus.READY)
    return result_objs


async def add_links(
    dataset_id: str,
    links: list[str],
    spa: bool,
    max_images: int,
    datasource_type: DatasourceType,
):
    if len(links) == 0:
        raise ValueError("no link posted")
    for link in links:
        file_id = uuid.uuid4()
        file_type = {
            DatasourceType.WEBSITE: VectorFileType.HTML,
            DatasourceType.SITEMAP: VectorFileType.SITEMAP,
            DatasourceType.GITBOOK: VectorFileType.GITBOOK,
        }.get(datasource_type, VectorFileType.HTML)
        await VectorFile.create(
            id=str(file_id),
            dataset_id=dataset_id,
            filename=link,
            file_type=file_type,
            file_status=VectorFileStatus.READY,
            file_size=0,
            content_hash=utils.link_md5(link),
            metadata={"spa": spa, "max_images": max_images},
            key=None,
        )
    await Dataset.filter(id=dataset_id).update(
        updated_at=datetime.now(), data_status=DATASET_STATUS.READY
    )
    await Robot.update_ai_status_from_dataset(dataset_id, AIStatus.READY)


################################################################


async def vectorfile_delete(vectorfile_id: str):
    vectorfile_before: VectorFile = await VectorFile.get_or_none(id=vectorfile_id)
    vectorfile: VectorFile = await VectorFile.get_or_none(id=vectorfile_id)
    if vectorfile_before is None or vectorfile is None:
        return
    await core.task_clear_by_vectorfile_id(vectorfile_id)
    await vectorfile.soft_delete()

    async def async_delete_resouces_indexes(vectorfile_before: VectorFile):
        try:
            del_keys = []
            if vectorfile.key:
                del_keys.append(vectorfile_before.key)
                key_name, key_ext = os.path.splitext(vectorfile.key)
                if key_ext.lower() != ".pdf":
                    del_keys.append(f"{key_name}.pdf")
            if vectorfile_before.resources:
                del_keys.extend(vectorfile_before.resources)
            if del_keys:
                await utils.s3_del(del_keys)
            await vectorfile_before.delete_search_index()
        except Exception as ex:
            logs.dump_exception(ex)

    asyncio.create_task(async_delete_resouces_indexes(vectorfile_before))


async def vectorfile_check_daemon():
    while True:
        await asyncio.sleep(10.0)
        if settings.NEW_TRAIN_ENABLED:
            logs.info(f"train-v2: vectorfile_check_daemon, start")
            try:
                await core.task_sync()
                async with utils.global_lock(utils.LOCK_VECTOR_FILE):
                    vectorfiles = await VectorFile.filter(
                        file_type__in=[
                            VectorFileType.UPLOAD,
                            VectorFileType.HTML,
                            VectorFileType.HTML_PDF,
                            VectorFileType.SITEMAP,
                            VectorFileType.GITBOOK,
                        ],
                        file_status=VectorFileStatus.PROCESS,
                        deleted_at__isnull=True,
                    )
                    async with utils.global_store() as store:
                        running_vectorfile_ids = {
                            task["vectorfile_id"] for task in store["tasks"].values()
                        }
                    not_running_vectorfiles = [
                        vectorfile
                        for vectorfile in vectorfiles
                        if str(vectorfile.id) not in running_vectorfile_ids
                    ]
                    for vectorfile in not_running_vectorfiles:
                        logs.info(
                            f"train-v2: vectorfile_check_daemon, hanging_vectorfile, vectorfile_id={str(vectorfile.id)}, filename={vectorfile.filename}"
                        )
                        await vectorfile_reset(
                            vectorfile_id=str(vectorfile.id),
                            file_status=VectorFileStatus.FAIL,
                            failed_reason="task not existed",
                        )
                        logs.info(
                            f"train-v2: vectorfile_check_daemon, hanging_vectorfile, vectorfile_id={str(vectorfile.id)}, filename={vectorfile.filename}"
                        )
                logs.info(f"train-v2: vectorfile_check_daemon, end")
            except Exception as ex:
                logs.error(f"train-v2: vectorfile_check_daemon, except, ex={repr(ex)}")
                logs.error(traceback.format_exc())
        await asyncio.sleep(settings.NEW_TRAIN_CHECK_INTERVAL)


async def vectorfile_distribute(parser_id: str) -> list[str]:
    if settings.NEW_TRAIN_DISTRIBUTE == False:
        async with utils.global_lock(utils.LOCK_VECTOR_FILE):
            await VectorFile.filter(
                file_type__in=[
                    VectorFileType.UPLOAD,
                    VectorFileType.HTML,
                    VectorFileType.HTML_PDF,
                    VectorFileType.SITEMAP,
                    VectorFileType.GITBOOK,
                ],
                file_status=VectorFileStatus.READY,
                deleted_at__isnull=True,
            ).update(
                file_status=VectorFileStatus.FAIL, failed_reason=f"task distribute off"
            )
        return []
    async with utils.global_store() as store:
        processing_tasks = [
            task for task in store["tasks"].values() if task["parser_id"] == parser_id
        ]
        if len(processing_tasks) >= settings.NEW_TRAIN_CONCURRENT:
            return []
        dist_count = settings.NEW_TRAIN_CONCURRENT - len(processing_tasks)
    assert dist_count > 0
    async with utils.global_lock(utils.LOCK_VECTOR_FILE):
        dist_vectorfiles = []
        if not hasattr(vectorfile_distribute, "dist_list"):
            vectorfile_distribute.dist_list = []
        dist_map = {dist: None for dist in vectorfile_distribute.dist_list}
        vectorfiles = await VectorFile.raw(
            f"""
            SELECT id, dataset_id, file_status, file_type, updated_at
                FROM vectorfile
                WHERE file_status='ready' AND deleted_at IS NULL
                AND file_type in ('upload','html','html_pdf','sitemap','gitbook') 
                ORDER BY updated_at ASC limit 1000
        """
        )
        for vectorfile in vectorfiles:
            dataset_id = str(vectorfile.dataset_id)
            if dataset_id not in dist_map:
                dist_vectorfiles.append(vectorfile)
                dist_map[dataset_id] = None
            else:
                dataset_vectorfiles = dist_map[dataset_id]
                if dataset_vectorfiles:
                    dataset_vectorfiles.append(vectorfile)
                else:
                    dist_map[dataset_id] = [vectorfile]
            if len(dist_vectorfiles) >= dist_count:
                break
        while len(dist_vectorfiles) < dist_count:
            ori_size = len(dist_vectorfiles)
            for dataset_id, dataset_vectorfiles in dist_map.items():
                if dataset_vectorfiles:
                    dist_vectorfiles.append(dataset_vectorfiles.pop(0))
                    dist_map[dataset_id] = dist_map.pop(dataset_id)
                    break
            if len(dist_vectorfiles) == ori_size:
                break
        vectorfile_distribute.dist_list = [dataset_id for dataset_id in dist_map]
        for vectorfile in dist_vectorfiles:
            vectorfile.file_status = VectorFileStatus.PROCESS
            vectorfile.updated_at = datetime.now()
            await vectorfile.save(update_fields=["file_status", "updated_at"])
        return [str(vectorfile.id) for vectorfile in dist_vectorfiles]


async def vectorfile_complete(task_id: str, vectorfile_id: str):
    try:
        vectorfile = await VectorFile.get_or_none(
            id=vectorfile_id, deleted_at__isnull=True
        )
        logs.asserts(vectorfile)
        vectorfile.file_status = VectorFileStatus.COMPLETE
        vectorfile.failed_reason = None
        await vectorfile.save()

        dataset = await vectorfile.dataset
        if dataset:
            user = await dataset.user
            user_id = user.id

            # 根据 learn_type 和 file_type 确定记录类型
            if 1 < vectorfile.learn_type < 100:
                # 记录为多模态解析
                await QuotaService.consume_quota(
                    user_id=user_id,
                    resource_type=UsageType.MULTI_MODAL_PARSING,
                    amount_to_consume=1,
                )
                logs.info(
                    f"Recorded usage log for multi-modal parsing: user_id={user_id}, vectorfile_id={vectorfile_id}"
                )
            elif vectorfile.file_type in [
                VectorFileType.SITEMAP,
                VectorFileType.GITBOOK,
                VectorFileType.HTML,
            ]:
                # 记录为网页数量
                await QuotaService.consume_quota(
                    user_id=user_id,
                    resource_type=UsageType.WEB_PAGE_LEARNED,
                    amount_to_consume=1,
                )
                logs.info(
                    f"Recorded usage log for web page learned: user_id={user_id}, vectorfile_id={vectorfile_id}"
                )

    except Exception as ex:
        logs.error(
            f"train-v2: vectorfile_complete, except, ex={repr(ex)}", task_id=task_id
        )
        logs.error(traceback.format_exc(), task_id=task_id)


async def vectorfile_reset(
    task_id: str = None,
    vectorfile_id: str = None,
    file_status: VectorFileStatus = None,
    failed_reason: str = None,
    learn_type: int = None,
):
    try:
        if vectorfile_id is None:
            return
        vectorfile_before = await VectorFile.get_or_none(
            id=vectorfile_id, deleted_at__isnull=True
        )
        vectorfile = await VectorFile.get_or_none(
            id=vectorfile_id, deleted_at__isnull=True
        )
        if vectorfile_before is None or vectorfile is None:
            return
        if file_status is not None:
            vectorfile.file_status = file_status
        if failed_reason:
            vectorfile.failed_reason = f"{failed_reason}{('|' + vectorfile.failed_reason) if vectorfile.failed_reason else ''}"
        else:
            vectorfile.failed_reason = ""
        if learn_type is not None:
            vectorfile.learn_type = learn_type
        metadata = {}
        metadata["resetting"] = True
        if vectorfile.metadata and "spa" in vectorfile.metadata:
            metadata["spa"] = vectorfile.metadata["spa"]
        if vectorfile.metadata and "max_images" in vectorfile.metadata:
            metadata["max_images"] = vectorfile.metadata["max_images"]
        vectorfile.metadata = metadata
        vectorfile.resources = []
        vectorfile.index_ids = []
        vectorfile.token_count = 0
        vectorfile.characters_count = 0
        await vectorfile.save()

        async def async_delete_resouces_indexes(
            task_id: str, vectorfile_before: VectorFile
        ):
            try:
                if vectorfile_before.resources:
                    await utils.s3_del(vectorfile_before.resources)
                await vectorfile_before.delete_search_index()
            except Exception as ex:
                logs.error(
                    f"train-v2: async_delete_resouces_indexes, except ex={repr(ex)}",
                    task_id=task_id,
                )
                logs.error(traceback.format_exc(), task_id=task_id)
            vectorfile = await VectorFile.get_or_none(
                id=str(vectorfile_before.id), deleted_at__isnull=True
            )
            if vectorfile is not None and "resetting" in vectorfile.metadata:
                vectorfile.metadata.pop("resetting")
                await vectorfile.save(update_fields=["metadata"])

        asyncio.create_task(async_delete_resouces_indexes(task_id, vectorfile_before))
    except Exception as ex:
        logs.error(
            f"train-v2: vectorfile_reset, except, ex={repr(ex)}", task_id=task_id
        )
        logs.error(traceback.format_exc(), task_id=task_id)


async def vectorfile_preparse(task_id: str, vectorfile_id: str):
    await vectorfile_reset(task_id=task_id, vectorfile_id=vectorfile_id)


async def vectorfile_posparse(task_id: str, vectorfile_id: str, res_filepath: str):
    metadata = None
    with open(res_filepath, "r", encoding="utf-8") as file:
        for item in ijson.items(file, "metadata"):
            metadata = item
    assert metadata is not None

    vectorfile = await VectorFile.get_or_none(id=vectorfile_id, deleted_at__isnull=True)
    logs.asserts(vectorfile, f"vectorfile not existed: vectorfile_id={vectorfile_id}")
    logs.asserts(
        vectorfile.file_status == VectorFileStatus.PROCESS,
        f"vectorfile status invalid: {vectorfile.file_status},{vectorfile.failed_reason}",
    )

    metadata_title = metadata.get("title", None)
    if not (vectorfile.title and vectorfile.title.startswith("user_define_title: ")):
        vectorfile.title = metadata_title
    vectorfile.resources = metadata.setdefault("resources", [])
    vectorfile.updated_at = datetime.now()
    await vectorfile.save(update_fields=["title", "resources", "updated_at"])

    index_ids = []
    chunk_count = 0
    page_size = 0
    total_tokens = 0
    characters_count = 0
    source = (
        f"/tmp/{vectorfile.filename}"
        if vectorfile.file_type == VectorFileType.UPLOAD
        else vectorfile.filename
    )
    dataset_obj = await Dataset.get_or_none(
        id=vectorfile.dataset_id, deleted_at__isnull=True
    )
    dataset_id = str(vectorfile.dataset_id)
    vector_storage_type = VectorStorageType(
        dataset_obj.metadata.get("vector_storage", VectorStorageType.PINECONE)
    )
    logs.asserts(vector_storage_type == VectorStorageType.QDRANT_ONE_COLLECTION)

    async def bulk_embedding(
        bulk_docs: list,
        metadata_title: str,
        source: str,
        vectorfile_id: str,
        chunk_index: int,
        dataset_obj,
        vector_storage_type,
    ):
        from mygpt.endpoints.dataset import init_dataset

        await init_dataset(str(dataset_obj.id), bulk_docs)

        nonlocal task_id
        nonlocal page_size
        logs.info(
            f"train-v2: bulk_embedding, start, {source}, {vectorfile_id}, {chunk_index}"
        )
        characters_count = 0
        total_tokens = 0
        docs = []
        for bulk_doc in bulk_docs:
            index_id = str(uuid.uuid4())
            bulk_doc.page_content = DocumentTranscoder(bulk_doc.page_content).encode()
            bulk_doc.metadata["title"] = metadata_title
            bulk_doc.metadata["source"] = source
            bulk_doc.metadata["doc_id"] = vectorfile_id
            bulk_doc.metadata["file_id"] = vectorfile_id
            bulk_doc.metadata["index_id"] = index_id
            bulk_doc.metadata["chunk_index"] = chunk_index
            bulk_doc.metadata["page_numbers"] = bulk_doc.metadata.get(
                "page_numbers", []
            )
            page_size = max(
                max(bulk_doc.metadata["page_numbers"], default=page_size), page_size
            )
            bulk_doc.metadata = {
                name: value
                for name, value in bulk_doc.metadata.items()
                if name
                in [
                    "title",
                    "source",
                    "doc_id",
                    "file_id",
                    "index_id",
                    "chunk_index",
                    "page_numbers",
                ]
            }
            chunk_index += 1
            characters_count += compute_charactors_count(bulk_doc.page_content)
            total_tokens += num_tokens_from_string(bulk_doc.page_content)
            docs.append(bulk_doc)
            short_docs = spliter_short_text(bulk_doc)
            if len(short_docs) >= 2:
                for short_doc in short_docs:
                    short_doc.page_content = DocumentTranscoder(
                        short_doc.page_content
                    ).encode()
                    short_doc.metadata["title"] = metadata_title
                    short_doc.metadata["source"] = source
                    short_doc.metadata["doc_id"] = vectorfile_id
                    short_doc.metadata["file_id"] = vectorfile_id
                    short_doc.metadata["index_id"] = str(uuid.uuid4())
                    short_doc.metadata["parent_index"] = index_id
                    short_doc.metadata["short_chunk"] = True
                    short_doc.metadata = {
                        name: value
                        for name, value in short_doc.metadata.items()
                        if name
                        in [
                            "title",
                            "source",
                            "doc_id",
                            "file_id",
                            "index_id",
                            "parent_index",
                            "short_chunk",
                        ]
                    }
                    total_tokens += num_tokens_from_string(short_doc.page_content)
                    short_doc.page_content = DocumentTranscoder(
                        short_doc.page_content
                    ).encode()
                    docs.append(short_doc)
        index_ids = [doc.metadata["index_id"] for doc in docs]
        collection_name = dataset_obj.collection_name
        os_client = get_open_search_client()
        if os_client:
            await asyncio.to_thread(
                os_client.add_documents, collection_name, index_ids, docs
            )
        embedding_params = dataset_obj.get_embedding_params()
        embeddings = EmbeddingFactory.get_instance(
            provider=embedding_params.provider,
            async_mode=False,
            model_name=embedding_params.model_name,
            dimensions=embedding_params.dimensions,
        )
        await VectorStorageFactory.save_points(
            embeddings=embeddings,
            doc_ids=index_ids,
            documents=docs,
            group_id=collection_name,
            storage_type=vector_storage_type,
        )
        logs.info(
            f"train-v2: bulk_embedding, end, {source}, {vectorfile_id}, {chunk_index}",
            task_id=task_id,
        )
        import gc

        del docs
        gc.collect()
        return index_ids, characters_count, total_tokens

    with open(res_filepath, "r", encoding="utf-8") as file:
        bulk_docs = []
        for item in ijson.items(file, "split_docs.item"):
            bulk_docs.append(Document(**item))
            if len(bulk_docs) >= settings.NEW_TRAIN_BULK_COUNT:
                bulk_index_ids, bulk_characters_count, bulk_total_tokens = (
                    await bulk_embedding(
                        bulk_docs,
                        metadata_title,
                        source,
                        vectorfile_id,
                        chunk_count,
                        dataset_obj,
                        vector_storage_type,
                    )
                )
                index_ids.extend(bulk_index_ids)
                characters_count += bulk_characters_count
                total_tokens += bulk_total_tokens
                chunk_count += len(bulk_docs)
                bulk_docs = []
        if len(bulk_docs) > 0:
            bulk_index_ids, bulk_characters_count, bulk_total_tokens = (
                await bulk_embedding(
                    bulk_docs,
                    metadata_title,
                    source,
                    vectorfile_id,
                    chunk_count,
                    dataset_obj,
                    vector_storage_type,
                )
            )
            index_ids.extend(bulk_index_ids)
            characters_count += bulk_characters_count
            total_tokens += bulk_total_tokens
            chunk_count += len(bulk_docs)
            bulk_docs = []

    logs.info(f"Success to storage_chunks in file_id:{vectorfile_id}", task_id=task_id)
    # logs.asserts(
    #    await verify_token_dataset(tokens=total_tokens, dataset_id=dataset_id),
    #    'You do not have enough tokens to pay for this activity.'
    # )
    # await sync_user_token(total_tokens, dataset_id)

    vectorfile = await VectorFile.get_or_none(id=vectorfile_id, deleted_at__isnull=True)
    logs.asserts(vectorfile, f"vectorfile not existed: vectorfile_id={vectorfile_id}")
    logs.asserts(
        vectorfile.file_status == VectorFileStatus.PROCESS,
        f"vectorfile status invalid: {vectorfile.file_status},{vectorfile.failed_reason}",
    )
    vectorfile.metadata["chunk_count"] = chunk_count
    vectorfile.metadata["page_size"] = page_size
    vectorfile.metadata = {
        name: value
        for name, value in vectorfile.metadata.items()
        if name
        in [
            "spa",
            "max_images",
            "resetting",
            "chunk_count",
            "page_size",
            "advanced_parser",
        ]
    }
    update_data = {
        "index_ids": index_ids,
        "token_count": total_tokens,
        "characters_count": characters_count,
        "metadata": vectorfile.metadata,
    }
    await vectorfile.update_from_dict(update_data).save()
