import random
import string
from uuid import uuid4

from fastapi_auth0 import Auth0
from loguru import logger

from mygpt.auth0.local import Auth0Local
from mygpt.models import User
from mygpt.settings import AUTH0_API_AUDIENCE, AUTH0_DOMAIN, AUTHORIZATION_TYPE
from mygpt.utils import colored_info_green, colored_info_yellow

logger.info("setting up authorization...")
# auth = Auth0Local(domain=AUTH0_DOMAIN,
#                       api_audience=AUTH0_API_AUDIENCE, scopes={})
# logger.info(colored_info_yellow("use local jwt"))
if AUTHORIZATION_TYPE == "jwt":
    auth = Auth0Local(domain=AUTH0_DOMAIN, api_audience=AUTH0_API_AUDIENCE, scopes={})
    logger.info(colored_info_yellow("use local jwt"))
else:
    auth = Auth0(domain=AUTH0_DOMAIN, api_audience=AUTH0_API_AUDIENCE, scopes={})
    logger.info(colored_info_yellow("use remote auth0"))

logger.info("setting up authorization...done")


async def init_users(emails: list[str], passwords: list[str] | None = None):
    logger.info(colored_info_yellow("init users..."))

    if passwords and len(emails) != len(passwords):
        raise ValueError("The number of emails and passwords must match")

    for i, email in enumerate(emails):
        password = passwords[i] if passwords else None
        email_striped = email.strip()
        password_striped = password.strip()
        logger.info(
            f"init_users {email=}, {password=}; {email_striped=}, {password_striped=}"
        )
        await init_single_user(email_striped, password_striped)


async def init_single_user(email: str, password: str | None = None):
    users = await User.filter(email=email, deleted_at__isnull=True)
    user = users[0] if users else None
    if not user:
        logger.info(colored_info_yellow(f"create user {email}..."))
        if not password:
            # Random password
            password = "".join(random.sample(string.ascii_letters + string.digits, 8))
        salt = User.generate_salt()
        hash_password = User.hash_password(password, salt)
        user = await User.create(
            email=email,
            name=email,
            user_id=f"gptbase|{uuid4()}",
            salt=salt,
            password=hash_password,
        )
        logger.info(colored_info_yellow(f"create user {email}...done"))
        # Output created account and password
        logger.info(colored_info_green(f"created user: {email}, password: {password}"))
    elif password:
        salt = user.salt
        hash_password = User.hash_password(password, salt)
        if hash_password != user.password:
            user.password = hash_password
            await user.save(update_fields=["password"])
            logger.info(colored_info_yellow(f'update user "{email}" password'))
    else:
        logger.info(colored_info_yellow(f'user "{email}" already exists'))
    return user
