import asyncio

from mygpt.models import Auth0Key

lock = asyncio.Lock()


async def get_or_create_auth_key():
    async with lock:
        auth0_key = await Auth0Key.get_or_none()
        if auth0_key is None:
            private_key, public_key = Auth0Key.create_pair()

            await Auth0Key.create(private_key=private_key, public_key=public_key)
        else:
            private_key = auth0_key.private_key
            public_key = auth0_key.public_key
        return private_key, public_key
