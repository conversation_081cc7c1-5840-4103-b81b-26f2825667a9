import base64
import logging
import urllib.parse

from pydantic import ValidationError
from typing import Dict, Optional, Type
from fastapi import Depends, HTTPException
from fastapi_auth0 import Auth0
from fastapi_auth0.auth import (
    Auth0<PERSON><PERSON>,
    Auth0<PERSON><PERSON><PERSON>earer,
    JwksDict,
    OAuth2ImplicitBearer,
    Auth0UnauthenticatedException,
    Auth0UnauthorizedException,
    Auth0User,
)
from fastapi.security import (
    HTTPAuthorizationCredentials,
    OAuth2PasswordBearer,
    OAuth2AuthorizationCodeBearer,
    OpenIdConnect,
    SecurityScopes,
)
from jose import jwt
from mygpt.auth0.key import get_or_create_auth_key  # type: ignore

from mygpt.settings import JWT_HEADER_KID

logger = logging.getLogger("Auth0Local")


class Auth0Local(Auth0):
    def __init__(
        self,
        domain: str,
        api_audience: str,
        scopes: Dict[str, str] = {},
        auto_error: bool = True,
        scope_auto_error: bool = True,
        email_auto_error: bool = False,
        auth0user_model: Type[Auth0User] = Auth0User,
    ):
        self.domain = domain
        self.audience = api_audience

        self.auto_error = auto_error
        self.scope_auto_error = scope_auto_error
        self.email_auto_error = email_auto_error

        self.auth0_user_model = auth0user_model

        self.algorithms = ["RS256"]
        self.jwks: JwksDict = {}

        authorization_url_qs = urllib.parse.urlencode({"audience": api_audience})
        authorization_url = f"https://{domain}/authorize?{authorization_url_qs}"
        self.implicit_scheme = OAuth2ImplicitBearer(
            authorizationUrl=authorization_url,
            scopes=scopes,
            scheme_name="Auth0ImplicitBearer",
        )
        self.password_scheme = OAuth2PasswordBearer(
            tokenUrl=f"https://{domain}/oauth/token", scopes=scopes
        )
        self.authcode_scheme = OAuth2AuthorizationCodeBearer(
            authorizationUrl=authorization_url,
            tokenUrl=f"https://{domain}/oauth/token",
            scopes=scopes,
        )
        self.oidc_scheme = OpenIdConnect(
            openIdConnectUrl=f"https://{domain}/.well-known/openid-configuration"
        )

    async def get_user(
        self,
        security_scopes: SecurityScopes,
        creds: Optional[HTTPAuthorizationCredentials] = Depends(
            Auth0HTTPBearer(auto_error=False)
        ),
    ) -> Optional[Auth0User]:
        if creds is None:
            if self.auto_error:
                # See HTTPBearer from FastAPI:
                # latest - https://github.com/tiangolo/fastapi/blob/master/fastapi/security/http.py
                # 0.65.1 - https://github.com/tiangolo/fastapi/blob/aece74982d7c9c1acac98e2c872c4cb885677fc7/fastapi/security/http.py
                # must be 403 until solving https://github.com/tiangolo/fastapi/pull/2120
                raise HTTPException(403, detail="Missing bearer token")
            else:
                return None

        token = creds.credentials
        payload: Dict = {}
        try:
            _, public_key = await get_or_create_auth_key()
            unverified_header = jwt.get_unverified_header(token)
            public_pem = base64.b64decode(public_key)
            if unverified_header["kid"] == JWT_HEADER_KID:
                payload = jwt.decode(
                    token,
                    public_pem,
                    algorithms=unverified_header["alg"],
                    audience=self.audience,
                    issuer=f"https://{self.domain}/",
                )
            else:
                msg = "Invalid kid header (wrong tenant or rotated public key)"
                if self.auto_error:
                    raise Auth0UnauthenticatedException(detail=msg)
                else:
                    logger.warning(msg)
                    return None

        except jwt.ExpiredSignatureError:
            msg = "Expired token"
            if self.auto_error:
                raise Auth0UnauthenticatedException(detail=msg)
            else:
                logger.warning(msg)
                return None

        except jwt.JWTClaimsError:
            msg = "Invalid token claims (wrong issuer or audience)"
            if self.auto_error:
                raise Auth0UnauthenticatedException(detail=msg)
            else:
                logger.warning(msg)
                return None

        except jwt.JWTError:
            msg = "Malformed token"
            if self.auto_error:
                raise Auth0UnauthenticatedException(detail=msg)
            else:
                logger.warning(msg)
                return None

        except Auth0UnauthenticatedException:
            raise

        except Exception as e:
            # This is an unlikely case but handle it just to be safe (maybe the token is specially crafted to bug our code)
            logger.error(f'Handled exception decoding token: "{e}"', exc_info=True)
            if self.auto_error:
                raise Auth0UnauthenticatedException(detail="Error decoding token")
            else:
                return None

        if self.scope_auto_error:
            token_scope_str: str = payload.get("scope", "")

            if isinstance(token_scope_str, str):
                token_scopes = token_scope_str.split()

                for scope in security_scopes.scopes:
                    if scope not in token_scopes:
                        raise Auth0UnauthorizedException(
                            detail=f'Missing "{scope}" scope',
                            headers={
                                "WWW-Authenticate": f'Bearer scope="{security_scopes.scope_str}"'
                            },
                        )
            else:
                # This is an unlikely case but handle it just to be safe (perhaps auth0 will change the scope format)
                raise Auth0UnauthorizedException(
                    detail='Token "scope" field must be a string'
                )

        try:
            user = self.auth0_user_model(**payload)

            if self.email_auto_error and not user.email:
                raise Auth0UnauthorizedException(
                    detail=f'Missing email claim (check auth0 rule "Add email to access token")'
                )

            return user

        except ValidationError as e:
            logger.error(f'Handled exception parsing Auth0User: "{e}"', exc_info=True)
            if self.auto_error:
                raise Auth0UnauthorizedException(detail="Error parsing Auth0User")
            else:
                return None
