from typing import Optional
from pydantic import BaseModel


class FaqRecall(BaseModel):
    faqs_id: str
    score: float
    title: Optional[str]


# 速度测试数据
speed_test_data = {
    "bcddcb88-f5cd-45b0-add8-96672d5235df": [
        "His Mobileの440パッケージの5GBのデータはいくらですか？",
        "HISモバイルのデータ定額440プラン（月額880円）に含まれる月間データ量は？",
        "定額440の回線種別はなんですか？",
        "自由自在290プランの290円の基本データ量はどれくらいですか？",
        "自由自在290プランの月額基本料金は？",
        "自由自在290プランの追加データ量は1Gあたりいくらですか？",
        "HISモバイルには実店舗がありますか？",
        "SIMカードが使えない場合はどうすればいいですか？",
        "お申込みから開通までどれぐらいかかる？",
        "月50Gデータの固定440プランはいくらですか？",
        "MNP予約番号の有効期限がHISモバイルに申し込む時点で7日未満になってしまった（もしくは有効期限が切れてしまった）場合",
        "データ通信のみで、毎月100MB未満を使用する場合、フリーフリースーパープランの月額基本料金はいくらになりますか？",
        "格安SIMのプランを教えて",
    ]
}
