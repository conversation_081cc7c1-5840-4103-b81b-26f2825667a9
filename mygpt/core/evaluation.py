import uuid
from llama_index.core.llms.utils import LLM


import time
from typing import Optional
from langchain_core.documents import Document

import pandas as pd
from llama_index.core.evaluation import (
    EmbeddingQAFinetuneDataset,
    RetrieverEvaluator,
    get_retrieval_results_df,
    generate_question_context_pairs,
)
from llama_index.core.schema import TextNode
from qdrant_client.conversions.conversion import GrpcToRest
from mygpt.core.embedding import EmbeddingFactory
from mygpt.core.retriever import Retriever

from mygpt.core.vector_storage import VectorStorage, VectorStorageFactory
from mygpt.enums import LLM_PROVIDER
from llama_index.core.base.base_retriever import BaseRetriever

QA_GENERATE_PROMPT_TMPL = """\
Context information is below.

---------------------
{context_str}
---------------------

Given the context information and not prior knowledge. \
generate only questions based on the below query. 

You are a Professor. Your task is to setup {num_questions_per_chunk} questions \
for an upcoming quiz/examination. The questions should be \
diverse in nature across the document. \
The questions should not contain options, \
not start with Q1/ Q2. Restrict the questions \
to the context information provided.\
The questions language should base on the context information provided.

Question:\
"""


def filter_qa_dataset(
    qa_dataset,
):
    """
    Filters out queries from the qa_dataset that contain certain phrases and the corresponding
    entries in the relevant_docs, and creates a new EmbeddingQAFinetuneDataset object with
    the filtered data.

    :param qa_dataset: An object that has 'queries', 'corpus', and 'relevant_docs' attributes.
    :return: An EmbeddingQAFinetuneDataset object with the filtered queries, corpus and relevant_docs.
    """

    def check_for_phrases_in_dict_values(query_string: str):
        phrase_arrs = ["Here are 2", "Here are two"]
        return any(phrase in query_string for phrase in phrase_arrs)

    # Extract keys from queries and relevant_docs that need to be removed
    queries_relevant_docs_keys_to_remove = {
        k for k, v in qa_dataset.queries.items() if check_for_phrases_in_dict_values(v)
    }

    # Filter queries and relevant_docs using dictionary comprehensions
    filtered_queries = {
        k: v
        for k, v in qa_dataset.queries.items()
        if k not in queries_relevant_docs_keys_to_remove
    }
    filtered_relevant_docs = {
        k: v
        for k, v in qa_dataset.relevant_docs.items()
        if k not in queries_relevant_docs_keys_to_remove
    }

    # Create a new instance of EmbeddingQAFinetuneDataset with the filtered data
    return EmbeddingQAFinetuneDataset(
        queries=filtered_queries,
        corpus=qa_dataset.corpus,
        relevant_docs=filtered_relevant_docs,
    )


async def generate_dataset(
    dataset_id: str,  # 数据集ID
    collection_name: str = "gptbase_col",  # 集合名称，默认为"gptbase_col"
    # data: Optional[list] = None,  # 可选的数据列表，默认为None
    # save_path: str = './data',  # 保存路径，默认为'./data'
    offset: Optional[str] = None,  # 可选的偏移量，默认为None
    limit: int = 1000,  # 限制获取的数据量，默认为100
):
    """
    异步函数，用于生成数据集的CSV文件
    """
    # 获取VectorStorageFactory的实例
    storage = VectorStorageFactory.get_instance()

    # 定义过滤器
    filters = {"must": [{"key": "group_id", "match": {"any": [dataset_id]}}]}

    # 从存储中索引点
    result = await storage.index_points(
        collection_name=collection_name,
        offset=offset,
        limit=limit,
        filters=filters,
        with_vectors=False,
    )

    # 将结果的下一页偏移量转换为字符串
    try:
        next_page_offset = (
            GrpcToRest.convert_point_id(result.next_page_offset)
            if result.next_page_offset
            else None
        )
    except Exception as e:
        next_page_offset = None

    # 如果结果的下一页偏移量等于当前偏移量
    if not next_page_offset or next_page_offset == offset:
        return []

    # 将结果中的点转换为记录
    points = [GrpcToRest.convert_record(point) for point in result.result]

    # 将点的数据添加到数据列表中
    d = [
        {
            "id": doc.id,
            "content": doc.payload["page_content"],
            "doc_id": doc.payload["metadata"]["doc_id"],
            "chunk_index": doc.payload["metadata"]["chunk_index"],
            "source": doc.payload["metadata"]["source"],
            "file_id": doc.payload["metadata"]["file_id"],
            "metadata": doc.payload["metadata"],
        }
        for doc in points
    ]

    # 递归调用自身，以获取下一页的数据
    sub_data = await generate_dataset(
        dataset_id,
        collection_name=collection_name,
        offset=next_page_offset,
        limit=limit,
    )
    return d + sub_data


class Evaluation:

    def __init__(self, dataset_id: str):
        self.dataset_id = dataset_id

    async def generate_qa_pairs(
        self,
        llm: LLM,
        pair_num: int = 100,
        csv_path: Optional[str] = None,
    ):
        """
        生成问答对
        """
        if not csv_path:
            csv_path = f"./data/{self.dataset_id}.csv"
        # 判断是否存在CSV文件
        try:
            data = pd.read_csv(csv_path)
        except FileNotFoundError:
            # 生成数据集
            print("Generating dataset...")
            rs = await generate_dataset(self.dataset_id)
            data = pd.DataFrame(rs)
            data.to_csv(csv_path, index=False)
        dataset_save_path = f"./data/{self.dataset_id}_qa_pairs.json"
        try:
            old_qa_dataset = EmbeddingQAFinetuneDataset.from_json(dataset_save_path)
        except FileNotFoundError:
            old_qa_dataset = EmbeddingQAFinetuneDataset(
                queries={}, corpus={}, relevant_docs={}
            )

        try:
            qa_dataset = EmbeddingQAFinetuneDataset(
                queries={}, corpus={}, relevant_docs={}
            )
            for idx, item in data.iterrows():
                if idx >= pair_num:
                    break
                print(f"starting with idx:{idx}")
                if old_qa_dataset.corpus.get(item["id"]):
                    continue
                # 生成问题-上下文对
                nodes = [
                    TextNode(
                        id_=item["id"],
                        text=item["content"],
                    )
                ]

                tmp = generate_question_context_pairs(
                    nodes,
                    llm=llm,
                    qa_generate_prompt_tmpl=QA_GENERATE_PROMPT_TMPL,
                    num_questions_per_chunk=2,
                )
                qa_dataset = EmbeddingQAFinetuneDataset(
                    queries={**qa_dataset.queries, **tmp.queries},
                    corpus={**qa_dataset.corpus, **tmp.corpus},
                    relevant_docs={**qa_dataset.relevant_docs, **tmp.relevant_docs},
                )
            qa_dataset = filter_qa_dataset(qa_dataset)

        finally:
            # 合并新旧问答对
            qa_dataset = EmbeddingQAFinetuneDataset(
                queries={**old_qa_dataset.queries, **qa_dataset.queries},
                corpus={**old_qa_dataset.corpus, **qa_dataset.corpus},
                relevant_docs={
                    **old_qa_dataset.relevant_docs,
                    **qa_dataset.relevant_docs,
                },
            )
            qa_dataset.save_json(dataset_save_path)

    async def save_database(
        self,
        dataset_path: str,
        storage: VectorStorage,
        model_name: Optional[str] = None,
        collection_name: str = "gptbase_col",
    ):
        qa_dataset = EmbeddingQAFinetuneDataset.from_json(dataset_path)
        docs, doc_ids = [], []
        for k, v in qa_dataset.corpus.items():
            docs.append(
                Document(
                    page_content=v,
                    metadata={},
                )
            )
            doc_ids.append(k)

        embeddings = EmbeddingFactory.get_instance(
            provider=LLM_PROVIDER.OPENAI,
            model_name=model_name,
        )
        storage.from_documents(
            embeddings=embeddings,
            collection_name=collection_name,
            group_id=self.dataset_id,
            documents=docs,
            ids=doc_ids,
        )

    async def save_csv_database(
        self,
        csv_path: str,
        storage: VectorStorage,
    ):
        data = pd.read_csv(csv_path)
        docs, doc_ids = [], []
        for idx, item in data.iterrows():
            docs.append(
                Document(
                    page_content=item["content"],
                    metadata={},
                )
            )
            doc_ids.append(item["id"])

        embeddings = EmbeddingFactory.get_instance(
            provider=LLM_PROVIDER.OPENAI, model_name="text-embedding-3-large"
        )
        storage.from_documents(
            embeddings=embeddings,
            collection_name="gptbase_col",
            group_id=self.dataset_id,
            documents=docs,
            ids=doc_ids,
        )

    async def aevaluate_dataset(
        self,
        dataset_path: str,
        retriever: BaseRetriever,
        save_path: Optional[str] = None,
    ):
        retriever_evaluator = RetrieverEvaluator.from_metric_names(
            ["mrr", "hit_rate"],
            retriever=retriever,
        )
        qa_dataset = EmbeddingQAFinetuneDataset.from_json(dataset_path)
        eval_results = []
        idx = 20
        while idx > 0:
            try:
                item_idx = 0
                for query_id, query in qa_dataset.queries.items():
                    item_idx += 1
                    if item_idx <= len(eval_results):
                        continue
                    expected_ids = qa_dataset.relevant_docs[query_id]
                    # expected_ids=['108fa63692cf471aafad9858d3af0a00']
                    # 将hex格式的id转换为字符串 expected_ids=['108fa636-92cf-471a-afad-9858d3af0a00']
                    expected_ids = [
                        uuid.UUID(hex=id).urn[9:] if len(id) == 32 else id
                        for id in expected_ids
                    ]
                    eval_result = await retriever_evaluator.aevaluate(
                        query,
                        expected_ids=expected_ids,
                    )
                    eval_results.append(eval_result)
                    print(f"query:{query} done. total finished {len(eval_results)}")
                break
            except Exception as e:
                idx -= 1
                print(
                    f"query:{query} Error: {e} retrying in 1 second,remaining {idx} times"
                )
                time.sleep(1)
        data = get_retrieval_results_df(
            names=["retriever"],
            results_arr=[eval_results],
            metric_keys=["mrr", "hit_rate"],
        )
        if not save_path:
            save_path = f"./data/{self.dataset_id}_retrieval_results.csv"
        data.to_csv(save_path, index=False)

    async def retriever_speed_check(
        self,
        queries: list[str],
        retriever: Retriever,
        collection_name: str = "gptbase_col",
        model_name: Optional[str] = None,
        limit: int = 10,
    ):
        retriever_result = []
        start_time = time.time()
        embedding_time = 0
        for query in queries:
            ed_start_time = time.time()
            embeddings = await EmbeddingFactory.concurrent_embedding(
                [query], model_name
            )
            ed_end_time = time.time()
            embedding_time += ed_end_time - ed_start_time
            result = await retriever.retrieve(
                [self.dataset_id],
                query,
                collection_name=collection_name,
                model_name=model_name,
                embeddings=embeddings,
                limit=limit,
            )
            retriever_result.append(result)
        end_time = time.time()
        total_time = end_time - start_time
        average_time = total_time / len(queries)  # seconds
        embedding_average_time = embedding_time / len(queries)
        print(
            f"average_time:{average_time} seconds | embedding_average_time:{embedding_average_time} seconds | query_num:{len(queries)}"
        )
        return retriever_result, average_time, embedding_average_time
