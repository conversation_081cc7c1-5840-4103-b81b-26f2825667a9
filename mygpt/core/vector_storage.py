import asyncio
import functools
import time
import traceback
import uuid
from abc import ABC, abstractmethod
from typing import Any, Callable, Iterable, List, Optional, Sequence

import grpc as grpc1
import qdrant_client
from langchain.schema import Document
from langchain_community.embeddings import OpenAIEmbeddings
from langchain_community.vectorstores import Qdrant
from loguru import logger as logging
from qdrant_client import QdrantClient, grpc, AsyncQdrantClient
from qdrant_client.conversions.conversion import GrpcToRest, RestToGrpc
from qdrant_client.http import models as http_models
from qdrant_client.http.models import PointRequest
from skywalking.decorators import trace

from mygpt import settings
from mygpt.core.utils import get_collection_name, handle_decode
from mygpt.enums import OpenAIModel, VectorStorageType
from mygpt.schemata import Embeddings
from mygpt.settings import (
    VECTOR_STORAGE_QDRANT_GRPC_PORT,
    VECTOR_STORAGE_QDRANT_HOST,
    LOCAL_EMBEDDING_PROXY,
    LOCAL_EMBEDDING_MODEL_NAME,
)
from mygpt.utils import async_timing_decorator


class CustomeQdrant(Qdrant):
    """
    重写Qdrant类，用于支持多个数据集
    注意只覆盖了同步函数的add_texts方法，异步函数的add_texts方法没有覆盖
    实际会通过langchain的from_documents方法调用
    """

    CONTENT_KEY = "page_content"
    METADATA_KEY = "metadata"
    VECTOR_NAME = None

    def __init__(
        self,
        client: Any,
        collection_name: str,
        embeddings: Optional[Embeddings] = None,
        content_payload_key: str = CONTENT_KEY,
        metadata_payload_key: str = METADATA_KEY,
        distance_strategy: str = "COSINE",
        vector_name: Optional[str] = VECTOR_NAME,
        embedding_function: Optional[Callable] = None,  # deprecated
        group_id: Optional[str] = None,
        **kwargs: Any,
    ):
        super().__init__(
            client=client,
            collection_name=collection_name,
            embeddings=embeddings,
            content_payload_key=content_payload_key,
            metadata_payload_key=metadata_payload_key,
            distance_strategy=distance_strategy,
            vector_name=vector_name,
            embedding_function=embedding_function,
            **kwargs,
        )
        self.group_id = group_id

    # def _generate_rest_batches(
    #     self,
    #     texts: Iterable[str],
    #     metadatas: Optional[List[dict]] = None,
    #     ids: Optional[Sequence[str]] = None,
    #     batch_size: int = 64,
    # ) -> Generator[Tuple[List[str], List[rest.PointStruct]], None, None]:
    #     from qdrant_client.http import models as rest
    #
    #     texts_iterator = iter(texts)
    #     metadatas_iterator = iter(metadatas or [])
    #     ids_iterator = iter(ids or [uuid.uuid4().hex for _ in iter(texts)])
    #     while batch_texts := list(islice(texts_iterator, batch_size)):
    #         # Take the corresponding metadata and id for each text in a batch
    #         batch_metadatas = list(islice(metadatas_iterator, batch_size)) or None
    #         batch_ids = list(islice(ids_iterator, batch_size))
    #
    #         # Generate the embeddings for all the texts in a batch
    #         batch_embeddings = self._embed_texts(batch_texts)
    #         try:
    #             points = [
    #                 rest.PointStruct(
    #                     id=point_id,
    #                     vector=vector
    #                     if self.vector_name is None
    #                     else {self.vector_name: vector},
    #                     payload=payload,
    #                 )
    #                 for point_id, vector, payload in zip(
    #                     batch_ids,
    #                     batch_embeddings,
    #                     self._build_payloads(
    #                         batch_texts,
    #                         batch_metadatas,
    #                         self.content_payload_key,
    #                         self.metadata_payload_key,
    #                     ),
    #                 )
    #             ]
    #         except Exception as e:
    #             # logging.error(
    #             #     f"Error in _generate_rest_batches: {e}, batch_ids: {batch_ids}, batch_embeddings: {batch_embeddings}, batch_texts: {batch_texts}, batch_metadatas: {batch_metadatas}"
    #             # )
    #             logging.error(f"{traceback.format_exc()}")
    #             raise
    #
    #         yield batch_ids, points

    def add_texts(
        self,
        texts: Iterable[str],
        metadatas: Optional[List[dict]] = None,
        ids: Optional[Sequence[str]] = None,
        batch_size: int = 64,
        **kwargs: Any,
    ) -> List[str]:
        """Run more texts through the embeddings and add to the vectorstore.

        Args:
            texts: Iterable of strings to add to the vectorstore.
            metadatas: Optional list of metadatas associated with the texts.
            ids:
                Optional list of ids to associate with the texts. Ids have to be
                uuid-like strings.
            batch_size:
                How many vectors upload per-request.
                Default: 64

        Returns:
            List of ids from adding the texts into the vectorstore.
        """
        added_ids = []
        rest_batches = self._generate_rest_batches(texts, metadatas, ids, batch_size)
        for batch_ids, points in rest_batches:
            # 对每个point添加group_id，用于区分不同的数据集
            if self.group_id:
                for point in points:
                    if settings.IS_USE_LOCAL_VLLM:
                        if not isinstance(point.vector, list):
                            logging.error(f"point.vector is not a list, point: {point}")
                            raise ValueError(
                                f"Embedding service error: point.vector is not a list, {point.vector}"
                            )
                        # else:
                        # logging.info(
                        #     f"point.vector is a list, len: {len(point.vector)}"
                        # )
                    point.payload["group_id"] = self.group_id

            # 执行插入
            self.client.upsert(
                collection_name=self.collection_name, points=points, **kwargs
            )
            added_ids.extend(batch_ids)

        return added_ids


def retry(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        max_retries = kwargs.pop("max_retries", 3)
        delay = kwargs.pop("delay", 0.1)
        final_exception = None
        while max_retries > 0:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                max_retries -= 1
                logging.warning(
                    f"Error: {e}, retrying func {func.__name__} remaining: {max_retries} times"
                )
                logging.error(f"{traceback.format_exc()}")
                final_exception = e
                time.sleep(delay)
        raise final_exception

    return wrapper


def retry_async(func):
    """
    重试装饰器,异步函数
    重试逻辑,在发生异常时切换模型提供商并对func进行重试
    """

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        max_retries = kwargs.pop("max_retries", 3)
        delay = kwargs.pop("delay", 0.1)
        final_exception = None
        while max_retries > 0:
            try:
                return await func(*args, **kwargs)
            except grpc1.RpcError as e:
                if e.code() == grpc1.StatusCode.NOT_FOUND:
                    # 如果集合不存在,直接返回空列表
                    raise e
                final_exception = e
                max_retries -= 1
                logging.warning(
                    f"Error: {e.debug_error_string()}, retrying func {func.__name__} remaining: {max_retries} times"
                )
                await asyncio.sleep(delay)
        raise final_exception

    return wrapper


def process_point(point):
    if not point:
        return None
    elif isinstance(point, grpc.ScoredPoint):
        index = GrpcToRest.convert_scored_point(point)
    else:
        index = GrpcToRest.convert_record(point)
    text, metadata = handle_decode(
        index.payload["page_content"], index.payload["metadata"]
    )
    dataset_id = metadata.get("dataset_id") or index.payload["group_id"]
    return Embeddings(
        id=index.id,
        text=text,
        metadata=metadata,
        score=index.score if hasattr(index, "score") else None,
        vector=index.vector,
        storage_type=VectorStorageType.QDRANT,
        dataset_id=dataset_id,
    )


class VectorStorage(ABC):
    def __init__(self):
        self.storage_type = None

    @abstractmethod
    async def insert_points(
        self,
        collection_name: str,
        points: list,
    ):
        pass

    @abstractmethod
    async def collection_exists(self, collection_name: str) -> bool:
        pass

    @abstractmethod
    async def create_index(
        self,
        collection_name: str,
        **kwargs: Any,
    ):
        pass

    @abstractmethod
    async def create_collection(
        self,
        collection_name: str,
        **kwargs: Any,
    ) -> "VectorStorage":
        pass

    @abstractmethod
    def from_documents(
        documents,
        embeddings,
        collection_name,
        **kwargs: Any,
    ):
        pass

    # 删除集合
    @abstractmethod
    async def delete_collection(self, collection_name: str):
        pass

    @abstractmethod
    async def index_points(
        self,
        collection_name: str,
        offset: None,
        filters: dict = None,
        limit: int = 100,
        with_payload=True,
        with_vectors=True,
    ):
        pass

    @abstractmethod
    async def index_fetch(
        self, ids: list[str], collection_name: str, with_vector: bool = True
    ) -> list[Embeddings]:
        pass

    @abstractmethod
    async def index_query(
        self,
        vectors: list[float],
        collection_name: str,
        limit: int = 10,
        filters: http_models.Filter = None,
        score_threshold: float = None,
    ) -> grpc.ScoredPoint:
        pass

    @abstractmethod
    async def index_delete(self, ids: list[str], collection_name: str):
        pass

    @abstractmethod
    async def index_count(
        self, collection_name: str, filters: dict = {}, adapt_old_ds=False
    ) -> int:
        pass

    @abstractmethod
    async def set_payload(
        self,
        payload: dict,
        collection_name: str,
        id: Optional[str] = None,
        filters: Optional[dict] = None,
        wait=False,
    ):
        pass

    @abstractmethod
    async def delete_by_filter(
        self,
        collection_name: str,
        filters: dict,
    ):
        pass


async_qdrant_client = qdrant_client.AsyncQdrantClient(
    host=settings.VECTOR_STORAGE_QDRANT_HOST,
    port=settings.VECTOR_STORAGE_QDRANT_HTTP_PORT,
    grpc_port=settings.VECTOR_STORAGE_QDRANT_GRPC_PORT,
    timeout=60,
)


class QdrantStorage(VectorStorage):
    def __init__(
        self,
        host: str,
        storage_type: VectorStorageType,
        grpc_port: int = 6334,
        **kwargs: Any,
    ):
        super().__init__()
        self.timeout = 150
        self.host = host
        self.grpc_port = grpc_port
        self.storage_type = storage_type
        self.collection_name = kwargs.pop("collection_name", None)
        self.client = QdrantClient(
            host=self.host,
            grpc_port=grpc_port,
            prefer_grpc=False if settings.QDRANT_HTTP else True,
            timeout=self.timeout,
            **kwargs,
        )
        self.async_client = AsyncQdrantClient(
            host=self.host,
            grpc_port=grpc_port,
            prefer_grpc=False if settings.QDRANT_HTTP else True,
            timeout=self.timeout,
            **kwargs,
        )

    async def insert_points(
        self,
        collection_name: str,
        points: list,
    ):
        col_name = collection_name
        grpc_points = self.client.async_grpc_points
        response = await grpc_points.Upsert(
            grpc.UpsertPoints(
                collection_name=col_name,
                points=[RestToGrpc.convert_point_struct(point) for point in points],
            ),
            timeout=self.timeout,
        )
        return GrpcToRest.convert_update_result(response.result)

    async def collection_exists(self, collection_name: str) -> bool:
        if settings.QDRANT_HTTP:
            exists = self.client.collection_exists(collection_name)
            return exists
        grpc_points = self.client.async_grpc_collections
        try:
            await grpc_points.Get(
                grpc.GetCollectionInfoRequest(
                    collection_name=collection_name,
                ),
                timeout=self.timeout,
            )
        except grpc1.RpcError as e:
            if e.code() == grpc1.StatusCode.NOT_FOUND:
                return False
            raise e
        return True

    async def create_index(
        self,
        collection_name: str,
        **kwargs: Any,
    ):
        grpc_points = self.client.async_grpc_points
        await grpc_points.CreateFieldIndex(
            grpc.CreateFieldIndexCollection(
                collection_name=collection_name,
                **kwargs,
            ),
            timeout=self.timeout,
        )

    async def create_collection(
        self,
        collection_name: str,
        **kwargs: Any,
    ):
        # if collection not found, use from documents.for recreate_collection
        hnsw_config = http_models.HnswConfigDiff(
            on_disk=True,
            payload_m=16,
            m=0,
        )
        optimizer_config = http_models.OptimizersConfigDiff(
            memmap_threshold=10000,
        )
        grpc_points = self.client.async_grpc_collections
        # create collection
        await grpc_points.Create(
            grpc.CreateCollection(
                collection_name=collection_name,
                replication_factor=2,
                hnsw_config=RestToGrpc.convert_hnsw_config(hnsw_config),
                optimizers_config=RestToGrpc.convert_optimizers_config(
                    optimizer_config
                ),
                vectors_config=RestToGrpc.convert_vectors_config(
                    http_models.VectorParams(
                        size=kwargs["vector_size"],
                        distance=http_models.Distance.COSINE,
                        on_disk=True,
                    )
                ),
            ),
            timeout=self.timeout,
        )

    @retry
    def from_documents(
        self,
        documents,
        embeddings,
        collection_name,
        **kwargs: Any,
    ):
        qdrant_client = CustomeQdrant(
            client=self.client,
            collection_name=collection_name,
            embeddings=embeddings,
            group_id=kwargs.get("group_id"),
        )
        if "group_id" in kwargs:
            del kwargs["group_id"]
        s_time = time.time()
        # use same collection and add documents
        res = qdrant_client.add_documents(
            documents,
            **kwargs,
        )
        logging.info(f"qdrant_client.add_documents: {time.time() - s_time}")
        return res

    @trace()
    async def index_fetch(
        self, ids: list[str], collection_name: str, with_vector: bool = True
    ) -> list[Embeddings]:
        if settings.QDRANT_HTTP:
            res = await async_qdrant_client.rest.points_api.get_points(
                collection_name=collection_name,
                point_request=PointRequest(
                    ids=ids, with_vector=with_vector, with_payload=True
                ),
            )
            return [
                process_point(RestToGrpc.convert_retrieved_point(i)) for i in res.result
            ]
        grpc_points = self.client.async_grpc_points
        response = await grpc_points.Get(
            grpc.GetPoints(
                collection_name=collection_name,
                ids=[RestToGrpc.convert_extended_point_id(id) for id in ids],
                with_payload=grpc.WithPayloadSelector(enable=True),
                with_vectors=grpc.WithVectorsSelector(enable=with_vector),
            ),
            timeout=self.timeout,
        )
        return [process_point(point) for point in response.result]

    async def delete_collection(self, collection_name: str):
        grpc_points = self.client.async_grpc_collections
        response = await grpc_points.Delete(
            grpc.DeleteCollection(
                collection_name=collection_name,
            ),
            timeout=self.timeout,
        )
        return response.result

    async def update_collection(self, collection_name: str, **kwargs: Any):
        col_name = self.collection_name or collection_name
        grpc_points = self.client.async_grpc_collections
        response = await grpc_points.Update(
            grpc.UpdateCollection(
                collection_name=col_name,
                config=RestToGrpc.convert_collection_config(**kwargs),
            ),
            timeout=self.timeout,
        )
        return response.result

    async def index_points(
        self,
        collection_name: str,
        offset=None,
        filters: dict = None,
        limit=100,
        with_payload=True,
        with_vectors=True,
    ):
        fi = (
            RestToGrpc.convert_filter(http_models.Filter(**filters))
            if filters
            else None
        )
        grpc_points = self.client.async_grpc_points
        try:
            response = await grpc_points.Scroll(
                grpc.ScrollPoints(
                    collection_name=collection_name,
                    offset=grpc.PointId(uuid=offset) if offset else None,
                    limit=limit,
                    filter=fi,
                    with_payload=grpc.WithPayloadSelector(enable=with_payload),
                    with_vectors=grpc.WithVectorsSelector(enable=with_vectors),
                ),
            )
        except grpc1.RpcError as e:
            if e.code() == grpc1.StatusCode.NOT_FOUND:
                return []
            raise e
        # transfer to dict

        return response

    @retry_async
    @trace()
    async def index_query(
        self,
        vectors: list[float],
        collection_name: str,
        offset: int = 0,
        limit: int = 10,
        filters: http_models.Filter = None,
        score_threshold: float = None,
    ) -> grpc.ScoredPoint:
        if settings.QDRANT_HTTP:
            t0 = time.monotonic()
            res = await async_qdrant_client.search(
                collection_name=collection_name,
                query_vector=vectors,
                limit=limit,
                query_filter=filters,
                score_threshold=score_threshold,
                search_params=http_models.SearchParams(exact=True),
            )
            gr = [RestToGrpc.convert_scored_point(i) for i in res]

            class PointsResult:
                def __init__(self, result, time):
                    self._result = result
                    self._time = time

                @property
                def result(self):
                    return self._result

                @property
                def time(self):
                    return self._time

            return PointsResult(result=gr, time=time.monotonic() - t0)
        fi = RestToGrpc.convert_filter(filters) if filters else None
        try:
            grpc_points = self.client.async_grpc_points
            if not isinstance(vectors, list):
                logging.error(f"vectors is not a list, inspecting object: {vectors}")
                logging.error(f"vectors attributes: {dir(vectors)}")
            response = await grpc_points.Search(
                grpc.SearchPoints(
                    collection_name=collection_name,
                    vector=vectors,
                    score_threshold=score_threshold,
                    filter=fi,
                    offset=offset,
                    limit=limit,
                    with_payload=grpc.WithPayloadSelector(enable=True),
                    params=grpc.SearchParams(
                        # hnsw_ef=256,
                        exact=True
                    ),
                ),
                timeout=self.timeout,
            )
        except grpc1.RpcError as e:
            # 记录所有相关参数的信息
            logging.error(f"Error in index_query:")
            logging.error(f"vectors type: {type(vectors)}, vectors content: {vectors}")
            if isinstance(vectors, list):
                for idx, item in enumerate(vectors):
                    logging.error(f"Element {idx} type: {type(item)}, content: {item}")
            else:
                logging.error(
                    f"vectors is not a list, vectors attributes: {dir(vectors)}"
                )

            logging.error(f"collection_name: {collection_name}")
            logging.error(f"offset: {offset}")
            logging.error(f"limit: {limit}")
            logging.error(f"filters: {filters}")
            logging.error(f"score_threshold: {score_threshold}")
            logging.exception("Exception details", exc_info=e)
            errText = f"An unexpected error occurred, error code: {uuid.uuid4()}, please try again later. If this error persists, please contact support."
            logging.error(errText)
            raise ValueError(errText)
        return response

    async def query_list(self, *args: Any, **kwargs: Any):
        result = await self.index_query(*args, **kwargs)
        return [process_point(point) for point in result.result]

    async def index_delete(
        self,
        ids: list[str],
        collection_name: str,
    ):
        response = await self.client.async_grpc_points.Delete(
            grpc.DeletePoints(
                collection_name=collection_name,
                points=RestToGrpc.convert_points_selector(
                    http_models.PointIdsList(points=ids)
                ),
            ),
            timeout=self.timeout,
        )
        return GrpcToRest.convert_update_result(response.result)

    async def delete_by_filter(
        self,
        collection_name: str,
        filters: dict,
    ):
        col_name = self.collection_name or collection_name
        if self.collection_name:
            filters = filters or {}
            must = filters.get("must", [])
            must.append(
                {
                    "key": "group_id",
                    "match": {
                        "value": collection_name,
                    },
                }
            )
            filters["must"] = must
        response = await self.client.async_grpc_points.Delete(
            grpc.DeletePoints(
                collection_name=col_name,
                points=RestToGrpc.convert_points_selector(
                    http_models.FilterSelector(filter=filters)
                ),
            ),
            timeout=self.timeout,
        )
        return GrpcToRest.convert_update_result(response.result)

    async def fetch_indexs(
        self,
        collection_name: str,
        vectors: list[float],
        count: Optional[int] = 8,
        score: Optional[float] = 0.75,
    ):
        return await self.index_query(
            vectors,
            collection_name,
            limit=count,
            score_threshold=score,
        )

    async def fetch_index_by_ids(
        self,
        collection_name: str,
        ids: list[str],
    ):
        return await self.index_fetch(ids=ids, namespace=collection_name)

    async def index_count(
        self, collection_name: str, filters: dict = {}, adapt_old_ds=False
    ) -> int:
        # col_name = self.collection_name or collection_name
        # if self.storage_type == VectorStorageType.QDRANT_ONE_COLLECTION:
        #     filters = filters or {}
        #     must = filters.get("must", [])
        #     must.append(
        #         {
        #             "key": "metadata.dataset_id",
        #             "match": {
        #                 "value": collection_name,
        #             },
        #         }
        #     )
        #     filters["must"] = must
        try:
            if settings.QDRANT_HTTP:
                response = await async_qdrant_client.count(
                    collection_name=collection_name,
                    count_filter=http_models.Filter(**filters),
                )
                return response.count
            else:
                fi = (
                    RestToGrpc.convert_filter(http_models.Filter(**filters))
                    if filters
                    else None
                )
                response = await self.client.async_grpc_points.Count(
                    grpc.CountPoints(
                        collection_name=collection_name,
                        filter=fi,
                    ),
                    timeout=self.timeout,
                )
        except grpc1.RpcError as e:
            if e.code() == grpc1.StatusCode.NOT_FOUND:
                logging.error(
                    f"collection not found: {self.collection_name}, {collection_name}, vector count: 0"
                )
                return 0
            raise e
        except Exception as e:
            logging.error(f"Error in index_count: {e}")
            logging.error(f"collection_name: {collection_name}")
            logging.error(f"filters: {filters}")
            logging.error(f"adapt_old_ds: {adapt_old_ds}")
            logging.error(f"Error in index_count: {e}")
            logging.error(f"Error in index_count: {traceback.format_exc()}")
            raise e
        count = response.result.count
        logging.info(
            f"collection: {self.collection_name}, {collection_name}, vector count: {count}, internal time: {response.time}"
        )
        # adapt for old datasets
        if count == 0 and adapt_old_ds:
            logging.warning(
                f"collection: {self.collection_name}, {collection_name}, maybe old datasets, vector count: 0, adapt for old datasets, try to fetch 200 vectors"
            )
            count = 200
        return count

    async def set_payload(
        self,
        payload: dict,
        collection_name: str,
        id: Optional[str] = None,
        filters: Optional[dict] = None,
        wait=False,
    ):
        points_selector = None
        if id:
            points_selector = RestToGrpc.convert_points_selector(
                http_models.PointIdsList(points=[id])
            )
        elif filters:
            points_selector = RestToGrpc.convert_points_selector(
                http_models.FilterSelector(filter=filters)
            )
        response = await self.client.async_grpc_points.SetPayload(
            grpc.SetPayloadPoints(
                collection_name=collection_name,
                points_selector=points_selector,
                payload=RestToGrpc.convert_payload(payload),
                wait=wait,
            ),
        )
        return GrpcToRest.convert_update_result(response.result)


class VectorStorageFactory:
    _instances = {}

    @classmethod
    def get_instance(
        cls,
        storage_type: VectorStorageType = VectorStorageType.QDRANT_ONE_COLLECTION,
        **kwargs,
    ) -> VectorStorage:
        if storage_type in cls._instances:
            return cls._instances[storage_type]
        if "host" not in kwargs:
            kwargs["host"] = VECTOR_STORAGE_QDRANT_HOST
        if "grpc_port" not in kwargs:
            kwargs["grpc_port"] = VECTOR_STORAGE_QDRANT_GRPC_PORT
        if "provider" in kwargs:
            kwargs.pop("provider")
        if storage_type == VectorStorageType.QDRANT:
            vs = QdrantStorage(
                storage_type=storage_type,
                **kwargs,
            )
        elif storage_type == VectorStorageType.QDRANT_ONE_COLLECTION:
            vs = QdrantStorage(
                storage_type=storage_type,
                collection_name="gptbase_col",
                **kwargs,
            )
        else:
            raise ValueError(f"Unknown storage type: {storage_type}")
        cls._instances[storage_type] = vs
        return vs

    @classmethod
    @async_timing_decorator
    async def create_collection(
        cls,
        collection_name: str,
        storage_type: VectorStorageType = VectorStorageType.QDRANT_ONE_COLLECTION,
        vector_size: Optional[int] = None,
        **kwargs,
    ) -> VectorStorage:
        vs = cls.get_instance(storage_type, **kwargs)
        collection_exists = await vs.collection_exists(collection_name)

        if not collection_exists:
            await vs.create_collection(collection_name, vector_size=vector_size)
            await vs.create_index(
                collection_name,
                field_name="group_id",
                field_type=grpc.FieldType.FieldTypeKeyword,
            )
            await vs.create_index(
                collection_name,
                field_name="metadata.file_id",
                field_type=grpc.FieldType.FieldTypeKeyword,
            )
        else:
            # 检查是否存在 metadata.file_id 索引，不存在则创建
            collection = await vs.async_client.get_collection(collection_name)
            payload_schema = collection.payload_schema
            if "metadata.file_id" not in payload_schema:
                await vs.create_index(
                    collection_name,
                    field_name="metadata.file_id",
                    field_type=grpc.FieldType.FieldTypeKeyword,
                )
        return vs

    @classmethod
    @async_timing_decorator
    async def save_points(
        cls,
        embeddings: OpenAIEmbeddings,
        doc_ids: list,
        documents: list[Document],
        group_id: str,
        storage_type: VectorStorageType = VectorStorageType.QDRANT_ONE_COLLECTION,
        **kwargs,
    ):
        vector_size = embeddings.model_kwargs.get("dimensions")
        if not vector_size:
            model = embeddings.model
            if settings.USE_LOCAL_EMBEDDING:
                vector_size = int(settings.LOCAL_EMBEDDING_VECTOR_SIZE)
            elif (
                OpenAIModel(model) == OpenAIModel.TEXT_EMBEDDING_ADA_002
                or OpenAIModel(model) == OpenAIModel.TEXT_EMBEDDING_3_SMALL
            ):
                vector_size = 1536
            elif OpenAIModel(model) == OpenAIModel.TEXT_EMBEDDING_3_LARGE:
                vector_size = 3072
            else:
                raise ValueError(f"Unknown model: {model}")

        if "collection_name" in kwargs:
            collection_name = kwargs.pop("collection_name")
        elif LOCAL_EMBEDDING_PROXY and LOCAL_EMBEDDING_MODEL_NAME is not None:
            collection_name = f"gptbase_local_{LOCAL_EMBEDDING_MODEL_NAME}"
        else:
            collection_name = get_collection_name(
                OpenAIModel(embeddings.model),
                vector_size,
            )
        vs = await cls.create_collection(
            collection_name, storage_type, vector_size, **kwargs
        )
        await asyncio.to_thread(
            vs.from_documents,
            embeddings=embeddings,
            documents=documents,
            collection_name=collection_name,
            ids=doc_ids,
            group_id=group_id,
            max_retries=10,
            wait=True,
        )

    @classmethod
    async def delete_points(
        cls,
        embeddings: OpenAIEmbeddings,
        doc_ids: list,
        storage_type: VectorStorageType = VectorStorageType.QDRANT_ONE_COLLECTION,
        **kwargs,
    ):
        vector_size = embeddings.model_kwargs.get("dimensions")
        if not vector_size:
            model = embeddings.model
            if settings.USE_LOCAL_EMBEDDING:
                vector_size = int(settings.LOCAL_EMBEDDING_VECTOR_SIZE)
            elif (
                OpenAIModel(model) == OpenAIModel.TEXT_EMBEDDING_ADA_002
                or OpenAIModel(model) == OpenAIModel.TEXT_EMBEDDING_3_SMALL
            ):
                vector_size = 1536
            elif OpenAIModel(model) == OpenAIModel.TEXT_EMBEDDING_3_LARGE:
                vector_size = 3072
            else:
                raise ValueError(f"Unknown model: {model}")

        if "collection_name" in kwargs:
            collection_name = kwargs.pop("collection_name")
        elif LOCAL_EMBEDDING_PROXY and LOCAL_EMBEDDING_MODEL_NAME is not None:
            collection_name = f"gptbase_local_{LOCAL_EMBEDDING_MODEL_NAME}"
        else:
            collection_name = get_collection_name(
                OpenAIModel(embeddings.model),
                vector_size,
            )
        vs = cls.get_instance(storage_type, **kwargs)
        await vs.index_delete(doc_ids, collection_name)
