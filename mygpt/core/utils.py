import asyncio
import functools
import json
from typing import Optional

import openai
from loguru import logger as logging

from mygpt import settings
from mygpt.enums import (
    LLM_PROVIDER,
    OpenAIModel,
    QdrantFaqCollectionName,
    VectorFileType,
)
from mygpt.loader.outline import OutlineParser
from mygpt.loader.transcoder import DocumentTranscoder


async def task_with_index(index, task):
    try:
        return index, await task
    except Exception as e:
        return index, e


async def task_first_completed(*tasks):
    """Wait for the first task to complete."""
    indexed_tasks = [task_with_index(i, task) for i, task in enumerate(tasks)]
    while indexed_tasks:
        done, pending = await asyncio.wait(
            indexed_tasks, return_when=asyncio.FIRST_COMPLETED
        )
        for task in done:
            index, result = task.result()
            if not isinstance(result, Exception):
                for task in pending:
                    task.cancel()
                return index, result
            indexed_tasks = list(pending)
    return 0, None


def get_concurrent_provider():
    # 默认：使用openai
    provides = [LLM_PROVIDER.OPENAI]
    # 如果开启了并发，使用openai和azure
    if settings.CONCURRENT_INTENT_DETECT:
        provides = [LLM_PROVIDER.OPENAI, LLM_PROVIDER.AZURE]
    # 如果指定只使用azure
    if settings.OPENAI_JUST_AZURE:
        provides = [LLM_PROVIDER.AZURE]
    return provides


def get_provider():
    from mygpt.openai_utils import _is_using_azure_openai

    if _is_using_azure_openai():
        return LLM_PROVIDER.AZURE
    return LLM_PROVIDER.OPENAI


def handle_decode(text, metadata):
    breadcrumb = OutlineParser.extract_breadcrumb(text)
    if breadcrumb:
        text = OutlineParser.delete_breadcrumb(text)
        if metadata is None:
            metadata = {}
        metadata["breadcrumb"] = breadcrumb

    document_transcoder = DocumentTranscoder(text)
    text = document_transcoder.decode()
    return text, metadata


def get_collection_name(
    model_name: OpenAIModel,
    dimensions: Optional[int] = None,
):
    if (
        settings.LOCAL_EMBEDDING_PROXY
        and settings.LOCAL_EMBEDDING_MODEL_NAME is not None
    ):
        return f"gptbase_local_{settings.LOCAL_EMBEDDING_MODEL_NAME}"
    if model_name == OpenAIModel.TEXT_EMBEDDING_ADA_002:
        return "gptbase_col"
    elif model_name == OpenAIModel.TEXT_EMBEDDING_3_LARGE:
        if not dimensions or dimensions == 3072:
            return "gptbase_col_large"
        return f"gptbase_col_large__{dimensions}"
    elif model_name == OpenAIModel.TEXT_EMBEDDING_3_SMALL:
        if not dimensions or dimensions == 1536:
            return "gptbase_col_small"
        return f"gptbase_col_small__{dimensions}"
    else:
        raise ValueError(f"Unknown model name: {model_name}")


def get_faq_collection_name(
    file_type: VectorFileType,
    model_name: OpenAIModel,
    dimensions: Optional[int] = None,
):
    if (
        settings.LOCAL_EMBEDDING_PROXY
        and settings.LOCAL_EMBEDDING_MODEL_NAME is not None
    ):
        if file_type == VectorFileType.FAQ:
            return f"gptbase_local_faq_{settings.LOCAL_EMBEDDING_MODEL_NAME}"
        else:
            return f"gptbase_local_faq_question_{settings.LOCAL_EMBEDDING_MODEL_NAME}"

    if model_name == OpenAIModel.TEXT_EMBEDDING_ADA_002:
        return (
            QdrantFaqCollectionName.FAQ.value
            if file_type == VectorFileType.FAQ
            else QdrantFaqCollectionName.FAQ_QUESTION.value
        )
    elif model_name == OpenAIModel.TEXT_EMBEDDING_3_LARGE:
        if not dimensions or dimensions == 3072:
            if file_type == VectorFileType.FAQ:
                return f"{QdrantFaqCollectionName.FAQ}_large"
            return f"{QdrantFaqCollectionName.FAQ_QUESTION}_large"
        else:
            if file_type == VectorFileType.FAQ:
                return f"{QdrantFaqCollectionName.FAQ}_large__{dimensions}"
            return f"{QdrantFaqCollectionName.FAQ_QUESTION}_large__{dimensions}"
    elif model_name == OpenAIModel.TEXT_EMBEDDING_3_SMALL:
        if not dimensions or dimensions == 1536:
            if file_type == VectorFileType.FAQ:
                return f"{QdrantFaqCollectionName.FAQ}_small"
            return f"{QdrantFaqCollectionName.FAQ_QUESTION}_small"
        else:
            if file_type == VectorFileType.FAQ:
                return f"{QdrantFaqCollectionName.FAQ}_small__{dimensions}"
            return f"{QdrantFaqCollectionName.FAQ_QUESTION}_small__{dimensions}"
    else:
        raise ValueError(f"Unknown model name: {model_name}")


async def on_request(request):
    await log_request(request)


async def on_response(response):
    await log_response(response)


event_hooks = {
    "request": [on_request],
    "response": [on_response],
}


def retry_async(func):
    """
    重试装饰器,异步函数
    重试逻辑,在发生异常时切换模型提供商并对func进行重试
    """

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        retries = 0
        final_exception = None
        max_retries = kwargs.pop("max_retries", 3)
        delay = kwargs.pop("delay", 0.1)
        while retries < max_retries:
            try:
                return await func(*args, **kwargs)
            except openai.OpenAIError as e:
                retries += 1
                if settings.DEBUG:
                    raise
                await asyncio.sleep(delay)
                final_exception = e
        raise final_exception

    return wrapper


async def log_request(request):
    logging.debug(f"URL: {request.url}")
    logging.debug(f"Method: {request.method}")
    if request.headers:
        logging.debug(f"Headers: {request.headers}")
    if request.content:
        try:
            content = json.loads(request.content)
            logging.debug(f"Request Body: {json.dumps(content, indent=2)}")
        except json.JSONDecodeError:
            logging.debug(f"Request Body: {request.content}")


async def log_response(response):
    logging.debug(f"Status Code: {response.status_code}")
    if response.headers:
        logging.debug(f"Headers: {response.headers}")

    if not response.is_stream_consumed:
        content = await response.aread()
    else:
        content = response.content

    if content:
        try:
            # json_content = (
            json.loads(content)
            # logging.debug(f"Response Body: {json.dumps(json_content, indent=2)}")
        except json.JSONDecodeError:
            logging.debug(f"Response Body: {content}")
