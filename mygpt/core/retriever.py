from typing import List, Optional

from grpc import RpcError, StatusCode
from llama_index.core.base.base_retriever import <PERSON><PERSON><PERSON>riever
from llama_index.core.schema import NodeWithScore, QueryBundle, TextNode
from loguru import logger as logging
from qdrant_client.conversions.conversion import GrpcToRest
from qdrant_client.http import models as http_models

from mygpt import settings
from mygpt.core.cohere_utils import Client
from mygpt.core.embedding import EmbeddingFactory
from mygpt.core.entity import FaqRecall
from mygpt.core.utils import get_collection_name, get_faq_collection_name, handle_decode
from mygpt.core.vector_storage import VectorStorage, VectorStorageFactory
from mygpt.enums import EMBEDDINGS_MODEL, OpenAIModel, VectorFileType, VectorStorageType
from mygpt.schemata import Embeddings


class Rerank:
    _instances = {}

    def __init__(self, **kwargs):
        self.client = Client(**kwargs)

    @classmethod
    def get_instance(
        cls,
        **kwargs,
    ) -> "Rerank":
        key = "cohere"
        if key not in cls._instances:
            cls._instances[key] = cls(**kwargs)
        return cls._instances[key]

    async def rerank(
        self,
        query: str,
        documents: list[str],
        model: str = "rerank-multilingual-v2.0",
    ) -> list[str]:
        return self.client.rerank(query, documents, model)


class Retriever:
    _instances = {}

    def __init__(
        self, vector_storage: VectorStorage, embedding_model: EMBEDDINGS_MODEL
    ):
        self.vector_storage = vector_storage
        self.embedding_model = embedding_model

    @classmethod
    def get_instance(
        cls,
        storage_type: VectorStorageType = VectorStorageType.QDRANT_ONE_COLLECTION,
        embedding_model: EMBEDDINGS_MODEL = EMBEDDINGS_MODEL.OPENAI,
        **kwargs,
    ) -> "Retriever":
        key = f"{storage_type}_{embedding_model}"
        if key not in cls._instances:
            cls._instances[key] = cls(
                VectorStorageFactory.get_instance(storage_type, **kwargs),
                embedding_model,
            )
        return cls._instances[key]

    def process_point(self, point, collection_name):
        index = GrpcToRest.convert_scored_point(point)
        text, metadata = handle_decode(
            index.payload["page_content"], index.payload["metadata"]
        )
        dataset_id = metadata.get("dataset_id") or index.payload["group_id"]
        return Embeddings(
            id=index.id,
            text=text,
            metadata=metadata,
            score=index.score if hasattr(index, "score") else None,
            vector=index.vector,
            storage_type=self.vector_storage.storage_type,
            dataset_id=dataset_id,
        )

    async def retrieve(
        self,
        dataset_ids: list[str],
        query: str,
        embedding_model_name: OpenAIModel,
        collection_name: Optional[str] = None,
        limit: int = 10,
        score_threshold: float = 0.6,
        embeddings: Optional[list[float]] = None,
        **kwargs,
    ) -> list[Embeddings]:
        if "additional_filters" in kwargs:
            additional_filters = kwargs.pop("additional_filters")
        else:
            additional_filters = None
        if not embeddings:
            # 如果没有传入embeddings，则使用query生成
            embeddings = await EmbeddingFactory.concurrent_embedding(
                [query], model_name=embedding_model_name, **kwargs
            )
            embeddings = embeddings[0]
        filters = http_models.Filter(
            must=[
                http_models.FieldCondition(
                    key="group_id",
                    match=http_models.MatchAny(any=dataset_ids),
                ),
            ]
        )

        if additional_filters:
            should_filters = []
            for item in additional_filters:
                field_condition = []
                field_condition.append(
                    http_models.FieldCondition(
                        key="metadata.file_id",
                        match=http_models.MatchValue(value=str(item.vector_file_id)),
                    )
                )
                if item.page_numbers:
                    field_condition.append(
                        http_models.FieldCondition(
                            key="metadata.page_number",
                            match=http_models.MatchAny(any=item.page_numbers),
                        )
                    )
                should_filters.append(http_models.Filter(must=field_condition))
            filters.should = should_filters
        if not collection_name:
            collection_name = get_collection_name(
                embedding_model_name,
                kwargs.get("dimensions", None),
            )
        try:
            result = await self.vector_storage.index_query(
                embeddings,
                collection_name,
                filters=filters,
                limit=limit,
                score_threshold=score_threshold,
                max_retries=5,
            )
        except RpcError as e:
            if e.code() == StatusCode.NOT_FOUND:
                return []
            raise e
        return [self.process_point(point, collection_name) for point in result.result]

    async def retrieve_faq_answer(
        self,
        dataset_ids: list[str],
        embeddings: list[float],
        limit: int = 4,
        score_threshold: float = 0.3 if settings.IS_USE_LOCAL_VLLM else 0.5,
        embedding_model_name: OpenAIModel = OpenAIModel.TEXT_EMBEDDING_ADA_002,
        dimensions: Optional[int] = None,
    ):
        filters = http_models.Filter(
            must=[
                http_models.FieldCondition(
                    key="metadata.dataset_id",
                    match=http_models.MatchAny(any=dataset_ids),
                ),
            ]
        )
        collection_name = get_faq_collection_name(
            VectorFileType.FAQ,
            embedding_model_name,
            dimensions=dimensions,
        )
        response = await self.vector_storage.index_query(
            embeddings,
            collection_name,
            filters=filters,
            limit=limit,
            score_threshold=score_threshold,
        )
        if len(response.result) == 0:
            return []
        text = ""
        final_point = None
        for point in response.result:
            final_point = point
            index = GrpcToRest.convert_scored_point(point)
            text = text.rstrip("\n") + "\n" + index.payload["page_content"].lstrip("\n")
        doc = self.process_point(final_point, collection_name)
        doc.text = text
        doc.metadata["title"] = "FAQ"
        return [doc]

    async def retrieve_faq(
        self,
        dataset_ids: list[str],
        query: str,
        embedding_model_name: OpenAIModel,
        faq_type: VectorFileType,
        collection_name: Optional[str] = None,
        limit: int = 10,
        score_threshold: float = 0.9,
        embeddings: Optional[list[float]] = None,
        **kwargs,
    ) -> list[FaqRecall]:
        if not embeddings:
            # 如果没有传入embeddings，则使用query生成
            embedding_arr = await EmbeddingFactory.concurrent_embedding(
                [query], **kwargs
            )
            embeddings = embedding_arr[0]
        filters = http_models.Filter(
            must=[
                http_models.FieldCondition(
                    key="metadata.dataset_id",
                    match=http_models.MatchAny(any=dataset_ids),
                ),
            ]
        )
        if not collection_name:
            collection_name = get_faq_collection_name(
                faq_type,
                embedding_model_name,
                kwargs.get("dimensions", None),
            )
        response = await self.vector_storage.index_query(
            embeddings,
            collection_name,
            filters=filters,
            limit=limit,
            score_threshold=score_threshold,
            max_retries=5,
        )
        if response.time > 1:
            logging.warning(
                f"get_indexs:{filters} api response time is:{response.time}"
            )
        faq_recalls = []
        for point in response.result:
            index = GrpcToRest.convert_scored_point(point)
            metadata = index.payload["metadata"]
            faq_id = metadata.get("faq_id")
            if not faq_id:
                continue
            faq_type = metadata.get("faq_type")
            title = metadata.get("title") if faq_type == "similar" else None
            faq_recalls.append(
                FaqRecall(
                    faqs_id=faq_id,
                    score=index.score,
                    title=title,
                )
            )
        return faq_recalls


class CustomRetriever(BaseRetriever):
    def __init__(
        self,
        retriever: Retriever,
        dataset_ids: list[str],
        collection_name: str = "gptbase_col",
        model_name: Optional[str] = None,
    ):
        self.retriever = retriever
        self.dataset_ids = dataset_ids
        self.collection_name = collection_name
        self.model_name = model_name

    async def aretrieve(self, query: str) -> list[NodeWithScore]:
        rs = await self.retriever.retrieve(
            self.dataset_ids,
            query,
            model_name=self.model_name,
            collection_name=self.collection_name,
            score_threshold=0.4,
        )
        return [
            NodeWithScore(node=TextNode(id_=r.id, text=r.text), score=r.score)
            for r in rs
        ]

    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        raise NotImplementedError
