import asyncio
import json as jsonlib
import os
from typing import Any, Dict, List, Optional, Union

import aiohttp
import cohere
import requests
from cohere import CohereAPIError, CohereConnectionError, CohereError
from cohere.client import Reranking
from cohere.client_async import AIOHTTPBackend
from cohere.utils import np_json_dumps
from loguru import logger
from requests.adapters import HTTPAdapter
from urllib3 import Retry


class Client(cohere.Client):
    def __init__(
        self,
        api_key: str = None,
        num_workers: int = 64,
        request_dict: dict = {},
        check_api_key: bool = True,
        client_name: Optional[str] = None,
        max_retries: int = 3,
        timeout: int = 120,
        api_url: str = None,
    ) -> None:
        super().__init__(
            api_key,
            num_workers,
            request_dict,
            check_api_key,
            client_name,
            max_retries,
            timeout,
            api_url,
        )
        retries = Retry(
            total=max_retries,
            backoff_factor=0.5,
            allowed_methods=["POST", "GET"],
            status_forcelist=cohere.RETRY_STATUS_CODES,
            raise_on_status=False,
        )
        self.session = requests.Session()
        self.session.proxies = {
            "http": os.getenv("HTTP_PROXY"),
            "https": os.getenv("HTTPS_PROXY"),
        }
        self.session.mount(
            "https://",
            HTTPAdapter(
                pool_connections=20,
                pool_maxsize=20,
                max_retries=retries,
            ),
        )

    def rerank(
        self,
        query: str,
        documents: Union[List[str], List[Dict[str, Any]]],
        model: str,
        top_n: Optional[int] = None,
        max_chunks_per_doc: Optional[int] = None,
    ) -> Reranking:
        """Returns an ordered list of documents ordered by their relevance to the provided query

        Args:
            query (str): The search query
            documents (list[str], list[dict]): The documents to rerank
            model (str): The model to use for re-ranking
            top_n (int): (optional) The number of results to return, defaults to returning all results
            max_chunks_per_doc (int): (optional) The maximum number of chunks derived from a document
        """
        parsed_docs = []
        for doc in documents:
            if isinstance(doc, str):
                parsed_docs.append({"text": doc})
            elif isinstance(doc, dict) and "text" in doc:
                parsed_docs.append(doc)
            else:
                raise CohereError(
                    message='invalid format for documents, must be a list of strings or dicts with a "text" key'
                )

        json_body = {
            "query": query,
            "documents": parsed_docs,
            "model": model,
            "top_n": top_n,
            "return_documents": False,
            "max_chunks_per_doc": max_chunks_per_doc,
        }

        reranking = Reranking(self._request(cohere.RERANK_URL, json=json_body))
        for rank in reranking.results:
            rank.document = parsed_docs[rank.index]
        return reranking

    def _request(
        self, endpoint, json=None, files=None, method="POST", stream=False, params=None
    ) -> Any:
        headers = {
            "Authorization": "BEARER {}".format(self.api_key),
            "Request-Source": self.request_source,
        }
        if json:
            headers["Content-Type"] = "application/json"

        url = f"{self.api_url}/{self.api_version}/{endpoint}"

        if stream:
            return self.session.request(
                method,
                url,
                headers=headers,
                json=json,
                **self.request_dict,
                stream=True,
            )

        try:
            response = self.session.request(
                method,
                url,
                headers=headers,
                json=json,
                files=files,
                timeout=self.timeout,
                params=params,
                **self.request_dict,
            )
            logger.info(f"cohere rerank response elapsed time: {response.elapsed}s")
        except requests.exceptions.ConnectionError as e:
            raise CohereConnectionError(str(e)) from e
        except requests.exceptions.RequestException as e:
            raise CohereError(
                f"Unexpected exception ({e.__class__.__name__}): {e}"
            ) from e

        try:
            json_response = response.json()
        except jsonlib.decoder.JSONDecodeError:  # CohereAPIError will capture status
            raise CohereAPIError.from_response(
                response, message=f"Failed to decode json body: {response.text}"
            )

        self._check_response(json_response, response.headers, response.status_code)
        return json_response


class AIOHTTPBackendSession(AIOHTTPBackend):
    async def session(self) -> aiohttp.ClientSession:
        if self._session is None:
            self._session = aiohttp.ClientSession(
                json_serialize=np_json_dumps,
                timeout=aiohttp.ClientTimeout(self.timeout),
                connector=aiohttp.TCPConnector(limit=0),
                trust_env=True,
            )
            self._semaphore = asyncio.Semaphore(self.max_concurrent_requests)
            self._requester = self.build_aio_requester()

        return self._session


class AsyncClientSession(cohere.AsyncClient):
    def __init__(
        self,
        api_key: str = None,
        num_workers: int = 16,
        request_dict: dict = {},
        check_api_key: bool = True,
        client_name: Optional[str] = None,
        max_retries: int = 3,
        timeout=600,
        api_url: str = None,
    ) -> None:
        self.api_key = api_key or os.getenv("CO_API_KEY")
        self.api_url = api_url or os.getenv("CO_API_URL", cohere.COHERE_API_URL)
        self.batch_size = cohere.COHERE_EMBED_BATCH_SIZE
        self.num_workers = num_workers
        self.request_dict = request_dict
        self.request_source = "python-sdk-" + cohere.SDK_VERSION
        self.max_retries = max_retries
        if client_name:
            self.request_source += ":" + client_name
        self.api_version = f"v{cohere.API_VERSION}"
        self._check_api_key_on_enter = check_api_key
        self._backend = AIOHTTPBackendSession(
            cohere.logging.logger, num_workers, max_retries, timeout
        )
