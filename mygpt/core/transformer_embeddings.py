from __future__ import annotations

import logging
from typing import List, Optional

from langchain.embeddings.base import Embeddings
from pydantic import BaseModel
from tenacity import retry, stop_after_attempt, wait_exponential

from mygpt import settings
from mygpt.huggingface_sentence import before_log_retry, after_log_retry, http

logger = logging.getLogger(__name__)


class TransformerEmbeddings(BaseModel, Embeddings):
    """
    Transformer Embeddings.
    """

    url: str
    max_retries: int = 6
    """Maximum number of retries to make when generating."""

    def _embedding_func(self, text: str) -> List[float]:
        """Call out to OpenAI's embedding endpoint."""
        return sentence_transformers_post_sync(
            self.url,
            {"input": text, "model": ""},
            headers={
                "Authorization": f"Bearer {settings.LOCAL_EMBEDDING_KEY}",
                "Content-Type": "application/json",
            },
        )

    def embed_documents(
        self, texts: List[str], chunk_size: Optional[int] = 0
    ) -> List[List[float]]:
        results = []
        for text in texts:
            results.append(self._embedding_func(text))
        return results

    def embed_query(self, text: str) -> List[float]:
        """Call out to OpenAI's embedding endpoint for embedding query text.

        Args:
            text: The text to embed.

        Returns:
            Embedding for the text.
        """
        embedding = self._embedding_func(text)
        return embedding


@retry(
    stop=stop_after_attempt(5),
    wait=wait_exponential(multiplier=1, min=3, max=10),
    before=before_log_retry,
    after=after_log_retry,
    reraise=True,
)
def sentence_transformers_post_sync(url, body, headers):
    # Make request using connection pool
    rs = http.post(url, json=body, timeout=10, headers=headers)
    logging.info(
        f"local embedding request: {rs.url} status - {rs.status_code} -time：{rs.elapsed.total_seconds()}s"
    )

    if rs.status_code == 200:
        data = rs.json()
        if not isinstance(data, list):
            logging.error(f"local embedding return err result: {rs.text}")
            raise ValueError(
                f"local embeddings Error: {rs.text}",
            )
        return data
    else:
        logging.warning(
            f"local embedding status_code: {rs.status_code}, data: {rs.text}"
        )
        raise ValueError(
            f"local embeddings Error: {rs.status_code}",
        )
