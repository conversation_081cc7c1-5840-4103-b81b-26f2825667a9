import asyncio
import functools
import json
import os
import random
import sys
import time
import traceback
from typing import Any, AsyncIterator, Awaitable, Dict, List, Optional, Tuple, Union

import openai
from langchain.adapters.openai import convert_openai_messages
from langchain.callbacks import AsyncIteratorCallbackHandler
from langchain.callbacks.base import As<PERSON><PERSON><PERSON>back<PERSON>and<PERSON>, Callbacks
from langchain.callbacks.manager import AsyncCallbackManager
from langchain.load import dumpd
from langchain.prompts.chat import (
    ChatPromptTemplate,
    HumanMessagePromptTemplate,
    SystemMessagePromptTemplate,
)
from langchain.schema import (
    AIMessage,
    HumanMessage,
    SystemMessage,
    get_buffer_string,
)
from langchain.schema.messages import BaseMessage, _message_to_dict, messages_from_dict
from langchain.schema.output import LLMResult, RunInfo
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.chat_models import AzureChatOpenAI, ChatOpenAI
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.runnables import RunnableSerializable
from loguru import logger as logging
from py3langid.langid import MODEL_FILE, LanguageIdentifier
from skywalking.decorators import trace

from mygpt import settings
from mygpt.enums import (
    LLM_PROVIDER,
    SessionMessageStatus,
    StreamingMessageDataFormat,
    StreamingMessageDataType,
    OpenAIModel,
    UserIntent,
    FunctionCallStyle,
)
from mygpt.error import sentry_sdk_capture_exception
from mygpt.models import SessionMessage, Faqs, VectorFile, SessionEvaluate
from mygpt.opensearch import fetch_from_opensearch
from mygpt.outputs import extract_json, SectionOutput, SectionsOutput
from mygpt.prompt import (
    get_keywords_dictionary_prompt,
    gpt_4_turbo_keywords_generation_prompt,
    determine_whether_can_answer_about_company,
    determine_whether_llm_answer_is_clarification,
    language_detect_prompt,
)
from mygpt.prompt_dynamic_section import PROMPTS
from mygpt.schemata import Embeddings, StreamOut
from mygpt.service.quality_assessment import save_to_quality_assessment_by_message_id
from mygpt.session_metrics import (
    create_or_update_session_unanswered_count_by_message_id,
)
from mygpt.settings import GEMINI_EXP
from mygpt.util.notice import notice
from mygpt.util import question_tracker

from mygpt.utils import (
    ends_with_markdown_image_syntax_index,
    ends_with_markdown_link_syntax_index,
    extract_first_valid_json,
    extract_image_from_content,
    num_tokens_for_langchain_messages,
    num_tokens_from_string,
    replace_image_url_to_uuid,
    replace_md_uuid_images,
    replace_markdown_images, format_sensitive_key,
)

# 确保环境变量已加载
from dotenv import load_dotenv, find_dotenv
import re

# 尝试加载 .env 文件，如果存在的话
dotenv_path = find_dotenv(usecwd=True)
if dotenv_path:
    load_dotenv(dotenv_path, override=True)
    logging.info(f"Loaded environment variables from {dotenv_path}")
else:
    logging.warning("No .env file found")

# 确保 LOCAL_VLLM_MODEL 环境变量已设置
is_use_local_vllm = os.environ.get("IS_USE_LOCAL_VLLM", "").lower() in ("true", "1", "yes", "y", "on")
if is_use_local_vllm and not os.environ.get("LOCAL_VLLM_MODEL"):
    default_model = "gbase-llama-33"  # 使用一个可能存在的默认模型名称
    os.environ["LOCAL_VLLM_MODEL"] = default_model
    logging.info(f"LOCAL_VLLM_MODEL not set, using default: {default_model}")


def task_done_exception_logging_callback(future):
    try:
        future.result()
    except Exception as e:
        try:
            # Python 3.10+ 版本
            error_info = "".join(traceback.format_exception(e))
        except TypeError:
            # 旧版本兼容
            exc_type, exc_value, exc_traceback = sys.exc_info()
            error_info = "".join(
                traceback.format_exception(exc_type, exc_value, exc_traceback)
            )
        logging.error(f"Task failed with error: {error_info}")


# 最终的 prompt 的 token数量限制
def get_llm_context_length(openai_model: OpenAIModel = None, llm_provider: str = None):
    if settings.IS_USE_LOCAL_VLLM:
        if settings.LLM_CONTEXT_LENGTH:
            # 限制对话时的 prompt 长度，预留给引用生成功能，生成引用时会使用更多的 token；实际 case: message token: 31575 -> 33304
            context_length = settings.LLM_CONTEXT_LENGTH * 0.95 - 500
            return context_length
        return 8000
    if settings.QUESTION_USE_LOCAL_LLM or llm_provider == LLM_PROVIDER.LOCAL_LLM:
        return 3990
    if openai_model == OpenAIModel.GPT_4:
        return 7980  # space for ref indexes gen
    elif openai_model == OpenAIModel.GPT_35_TURBO:
        return 14000
    elif openai_model == OpenAIModel.GPT_4_TURBO_PREVIEW:
        return 15000
    elif (
        openai_model == OpenAIModel.GPT_4_OMNI
        or openai_model.startswith("gpt-4o")
        or openai_model == OpenAIModel.CLAUDE_35_SONNET
        or openai_model == OpenAIModel.CLAUDE_37_SONNET
        or openai_model == OpenAIModel.CLAUDE_37_SONNET_THINKING
        or openai_model.startswith("claude")
    ):
        return 65536
    elif (
        openai_model == OpenAIModel.GPT_4_TURBO_PREVIEW
        or openai_model == OpenAIModel.GPT_4_1106_PREVIEW
        or openai_model.startswith("gpt-4-turbo")
    ):
        return 18000
    else:
        return 14000


if settings.OPENAI_JUST_AZURE:
    logging.info("Just use azure openai")

# OPENAI_JUST_AZURE为False才启动OpenAI和Azure互切机制
using_azure_openai = settings.OPENAI_JUST_AZURE


# http_client = httpx.AsyncClient(http2=True, verify=False)


def _change_using_azure_openai():
    # 非仅使用Azure OpenAI和开启意图并发检测时才切换
    if not settings.OPENAI_JUST_AZURE and settings.CONCURRENT_INTENT_DETECT:
        global using_azure_openai
        using_azure_openai = not using_azure_openai


def _is_using_azure_openai():
    return using_azure_openai


def retry_async(max_retries: int = 2, delay: float = 0.1):
    """
    重试装饰器,异步函数
    重试逻辑,在发生异常时切换模型提供商并对func进行重试
    """

    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            retries = 0
            final_exception = None
            while retries < max_retries:
                try:
                    return await func(*args, **kwargs)
                except openai.OpenAIError as e:
                    retries += 1
                    if settings.DEBUG:
                        raise e
                    final_exception = e
                    llm_provider = (
                        LLM_PROVIDER.AZURE
                        if _is_using_azure_openai()
                        else LLM_PROVIDER.OPENAI
                    )
                    logging.warning(
                        f"exception on {llm_provider} error:{e}, retry another provider"
                    )
                    _change_using_azure_openai()
                    time.sleep(delay)
            raise final_exception

        return wrapper

    return decorator


class RedisChatMessageHistory:
    """Chat message history stored in a Redis database."""

    def __init__(
        self,
        key_prefix: str = "message_store:",
        ttl: Optional[int] = None,
    ):
        try:
            import redis
        except ImportError:
            raise ImportError(
                "Could not import redis python package. "
                "Please install it with `pip install redis`."
            )

        try:
            self.redis_client = settings.RedisClient.get_client()
        except redis.exceptions.ConnectionError as error:
            logging.error(error)

        self.key_prefix = key_prefix
        self.ttl = ttl

    @property
    def key(self) -> str:
        """Construct the record key to use"""
        return self.key_prefix

    # @property
    async def messages(self, session_id) -> List[BaseMessage]:  # type: ignore
        """Retrieve the messages from Redis"""
        _items = await self.redis_client.lrange(self.key + session_id, -10, -1)
        items = [json.loads(m) for m in _items[::-1]]
        msg = messages_from_dict(items)
        return msg

    async def add_message(self, session_id: str, message: BaseMessage) -> None:
        """Append the message to the record in Redis"""
        await self.redis_client.lpush(
            self.key + session_id, json.dumps(_message_to_dict(message))
        )
        if self.ttl:
            await self.redis_client.expire(self.key + session_id, self.ttl)

    async def get_stamp_session_id(self, session_id: str) -> str:
        """Append the message to the record in Redis"""
        return await self.redis_client.get(self.key + session_id)

    async def add_stamp_session_id(
        self, session_id: str, stamp_session_id: str
    ) -> None:
        """Append the message to the record in Redis"""
        await self.redis_client.set(self.key + session_id, stamp_session_id)
        if self.ttl:
            await self.redis_client.expire(self.key + session_id, self.ttl)

    async def clear(
        self,
        session_id: str,
    ) -> None:
        """Clear session memory from Redis"""
        await self.redis_client.delete(self.key + session_id)


class EmptyTextProofAzureChatOpenAI(AzureChatOpenAI):
    async def agenerate(
        self,
        messages: List[List[BaseMessage]],
        stop: Optional[List[str]] = None,
        callbacks: Callbacks = None,
        *,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        run_name: Optional[str] = None,
        **kwargs: Any,
    ) -> LLMResult:
        """Top Level call"""
        params = self._get_invocation_params(stop=stop, **kwargs)
        options = {"stop": stop}

        callback_manager = AsyncCallbackManager.configure(
            callbacks,
            self.callbacks,
            self.verbose,
            tags,
            self.tags,
            metadata,
            self.metadata,
        )

        run_managers = await callback_manager.on_chat_model_start(
            dumpd(self),
            messages,
            invocation_params=params,
            options=options,
            name=run_name,
        )

        results = await asyncio.gather(
            *[
                self._agenerate_with_cache(
                    m,
                    stop=stop,
                    run_manager=run_managers[i] if run_managers else None,
                    **kwargs,
                )
                for i, m in enumerate(messages)
            ],
            return_exceptions=True,
        )
        exceptions = []
        for i, res in enumerate(results):
            if isinstance(res, BaseException):
                if run_managers:
                    await run_managers[i].on_llm_error(res)
                exceptions.append(res)
        if exceptions:
            # ---------------- retry start ----------------
            if len(exceptions) == 1 and not settings.OPENAI_JUST_AZURE:
                logging.warning(
                    f"azure generation exception {exceptions}, retry with openai. using: {self.model_name}, messages: {messages}"
                )
                openai_chat_model = get_chat_model(
                    model=OpenAIModel(self.model_name),
                    llm_provider=LLM_PROVIDER.OPENAI,
                    max_tokens=self.max_tokens,
                    temperature=self.temperature,
                    max_retries=self.max_retries,
                    request_timeout=self.request_timeout,
                )
                openai_res = await openai_chat_model.agenerate(
                    messages, stop, callbacks, **kwargs
                )
                return openai_res
            # ---------------- retry end ---------------
            else:
                if run_managers:
                    await asyncio.gather(
                        *[
                            run_manager.on_llm_end(
                                LLMResult(
                                    generations=[res.generations],
                                    llm_output=res.llm_output,
                                )
                            )
                            for run_manager, res in zip(run_managers, results)
                            if not isinstance(res, Exception)
                        ]
                    )
                raise exceptions[0]
        flattened_outputs = [
            LLMResult(generations=[res.generations], llm_output=res.llm_output)
            for res in results
        ]
        llm_output = self._combine_llm_outputs([res.llm_output for res in results])
        generations = [res.generations for res in results]
        # ---------------- retry start ----------------
        if (
            len(generations) == 1
            and not generations[0][0].text
            and not settings.OPENAI_JUST_AZURE
        ):
            logging.warning(
                f"azure generation not content, retry with openai. using: {self.model_name}, messages: {messages}"
            )
            openai_chat_model = get_chat_model(
                model=OpenAIModel(self.model_name),
                llm_provider=LLM_PROVIDER.OPENAI,
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                max_retries=self.max_retries,
                request_timeout=self.request_timeout,
            )
            openai_res = await openai_chat_model.agenerate(
                messages, stop, callbacks, **kwargs
            )
            return openai_res
        # ---------------- retry end ----------------
        else:
            output = LLMResult(generations=generations, llm_output=llm_output)
            await asyncio.gather(
                *[
                    run_manager.on_llm_end(flattened_output)
                    for run_manager, flattened_output in zip(
                        run_managers, flattened_outputs
                    )
                ]
            )
            if run_managers:
                output.run = [
                    RunInfo(run_id=run_manager.run_id) for run_manager in run_managers
                ]
            return output


@trace()
def get_chat_model(
    # 2024.3月 model 的类型更改为 OpenAIModel，但是实际上运行还是str
    model: OpenAIModel = None,
    llm_provider: str = None,
    max_tokens: int = 600,
    temperature: float = 0,
    max_retries: int = 1,
    request_timeout: int = settings.LLM_TIMEOUT if settings.IS_USE_LOCAL_VLLM else 10,
    **kwargs: Any,
) -> ChatOpenAI:
    """
    获取langchain chat模型
    """
    # 确保model有值
    if model is None:
        model = OpenAIModel.default()
        logging.info(f"No model provided, using default model: {model}")
    token_params = {}
    if "max_completion_tokens" in kwargs:
        max_completion_tokens = kwargs.pop("max_completion_tokens")
        if not isinstance(max_completion_tokens, int):
            raise ValueError("max_completion_tokens must be an integer")
        token_params["max_completion_tokens"] = max_completion_tokens
    else:
        token_params["max_tokens"] = max_tokens
    if not llm_provider:
        if settings.QUESTION_USE_LOCAL_LLM or llm_provider == LLM_PROVIDER.LOCAL_LLM:
            llm_provider = LLM_PROVIDER.LOCAL_LLM
        elif isinstance(model, OpenAIModel) and str(model.value).startswith("claude"):
            llm_provider = LLM_PROVIDER.CLAUDE
        elif _is_using_azure_openai():
            llm_provider = LLM_PROVIDER.AZURE
        # 检查是否为GPT或O3模型
        elif isinstance(model, OpenAIModel) and (
            str(model.value).startswith("gpt") or 
            str(model.value).startswith("o3")
        ):
            llm_provider = LLM_PROVIDER.OPENAI
        # 检查是否为Gemini模型
        elif isinstance(model, OpenAIModel) and str(model.value).startswith("gemini"):
            llm_provider = LLM_PROVIDER.GEMINI
        else:
            # 默认使用Claude
            llm_provider = LLM_PROVIDER.CLAUDE
    # 确保如果使用本地模型，进的是OPENAI分支的逻辑
    if settings.IS_USE_LOCAL_VLLM:
        llm_provider = LLM_PROVIDER.OPENAI
    if llm_provider == LLM_PROVIDER.AZURE:
        if (
            model == OpenAIModel.GPT_35_TURBO
            or model == OpenAIModel.GPT_4
            or model == OpenAIModel.GPT_35_TURBO_16K
        ):
            model = OpenAIModel.GPT_4_OMNI

        model_config = settings.AZURE_MODEL.get(model)
        if not model_config:
            raise ValueError(f"invalid model: {model}")

        model_info = {
            "deployment_name": model_config.deployment_name,
            "openai_api_key": model_config.identity_key,
            "azure_endpoint": model_config.endpoints,
            "openai_api_version": model_config.version,
            "model_name": model if isinstance(model, str) else model.value,
        }
        return EmptyTextProofAzureChatOpenAI(
            **model_info,
            temperature=temperature,
            max_retries=max_retries,
            request_timeout=request_timeout,
            # http_client=http_client,
            **token_params,  # 动态传入token相关参数
            **kwargs,
        )

    elif llm_provider == LLM_PROVIDER.CLAUDE:
        # 使用Claude模型
        model_name = model if isinstance(model, str) else model.value
        logging.info(
            f"get_chat_model-->llm_provider: {llm_provider}, model: {model}, model_name: {model_name}"
        )

        return ChatOpenAI(
            model_name=model_name,
            openai_api_key=settings.CLAUDE_API_KEY,
            temperature=temperature,
            max_retries=max_retries,
            request_timeout=request_timeout,
            **token_params,
            **kwargs,
        )
    elif llm_provider == LLM_PROVIDER.OPENAI:
        if model == OpenAIModel.GPT_4_TURBO_PREVIEW:
            model_name = settings.GPT_4_TURBO_PREVIEW or (model if isinstance(model, str) else model.value)
        elif model == OpenAIModel.GPT_35_TURBO:
            model_name = settings.GPT_35_TURBO or (model if isinstance(model, str) else model.value)
        else:
            model_name = model if isinstance(model, str) else model.value

        if (
            model == OpenAIModel.GPT_35_TURBO
            or model == OpenAIModel.GPT_4
            or model == OpenAIModel.GPT_35_TURBO_16K
        ):
            model_name = OpenAIModel.GPT_4_OMNI.value
        # todo delete useless code
        if model == OpenAIModel.GEMINI_15_PRO_EXP_0801:
            if not GEMINI_EXP:
                raise ValueError(
                    f"model {model} is used without enable GEMINI_EXP flag"
                )
            model_name = OpenAIModel.GEMINI_15_PRO_EXP_0801.value

        if settings.IS_USE_LOCAL_VLLM:
            # 获取本地模型名称，如果环境变量未设置，使用安全的默认值
            model_name = os.environ.get("LOCAL_VLLM_MODEL", "gbase-llama-33")
            model = OpenAIModel.LOCAL_MODEL
            logging.info(f"Using local LLM model: {model_name}")

        logging.info(
            f"get_chat_model-->llm_provider: {llm_provider}, model: {model}, model_name: {model_name}"
        )
        api_key = settings.OPENAI_API_KEY
        # todo delete useless code
        if model_name.startswith("claude"):
            api_key = settings.CLAUDE_API_KEY
        elif model_name.startswith("gemini"):
            api_key = settings.GEMINI_API_KEY
        if model == OpenAIModel.CLAUDE_37_SONNET_THINKING:
            model_name = OpenAIModel.CLAUDE_37_SONNET.value
            temperature = 1
            if "model_kwargs" not in kwargs:
                kwargs["model_kwargs"] = {}
            kwargs["model_kwargs"]["extra_body"] = {
                "thinking": {"budget_tokens": 1024, "type": "enabled"}
            }
        elif model == OpenAIModel.O3_MINI_THINKING:
            model_name = OpenAIModel.O3_MINI.value
            if "model_kwargs" not in kwargs:
                kwargs["model_kwargs"] = {}
            kwargs["model_kwargs"]["extra_body"] = {"reasoning_effort": "high"}
        return ChatOpenAI(
            model_name=model_name,
            openai_api_key=api_key,
            temperature=temperature,
            max_retries=max_retries,
            request_timeout=request_timeout,
            # http_client=http_client,
            **token_params,  # 动态传入token相关参数
            **kwargs,
        )
    elif llm_provider == LLM_PROVIDER.GEMINI:
        model_name = model if isinstance(model, str) else model.value
        logging.info(
            f"get_chat_model-->llm_provider: {llm_provider}, model: {model}, model_name: {model_name}"
        )
        api_key = settings.GEMINI_API_KEY
        return ChatOpenAI(
            model_name=model_name,
            openai_api_key=api_key,
            temperature=temperature,
            max_retries=max_retries,
            request_timeout=request_timeout,
            **token_params,
            **kwargs,
        )
    else:
        raise ValueError(f"invalid llm provider: {llm_provider}")


lang_text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=1500,
    chunk_overlap=0,
)


@trace()
def _lang_detect_by_local(text: str):
    identifier = LanguageIdentifier.from_pickled_model(MODEL_FILE, norm_probs=True)
    result = identifier.rank(text)

    # 将结果转换为数组，包含每个语言的打分
    lang_scores = [{"language": lang, "score": score} for lang, score in result[:3]]

    return lang_scores


if __name__ == "__main__":
    print(_lang_detect_by_local("H.I.S.Mobile株式会社所在地"))  # ('zh', 0.9999993)
    print(_lang_detect_by_local("打印机"))  #


@trace()
async def _lang_detect_by_llm(
    chain: RunnableSerializable[dict, BaseMessage], ai_language: str, query: str
):
    rsp = None
    try:
        # logging.info(
        #     chain.get_prompts()[0].format(ai_language=ai_language, query=query)
        # )
        rsp = await chain.ainvoke({"ai_language": ai_language, "query": query})
        token = num_tokens_from_string(rsp.content)
        json_str = extract_first_valid_json(rsp.content)
        resp = json.loads(json_str)
        logging.info(
            f"llm language detect response:{resp}, token:{token}, raw rsp: {rsp}"
        )
        return resp.get("language", None), token
    except Exception as e:
        sentry_sdk_capture_exception(e)
        logging.info(f"language detect response:{rsp} error:{e}")
        raise e


@trace()
async def language_detect(
    content: str,
    user_id: str = None,
    ai_language: str = None,
    ai_id: str = None,
    trust_small_model=False,
):
    start_time = time.time()
    if not content:
        raise ValueError("content is empty")
    chunks = lang_text_splitter.split_text(content)
    prompt_info = language_detect_prompt
    chat_prompt = ChatPromptTemplate.from_messages(
        [
            SystemMessagePromptTemplate.from_template(
                template=prompt_info["system"],
                input_variables=["ai_language", "query"],
            ),
            HumanMessagePromptTemplate.from_template(
                template=prompt_info["user"],
                input_variables=["ai_language", "query"],
            ),
        ]
    )

    count = len(chunks)
    if count <= 5:
        query = "".join(chunks)
    else:
        # 取随机数
        random_count = random.randint(5, min(10, count))
        random_chunks = random.sample(chunks, random_count)
        query = "".join(random_chunks)

    tokens = num_tokens_from_string(
        chat_prompt.format(ai_language=ai_language, query=query)
    )

    if tokens > 8000:
        query = query[: int(len(query) * 0.7)]
    try:
        lang_scores = await asyncio.to_thread(_lang_detect_by_local, query)

        if trust_small_model:
            # 如果数组第1个元素是ja，直接返回
            if (
                lang_scores
                and lang_scores[0]["language"] == "ja"
                and lang_scores[0]["score"] == 1
            ):
                return "ja", 0

            # 如果数组第1个元素得分1，直接返回
            # sparticle是一家什么公司?请用韩文来介绍， should be kr，exactly zh
            if lang_scores and lang_scores[0]["score"] == 1:
                return lang_scores[0]["language"], 0

        # if settings.IS_USE_LOCAL_VLLM:
        #     return lang_scores[0]["language"], lang_scores[0]["score"]

        logging.info(
            "language_detect by local: total time {}s, lang: {}".format(
                time.time() - start_time, lang_scores[0]["language"]
            )
        )

        start_time = time.time()
        chat_model = get_chat_model()
        chain = chat_prompt | chat_model
        langid, token = await _lang_detect_by_llm(chain, ai_language, query)
        logging.info(
            f"language_detect by llm: total time {time.time() - start_time}s, result: {langid}, token: {token}"
        )
        return langid, tokens + token

    except Exception as e:
        logging.error(f"language detect error:{e}, {traceback.format_exc()}")
        return None, 0


if settings.IS_USE_LOCAL_VLLM:
    CONTEXT_DOC_COUNT_LIMIT = 1000
else:
    CONTEXT_DOC_COUNT_LIMIT = 12


def deduplicate_by_doc_id_chunk_index(embeddings):
    all_embeddings_have_doc_id = all(
        embedding.metadata and "doc_id" in embedding.metadata
        for embedding in embeddings
    )
    if not all_embeddings_have_doc_id:
        logging.warning(
            f"not all embeddings have doc_id, len: {len(embeddings)}, embeddings: {embeddings[:1]}"
        )
    else:
        embeddings.sort(key=lambda x: -x.score)
        sorted_embeddings = embeddings
        if all_embeddings_have_doc_id and getattr(sorted_embeddings[0], "dataset_id"):
            sorted_embeddings = sorted(
                embeddings,
                key=lambda x: (
                    -x.score,
                    x.metadata.get("doc_id"),
                    x.metadata.get("chunk_index"),
                ),
            )

        # Deduplicate based on doc_id and chunk_index
        unique_embeddings = []
        seen = set()
        for embedding in sorted_embeddings:
            key = (
                embedding.metadata.get("doc_id"),
                embedding.metadata.get("chunk_index"),
            )
            if key not in seen:
                seen.add(key)
                unique_embeddings.append(embedding)

        return unique_embeddings

    return embeddings


l_tokenizer = None
if settings.IS_USE_LOCAL_VLLM and settings.LOCAL_LLM_MODEL:
    from huggingface_hub import login
    from transformers import AutoTokenizer
    from mygpt import settings

    login(token=settings.HF_TOKEN)

    l_tokenizer = AutoTokenizer.from_pretrained(
        settings.LOCAL_LLM_MODEL,
        trust_remote_code=True,
    )
    logging.info(f"local llm tokenizer: {l_tokenizer}")


# -----------------------------------
# doc_id: 1568f359-555a-4bc6-b823-f73853c73981
# source: http://A-product.com
# chunk_indexes: [0, 1, 3]
#
# product A
#
# ... content skipped...
# weight: 1g
# -----------------------------------
# doc_id: c66eb649-847d-48b7-9d40-98a1b927425e
# source: B-product.pdf
# chunk_indexes: [1, 5, 6]
#
# product B
#
# ... content skipped...
# price: $1
# -----------------------------------
@trace()
async def get_final_chat_context(
    openai_model: Optional[OpenAIModel],
    embeddings: List[Embeddings],
    used_tokens: Optional[int] = None,
    llm_provider: str = None,
    enable_images: bool = False,
):
    """
    Args:
    openai_model (str): final question model
    sorted_embeddings (List[Embeddings]): A list of Embeddings objects.
    used_tokens (Optional[int], optional): Number of tokens used so far. Defaults to None.

    Returns:
    [type]: [description]
    """
    if len(embeddings) == 0:
        empty = "No relevant learned knowledge found."
        tokens = num_tokens_from_string(empty)
        return empty, tokens, [], {}
    new_tokens = 0
    summaries = ""
    prev_chunk_index: int = -1
    # If doc_id in all metadata, then doc_id and chunk_index should not be None
    all_embeddings_have_doc_id = all(
        embedding.metadata and "doc_id" in embedding.metadata
        for embedding in embeddings
    )
    sorted_embeddings = embeddings
    if all_embeddings_have_doc_id and getattr(sorted_embeddings[0], "dataset_id"):
        sorted_embeddings = sorted(
            embeddings,
            key=lambda x: (
                -x.score,
                x.metadata.get("doc_id"),
                x.metadata.get("chunk_index"),
            ),
        )
    else:
        sorted_embeddings = sorted(embeddings, key=lambda x: (-x.score,))
    # fetch the necessary docs for first doc
    # for dev - 20aa811c-1370-45c3-a19a-99ba137ef111, 请详细列出使用打印机前的注意事项, 26 points answer
    if all_embeddings_have_doc_id:
        distinct_doc_id_list = []
        doc_id_chunk_indexes_map = {}
        doc_id_score_map = {}
        for embedding in sorted_embeddings:
            doc_id = embedding.metadata.get("doc_id")
            if doc_id not in distinct_doc_id_list:
                distinct_doc_id_list.append(doc_id)
                doc_id_chunk_indexes_map[doc_id] = []
                doc_id_score_map[doc_id] = embedding.score
            chunk_index = embedding.metadata.get("chunk_index")
            doc_id_chunk_indexes_map[doc_id].append(chunk_index)
        first_doc_chunk_count = 0
        first_doc_id = sorted_embeddings[0].metadata.get("doc_id")
        first_chunk_index = sorted_embeddings[0].metadata.get("chunk_index")
        for i in sorted_embeddings:
            if i.metadata.get("doc_id") == sorted_embeddings[0].metadata.get("doc_id"):
                first_doc_chunk_count += 1
        to_fetch = []
        if first_doc_chunk_count == 1:
            to_fetch = [
                (first_doc_id, first_chunk_index + 1),
                (first_doc_id, first_chunk_index + 2),
            ]
        elif first_doc_chunk_count == 2:
            second_chunk_index = sorted_embeddings[1].metadata.get("chunk_index")
            if first_chunk_index == second_chunk_index - 1:
                to_fetch = [
                    (first_doc_id, second_chunk_index + 1),
                    (first_doc_id, second_chunk_index + 2),
                ]
            elif first_chunk_index == second_chunk_index - 2:
                to_fetch = [
                    (first_doc_id, first_chunk_index + 1),
                    (first_doc_id, second_chunk_index + 1),
                ]
            elif first_chunk_index == second_chunk_index - 3:
                to_fetch = [
                    (first_doc_id, first_chunk_index + 1),
                    (first_doc_id, first_chunk_index + 2),
                    (first_doc_id, second_chunk_index + 1),
                ]
            else:
                to_fetch = [
                    (first_doc_id, first_chunk_index + 1),
                    (first_doc_id, second_chunk_index + 1),
                ]
        else:  # first_doc_chunk_count >= 3
            # dev: 4983518c-0b32-4144-9d25-34172d6092d5
            # His Mobileの440パッケージの5GBのデータはいくらですか？
            # for every chunk, fetch the next chunk, if continuous chunks, fetch the next 2 chunks
            maximum_fetch_count = 7
            prev_chunk_index = first_chunk_index
            cont_c = False
            fetch_count = 0
            prev_chunk = sorted_embeddings[0]
            for i in sorted_embeddings[1:]:
                this_chunk = i
                if fetch_count >= maximum_fetch_count:
                    break
                if not i.metadata.get("doc_id") == sorted_embeddings[0].metadata.get(
                    "doc_id"
                ):
                    break
                this_chunk_index = i.metadata.get("chunk_index")
                if prev_chunk_index >= this_chunk_index:
                    # just skip
                    continue
                if this_chunk_index == prev_chunk_index + 1:
                    prev_chunk_index = this_chunk_index
                    cont_c = True
                    continue
                elif this_chunk_index == prev_chunk_index + 2:
                    to_fetch.append((first_doc_id, prev_chunk_index + 1))
                    cont_c = True
                    prev_chunk_index = prev_chunk_index + 2
                    fetch_count += 1
                    continue
                else:
                    if cont_c:
                        to_fetch.append(
                            (first_doc_id, prev_chunk_index + 1),
                        )
                        to_fetch.append(
                            (first_doc_id, prev_chunk_index + 2),
                        )
                        fetch_count += 2
                        cont_c = False
                    elif "rrf_score" in prev_chunk.metadata:
                        if (
                            prev_chunk.metadata["rrf_score"]
                            == prev_chunk.metadata["rrf_max_score"]
                        ):
                            to_fetch.append(
                                (first_doc_id, prev_chunk_index + 1),
                            )
                            to_fetch.append(
                                (first_doc_id, prev_chunk_index + 2),
                            )
                            fetch_count += 2
                        elif (
                            prev_chunk.metadata["rrf_score"]
                            >= 0.8 * prev_chunk.metadata["rrf_max_score"]
                        ):
                            to_fetch.append((first_doc_id, prev_chunk_index + 1))
                            fetch_count += 1
                    prev_chunk_index = this_chunk_index
                    prev_chunk = this_chunk
        # for second and third doc,进行 1-2 间隔fetch，doc 2最多5个chunks, doc 3最多3个chunks
        # 1-2 间隔fetch: [chunk_indexes: 1..4, fetch 2,3]; [chunk_indexes: 1..3, fetch 2]
        # fetches for second doc
        if len(distinct_doc_id_list) >= 2:
            max_second_fetch_count = 5
            second_doc_id = distinct_doc_id_list[1]
            second_doc_chunk_indexes = doc_id_chunk_indexes_map[second_doc_id]
            second_fetched_count = 0
            prev_i = second_doc_chunk_indexes[0]
            for i in second_doc_chunk_indexes[1:]:
                if second_fetched_count >= max_second_fetch_count:
                    break
                if i - prev_i == 1 and i + 1 not in second_doc_chunk_indexes:
                    to_fetch.append((second_doc_id, i + 1))
                    second_fetched_count += 1
                    if i + 2 not in second_doc_chunk_indexes:
                        to_fetch.append((second_doc_id, i + 2))
                        second_fetched_count += 1
                elif i - prev_i == 2:
                    to_fetch.append((second_doc_id, prev_i + 1))
                    second_fetched_count += 1
                elif i - prev_i == 3:
                    to_fetch.append((second_doc_id, prev_i + 1))
                    to_fetch.append((second_doc_id, prev_i + 2))
                    second_fetched_count += 2
                prev_i = i
        if len(distinct_doc_id_list) >= 3:
            max_third_fetch_count = 3
            third_doc_id = distinct_doc_id_list[2]
            third_doc_chunk_indexes = doc_id_chunk_indexes_map[third_doc_id]
            third_fetched_count = 0
            prev_i = third_doc_chunk_indexes[0]
            for i in third_doc_chunk_indexes[1:]:
                if third_fetched_count >= max_third_fetch_count:
                    break
                if i - prev_i == 2:
                    to_fetch.append((third_doc_id, prev_i + 1))
                    third_fetched_count += 1
                elif i - prev_i == 3:
                    to_fetch.append((third_doc_id, prev_i + 1))
                    to_fetch.append((third_doc_id, prev_i + 2))
                    third_fetched_count += 2
                prev_i = i

        if to_fetch:
            time1 = time.time()
            new_chunks = await fetch_from_opensearch(
                sorted_embeddings[0].dataset_id, to_fetch=to_fetch
            )
            logging.info(
                f"get_final_chat_context fetch {len(to_fetch)} chunks cost {time.time() - time1}s"
            )
            if new_chunks:
                for chunk in new_chunks:
                    if chunk:
                        chunk.score = doc_id_score_map[chunk.metadata["doc_id"]]
                        sorted_embeddings.append(chunk)
                    else:
                        logging.warning(
                            f"fetched chunks contains None: {to_fetch}, {new_chunks}"
                        )
                sorted_embeddings = sorted(
                    sorted_embeddings,
                    key=lambda x: (
                        -x.score,
                        x.metadata.get("doc_id"),
                        x.metadata.get("chunk_index"),
                    ),
                )
    deduplicate_by_doc_id_chunk_index(sorted_embeddings)
    reference_list = []

    async def append_reference(embedding: Embeddings):
        source = embedding.metadata.get("source")
        title: str = embedding.metadata.get("title", "")
        try:
            if (title is None or not title.strip()) and source:
                title = os.path.basename(source)
        except Exception as e:
            logging.warning(f"get title from source error: {e}")
        page_numbers = embedding.metadata.get("page_numbers", [])
        file_id = embedding.metadata.get("file_id")
        # if source == "GPTBASE_FAQ" or source == "GPTBASE_FAQ_QUESTION":
        #     return
        lark_file = None
        if file_id:
            vector_file = await VectorFile.get_or_none(id=file_id).prefetch_related(
                "lark_file"
            )
            if vector_file:
                lark_file = vector_file.lark_file
        reference_index = len(reference_list)
        ref = {
            "id": embedding.id,
            "score": embedding.score,
            "dataset_id": embedding.dataset_id,
            "storage_type": embedding.storage_type,
            "file_id": file_id,
            "index": reference_index,
            "source": source,
            "title": title,
            "content": embedding.text,
            "page_numbers": page_numbers,
            "chunk_index": embedding.metadata.get("chunk_index"),
        }
        if lark_file:
            # ref["file_type"] = vector_file.file_type.value
            ref["lark_file"] = {
                "name": lark_file.name,
                "type": lark_file.type,
                "url": lark_file.url,
            }
        reference_list.append(ref)

    doc_index = 0
    # reset
    prev_doc_id: str = ""
    resources = {}
    image_resources = {}
    image_index = 1
    score_sum = 0
    score_sum_threshold = 10000  # unlimited
    # if settings.IS_USE_LOCAL_VLLM:
    #     score_sum_threshold = 10
    # else:
    #     score_sum_threshold = 8

    question_tracker.__track_embeddings_results(sorted_embeddings, "final_sorted_embeddings")

    for embedding in sorted_embeddings:
        original = ""
        if all_embeddings_have_doc_id:
            # if embedding.score < 0.0001:
            #     # 0.00012731104 https://dev.gbase.ai/bot/4e9db258-d137-42fc-9fa6-ec60a26f2b12
            #     # 一周中什么课占比最多
            #     logging.info("embedding.score < 0.0001, break")
            #     break
            doc_id = embedding.metadata.get("doc_id")
            file_id = embedding.metadata.get("file_id")
            chunk_index = embedding.metadata.get("chunk_index")
            if doc_id == prev_doc_id:
                if chunk_index == prev_chunk_index + 1:
                    content_item = f"""{original or embedding.text}"""
                else:
                    content_item = f"""\n\n... (chunk {prev_chunk_index + 1} - {chunk_index - 1} skipped) ...\n\n{original or embedding.text}"""
            else:
                score_sum += embedding.score
                if score_sum > score_sum_threshold:
                    logging.info(f"score_sum > {score_sum_threshold}, break")
                    break
                if doc_index == CONTEXT_DOC_COUNT_LIMIT:
                    break
                # doc_index is 1, 2...
                doc_index += 1
                title = embedding.metadata.get("title")
                content_item = f"\n\n================== DOCUMENT *{title}* BEGIN ==================\n\n"
                # new doc
                source = embedding.metadata.get("source")
                # get chunk_index list
                chunk_indexes = []
                for embedding1 in sorted_embeddings:
                    this_doc_id = embedding1.metadata.get("doc_id")
                    if this_doc_id == doc_id:
                        this_index = embedding1.metadata.get("chunk_index")
                        chunk_indexes.append(this_index)
                content_item += f"### Metadata\n"
                content_item += f"doc_index: {doc_index}\n"
                content_item += f"score: {embedding.score}\n"
                content_item += f"source: {source if source else 'Unknown'}\n"
                content_item += f"title: {title if title else 'None'}\n"
                content_item += f"doc_id: {doc_id}\n"
                content_item += f"file_id: {file_id}\n"
                content_item += f"chunk_indexes: {str(chunk_indexes)}\n\n\n"
                content_item += f"### Content\n{original or embedding.text}\n\n\n"
            # if (
            #     embedding_index == len(embeddings) - 1
            #     or embeddings[embedding_index + 1].metadata.get("doc_id") != doc_id
            # ):
            #     content_item += f"\n\n================== DOCUMENT {doc_index} END ==================\n\n"
            prev_doc_id = doc_id
            prev_chunk_index = chunk_index
        else:
            content_item = f'''"""{original or embedding.text}"""\n\n'''

        if enable_images:
            images = extract_image_from_content(content_item)
            for image in images:
                # 替换图片链接为 https://uuid
                uuid = image.get("uuid")
                url = image.get("url")
                content_item = replace_image_url_to_uuid(content_item, url, uuid)
                image_resources[uuid] = url
                image_index += 1
        else:
            content_item = replace_markdown_images(content_item)

        if settings.IS_USE_LOCAL_VLLM and settings.LOCAL_LLM_MODEL:
            content_tokens = len(l_tokenizer.encode(content_item))
        else:
            content_tokens = num_tokens_from_string(content_item)
        new_tokens += content_tokens

        if used_tokens + new_tokens >= get_llm_context_length(
            openai_model, llm_provider
        ):
            break

        await append_reference(embedding)
        summaries += content_item
    logging.info(
        f"token for prompt used: {used_tokens} + new: {new_tokens}, sum total: {used_tokens + new_tokens}, context cnt: {doc_index}"
    )

    question_tracker.__track_reference_results(reference_list, "reference_list")

    if image_resources:
        resources["image"] = image_resources

    reference_list = await _reference_list_faq_edit(reference_list)
    return (summaries, new_tokens, reference_list, resources)


class ChainResponseAsyncHandler(AsyncCallbackHandler):
    """
    langchain回调处理器 - 处理非流式输出
    """

    def __init__(
        self,
        session_id=None,
        message_id=None,
        final_question_use_gpt4=False,
        human_message: HumanMessage = None,
        messages: List[BaseMessage] = None,
        event: asyncio.Event = None,
        resources=None,
        openai_model: OpenAIModel = None,
    ) -> None:
        super().__init__()
        self.messages = messages
        self.message_id = message_id
        self.session_id = session_id
        self.final_question_use_gpt4 = final_question_use_gpt4
        self.human_message = human_message
        self.event = event
        self.resources = resources or {}
        self.openai_model = openai_model

    async def on_llm_new_token(self, token: str, **kwargs: Any) -> None:
        # 流式输出，统计第1个字符接收到的时长
        if self.event and not self.event.is_set():
            self.event.set()

    async def on_llm_end(self, response, **kwargs):
        # 非流式输出，统计完整消息接收到的时长
        if self.event and not self.event.is_set():
            self.event.set()
        if not self.message_id:
            return
        answer = ""
        total_tokens = 0
        try:
            answer = response.generations[0][0].message.content
            if not answer:
                logging.warning(
                    f"empty answer for session_id:{self.session_id} message_id: {self.message_id}"
                )
                return

            image_resources = self.resources.get("image", {})
            answer = replace_md_uuid_images(answer, image_resources)

            # 聊天记录缓存
            chat_memory = RedisChatMessageHistory(ttl=3600)
            await chat_memory.add_message(self.session_id, self.human_message)
            await chat_memory.add_message(self.session_id, AIMessage(content=answer))
            total_tokens += num_tokens_for_langchain_messages(self.messages)
            tokens = num_tokens_from_string(answer)
            total_tokens += tokens
            if self.openai_model == OpenAIModel.GPT_4:
                # estimation from https://openai.com/pricing
                total_tokens *= 12
            elif self.openai_model == OpenAIModel.GPT_4_TURBO_PREVIEW:
                total_tokens *= 4

            asyncio.create_task(
                save_to_quality_assessment_by_message_id(self.message_id)
            ).add_done_callback(task_done_exception_logging_callback)
        finally:
            asyncio.create_task(
                update_message_to_finished(
                    message_id=self.message_id, answer=answer, total_tokens=total_tokens
                )
            ).add_done_callback(task_done_exception_logging_callback)

    async def on_error(
        self, error: Exception | KeyboardInterrupt, **kwargs: Any
    ) -> None:
        # 大模型接口非流式返回异常
        if self.event and not self.event.is_set():
            self.event.set()


async def acquire_message_lock(message_id: str, timeout_ms: int = 10000) -> bool:
    """获取消息锁"""
    lock_key = f"message_lock:{message_id}"
    try:
        # 尝试获取Redis锁
        lock = await settings.RedisClient.get_client().set(
            lock_key, "1", nx=True, px=timeout_ms
        )
        return bool(lock)
    except Exception as e:
        logging.error(f"Error acquiring lock for message {message_id}: {str(e)}")
        return False


async def release_message_lock(message_id: str) -> None:
    """释放消息锁"""
    lock_key = f"message_lock:{message_id}"
    try:
        await settings.RedisClient.get_client().delete(lock_key)
    except Exception as e:
        logging.error(f"Error releasing lock for message {message_id}: {str(e)}")


async def update_message_to_finished(
    message_id: str, answer: str, total_tokens
) -> bool:
    if not message_id:
        logging.error(f"message_id is empty")
        return False

    if not await acquire_message_lock(message_id):
        logging.error(f"Failed to acquire lock for message: {message_id}")
        return False
    logging.info(f"message locked: {message_id}")
    try:
        message = await SessionMessage.filter(id=message_id).first()
        if not message:
            logging.error(f"message not found: {message_id}")
            return False

        if message.status == SessionMessageStatus.FINISHED:
            logging.error(f"message already finished: {message_id}")
            return False

        logging.info(f"Message status: {message_id} ==original== {message.status}")

        # 更新消息
        message.answer = answer
        message.status = SessionMessageStatus.FINISHED
        message.total_tokens = total_tokens
        await message.save(update_fields=["answer", "status", "total_tokens"])

        # 更新计数
        evaluate = await SessionEvaluate.filter(session_id=message.session_id).first()
        if evaluate:
            evaluate.message_count += 1
            await evaluate.save(update_fields=["message_count"])
        else:
            await SessionEvaluate.create(session_id=message.session_id, message_count=1)

        logging.info(
            f"update message to finished: {message_id} & add message count for session: {message.session_id}, status to {message.status}"
        )
        return True

    except Exception as e:
        logging.error(f"Error updating message {message_id} to finished: {str(e)}")
        return False
    finally:
        # 操作完成后释放锁
        logging.info(f"message unlocked: {message_id}")
        await release_message_lock(message_id)


async def check_llm_can_answer(question: str, answer: str) -> Tuple[bool, bool]:
    # if settings.IS_USE_LOCAL_VLLM:
    #     return True, True
    message = ChatPromptTemplate.from_messages(
        [
            SystemMessagePromptTemplate.from_template(
                template=determine_whether_can_answer_about_company,
                input_variables=["question", "answer"],
            )
        ]
    )
    # todo change this to other model
    chat_model = get_chat_model(
        request_timeout=(
            settings.LLM_TIMEOUT
            if settings.IS_USE_LOCAL_VLLM and settings.LLM_TIMEOUT
            else 120
        ),
        model=OpenAIModel.GPT_4_OMNI
    )
    chain = message | chat_model
    result = await chain.ainvoke(input={"question": question, "answer": answer})

    try:
        llm_can_answer, not_found_in_the_context = json.loads(result.content)
        if not isinstance(llm_can_answer, bool):
            logging.error(
                f"llm_can_answer is not a boolean: {llm_can_answer}, type: {type(llm_can_answer)}"
            )
            sentry_sdk_capture_exception(
                ValueError(
                    f"llm_can_answer is not a boolean: {llm_can_answer}, type: {type(llm_can_answer)}"
                )
            )
            llm_can_answer = bool(llm_can_answer)
        if not isinstance(not_found_in_the_context, bool):
            logging.error(
                f"not_found_in_the_context is not a boolean: {not_found_in_the_context}, type: {type(not_found_in_the_context)}"
            )
            sentry_sdk_capture_exception(
                ValueError(
                    f"not_found_in_the_context is not a boolean: {not_found_in_the_context}, type: {type(not_found_in_the_context)}"
                )
            )
            not_found_in_the_context = bool(not_found_in_the_context)
        show_reference_button = not not_found_in_the_context
    except Exception as e:
        single_line_question = question.replace("\n", "\\n")
        single_line_answer = answer.replace("\n", "\\n")
        logging.error(
            f"parse llm_can_answer error: {e}, result: {result.content}, answer: {single_line_answer}, question: {single_line_question}"
        )
        llm_can_answer = True
        show_reference_button = True
    logging.info(
        f"question: {question}, answer: {answer}, result: {result.content}, llm_can_answer: {llm_can_answer}, show_reference_button: {show_reference_button}"
    )
    return llm_can_answer, show_reference_button


async def check_llm_is_clarification(
    question: str, answer: str, clarification_type: List
) -> Union[bool, str]:
    # if settings.IS_USE_LOCAL_VLLM:
    #     return True, True

    message = ChatPromptTemplate.from_messages(
        [
            SystemMessagePromptTemplate.from_template(
                template=determine_whether_llm_answer_is_clarification,
                input_variables=["question", "answer", "clarification_type"],
            )
        ]
    )
    chat_model = get_chat_model(model=OpenAIModel.GPT_4_OMNI)
    chain = message | chat_model
    try:
        result = await chain.ainvoke(
            input={
                "question": question,
                "answer": answer,
                "clarification_type": clarification_type,
            }
        )
    except Exception as e:
        print(e)
    try:
        is_clarification = json.loads(result.content)
    except Exception as e:
        single_line_question = question.replace("\n", "\\n")
        single_line_answer = answer.replace("\n", "\\n")
        logging.error(
            f"parse is_clarification error: {e}, result: {result.content}, answer: {single_line_answer}, question: {single_line_question}"
        )
        is_clarification = "nonclarification"
    logging.info(
        f"question: {question}, answer: {answer}, result: {result.content}, is_clarification: {is_clarification}"
    )
    return is_clarification


class MyCallbackHanlder(AsyncIteratorCallbackHandler):
    """
    langchain回调处理器 - 处理流式输出
    """

    def __init__(
        self,
        message_id=None,
        is_faq: bool = False,
        tokens: int = 0,
        format=None,
        reference_list=None,
        recommend_list=None,
        resources=None,
        event: asyncio.Event = None,
        origin_question: str = "",
        user_intent: str = None,
        faq_child_node_list=None,
    ):
        super().__init__()
        self.message_id = message_id
        self.is_faq = is_faq
        self.tokens = tokens
        self.format = format
        self.pending_texts = []
        self.reference_list = reference_list
        self.recommend_list = recommend_list
        self.resources = resources or {}
        self.event = event
        self.origin_question = origin_question
        self.cache_links = []
        self.user_intent = user_intent
        self.faq_child_node_list = faq_child_node_list

    def handle_image_content(self, content: str, resources: dict[str, str]):
        if self.pending_texts:
            text = "".join(self.pending_texts)
            content = text + content
            self.pending_texts = []

        content = replace_md_uuid_images(content, resources, self.cache_links)

        image_index = ends_with_markdown_image_syntax_index(content)
        link_index = ends_with_markdown_link_syntax_index(content)
        if image_index > -1:
            ends_with_index = image_index
        else:
            ends_with_index = link_index

        if ends_with_index > -1:
            ends_text = content[ends_with_index:]
            content = content[:ends_with_index]
            # cache pending text
            self.pending_texts.append(ends_text)
        return content

    def handle_image_end(self, resources: dict[str, str]):
        content = "".join(self.pending_texts)
        self.pending_texts = []
        content = replace_md_uuid_images(content, resources, self.cache_links)
        ends_with_index = ends_with_markdown_image_syntax_index(content)
        if ends_with_index > -1:
            # 移除不完整的图片
            content = content[:ends_with_index]
        if content:
            return (
                self.create_stream_out(
                    message_type=StreamingMessageDataType.PLAIN_TEXT,
                    content=content,
                ).json(ensure_ascii=False)
                if self.format == StreamingMessageDataFormat.JSON_AST
                else content
            )

    def handle_content(self, content: str):
        image_resources = self.resources.get("image", {})
        content = self.handle_image_content(content, image_resources)

        return content

    def create_stream_out(
        self, message_type: StreamingMessageDataType, content: str | List | dict
    ):
        if message_type == StreamingMessageDataType.PLAIN_TEXT:
            content = self.handle_content(content)

        return StreamOut(
            message_id=self.message_id,
            message_type=message_type,
            content=content,
            use_faq=self.is_faq,
            tokens=self.tokens,
        )

    async def on_llm_new_token(self, token: str, **kwargs: Any) -> None:
        # 处理流式返回内容
        if token is None or token == "":
            return
        if self.format == StreamingMessageDataFormat.JSON_AST:
            rs = self.create_stream_out(
                message_type=StreamingMessageDataType.PLAIN_TEXT,
                content=token,
            ).json(ensure_ascii=False)
            await super().on_llm_new_token(rs + "\n", **kwargs)
        else:
            token = self.handle_content(token)
            await super().on_llm_new_token(token, **kwargs)

    async def on_error(
        self, error: Exception | KeyboardInterrupt, **kwargs: Any
    ) -> None:
        try:
            # 大模型接口流式返回异常
            if self.format == StreamingMessageDataFormat.JSON_AST:
                rs = self.create_stream_out(
                    message_type=StreamingMessageDataType.ERROR,
                    content=str(error),
                ).json(ensure_ascii=False)
                await super().on_llm_new_token(rs + "\n", **kwargs)
                return

            await super().on_llm_new_token(str(error) + "\n", **kwargs)
        finally:
            if self.event and not self.event.is_set():
                self.event.set()

    async def on_llm_end(self, response, **kwargs):
        rs = self.handle_image_end(self.resources.get("image", {}))
        if rs:
            await super().on_llm_new_token(rs + "\n", **kwargs)
        # 大模型接口流式返回结束
        if self.format == StreamingMessageDataFormat.JSON_AST:
            # logging.info(f"sending REFERENCE_LIST {self.reference_list}")
            rs = self.create_stream_out(
                message_type=StreamingMessageDataType.REFERENCE_LIST,
                content=self.reference_list,
            ).json(ensure_ascii=False)
            await super().on_llm_new_token(rs + "\n", **kwargs)

            logging.info(f"sending RECOMMEND_LIST {self.recommend_list}")
            rs = self.create_stream_out(
                message_type=StreamingMessageDataType.RECOMMEND_LIST,
                content=self.recommend_list,
            ).json(ensure_ascii=False)
            await super().on_llm_new_token(rs + "\n", **kwargs)

            rs = self.create_stream_out(
                message_type=StreamingMessageDataType.FAQ_CHILD_NODE,
                content=self.faq_child_node_list,
            ).json(ensure_ascii=False)

            await super().on_llm_new_token(rs + "\n", **kwargs)

        question = self.origin_question
        answer = response.generations[0][0].message.content
        if self.user_intent != UserIntent.ASK_INFO_ABOUT_BOT:
            if not self.is_faq:
                # work both for PLAIN_TEXT and JSON_AST
                llm_can_answer, show_reference_button = await check_llm_can_answer(
                    question, answer
                )
            else:  # is_faq
                llm_can_answer = True
                show_reference_button = True
        else:
            llm_can_answer = True
            show_reference_button = False
        logging.info(
            f"intent is {self.user_intent}, question: {question}, answer: {answer}, is_faq: {self.is_faq}  llm_can_answer: {llm_can_answer}, show_reference_button: {show_reference_button}"
        )
        if answer == "" or answer == " " or answer is None:
            logging.error(
                f"empty answer [{answer}], message: {response.generations[0][0].to_json()}"
            )
        asyncio.create_task(
            process_llm_can_answer(self, llm_can_answer)
        ).add_done_callback(task_done_exception_logging_callback)
        if self.format == StreamingMessageDataFormat.JSON_AST:
            show_reference_button_rs = self.create_stream_out(
                message_type=StreamingMessageDataType.SHOW_REFERENCE_BUTTON,
                content=str(show_reference_button).lower(),
            ).json(ensure_ascii=False)
            await super().on_llm_new_token(show_reference_button_rs + "\n", **kwargs)

            llm_can_answer_rs = self.create_stream_out(
                message_type=StreamingMessageDataType.LLM_CAN_ANSWER,
                content=str(llm_can_answer).lower(),
            ).json(ensure_ascii=False)
            await super().on_llm_new_token(llm_can_answer_rs + "\n", **kwargs)


async def record_llm_can_answer(message_id, llm_can_answer: bool):
    if message_id and message_id != "":
        await SessionMessage.set_unanswerable(message_id, not llm_can_answer)
        if not llm_can_answer:
            await create_or_update_session_unanswered_count_by_message_id(message_id)
        asyncio.create_task(
            save_to_quality_assessment_by_message_id(message_id)
        ).add_done_callback(task_done_exception_logging_callback)
    else:
        logging.error(f"record_llm_can_answer: message_id is empty, {llm_can_answer=}")


async def process_llm_can_answer(self, llm_can_answer: bool):
    if self.message_id and self.message_id != "":
        await SessionMessage.set_unanswerable(self.message_id, not llm_can_answer)
        if not llm_can_answer:
            await create_or_update_session_unanswered_count_by_message_id(
                self.message_id
            )
        asyncio.create_task(
            save_to_quality_assessment_by_message_id(self.message_id)
        ).add_done_callback(task_done_exception_logging_callback)
    else:
        logging.error(
            f"process_llm_can_answer: {self} message_id is empty, {llm_can_answer=}"
        )


@trace()
async def chat_async_iterator(
    model: ChatOpenAI,
    messages: list[BaseMessage],
    callback: MyCallbackHanlder,
) -> AsyncIterator[str]:
    """
    langchain chat模型异步迭代器
    """

    async def wrap_done(fn: Awaitable, event: asyncio.Event):
        """Wrap an awaitable with an event to signal when it's done or an exception is raised."""
        try:
            await fn
        except Exception as e:
            # 获取当前使用的模型信息
            model_name = getattr(model, 'model_name', 'unknown_model')
            api_key = format_sensitive_key(getattr(model, 'openai_api_key'))
            logging.error(f"chat:{messages} create for error: {e}")
            await notice(f"create_question -> wrap one Error: model={model_name}, api_key={api_key}, error={str(e)}")
            await callback.on_error(e)
        finally:
            # Signal the aiter to stop.
            event.set()

    async def _test(event: asyncio.Event):
        await callback.on_error(Exception("Test for: Readtime out."))
        await asyncio.sleep(0.2)
        event.set()

    if "@#$%" in messages[-1].content:
        # 测试用途 在提问包含特殊字符时，抛出异常
        methond = _test(callback.done)
    else:
        methond = wrap_done(
            model.ainvoke(input=messages),
            callback.done,
        )
    task = asyncio.create_task(methond)
    got_token = False
    logging.info(
        f"final Q wait for aiter, using model: {model}, using model_name: {model.model_name}"
    )
    async for token in callback.aiter():
        # Use server-sent-events to stream the response
        got_token = True
        yield token
    if not got_token and getattr(model, "openai_api_type", "") == "azure":
        ans = ""
        logging.warning(
            f"========= \nchat_async_iterator: azure generate no text, retry with openai.\n\nusing: {model.model_name}, messages: {messages}\n=========="
        )
        # callback = MyCallbackHanlder(
        #     format=callback.format,
        #     message_id=callback.message_id,
        #     is_faq=callback.is_faq,
        #     reference_list=callback.reference_list,
        #     recommend_list=callback.recommend_list,
        #     tokens=callback.tokens,
        #     event=callback.event,
        # )
        openai_chat_model = get_chat_model(
            model=model.model_name,
            llm_provider=LLM_PROVIDER.OPENAI,
            max_tokens=model.max_tokens,
            temperature=model.temperature,
            max_retries=model.max_retries,
            request_timeout=model.request_timeout,
            callbacks=[callback],
        )
        async for chunk in openai_chat_model.astream(messages):
            ans += chunk.content
            yield chunk.content
        logging.info(f"retried with OpenAI, ans: {ans}")
    await task


@trace()
@retry_async()
async def chat_acreate(
    messages: list,
    origin_question: str = "question placeholder",
    max_tokens: int = 1200,
    temperature: float = 0,
    session_id: str = None,
    message_id: str = None,
    stream: bool = False,
    is_faq: bool = False,
    reference_list: List[dict] = None,
    resources: dict = None,
    format: StreamingMessageDataFormat = None,
    final_question_use_gpt_4: bool = False,
    openai_model: OpenAIModel = OpenAIModel.default(),
    event: asyncio.Event = None,
    recommend_list: List[dict] = None,
    llm_provider: str = None,
    request_timeout=(
        settings.LLM_TIMEOUT
        if settings.IS_USE_LOCAL_VLLM and settings.LLM_TIMEOUT
        else 120
    ),
    user_intent=UserIntent.ASK_INFO_ABOUT_COMPANY,
    faq_child_node_list: List[dict] = None,
):
    tokens = num_tokens_for_langchain_messages(messages)
    origin_message = HumanMessage(content=origin_question)

    try:
        logging.info(f"_is_using_azure_openai(): {_is_using_azure_openai()}")
        if (
            openai_model == OpenAIModel.GPT_4_TURBO_PREVIEW
            or openai_model == OpenAIModel.GPT_4
        ):
            # token计算需要乘以12
            final_question_use_gpt_4 = True

        callback = MyCallbackHanlder(
            format=format,
            message_id=message_id,
            is_faq=is_faq,
            reference_list=reference_list,
            recommend_list=recommend_list,
            resources=resources,
            tokens=tokens,
            event=event,
            origin_question=origin_question,
            user_intent=user_intent,
            faq_child_node_list=faq_child_node_list,
        )

        # 与stream返回无关
        callback_done = ChainResponseAsyncHandler(
            session_id=session_id,
            message_id=message_id,
            human_message=origin_message,
            final_question_use_gpt4=final_question_use_gpt_4,
            messages=messages,
            event=event,
            resources=resources,
            openai_model=openai_model,
        )
        callbacks = [callback_done]
        if stream:
            callbacks.append(callback)
        chat = get_chat_model(
            model=openai_model,
            llm_provider=llm_provider,
            streaming=stream,
            max_tokens=max_tokens,
            callbacks=callbacks,
            verbose=True,
            request_timeout=request_timeout,
            temperature=temperature,
        )
        if stream:
            # 如果是streaming模式，返回一个异步迭代器
            return chat_async_iterator(chat, messages, callback)
        else:
            # 如果是非streaming模式，直接返回结果
            res = await no_stream_chat_ainvoke(chat, messages)
            content = res.content

            total_tokens = num_tokens_for_langchain_messages(messages)
            tokens = num_tokens_from_string(res.content)
            total_tokens += tokens

            # only record llm_can_answer for messages with id
            if message_id:
                if user_intent != UserIntent.ASK_INFO_ABOUT_BOT:
                    if not is_faq:
                        # work both for PLAIN_TEXT and JSON_AST
                        (
                            llm_can_answer,
                            show_reference_button,
                        ) = await check_llm_can_answer(origin_question, content)
                    else:  # is_faq
                        llm_can_answer = True
                        show_reference_button = True
                else:
                    llm_can_answer = True
                    show_reference_button = False
                asyncio.create_task(
                    record_llm_can_answer(message_id, llm_can_answer)
                ).add_done_callback(task_done_exception_logging_callback)

            if resources:
                image_resources = resources.get("image", {})
                if image_resources:
                    content = replace_md_uuid_images(content, image_resources)
                    res.content = content
                ends_with_index = ends_with_markdown_image_syntax_index(content)
                if ends_with_index > -1:
                    content = content[:ends_with_index]
                    res.content = content
            logging.info(f"non-stream res: {res}")
            return res

    except Exception as e:
        logging.error(f"chat_create error:{e}")
        raise e


@trace()
async def no_stream_chat_ainvoke(chat, messages):
    time0 = time.monotonic()
    res = await chat.ainvoke(input=messages)
    time1 = time.monotonic()
    logging.info(f"chat.ainvoke time: {time1 - time0}")
    return res


@trace()
@retry_async()
async def question_answer_turbo_16k_with_function_call(
    messages,
    functions: list,
    function_call: dict[str, str] | str,
    max_tokens: int = 800,
    stream: bool = False,
    max_retries: int = 1,
    request_timeout: int = 15,
):
    model = OpenAIModel.GPT_4_OMNI
    chat = get_chat_model(
        model=model,
        streaming=stream,
        max_tokens=max_tokens,
        request_timeout=request_timeout,
        verbose=True,
        temperature=0,
        model_kwargs={
            "presence_penalty": 0,
            "frequency_penalty": 0,
            "top_p": 1,
        },
    ).bind(function_call=function_call, functions=functions)
    langchain_messages = convert_openai_messages(messages)
    return await chat.ainvoke(langchain_messages)


async def question_answer_with_function_call(
    messages,
    functions: list,
    function_call: dict[str, str] | str,
    max_tokens: int = 800,
    stream: bool = True,
    max_retries: int = 2,
    request_timeout: int = 15,
    callbacks: list = None,
    model_name: OpenAIModel = OpenAIModel.GPT_4_OMNI,
    function_call_style: FunctionCallStyle = FunctionCallStyle.OPENAI_STYLE_OLD,
    llm_provider: Union[str, LLM_PROVIDER] = None,
    **kwargs,
):
    async def wrap_done(
        fn: Awaitable, callback: AsyncIteratorCallbackHandler, event: asyncio.Event
    ):
        """Wrap an awaitable with an event to signal when it's done or an exception is raised."""
        try:
            await fn
        except Exception as e:
            # 获取当前使用的模型信息
            model_name = getattr(chat, 'model_name', 'unknown_model')
            api_key = format_sensitive_key(getattr(chat, 'openai_api_key'))
            logging.error(f"chat:{messages} create for error: {e}")
            await notice(f"create_question -> wrap one Error: model={model_name}, api_key={api_key}, error={str(e)}")
            await callback.on_error(e)
        finally:
            # Signal the aiter to stop.
            event.set()

    async def async_chat_with_function_call(chat_model, messages):
        cur_retries = 0
        while cur_retries < max_retries:
            try:
                async_iterator_callback_handler = None
                for callback in callbacks:
                    if isinstance(callback, AsyncIteratorCallbackHandler):
                        async_iterator_callback_handler = callback
                        break
                if async_iterator_callback_handler:
                    methond = wrap_done(
                        chat.ainvoke(input=messages),
                        async_iterator_callback_handler,
                        async_iterator_callback_handler.done,
                    )
                    task = asyncio.create_task(methond)
                    async for token in async_iterator_callback_handler.aiter():
                        yield token
                else:
                    async for chunk in chat_model.astream(messages):
                        if chunk.additional_kwargs:
                            yield chunk
                        else:
                            yield chunk.content
                break
            except Exception as e:
                logging.error(f"[question_answer_with_function_call] error: {e}")
                logging.info(f"[question_answer_with_function_call] retrying...")
                cur_retries += 1
                if cur_retries >= max_retries:
                    raise e
        await task

    token_params = {}
    if "max_completion_tokens" in kwargs:
        max_completion_tokens = kwargs.pop("max_completion_tokens")
        token_params["max_completion_tokens"] = max_completion_tokens
    else:
        token_params["max_tokens"] = max_tokens
    chat = get_chat_model(
        model=model_name,
        streaming=stream,
        request_timeout=request_timeout,
        verbose=True,
        temperature=0,
        model_kwargs={
            "presence_penalty": 0,
            "frequency_penalty": 0,
            "top_p": 1,
        },
        callbacks=callbacks,
        llm_provider=llm_provider,
        **token_params,
    )
    if functions:
        if function_call_style == FunctionCallStyle.OPENAI_STYLE_OLD:
            # 旧的方式绑定 function call
            chat = chat.bind(function_call=function_call, functions=functions)
        else:
            # 新方式, 绑定 tool call
            chat = chat.bind(tools=functions)
    langchain_messages = convert_openai_messages(messages)
    # 添加retry
    cur_retries = 0
    if stream:
        gpt_response = async_chat_with_function_call(chat, langchain_messages)
        return gpt_response
    else:
        while cur_retries < max_retries:
            try:
                gpt_response = await chat.ainvoke(langchain_messages)
                return gpt_response
            except Exception as e:
                logging.error(f"[question_answer_with_function_call] error: {e}")
                logging.info(f"[question_answer_with_function_call] retrying...")
                cur_retries += 1
                if cur_retries >= max_retries:
                    raise e


@trace()
async def get_messages(session_id: str):
    client = RedisChatMessageHistory()
    return await client.messages(session_id=session_id)


@trace()
async def get_chat_history_turbo(
    session_id: str, max_conversation_count: int = 2
) -> Tuple[List[Union[AIMessage, HumanMessage]], int]:
    try:
        messages = await get_messages(session_id)
        if not messages or len(messages) == 0:
            return [], 0
        # 对memory.messages进行循环，每次循环获取AI消息跟HUMAN消息
        conversation_count = 0
        all_conversation = []
        # 对memory.messages逆序循环
        for m in reversed(messages):
            if isinstance(m, AIMessage):
                all_conversation.append(m)
            elif isinstance(m, HumanMessage):
                all_conversation.append(m)
                conversation_count += 1
            if conversation_count >= max_conversation_count:
                break

    except Exception as e:
        logging.error(f"get_chat_history error:{e}")
        return [], 0
    # 逆序然后返回
    return all_conversation[::-1], conversation_count


@trace()
async def get_chat_history(session_id: str, max_tokens=2000):
    try:
        messages = await get_messages(session_id)
        if not messages or len(messages) == 0:
            return None
        tokens = 0
        # 对memory.messages进行循环，每次循环获取AI消息跟HUMON消息
        single_conversation = []
        all_conversation = []
        # 对memory.messages逆序循环
        for m in reversed(messages):
            if isinstance(m, AIMessage):
                single_conversation = []
                single_conversation.append(m)
            elif isinstance(m, HumanMessage):
                if len(single_conversation) > 0:
                    single_conversation.append(m)
            if len(single_conversation) == 2:
                conversation_str = get_buffer_string(reversed(single_conversation))
                tokens += num_tokens_from_string(conversation_str)
                if tokens > max_tokens:
                    break
                # 只取最新的3条聊天记录
                if len(all_conversation) > 3:
                    break
                all_conversation.append(conversation_str)
        all_conversation_str = ""
        for conversation_str in reversed(all_conversation):
            all_conversation_str += conversation_str + "\n"
        return all_conversation_str
    except Exception as e:
        logging.error(f"get_chat_history error:{e}")
        return None


@trace()
async def save_chat_message(session_id: str, question: str, answer: str):
    chat_memory = RedisChatMessageHistory()
    await chat_memory.add_message(session_id, HumanMessage(content=question))
    await chat_memory.add_message(session_id, AIMessage(content=answer))


@trace()
async def user_intent_detect(
    session_id: str,
    question: str,
    chat_history_str: str = None,
    dictionaries: list = None,
    ai_language: str = None,
    user_id: str = None,
    ai_id: str = None,
):
    results = await asyncio.gather(
        language_detect(question, user_id, ai_language, ai_id),
        user_intent(question, user_id, ai_id),
        _keywords_generation_4_turbo(
            question, chat_history_str, dictionaries, ai_language
        ),
    )

    question_language, total_tokens = results[0]
    intent_info = results[1]

    key_words, tokens = results[2]
    total_tokens += tokens

    # 创建一个名为resp的dict
    resp = {}

    if not key_words:
        resp = None
    else:
        # 去除可能存在的<think>标签内容
        if isinstance(intent_info, str) and "<think>" in intent_info:
            intent_info = re.sub(r'<think>.*?</think>', '', intent_info, flags=re.DOTALL)
        
        if isinstance(question_language, str) and "<think>" in question_language:
            question_language = re.sub(r'<think>.*?</think>', '', question_language, flags=re.DOTALL)
            
        if isinstance(key_words, str) and "<think>" in key_words:
            key_words = re.sub(r'<think>.*?</think>', '', key_words, flags=re.DOTALL)
            
        resp["user_intent"] = intent_info.strip()
        resp["question_language"] = question_language.strip()
        resp["query_key_words"] = key_words.strip()

    logging.info(f"user_intent_detect results: {results}, resp: {resp}")
    return resp, total_tokens


def get_concurrent_provider():
    if settings.IS_USE_LOCAL_VLLM:
        return LLM_PROVIDER.LOCAL_LLM
    # 默认：不再使用openai，而是使用claude做为第一选项
    provides = [LLM_PROVIDER.CLAUDE]
    # 如果开启了并发，使用openai和azure
    if settings.CONCURRENT_INTENT_DETECT:
        provides = [LLM_PROVIDER.CLAUDE, LLM_PROVIDER.AZURE, LLM_PROVIDER.OPENAI]
    # 如果指定只使用azure
    if settings.OPENAI_JUST_AZURE:
        provides = [LLM_PROVIDER.AZURE]
    return provides


@trace()
async def run_chain(provide: LLM_PROVIDER, model: OpenAIModel, messages):
    try:
        # 确保provider和model兼容，出现错误时使用厂家默认model
        compatible_model = model
        if provide == LLM_PROVIDER.OPENAI and not str(model.value).startswith("gpt"):
            compatible_model = OpenAIModel.GPT_4_OMNI_MINI
        elif provide == LLM_PROVIDER.CLAUDE and not str(model.value).startswith("claude"):
            compatible_model = OpenAIModel.CLAUDE_37_SONNET
        elif provide == LLM_PROVIDER.GEMINI and not str(model.value).startswith("gemini"):
            compatible_model = OpenAIModel.GEMINI_20_FLASH
        start_t = time.time()
        chat_model = get_chat_model(
            model=compatible_model, 
            llm_provider=provide,
            request_timeout=(
                settings.LLM_TIMEOUT
                if settings.IS_USE_LOCAL_VLLM and settings.LLM_TIMEOUT
                else 15
            ),
        )
        result = await chat_model.ainvoke(input=messages)
        logging.info(
            f"model:{model} provide:{provide} timing cost {time.time() - start_t}s, result: {result}."
        )
        return result.content, provide
    except Exception as e:
        logging.error(f"run_chain error: {e}")
        return "", provide


@trace()
async def wait_for_first_successful(tasks):
    pending = set(tasks)
    while pending:
        done, pending = await asyncio.wait(pending, return_when=asyncio.FIRST_COMPLETED)
        for task in done:
            try:
                text, provide = task.result()
                if provide == LLM_PROVIDER.OPENAI:
                    logging.info("The OpenAI task first succeeded.")
                elif provide == LLM_PROVIDER.AZURE:
                    logging.info("The Azure task first succeeded.")
                if text != "":
                    return text
                else:
                    logging.warning("Task text is empty. Continuing to the next task.")
            except Exception as e:
                logging.error(f"Task failed: {e}")
    raise RuntimeError("All tasks failed")


@trace()
async def concurrent_invoke(messages: list, model=OpenAIModel.CLAUDE_37_SONNET):
    message_tokens = num_tokens_for_langchain_messages(messages)
    provides = get_concurrent_provider()
    if len(provides) == 1:
        logging.info(f"use single provider for model:{model}")
        text, _ = await run_chain(provides[0], model, messages)
        return text, message_tokens
    tasks = [run_chain(provider, model, messages) for provider in provides]
    text = await wait_for_first_successful(tasks)
    return text.strip(), message_tokens


@trace()
@retry_async()
async def user_intent(
    question: str,
    user_id: str = None,
    ai_id: str = None,
):
    # prompt_info = await PromptCache.get_prompt_content(
    #     PROMPT_TYPE.INTENT_V1, user_id, ai_id
    # )
    prompt_info = {
        "system": """\
As an expert language model specialized in detecting user intents, you should  accurately determines the intent of a user query based on specific scenarios. 

Input: A user query in the form of a string. 
Output: A classification tag that represents the intent of the user query. 

You should follow the following guidelines: 

- For the given user input ['Greeting', 'Gratitude ', 'Goodbye', 'Ask if you can answer a question', 'Asking what can you do', 'Asking about your name', 'Ask about your company's name', 'I understand', 'thanks you', 'Can you repeat the answer with chinese again?', "请把这个答案用中文再说一遍"], if the user input matches any of these scenarios or something alike, return the tag "ABOUT_BOT" to indicate that the user wants to know more about the language model itself or just express gratefulness. 

- For any other query scenarios, return the tag "ABOUT_COMPANY" to indicate that the user is inquiring about the company or organization behind the language model, return the tag "ABOUT_COMPANY".

- Take into consideration variations and potential different phrasings of the given scenarios to ensure accurate classification. 

- You should be able to handle user queries of any length and complexity. 

- Aim for precision in determining the intent and provide the most relevant classification based on the given information. 

- Finally, if you're not sure the scenarios, return the tag "ABOUT_COMPANY" .

- Please only respond with ABOUT_BOT or ABOUT_COMPANY, follow the guidelines above.\
""",
        "user": "Customer input is wrapped inside <user_input> tags given below: <user_input>{question}</user_input>",
    }

    messages = [
        SystemMessage(content=prompt_info["system"]),
        HumanMessage(content=prompt_info["user"].format(question=question)),
    ]
    text = None
    try:
        start_time = time.time()
        text, _ = await concurrent_invoke(messages)
        logging.info(f"user intent detect cost {time.time() - start_time}s")
        return text.strip()
    except Exception as e:
        logging.error(f"user intent exception, response is:{text}, e.args: {e.args}")
        return None


@trace()
@retry_async()
async def _keywords_generation_4_turbo(
    question: str,
    chat_history_str: str = None,
    dictionaries: list = None,
    ai_language: str = None,
):
    # zh有时会返回繁体
    if ai_language == "zh":
        ai_language = "zh-CN"

    system_p, user_p = (
        gpt_4_turbo_keywords_generation_prompt["system"],
        gpt_4_turbo_keywords_generation_prompt["user"],
    )
    keywords_dictionary_prompt = get_keywords_dictionary_prompt(dictionaries)
    chat_history_str = chat_history_str or ""
    messages = [
        SystemMessage(
            content=system_p.format(
                chat_history=chat_history_str,
                keywords_dictionary=keywords_dictionary_prompt,
            )
        )
    ]
    messages.append(HumanMessage(content=user_p.format(question=question)))
    # system + history + user message + ai max_token

    try:
        start_time = time.time()
        text, message_tokens = await concurrent_invoke(messages)
        logging.info(
            f"keywords generation cost {time.time() - start_time}s, input: [{question}], res: {text}"
        )

        question_token = num_tokens_from_string(question)
        text_token = num_tokens_from_string(text)
        tokens = question_token + text_token
        message_tokens += tokens
        text = text.strip()
    except Exception as e:
        message_tokens = 0
        logging.error(
            f"key words detection exception, response is:{text}, e.args: {e.args}"
        )
        return None, message_tokens

    if not text:
        text = question

    return text, message_tokens


@trace()
def create_langchain_chat_history_from_openai_json(
    chat_history_json,
) -> List[BaseMessage]:
    return convert_openai_messages(chat_history_json)


async def _reference_list_faq_edit(reference_list):
    start_time = time.time()
    vector_file_objs, faq_info = None, None
    i = 0
    for no, ref in enumerate(reference_list, start=1):
        if ref["source"] in ["GPTBASE_FAQ", "GPTBASE_FAQ_QUESTION"]:
            vector_file_objs = await VectorFile.filter(id=ref["file_id"]).first()
            if vector_file_objs:
                faq_info = await Faqs.filter(id=vector_file_objs.key).first()

            if faq_info and faq_info.citation:
                ref["faq_id"] = str(faq_info.id)
                reference_list += [
                    {
                        "id": str(i),
                        "score": 0,
                        "dataset_id": ref["dataset_id"],
                        "file_id": "",
                        "index": i + len(reference_list),
                        "source": citation["val"],
                        "title": citation["title"],
                        "page_numbers": [],
                        "faq_id": str(faq_info.id),
                        "type": "vector_faq",
                    }
                    for i, citation in enumerate(faq_info.citation, start=1)
                ]
    logging.info(f"reference_list_faq_edit spend time:{time.time() - start_time}")

    return reference_list


@trace()
@retry_async()
async def aget_sections(doc_text: str, is_ppt: bool = False) -> List[SectionOutput]:
    """Get extracted sections from a provided text."""
    parser = PydanticOutputParser(pydantic_object=SectionsOutput)
    output_format = parser.get_format_instructions()
    if is_ppt:
        system_p = PROMPTS["get_sections_ppt"]["system"]
    else:
        return None  # 非ppt文件：1）需要兼容多级标题情况，2）容易出现合并后章节非常大的情况，待充分测试后再开放非ppt文件章节合并
        # system_p = PROMPTS["get_sections_notppt"]["system"]
    messages = [
        SystemMessage(content=system_p + "\n" + output_format),
        HumanMessage(content="Document text: {doc_text}".format(doc_text=doc_text)),
    ]
    text = None
    try:
        start_time = time.time()
        text, _ = await concurrent_invoke(messages)
        text = extract_json(text)
        sectionsOutput = SectionsOutput.parse_obj(text[0])
        logging.info(f"get dinamic sections cost {time.time() - start_time}s")
        return sectionsOutput.sections
    except Exception as e:
        logging.warning(
            f"get dinamic sections exception, response is:{text}, e.args: {e.args}"
        )
        return None


async def arefine_sections(
    sections: List[SectionOutput], is_ppt: bool = False
) -> List[SectionOutput]:
    """Refine sections based on extracted text."""
    if sections is None or len(sections) == 0:
        return None
    parser = PydanticOutputParser(pydantic_object=SectionsOutput)
    output_format = parser.get_format_instructions()
    if is_ppt:
        system_p = PROMPTS["refine_sections_ppt"]["system"]
    else:
        system_p = PROMPTS["refine_sections_notppt"]["system"]
    section_texts = "\n".join(
        [
            f"{idx}: {json.dumps(s.dict(), ensure_ascii=False)}"
            for idx, s in enumerate(sections)
        ]
    )
    messages = [
        SystemMessage(content=system_p + "\n" + output_format),
        HumanMessage(
            content="Sections in text:\n{section_texts}".format(
                section_texts=section_texts
            )
        ),
    ]
    text = None
    try:
        start_time = time.time()
        text, _ = await concurrent_invoke(messages)
        text = extract_json(text)
        logging.info(f"refine dinamic sections cost {time.time() - start_time}s")
        return text[0]["sections"]
    except Exception as e:
        logging.warning(
            f"refine dinamic sections exception, response is:{text}, e.args: {e.args}"
        )
        return None
