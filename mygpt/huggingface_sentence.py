import aiohttp
import requests
from fastapi import HTT<PERSON>Exception
from loguru import logger

from mygpt.enums import VectorStorageType
from mygpt.models import Dataset
from mygpt.settings import LOCAL_PROXY


async def sentence_transformers_post(url, body):
    namespace = body["namespace"]
    # 确定使用的向量数据库

    dataset_obj = await Dataset.filter(id=namespace).first()
    body["storage_type"] = dataset_obj.metadata.get(
        "faq_vector_storage", VectorStorageType.PINECONE
    )
    body["namespace"] = dataset_obj.collection_name
    async with aiohttp.ClientSession() as session:
        async with session.post(url, json=body) as r:
            if r.status == 200:
                return await r.json()
            else:
                raise HTTPException(
                    status_code=400, detail=f"Question Error: {r.status}"
                )


# Define retry strategy
# retry_strategy = Retry(
#     total=3,
#     backoff_factor=1,
#     status_forcelist=[500, 502, 503, 504],
#     method_whitelist=["HEAD", "GET", "POST"],
# )

# Define connection pool
# adapter = HTTPAdapter(max_retries=retry_strategy)
http = requests.Session()
# http.mount("http://", adapter)
# http.mount("https://", adapter)
if LOCAL_PROXY:
    http.proxies = {
        "https": LOCAL_PROXY,
    }


# from requests.packages. import Retry


import traceback


def before_log_retry(retry_state):
    if retry_state.attempt_number > 1:
        logger.warning(
            f"Retrying {retry_state.fn.__name__} (attempt {retry_state.attempt_number})"
        )


def after_log_retry(retry_state):
    if retry_state.attempt_number > 1:
        logger.warning(
            f"Finished call to {retry_state.fn.__name__} (attempt {retry_state.attempt_number})"
        )
    if retry_state.outcome.failed:
        exception = retry_state.outcome.exception()
        tb_str = traceback.format_exception(None, exception, exception.__traceback__)
        logger.warning(
            f"Attempt {retry_state.attempt_number} raised {exception!r}, traceback: {''.join(tb_str)}"
        )
