import uuid
from typing import Optional
from uuid import UUID

from fastapi import Depends, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SecurityScopes
from fastapi_auth0 import Auth0User
from fastapi_auth0.auth import Auth0<PERSON><PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from loguru import logger as logging

from mygpt.auth0.init import auth
from mygpt.auth0.utils import check_user_role
from mygpt.enums import DATASET_STATUS, ResourceType, UserConfigType, UserRole
from mygpt.error import (
    DatasetForzenException,
    ForbiddenException,
    NotFoundException,
    UnauthorizedException,
)
from mygpt.models import AccountMember, Apikey, Dataset, Robot, User, UserConfig
from mygpt.schemata import ApiKey
from mygpt.settings import IS_LOCAL, DEBUG


class Auth0UserWGbaseUser(Auth0User):
    user: User

    class Config:
        arbitrary_types_allowed = True


async def get_current_oauth_user_with_gbase_user(
    security_scopes: SecurityScopes,
    creds: Optional[HTTPAuthorizationCredentials] = Depends(
        Auth0HTTPBearer(auto_error=False)
    ),
) -> Auth0UserWGbaseUser | None:
    """
    获取当前用户，基于jwt token解析出来的用户。
    用户不存在没有抛出异常，而是返回None
    """
    # Check if we have credentials and they look like a JWT token (longer than 100 chars)
    if creds and creds.credentials and len(creds.credentials) > 100:
        auth0_user = await auth.get_user(security_scopes, creds)
        if not auth0_user:
            return None
        gbase_user = await User.get_or_none(user_id=auth0_user.id)
        # 创建一个新的Auth0UserWGbaseUser实例，确保所有字段都正确传递
        # 1. 显式设置必需的sub字段
        user = Auth0UserWGbaseUser(
            sub=auth0_user.id,
            permissions=getattr(auth0_user, "permissions", None),
            user=gbase_user,
        )
        # 2. 确保email字段正确传递
        user.email = auth0_user.email
        return user


http_bearer_scheme = HTTPBearer(auto_error=False)


async def depend_api_key(credentials: str = Depends(http_bearer_scheme)) -> ApiKey:
    """
    api_key认证，认证失败会有异常
    """
    token = credentials.credentials
    if len(token) > 60:
        raise UnauthorizedException("Invalid authorization api key")
    api_key = await Apikey.get_or_none(api_key=token)
    if not api_key:
        raise UnauthorizedException("Invalid authorization api key")
    return ApiKey(api_key=token, user_id=api_key.user_id)


async def depend_api_key_with_none(
    credentials: str = Depends(http_bearer_scheme),
) -> ApiKey | None:
    """
    api_key认证，认证失败返回None
    返回 ApiKey 对象，其中包含 user 属性，可以通过 api_key.user.id 获取用户ID
    """
    if not credentials:
        return None
    token = credentials.credentials
    if not token:
        return None
    if len(token) > 60:
        return None
    api_key = await Apikey.get_or_none(api_key=token)
    if not api_key:
        logging.warning(f"Invalid authorization api key: {token}")
        return None
    # 获取用户对象
    user = await User.get_or_none(user_id=api_key.user_id)

    # 创建ApiKey对象并设置user属性
    api_key_obj = ApiKey(
        api_key=token, user_id=api_key.user_id, is_super_admin=api_key.is_super_admin
    )
    api_key_obj.user = user

    return api_key_obj


async def current_user(
    user: Optional[Auth0User] = Security(get_current_oauth_user_with_gbase_user),
    api_key: ApiKey = Depends(depend_api_key_with_none),
) -> User:
    """
    获取当前用户，基于jwt token解析出来的用户。
    如果用户不存在，会尝试使用api_key来获取用户
    如果api_key也不存在，会抛出异常
    """
    user_id = api_key.user_id if api_key else (user.id if user else None)
    if not user_id:
        logging.error("No user_id.")
        raise UnauthorizedException("Login required.")
    user_obj = await User.get_or_none(user_id=user_id)
    if not user_obj:
        raise ForbiddenException("Request users/me before other requests.")
    return user_obj


async def verify_robot(ai_id: str):
    try:
        id = uuid.UUID(ai_id)
    except Exception as e:
        logging.warning(f"verify_robot for id:{ai_id} error: {e}")
        raise NotFoundException("AI not found")
    ai_obj = await Robot.get_or_none(id=id, deleted_at__isnull=True).prefetch_related(
        "robotconfigs", "dictionary", "apis", "datasets", "datasets__vectorfiles"
    )
    if not ai_obj:
        raise NotFoundException("AI not found")
    return ai_obj


async def verify_robots_owner(
    ai_id: str,
    user: User = Depends(current_user),
) -> Robot:
    """
    验证是否是AI的所有者
    如果是本地运行，忽略验证
    """
    robot_obj = await Robot.get_or_none(id=ai_id, deleted_at__isnull=True)
    if not robot_obj:
        raise NotFoundException("Robot not found.")
    if IS_LOCAL:
        return robot_obj
    if robot_obj.user_id != user.user_id:
        raise ForbiddenException("No rights to access.")
    return robot_obj


async def verify_admin_access(
    ai_id: str,
    user: User = Depends(current_user),
):
    """
    验证是否是AI的管理员
    如果是本地运行，忽略验证
    """
    robot_obj = await Robot.get_or_none(id=ai_id, deleted_at__isnull=True)
    if DEBUG:
        return robot_obj
    if IS_LOCAL:
        return robot_obj
    if robot_obj and robot_obj.user_id == user.user_id:
        return robot_obj
    exists = await AccountMember.filter(
        member_id=user.user_id,
        resource_type=ResourceType.ROBOT,
        deleted_at__isnull=True,
        resource_id=ai_id,
    ).exists()
    if not exists:
        raise ForbiddenException("No rights to access.")
    return robot_obj


async def verify_dataset_owner(
    dataset_id: UUID,
    user: User = Depends(current_user),
) -> Dataset:
    """
    验证是否是数据集的所有者
    如果是本地运行，忽略验证
    """

    dataset_obj = await Dataset.get_or_none(id=dataset_id, deleted_at__isnull=True)
    if not dataset_obj:
        raise NotFoundException("Dataset not found.")
    if IS_LOCAL:
        return dataset_obj
    if dataset_obj.user_id == user.user_id:
        return dataset_obj
    raise ForbiddenException("No rights to access.")


async def verify_dataset_admin(
    dataset_id: UUID,
    user: User = Depends(current_user),
):
    """
    验证是否是数据集的管理员
    如果是本地运行，忽略验证
    """

    dataset_obj = await Dataset.get_or_none(id=dataset_id, deleted_at__isnull=True)
    if not dataset_obj:
        raise NotFoundException("Dataset not found.")

    if dataset_obj.user_id == user.user_id:
        return dataset_obj

    is_collaborative = await AccountMember.filter(
        member_id=user.user_id,
        resource_id=dataset_id,
        resource_type=ResourceType.DATASET,
        deleted_at__isnull=True,
    ).exists()
    if is_collaborative:
        return dataset_obj

    ai_ids = await Robot.filter(datasets__id=dataset_id).values_list("id", flat=True)
    if not ai_ids:
        raise ForbiddenException("No rights to access.")
    exists = await AccountMember.filter(
        member_id=user.user_id,
        resource_type=ResourceType.ROBOT,
        deleted_at__isnull=True,
        resource_id__in=ai_ids,
    ).exists()
    if not exists:
        raise ForbiddenException("No rights to access.")
    return dataset_obj


async def verify_dataset_access(
    dataset_obj: Dataset = Depends(verify_dataset_admin),
) -> Dataset:
    """
    验证数据集是否可用
    """
    if dataset_obj.data_status == DATASET_STATUS.FROZEN:
        raise DatasetForzenException()
    return dataset_obj


async def verify_super_admin_access(
    user: User = Depends(current_user),
):
    """
    验证是否是超级管理员
    """
    if not await user.is_super_admin(user.user_id):
        if await check_user_role(user.user_id, UserRole.SUPER_ADMIN):
            await UserConfig.set_config(user.user_id, UserConfigType.SUPER_ADMIN, True)
        raise ForbiddenException("No rights to access.")
