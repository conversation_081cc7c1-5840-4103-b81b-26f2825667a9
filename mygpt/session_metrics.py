from datetime import datetime, timezone
from uuid import UUID

from skywalking.decorators import trace

from mygpt.enums import MessagePageInfo
from mygpt.models import (
    SessionMessage,
    SessionMessagePageAccessInfo,
    SessionMessagePageAccessMetrics,
    SessionMetricsHourly,
)


async def create_or_update_session_metrics_hourly(session_metrics_hourly: dict):
    now = datetime.now().astimezone(timezone.utc)
    hour = now.hour
    date = now.date()
    if not session_metrics_hourly.get("date"):
        session_metrics_hourly["date"] = date
    if not session_metrics_hourly.get("hour_of_day"):
        session_metrics_hourly["hour_of_day"] = hour
    robot_id = session_metrics_hourly.get("robot_id")

    record = await SessionMetricsHourly.filter(
        robot_id=robot_id, hour_of_day=hour, date=date
    ).first()

    if record is None:
        await SessionMetricsHourly.create(**session_metrics_hourly)
    else:
        update = {}
        for key, value in session_metrics_hourly.items():
            if key in ["robot_id", "date", "hour_of_day"]:
                continue
            current_value = getattr(record, key)
            if isinstance(value, list):
                update[key] = (
                    list(set(current_value + value)) if current_value else value
                )
            elif isinstance(value, int) or isinstance(value, float):
                update[key] = max(
                    0, (current_value + value) if current_value else value
                )
        if update:
            await record.update_from_dict(update).save()


@trace()
async def create_or_update_session_user_count(
    robot_id: UUID, new_users: list, old_users: list
):
    await create_or_update_session_metrics_hourly(
        {
            "robot_id": robot_id,
            "new_users": new_users,
            "old_users": old_users,
            "created_session_count": 1,
        }
    )


@trace()
async def create_or_update_session_message_count(
    robot_id: UUID, incremented_message_count: int
):
    await create_or_update_session_metrics_hourly(
        {
            "robot_id": robot_id,
            "message_count": incremented_message_count,
        }
    )


@trace()
async def create_or_update_session_unanswered_count(robot_id: UUID, value: int):
    await create_or_update_session_metrics_hourly(
        {
            "robot_id": robot_id,
            "unanswered_count": value,
        }
    )


@trace()
async def create_or_update_session_unanswered_count_by_message_id(message_id: UUID):
    message = (
        await SessionMessage.get_or_none(id=message_id)
        .prefetch_related("session")
        .values("session__robot_id", "is_test")
    )
    if (
        message is None
        or not message.get("session__robot_id")
        or message.get("is_test") is True
    ):
        return
    await create_or_update_session_unanswered_count(message.get("session__robot_id"), 1)


@trace()
async def create_or_update_session_score(robot_id: UUID, count: int, value: float):
    await create_or_update_session_metrics_hourly(
        {
            "robot_id": robot_id,
            "evaluation_score_count": count,
            "evaluation_score_value": value,
        }
    )


@trace()
async def create_or_update_session_answer_count(robot_id: UUID, value: int):
    await create_or_update_session_metrics_hourly(
        {
            "robot_id": robot_id,
            "evaluation_answer_count": value,
        }
    )


@trace()
async def create_or_update_session_source_count(robot_id: UUID, value: int):
    await create_or_update_session_metrics_hourly(
        {
            "robot_id": robot_id,
            "evaluation_source_count": value,
        }
    )


@trace()
async def create_or_update_session_rating_count(
    robot_id: UUID, like_count: int, dislike_count: int
):
    await create_or_update_session_metrics_hourly(
        {
            "robot_id": robot_id,
            "like_count": like_count,
            "dislike_count": dislike_count,
        }
    )


@trace()
async def create_session_message_page_access_metrics(
    robot_id: UUID,
    url: str,
    title: str = "",
    message_count: int = 1,
    source: str = MessagePageInfo.API.value,
):
    page_access_info = await SessionMessagePageAccessInfo.filter(
        robot_id=robot_id, url=url, title=title, source=source
    ).first()
    if page_access_info is None:
        page_access_info = await SessionMessagePageAccessInfo.create(
            robot_id=robot_id, url=url, source=source, defaults={"title": title}
        )
    await SessionMessagePageAccessMetrics.create(
        page_access_info=page_access_info, message_count=message_count
    )
    return page_access_info
