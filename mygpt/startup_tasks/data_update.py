import asyncio
import json
import logging
import random
import traceback
import uuid
from typing import Type, TypeVar, Tuple

import aiohttp
import qdrant_client
from loguru import logger
from qdrant_client.conversions.common_types import Points
from qdrant_client.http.models import PointRequest
from redis.exceptions import ResponseError
from tortoise import Model

from mygpt import settings
from mygpt.core.embedding import file_embeddings, EmbeddingFactory
from mygpt.core.utils import get_collection_name
from mygpt.enums import (
    VectorStorageType,
    OpenAIModel,
    LLM_PROVIDER,
)
from mygpt.error import sentry_sdk_capture_exception
from mygpt.file_embeddings import spliter_document
from mygpt.loader.service import split_by_file
from mygpt.models import Embedding<PERSON>arams, VectorFile, Dataset
from mygpt.opensearch import get_open_search_client, get_async_open_search
from mygpt.schemata import Embeddings
from mygpt.settings import RedisClient, IS_LOCAL
from mygpt.site_crawler import SiteCrawler
from mygpt.utils import (
    get_hash,
)

async_qdrant_client = qdrant_client.AsyncQdrantClient(
    host=settings.VECTOR_STORAGE_QDRANT_HOST,
    port=settings.VECTOR_STORAGE_QDRANT_HTTP_PORT,
    grpc_port=settings.VECTOR_STORAGE_QDRANT_GRPC_PORT,
    timeout=60,
)
redis_client = RedisClient.get_client()
os_client = get_open_search_client()
async_opensearch = get_async_open_search()

from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
)


def before_log_retry(retry_state):
    if retry_state.attempt_number > 1:
        logger.warning(
            f"Retrying {retry_state.fn.__name__} (attempt {retry_state.attempt_number})"
        )


def after_log_retry(retry_state):
    if retry_state.attempt_number > 1:
        logger.warning(
            f"Finished call to {retry_state.fn.__name__} (attempt {retry_state.attempt_number})"
        )
    # 检查是否有异常，并打印出来，包括堆栈跟踪
    if retry_state.outcome.failed:
        exception = retry_state.outcome.exception()
        tb_str = traceback.format_exception(None, exception, exception.__traceback__)
        logger.warning(
            f"Attempt {retry_state.attempt_number} raised {exception!r}, traceback: {''.join(tb_str)}"
        )


@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=3, max=10),
    before=before_log_retry,
    after=after_log_retry,
    reraise=True,
)
async def get_points_with_retry(
    collection_name, ids, with_payload: bool = True, with_vector: bool = True
):
    result = await async_qdrant_client.rest.points_api.get_points(
        collection_name=collection_name,
        point_request=PointRequest(
            ids=ids,
            with_payload=with_payload,
            with_vector=with_vector,
        ),
    )
    return result


@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=3, max=10),
    before=before_log_retry,
    after=after_log_retry,
    reraise=True,
)
async def upsert_points_with_retry(collection_name: str, points: Points):
    result = await async_qdrant_client.upsert(
        collection_name=collection_name,
        points=points,
    )
    return result


async_embeddings = EmbeddingFactory.get_instance(
    provider=LLM_PROVIDER.AZURE if settings.OPENAI_JUST_AZURE else LLM_PROVIDER.OPENAI,
    model_name=OpenAIModel.TEXT_EMBEDDING_3_LARGE,
    dimensions=3072,
)

# Define a type variable that can be any subclass of Model
T = TypeVar("T", bound=Model)


async def update_or_create(model: Type[T], entry_dict: dict) -> Tuple[T, bool]:
    new_file, created = await model.update_or_create(
        **entry_dict,
    )
    if not created:
        logger.warning(f"file {model} already exists for {entry_dict}")
    return new_file, created


async def poll_data_update_task_streams():
    while True:
        # print(
        #     f"Cloning in thread ID: {threading.get_ident()}, {id(asyncio.current_task())}"
        # )
        try:
            stream_pattern_prefix = "retrain_task_stream-"
            stream_pattern = stream_pattern_prefix + "*"
            streams = await redis_client.keys(stream_pattern)

            if streams:
                selected_stream = random.choice(streams)
                old_dataset_id = selected_stream[len(stream_pattern_prefix) :]
                consumer_group_name = "consumer_group-" + old_dataset_id
                consumer_name = f"consumer-{old_dataset_id}"

                try:
                    await redis_client.xgroup_create(
                        selected_stream, consumer_group_name, "0", mkstream=True
                    )
                except ResponseError as e:
                    if "BUSYGROUP Consumer Group name already exists" not in str(e):
                        logger.error(e)
                        sentry_sdk_capture_exception(e)
                except Exception as e:
                    logger.error(e)
                    sentry_sdk_capture_exception(e)
                message = await redis_client.xreadgroup(
                    consumer_group_name,
                    consumer_name,
                    {selected_stream: ">"},
                    count=1,
                )
                auto_claimed_message = None
                if message:
                    file_obj = message[0][1][0][1]
                    message_id = message[0][1][0][0]
                else:  # if not message:
                    # check idle messages idle larger than t
                    auto_claimed_message = await redis_client.xautoclaim(
                        name=selected_stream,
                        groupname=consumer_group_name,
                        consumername=consumer_name,
                        min_idle_time=600 * 1000,
                        count=1,
                        justid=False,
                    )
                    if not auto_claimed_message[1]:
                        continue
                    else:
                        file_obj = auto_claimed_message[1][0][1]
                        message_id = auto_claimed_message[1][0][0]
                        await redis_client.xack(
                            selected_stream, consumer_group_name, message_id
                        )
                try:
                    file_id = file_obj["file_id"]
                    embedding_params_str = file_obj.get("embedding_params", "")
                    if embedding_params_str:
                        embedding_params = EmbeddingParams(
                            **json.loads(embedding_params_str)
                        )
                    else:
                        embedding_params = None
                    vector_file = await VectorFile.get(id=file_id)
                except Exception as e:
                    logger.error(e)
                    sentry_sdk_capture_exception(e)
                    continue
                url = vector_file.filename
                # crawl url and get content
                try:
                    site_crawler = SiteCrawler(url)
                    async with aiohttp.ClientSession() as session:
                        doc = await site_crawler.parse(
                            url, max_images=0, client=session
                        )
                        if not doc:
                            logging.error(
                                "[web search] fail to get html from url: {}".format(url)
                            )
                            return []
                        if vector_file.content_hash == get_hash(doc.page_content):
                            if not IS_LOCAL:
                                continue
                        metadata = doc.metadata
                        file_id: str = metadata.get("parser_file_id")

                        data = await split_by_file(file_id, client=session)
                except Exception as e:
                    logging.error("[retrain] fail to get html from url: {}".format(url))
                    raise e
                embeddings = []
                title = metadata.get("title", "")
                description = metadata.get("description")
                doc_id = str(vector_file.id)
                for index, chunk in enumerate(data):
                    embeddings.append(
                        Embeddings(
                            id=str(uuid.uuid4()),
                            text=chunk.page_content,
                            metadata={
                                "title": title,
                                "description": description,
                                "doc_id": doc_id,
                                "chunk_index": index,
                                "source": url,
                            },
                        )
                    )
                old_index_ids = vector_file.index_ids
                delete_operations = [
                    {
                        "delete": {
                            "_index": doc_id,
                            "_id": vector_file.dataset_id,
                        }
                    }
                    for doc_id in old_index_ids
                ]
                response = await async_opensearch.bulk(body=delete_operations)
                if response["errors"]:
                    logger.error("Errors occurred during batch deletion:")
                    for item in response["items"]:
                        if "error" in item["delete"]:
                            print(
                                f"Error deleting document with ID {item['delete']['_id']}: {item['delete']['error']}"
                            )
                else:
                    print(
                        f"Batch deletion completed successfully. Deleted {len(old_index_ids)} documents."
                    )
                embedding_params_str = file_obj.get("embedding_params")
                embedding_params = EmbeddingParams(**json.loads(embedding_params_str))
                collection_name = get_collection_name(
                    embedding_params.model_name,
                    embedding_params.dimensions,
                )
                await async_qdrant_client.delete(
                    collection_name=collection_name,
                    points_selector=old_index_ids,
                )
                new_index_ids, data = await spliter_document(data)
                # new_index_ids = [str(uuid.uuid4()) for _ in range(len(data))]
                for i, chunk in enumerate(data):
                    new_index_id = new_index_ids[i]
                    await async_opensearch.index(
                        index=vector_file.dataset_id,
                        body=dict(chunk),
                        id=new_index_id,
                    )
                dataset = await Dataset.get(id=vector_file.dataset_id)
                await file_embeddings(
                    VectorStorageType(dataset.metadata.get("vector_storage")),
                    str(dataset.id),
                    dataset.collection_name,
                    str(file_obj.id),
                    data,
                    metadata,
                    embedding_model=embedding_params.provider,
                    embedding_model_name=embedding_params.model_name,
                    embedding_dimensions=embedding_params.dimensions,
                    is_first=index == 0,
                    is_last=index == len(data) - 1,
                )
                vector_file.index_ids = new_index_ids
                await vector_file.save()
                if not auto_claimed_message:
                    await redis_client.xack(
                        selected_stream, consumer_group_name, message_id
                    )
                stream_info = await redis_client.xinfo_stream(selected_stream)
                if stream_info["length"] == 0:
                    await redis_client.delete(selected_stream)
        except Exception as e:
            logger.error(e)
            # 打印完整异常
            logger.error(traceback.format_exc())
            sentry_sdk_capture_exception(e)
        finally:
            await asyncio.sleep(1)
