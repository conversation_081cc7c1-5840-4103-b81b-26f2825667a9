import asyncio
import json
import random
import traceback
from datetime import datetime
from typing import Type, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, List

import qdrant_client
from loguru import logger
from qdrant_client.conversions.common_types import Points
from qdrant_client.http.models import PointRequest, PointStruct
from redis.exceptions import ResponseError
from tortoise import Model

from mygpt import settings
from mygpt.core.embedding import EmbeddingFactory
from mygpt.core.utils import get_collection_name
from mygpt.endpoints.faqs import insert_to_vector, update_to_similar_questions
from mygpt.enums import (
    VectorFileType,
    VectorStorageType,
    DATASET_STATUS,
    OpenAIModel,
)
from mygpt.error import sentry_sdk_capture_exception
from mygpt.models import Embedding<PERSON><PERSON><PERSON>, <PERSON>aqs, FunctionCallApi, VectorFile, Dataset
from mygpt.opensearch import get_open_search_client, get_async_open_search
from mygpt.settings import RedisClient
from mygpt.utils import (
    copy_binary,
)

async_qdrant_client = qdrant_client.AsyncQdrantClient(
    host=settings.VECTOR_STORAGE_QDRANT_HOST,
    port=settings.VECTOR_STORAGE_QDRANT_HTTP_PORT,
    grpc_port=settings.VECTOR_STORAGE_QDRANT_GRPC_PORT,
    timeout=60,
)
redis_client = RedisClient.get_client()
os_client = get_open_search_client()
async_opensearch = get_async_open_search()

import os
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
)


def before_log_retry(retry_state):
    if retry_state.attempt_number > 1:
        logger.warning(
            f"Retrying {retry_state.fn.__name__} (attempt {retry_state.attempt_number})"
        )


def after_log_retry(retry_state):
    if retry_state.attempt_number > 1:
        logger.warning(
            f"Finished call to {retry_state.fn.__name__} (attempt {retry_state.attempt_number})"
        )
    # 检查是否有异常，并打印出来，包括堆栈跟踪
    if retry_state.outcome.failed:
        exception = retry_state.outcome.exception()
        tb_str = traceback.format_exception(None, exception, exception.__traceback__)
        logger.warning(
            f"Attempt {retry_state.attempt_number} raised {exception!r}, traceback: {''.join(tb_str)}"
        )


@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=3, max=10),
    before=before_log_retry,
    after=after_log_retry,
    reraise=True,
)
async def get_points_with_retry(
    collection_name, ids, with_payload: bool = True, with_vector: bool = True
):
    result = await async_qdrant_client.rest.points_api.get_points(
        collection_name=collection_name,
        point_request=PointRequest(
            ids=ids,
            with_payload=with_payload,
            with_vector=with_vector,
        ),
    )
    return result


@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=3, max=10),
    before=before_log_retry,
    after=after_log_retry,
    reraise=True,
)
async def upsert_points_with_retry(collection_name: str, points: Points):
    result = await async_qdrant_client.upsert(
        collection_name=collection_name,
        points=points,
    )
    return result


@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=3, max=10),
    before=before_log_retry,
    after=after_log_retry,
    reraise=True,
)
async def async_embeddings_retry(
    input_str: str, target_embedding_model: OpenAIModel
) -> List[float]:
    res = await EmbeddingFactory.concurrent_embedding(
        [input_str], model_name=target_embedding_model
    )
    return res[0]


# Define a type variable that can be any subclass of Model
T = TypeVar("T", bound=Model)


async def update_or_create(model: Type[T], entry_dict: dict) -> Tuple[T, bool]:
    existing_one = await model.get_or_none(id=entry_dict["id"])
    if existing_one:
        await (await model.get(id=entry_dict["id"])).delete()
    new_file, created = await model.update_or_create(
        **entry_dict,
    )
    if not created:
        logger.warning(f"file {model} already exists for {entry_dict}")
    return new_file, created


async def poll_clone_task_streams():
    selected_stream = None
    consumer_group_name = None
    message_id = None
    old_dataset = None
    new_dataset = None
    file_obj = None
    while True:
        try:
            stream_pattern_prefix = "clone_task_stream-"
            stream_pattern = stream_pattern_prefix + "*"
            streams = await redis_client.keys(stream_pattern)

            if streams:
                logger.info(f"{len(streams)} streams: {streams}")
                # stream 名字 == clone_task_stream-{old_dataset_id}__{new_dataset_id}
                selected_stream = random.choice(streams)
                consumer_id = selected_stream[len(stream_pattern_prefix) :]
                old_dataset_id_str = selected_stream[
                    len(stream_pattern_prefix) : len(stream_pattern_prefix) + 36
                ]
                old_dataset = await Dataset.get(id=old_dataset_id_str)

                new_dataset_id_str = selected_stream[
                    len(stream_pattern_prefix) + 36 + 2 :
                ]
                new_dataset = await Dataset.get(id=new_dataset_id_str)
                consumer_group_name = "consumer_group-" + consumer_id
                consumer_name = f"consumer-{consumer_id}"
                try:
                    await redis_client.xgroup_create(
                        selected_stream, consumer_group_name, "0", mkstream=True
                    )
                except ResponseError as e:
                    if "BUSYGROUP Consumer Group name already exists" not in str(e):
                        logger.error(e)
                        sentry_sdk_capture_exception(e)
                except Exception as e:
                    logger.error(e)
                    sentry_sdk_capture_exception(e)
                message = await redis_client.xreadgroup(
                    consumer_group_name,
                    consumer_name,
                    {selected_stream: ">"},
                    count=1,
                )
                auto_claimed_message = None
                if message:
                    file_obj = message[0][1][0][1]
                    message_id = message[0][1][0][0]
                else:  # if not message:
                    # check idle messages idle larger than t, in milliseconds
                    t = 10 * 60 * 1000
                    # if the app restarts when processing, the message will be reprocessed
                    # if exception breaks processing, the message will be reprocessed
                    auto_claimed_message = await redis_client.xautoclaim(
                        name=selected_stream,
                        groupname=consumer_group_name,
                        consumername=consumer_name,
                        min_idle_time=t,
                        count=1,
                        justid=False,
                    )
                    if auto_claimed_message[1]:
                        # 如果有超时任务，放弃等待该任务；如果重试计数小于3，就增加重试计数，并把新的重试任务添加到队尾，等待下次重试
                        logger.warning(
                            f"found long time idle task at auto_claimed {selected_stream}, o: {old_dataset.id}, n: {new_dataset.id}"
                        )
                        # the task is too long
                        message_id = auto_claimed_message[1][0][0]
                        file_obj = auto_claimed_message[1][0][1]
                        # should not happen unless the app forcefully stopped (kill -9) without interrupting the processing
                        err_str = f"Found unexpected long idle task when auto_claiming stream: {selected_stream}, file_obj: {file_obj}"
                        logger.error(err_str)
                        sentry_sdk_capture_exception(ValueError(err_str))
                        # give up previous task
                        # 遇到超时任务，放弃等待前一个，重试次数小于3，就增加重试次数，重新入队
                        await redis_client.xack(
                            selected_stream, consumer_group_name, message_id
                        )
                        retry_count = int(file_obj.get("retry_count", 0))
                        if retry_count < 3:
                            # at most retry three times, if task is not complete for unknown reason, give up
                            file_obj["retry_count"] = retry_count + 1
                            await redis_client.xadd(selected_stream, file_obj)
                        else:
                            # 如果重试次数等于3，写入这个文件记录，标记为失败？
                            pass
                        continue
                    else:
                        # no idle message and no new message
                        # but there can be messages in the stream because of the idle time is not passing threshold
                        # so we need to check the pending list
                        # 如果没有任务，且没有pending的任务，那么删除stream，更改克隆状态为完成
                        pending_list = await redis_client.xpending(
                            name=selected_stream, groupname=consumer_group_name
                        )
                        if pending_list["pending"] == 0:
                            logger.info(
                                f"delete stream {selected_stream}, because no message, o: {old_dataset.id}, n: {new_dataset.id}"
                            )
                            old_dataset.data_status = DATASET_STATUS.READY
                            await old_dataset.save(update_updated_at=False)
                            new_dataset.data_status = DATASET_STATUS.READY
                            await new_dataset.save()
                            await redis_client.delete(selected_stream)
                        continue
                embedding_params_str = file_obj.get("embedding_params", None)
                should_regenerate_embeddings = file_obj.get(
                    "should_regenerate_embeddings", None
                )
                target_embedding_model = OpenAIModel(
                    file_obj.get("target_embedding_model", None)
                )
                if embedding_params_str:
                    embedding_params = EmbeddingParams(
                        **json.loads(embedding_params_str)
                    )
                    old_collection_name = get_collection_name(
                        embedding_params.model_name,
                        embedding_params.dimensions,
                    )
                    new_collection_name = old_collection_name
                    if should_regenerate_embeddings:
                        new_collection_name = get_collection_name(
                            target_embedding_model,
                        )
                else:
                    old_collection_name = old_dataset_id_str
                    embedding_params = None
                    new_collection_name = new_dataset_id_str
                    if should_regenerate_embeddings:
                        new_collection_name = get_collection_name(
                            target_embedding_model,
                        )
                file_type = file_obj["type"]
                if file_type == "vector_file":
                    # website (url, gitbook, sitemap), files
                    try:
                        file_id = file_obj["file_id"]
                        old_file = await VectorFile.get(id=file_id)
                    except Exception as e:
                        logger.error(e)
                        sentry_sdk_capture_exception(e)
                        continue
                    new_file_dict = dict(old_file)
                    target_embedding_model = OpenAIModel(
                        file_obj["target_embedding_model"]
                    )
                    new_file_id = file_obj["new_file_id"]
                    new_file_dict["id"] = new_file_id
                    new_file_dict["dataset_id"] = new_dataset_id_str
                    old_index_ids = new_file_dict["index_ids"]
                    del new_file_dict["index_ids"]
                    del new_file_dict["created_at"]
                    del new_file_dict["updated_at"]
                    if old_file.file_type == VectorFileType.UPLOAD.value:
                        original_ext = os.path.split(old_file.key)[-1].split(".")[-1]
                        new_key = new_file_dict["id"] + "." + original_ext
                        try:
                            await copy_binary(
                                old_file.key,
                                new_key,
                            )
                            new_file_dict["key"] = new_key
                        except Exception as e:
                            logger.error(e)
                            sentry_sdk_capture_exception(e)
                    # new_file_dict["file_status"] = VectorFileStatus.PROCESS.value
                    # await update_or_create(VectorFile, new_file_dict)
                    if old_index_ids:
                        index_id_map: dict = json.loads(file_obj["index_id_map"])
                        new_index_ids = list(index_id_map.values())
                        for index_id in old_index_ids:
                            new_index_id = index_id_map.get(index_id, None)
                            if not new_index_id:
                                continue
                            os_res = os_client.get_document(
                                old_dataset_id_str, index_id
                            )
                            if os_res:
                                _source = os_res["_source"]
                                _source["metadata"]["file_id"] = new_file_id
                                os_client.os_client.index(
                                    index=new_dataset_id_str,
                                    body=_source,
                                    id=new_index_id,
                                )
                            old_v_type = file_obj["vector_storage"]
                            # 不同的向量存储方式，处理方式不同，目前支持 QDRANT 和 QDRANT_ONE_COLLECTION
                            # 处理时，维持原来的储存方式
                            if old_v_type == VectorStorageType.QDRANT:
                                result = await get_points_with_retry(
                                    collection_name=old_collection_name,
                                    ids=[index_id],
                                )
                                points = result.result
                                if points:
                                    for point in points:
                                        point.id = new_index_id
                                        point.payload["group_id"] = new_dataset_id_str
                                        point.payload["metadata"][
                                            "dataset_id"
                                        ] = new_dataset_id_str
                                        point.payload["metadata"][
                                            "file_id"
                                        ] = new_file_id
                                        if "parent_index" in point.payload["metadata"]:
                                            point.payload["metadata"][
                                                "parent_index"
                                            ] = index_id_map.get(
                                                point.payload["metadata"][
                                                    "parent_index"
                                                ],
                                                "",
                                            )
                                    new_points = [
                                        PointStruct(
                                            id=point.id,
                                            vector=point.vector,
                                            payload=point.payload,
                                        )
                                        for point in points
                                    ]
                                    if should_regenerate_embeddings:
                                        for point in new_points:
                                            res = await async_embeddings_retry(
                                                point.payload["page_content"],
                                                target_embedding_model,
                                            )
                                            point.vector = res
                                    logger.info(f"upserting, {len(new_points)} points")
                                    await upsert_points_with_retry(
                                        collection_name=new_collection_name,
                                        points=new_points,
                                    )
                            elif old_v_type == VectorStorageType.QDRANT_ONE_COLLECTION:
                                result = await get_points_with_retry(
                                    collection_name=old_collection_name, ids=[index_id]
                                )
                                # Access the retrieved points
                                points = result.result
                                if points:
                                    for point in points:
                                        point.id = new_index_id
                                        point.payload["group_id"] = new_dataset_id_str
                                        point.payload["metadata"][
                                            "dataset_id"
                                        ] = new_dataset_id_str
                                        point.payload["metadata"][
                                            "file_id"
                                        ] = new_file_id
                                        if "parent_index" in point.payload["metadata"]:
                                            point.payload["metadata"][
                                                "parent_index"
                                            ] = index_id_map.get(
                                                point.payload["metadata"][
                                                    "parent_index"
                                                ],
                                                "",
                                            )
                                    new_points = [
                                        PointStruct(
                                            id=point.id,
                                            vector=point.vector,
                                            payload=point.payload,
                                        )
                                        for point in points
                                    ]
                                    if should_regenerate_embeddings:
                                        for point in new_points:
                                            res = await async_embeddings_retry(
                                                point.payload["page_content"],
                                                target_embedding_model,
                                            )
                                            point.vector = res
                                    logger.info(f"upserting, {len(new_points)} points")
                                    await upsert_points_with_retry(
                                        collection_name=new_collection_name,
                                        points=new_points,
                                    )
                            else:
                                raise ValueError(
                                    f"Unknown vector storage type: {old_v_type}"
                                )
                        new_file_dict["index_ids"] = new_index_ids
                    # new_file_dict["file_status"] = VectorFileStatus.COMPLETE.value
                    await update_or_create(VectorFile, new_file_dict)
                elif file_type == "function_call_api":
                    function_call_api_input = await FunctionCallApi.get(
                        id=file_obj["id"]
                    )
                    function_call_api_input_dict = dict(function_call_api_input)
                    function_call_api_input_dict["id"] = file_obj["new_id"]
                    function_call_api_input_dict["dataset_id"] = new_dataset_id_str
                    del function_call_api_input_dict["created_at"]
                    del function_call_api_input_dict["updated_at"]
                    obj = await update_or_create(
                        FunctionCallApi, function_call_api_input_dict
                    )
                elif file_type == "faq":
                    # 处理问答库(包含问答对和问答树) 一个任务里处理
                    # 问答对处理
                    faq_list = await Faqs.filter(
                        dataset_id=old_dataset_id_str,
                        deleted_at=None,
                        hierarchy_parent_id=None,
                    )
                    faq_id_map: dict = json.loads(file_obj["faq_id_map"])
                    if len(faq_list) > 0:
                        for faq in faq_list:
                            # faqs表处理
                            new_faq_id = faq_id_map.get(str(faq.id), None)
                            if not new_faq_id:
                                continue

                            is_faq = await Faqs.get_or_none(
                                id=new_faq_id, dataset_id=new_dataset_id_str
                            )
                            if is_faq:
                                await Faqs.filter(
                                    id=new_faq_id, dataset_id=new_dataset_id_str
                                ).delete()
                            new_faq = dict(faq)
                            new_faq["id"] = new_faq_id
                            new_faq["dataset_id"] = new_dataset_id_str
                            new_faq["created_at"] = datetime.now()
                            new_faq_o, _ = await update_or_create(Faqs, new_faq)
                            # 生成新的向量数据
                            await insert_to_vector(new_dataset_id_str, [new_faq_o])
                            await insert_to_vector(
                                dataset_id=new_dataset_id_str,
                                faqs=[new_faq_o],
                                file_type=VectorFileType.FAQ_QUESTION.value,
                            )
                            await update_to_similar_questions(
                                new_dataset_id_str, new_faq_o, False
                            )
                    # 问答树处理
                    faq_tree_list = await Faqs.filter(
                        dataset_id=old_dataset_id_str,
                        deleted_at=None,
                        hierarchy_parent_id__isnull=False,
                    ).order_by("created_at")
                    # old_to_new_faq_map = {}
                    for faq_tree in faq_tree_list:
                        # 创建新的 FAQ 数据
                        new_faq_tree = dict(faq_tree)
                        new_faq_tree_id = faq_id_map.get(str(faq_tree.id), None)
                        if not new_faq_tree_id:
                            continue
                        new_faq_tree["id"] = new_faq_tree_id
                        new_faq_tree["dataset_id"] = new_dataset_id_str
                        new_faq_tree["created_at"] = datetime.now()

                        # 处理 hierarchy_parent_id 关系
                        if faq_tree.hierarchy_parent_id == 0:
                            # 如果是根节点 (hierarchy_parent_id == 0)，则设置为 0
                            new_faq_tree["hierarchy_parent_id"] = 0
                        else:
                            # 如果不是根节点，则查找新 FAQ 的父节点 ID
                            if faq_tree.hierarchy_parent_id in faq_id_map:
                                new_faq_tree["hierarchy_parent_id"] = faq_id_map[
                                    faq_tree.hierarchy_parent_id
                                ]
                            else:
                                # 如果父节点还没有被创建，则暂时设置为 0，等待后续更新
                                new_faq_tree["hierarchy_parent_id"] = 0
                        # 创建或更新新的 FAQ 数据
                        new_faq_tree_o, _ = await update_or_create(Faqs, new_faq_tree)
                        # 生成新的向量数据
                        if faq_tree.is_search:
                            await insert_to_vector(new_dataset_id_str, [new_faq_tree_o])
                            await insert_to_vector(
                                dataset_id=new_dataset_id_str,
                                faqs=[new_faq_tree_o],
                                file_type=VectorFileType.FAQ_QUESTION.value,
                            )
                            await update_to_similar_questions(
                                new_dataset_id_str, new_faq_tree_o, False
                            )

                await redis_client.xack(
                    selected_stream, consumer_group_name, message_id
                )
                # pending_list = await redis_client.xpending(
                #     name=selected_stream, groupname=consumer_group_name
                # )
                # if pending_list["pending"] == 0:
                #     file_logger.info(
                #         f"delete stream {selected_stream}, because no message, o: {old_dataset.id}, n: {new_dataset.id}"
                #     )
                #     old_dataset.data_status = DATASET_STATUS.READY
                #     await old_dataset.save()
                #     new_dataset.data_status = DATASET_STATUS.READY
                #     await new_dataset.save()
                #     await redis_client.delete(selected_stream)
        except Exception as e:
            # app restart, SIGTERM, SIGKILL, SIGINT
            # uncaught exceptions
            # 如果出现未知异常，那么先ack，表示这个任务结束了；再看已经重试的次数，如果小于3，那么加到队列尾部等待重试
            try:
                await redis_client.xack(
                    selected_stream, consumer_group_name, message_id
                )
                # 如果重试次数小于3，把重试次数加 1，这个任务重新入队
                if selected_stream and file_obj:
                    retry_count = int(file_obj.get("retry_count", 0))
                    if retry_count < 3:
                        file_obj["retry_count"] = retry_count + 1
                        await RedisClient.get_client().xadd(selected_stream, file_obj)
                logger.error(e)
                logger.error(traceback.format_exc())
                sentry_sdk_capture_exception(e)
            except Exception as inner_e:
                # Handle the exception that occurred within the except block
                logger.error(
                    f"An exception occurred within the except block: {inner_e}"
                )
                logger.error(traceback.format_exc())
                # Potentially capture this exception as well
                sentry_sdk_capture_exception(inner_e)
        finally:
            selected_stream = None
            consumer_group_name = None
            message_id = None
            old_dataset = None
            new_dataset = None
            file_obj = None
            await asyncio.sleep(1)
