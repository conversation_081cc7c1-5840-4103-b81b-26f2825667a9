import uuid
from datetime import datetime
from typing import Any, List, Optional, Union, Dict
from uuid import UUID

from pydantic import AnyHttpUrl, BaseModel, Field
from pydantic.class_validators import validator
from tortoise import Tortoise
from tortoise.contrib.pydantic import pydantic_model_creator

from mygpt import models
from mygpt.enums import (
    EMBEDDINGS_MODEL,
    FAQ_TYPE,
    FaqSourceType,
    AIModel,
    AIType,
    OpenAIModel,
    StreamingMessageDataType,
    StripeModel,
    VectorStorageType,
    StreamingMessageDataFormat,
    RobotType,
    ResourceType,
    VectorFileStatus,
    VectorFileType,
    VectorFileSourceType,
    DatasourceType,
)

Tortoise.init_models(["mygpt.models"], "models")


class DatasetsTrainingState(BaseModel):
    dataset_id: Union[str, UUID]
    dataset_name: str
    integrations: List = []
    files: List = []


class DatasetsTrainingStateOut(BaseModel):
    class DatasetStatisticalInfo(BaseModel):
        dataset_id: str
        dataset_name: str
        datasource_statistical_map: Dict[str, Dict[str, int]] = Field(
            default_factory=dict
        )

        @validator("datasource_statistical_map", pre=True, always=True)
        def init_statistical_map(cls, v):
            """
            {
                "total": {
                    "crawled": 0,
                    "process": 0,
                    "complete": 0,
                    "fail": 0,
                    "exceeded": 0,
                    "deleted": 0,
                    "ready": 0,
                },
                "integration": {
                    "crawled": 0,
                    "process": 0,
                    "complete": 0,
                    "fail": 0,
                    "exceeded": 0,
                    "deleted": 0,
                    "ready": 0,
                },
                ...
            }
            """
            if not v:
                # 初始化统计字典
                v = {
                    key.value: {
                        status_key.value: 0
                        for status_key in VectorFileStatus.__members__.values()
                    }
                    for key in DatasourceType.__members__.values()
                }
                # 添加 total 统计
                v["total"] = {
                    status_key.value: 0
                    for status_key in VectorFileStatus.__members__.values()
                }
                # 为每个类型添加 total 计数
                for key in v:
                    v[key]["total"] = 0
            return v

    dataset_state: Optional[Dict[str, DatasetStatisticalInfo]] = Field(
        default_factory=dict
    )


class DatasetTrainingRecord(BaseModel):
    """
    SELECT
        r.id as robot_id,
        r.name as robot_name,
        d.id as dataset_id,
        d.name as dataset_name,
        vf.*
    FROM robot as r
    JOIN dataset_robot as dr ON r.id = dr.robot_id
    JOIN dataset as d ON dr.dataset_id = d.id
    JOIN vectorfile as vf ON d.id = vf.dataset_id
    WHERE r.id = $1 AND vf.deleted_at IS NULL
    的结果对应的数据结构
    """

    # Robot信息
    robot_id: UUID
    robot_name: str

    # Dataset信息
    dataset_id: UUID
    dataset_name: str

    # VectorFile信息
    vector_file_id: UUID  # vectorfile id
    key: str = ""
    filename: str
    file_type: VectorFileType
    file_status: VectorFileStatus
    metadata: Optional[Union[Dict[str, Any], str]] = None
    index_ids: Optional[Union[List[str], str]] = None
    source_type: Optional[VectorFileSourceType] = None
    resources: Optional[Union[List[Any], str]] = None
    title: str | None
    file_size: int = 0
    token_count: int = 0
    characters_count: int = 0
    learn_type: Optional[int] = None
    failed_reason: Optional[str] = None

    class Config:
        orm_mode = True


class IntegrationTrainingStateOut(BaseModel):
    """
    集成训练状态输出
    {
        "integration_state": {
            "integration_id (uuid)": {
                "integration_name": "integration_name",
                "type_count": {
                    "complete": 5,
                    "fail": 0,
                },
            "integration_id (uuid)": {
                ...
            }
        }
    }
    """

    class IntegrationStatisticalInfo(BaseModel):
        integration_id: str
        integration_name: str
        type_count: Dict[str, int] = Field(default_factory=dict)

        @validator("type_count", pre=True, always=True)
        def init_type_count(cls, v):
            if not v:
                v = {
                    status_key.value: 0
                    for status_key in VectorFileStatus.__members__.values()
                }
            return v

    integration_state: Optional[Dict[str, IntegrationStatisticalInfo]] = Field(
        default_factory=dict
    )
