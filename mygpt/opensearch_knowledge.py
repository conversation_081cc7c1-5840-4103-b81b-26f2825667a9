import asyncio
import functools
import logging
import time
from typing import Any, Union, Dict, List

from lingua import Language, LanguageDetectorBuilder
from loguru import logger as logging
from opensearchpy import NotFoundError, OpenSearch
from opensearchpy.helpers import bulk
from opensearchpy.helpers.errors import BulkIndexError

from mygpt.indices.knowledge_docs import KNOWLEDGE_DOCS_INDEX
from mygpt.opensearch import retry
from mygpt.settings import OPENSEARCH_URL
from mygpt.utils import SingletonManager


class OpenSearchKnowledgeClient:
    SUPPORTED_LANGUAGES = {
        "en": Language.ENGLISH,
        "ja": Language.JAPANESE,
        "zh": Language.CHINESE,
    }

    def __init__(
        self,
        server_url: str,
        verify_certs: bool = False,
    ) -> None:
        use_ssl = server_url.startswith("https://")
        self.os_client = OpenSearch(
            server_url,
            use_ssl=use_ssl,
            verify_certs=verify_certs,
            ssl_show_warn=False,
            timeout=2,
            max_retries=0,
            trust_env=True,
        )
        self.index = KNOWLEDGE_DOCS_INDEX
        self.lang_detector = (
            LanguageDetectorBuilder.from_languages(*self.SUPPORTED_LANGUAGES.values())
            .with_preloaded_language_models()
            .build()
        )

    @classmethod
    def get_instance(cls) -> Union["OpenSearchKnowledgeClient", None]:
        if OPENSEARCH_URL:
            return SingletonManager.singleton(cls, OPENSEARCH_URL)
        return None

    def close(self):
        try:
            self.os_client.close()
        except Exception as e:
            logging.error(f"Failed to close OpenSearch client: {e}")

    def get_index(self):
        try:
            return self.os_client.indices.get(index=self.index.alias)
        except NotFoundError as e:
            return None

    @retry()
    def create_index(self):
        if self.get_index():
            return

        # Create the index with the specified settings and mappings
        self.os_client.indices.create(index=self.index.name, body=self.index.schema)

    def get_document(self, id_: str):
        try:
            return self.os_client.get(index=self.index.alias, id=id_)
        except NotFoundError as e:
            logging.info(f"index:{self.index.alias} not found")
            return None

    def get_by_doc_ids(self, doc_ids: List[str], source: List[str] = None) -> Dict:
        """通过doc_id获取文档"""
        if not source:
            source = [
                "doc_id",
                "title",
                "content",
                "tags",
                "topics",
            ]
        body = self.index.query(
            is_terms=True,
            term="doc_id",
            value=doc_ids,
            size=100,
            source=source
        )
        try:
            res = self.os_client.search(index=self.index.alias, body=body)
            return res
        except NotFoundError as e:
            return {}


    async def aget_by_doc_ids(self, doc_ids: List[str], fields: List[str]=None) -> Dict:
        """通过doc_id获取文档"""
        return await asyncio.get_running_loop().run_in_executor(
            None, self.get_by_doc_ids, doc_ids, fields
        )

    def get_by_project_id(self, project_id: str, source: List[str] = None) -> Dict:
        """通过project_id获取文档"""
        if not source:
            source = [
                "doc_id",
                "title",
                "content",
                "tags",
                "topics",
            ]
        body = self.index.query(
            is_terms=False,
            term="project_id",
            value=project_id,
            size=100,
            source=source
        )
        try:
            res = self.os_client.search(index=self.index.alias, body=body)
            return res
        except NotFoundError as e:
            return {}

    async def aget_by_project_id(self, project_id: str, fields: List[str] = None) -> Dict:
        """通过project_id获取文档"""
        return await asyncio.get_running_loop().run_in_executor(
            None, self.get_by_project_id, project_id, fields
        )

    def find_docs_by_project_id_without_dir(self, project_id: str, fields: list = None, size=1000) -> Dict:
        """通过项目ID查找文档，不包含目录"""
        logging.info(f"[KnowledgeDocsDao] find_docs_by_project_id_without_dir: {project_id}")
        try:
            body = {
                "size": size,
                "query": {
                    "bool": {
                        "filter": [
                            {
                                "term": {
                                    "project_id": project_id
                                }
                            },
                            {
                                "term": {
                                    "is_directory": False
                                }
                            }
                        ]
                    }
                },
                "_source": fields
            }
            res = self.os_client.search(index=self.index.alias, body=body)
            return res
        except Exception as e:
            logging.error(f"[KnowledgeDocsDao] find_docs_by_project_id_without_dir error: {e}")
            return {}

    async def afind_docs_by_project_id_without_dir(self, project_id: str, fields: list = None, size=1000) -> Dict:
        return await asyncio.get_running_loop().run_in_executor(
            None, self.find_docs_by_project_id_without_dir, project_id, fields, size
        )

    def find_docs_info_by_dir_ids(
        self,
        dir_ids: List[str],
        fields: List[str] = None,
        size=1000
    ) -> Dict:
        """通过多个目录ID查找目录下的文档summary相关信息 (非递归)"""
        if not fields:
            fields = [
                "project_name", "project_id", "doc_id", "title", "is_directory", "summary", "content_token_count",
                "summary_token_count", "information_coverage", "missing_key_aspects", "language", "tags", "topics",
                "path", "path_parts", "depth_level", "current_directory_id", "directory_ids"
            ]
        logging.info(f"[KnowledgeDocsDao] find_docs_info_by_dir_ids: {dir_ids}")
        try:
            body = {
                "size": size,
                "query": {
                    "bool": {
                        "filter": [
                            {
                                "terms": {
                                    "current_directory_id": dir_ids
                                }
                            },
                            {
                                "term": {
                                    "is_directory": False
                                }
                            }
                        ]
                    }
                },
                "_source": fields
            }
            res = self.os_client.search(
                index=self.index.alias,
                body=body
            )
            return res
        except Exception as e:
            logging.error(f"[KnowledgeDocsDao] find_info_by_dir_ids error: {e}")
            return {}

    async def afind_docs_info_by_dir_ids(
        self,
        dir_ids: List[str],
        fields: List[str] = None,
        size=1000
    ) -> Dict:
        """通过多个目录ID查找目录下的文档summary相关信息 (非递归)"""
        return await asyncio.get_running_loop().run_in_executor(
            None, self.find_docs_info_by_dir_ids, dir_ids, fields, size
        )

    def find_dir_info_by_project_id(self, project_id: str, fields: list = None, size=500) -> Dict:
        """通过项目ID查找目录"""
        logging.info(f"[KnowledgeDocsDao] find_dir_info_by_project_id: {project_id}")
        if not fields:
            fields = [
                "dir_id", "dir_name", "is_directory", "directory_description", "is_directory",
                "path", "path_parts", "depth_level", "current_directory_id", "directory_ids",
            ]
        try:
            body = {
                "size": size,
                "query": {
                    "bool": {
                        "filter": [
                            {
                                "term": {
                                    "is_directory": True
                                }
                            },
                            {
                                "term": {
                                    "project_id": project_id
                                }
                            }
                        ]
                    }
                },
                "_source": fields
            }
            res = self.os_client.search(
                index=self.index.alias,
                body=body
            )
            return res
        except Exception as e:
            logging.error(f"[KnowledgeDocsDao] find_dir_info_by_project_id error: {e}")
            return {}

    async def afind_dir_info_by_project_id(
        self,
        project_id: str,
        fields: list = None,
        size=500
    ) -> Dict:
        """通过项目ID获取目录信息"""
        return await asyncio.get_running_loop().run_in_executor(
            None, self.find_dir_info_by_project_id, project_id, fields, size
        )