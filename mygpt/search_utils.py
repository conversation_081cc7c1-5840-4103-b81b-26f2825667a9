import traceback

import qdrant_client
from opensearchpy._async.helpers.actions import async_bulk
from opensearchpy.helpers import BulkIndexError
from pydantic import StrictStr
from qdrant_client.http import models
from qdrant_client.http.models import Filter

from mygpt import settings

async_qdrant_client = qdrant_client.AsyncQdrantClient(
    host=settings.VECTOR_STORAGE_QDRANT_HOST,
    port=settings.VECTOR_STORAGE_QDRANT_HTTP_PORT,
    grpc_port=settings.VECTOR_STORAGE_QDRANT_GRPC_PORT,
    timeout=60,
)


from tenacity import (
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    retry,
)

from mygpt.core.utils import get_collection_name
from mygpt.models import VectorFile
from mygpt.opensearch import get_async_open_search

import asyncio
from loguru import logger as logging

from opensearchpy.exceptions import NotFoundError


def before_log_retry(retry_state):
    if retry_state.attempt_number > 1:
        logging.warning(
            f"Retrying {retry_state.fn.__name__} (attempt {retry_state.attempt_number})"
        )


async def delete_search_indexes(vector_file: VectorFile):
    """并发删除 Qdrant 和 OpenSearch 索引"""
    try:
        # 创建重试装饰器
        retry_decorator = retry(
            stop=stop_after_attempt(3),  # 最多尝试3次（1次初始 + 2次重试）
            wait=wait_exponential(multiplier=1, min=1, max=10),
            retry=retry_if_exception_type(Exception),
            reraise=False,
            before=before_log_retry,
        )

        # 直接应用装饰器创建重试版本的函数
        retry_qdrant = retry_decorator(delete_qdrant_index)
        retry_opensearch = retry_decorator(delete_opensearch_indexes)

        # 并发执行两个删除操作，带重试
        results = await asyncio.gather(
            retry_qdrant(vector_file),
            retry_opensearch(vector_file),
            return_exceptions=True,  # 防止一个失败影响另一个
        )

        # 检查结果
        for i, result in enumerate(results):
            operation = "Qdrant" if i == 0 else "OpenSearch"
            if isinstance(result, Exception):
                logging.error(
                    f"{operation} delete failed for file {vector_file.id}: {result}"
                )
            else:
                logging.info(
                    f"{operation} delete succeeded for file {vector_file.id}: {result}"
                )

        return results

    except Exception as e:
        logging.error(f"Error in delete_indexes for file {vector_file.id}: {e}")
        raise


async def delete_opensearch_indexes(vector_file: VectorFile) -> bool:
    """
    Delete the OpenSearch indexes associated with a given vector file.

    Returns:
        bool: True if the indexes were successfully deleted or if a NotFoundError was caught,
              False in case of early termination (e.g. vector_file is None, dataset not found).
    """
    if not vector_file:
        logging.error("vector file is None")
        return False
    try:
        dataset_obj = await vector_file.dataset.get()
    except Exception as e:
        logging.error(f"Failed to retrieve dataset for file {vector_file.id}: {e}")
        return False
    try:
        # 构建查询条件
        query = {"query": {"term": {"metadata.file_id": str(vector_file.id)}}}

        # 获取 OpenSearch 客户端
        search_client = get_async_open_search()
        if search_client:
            # 使用 query 删除文档
            del_result = await search_client.delete_by_query(
                index=str(dataset_obj.id), body=query, timeout=60
            )
            logging.info(
                f"Deleted search indexes for file by query {vector_file.id}: {del_result}"
            )
            index_ids = vector_file.index_ids
            ret_ids = []
            if index_ids:
                actions = [
                    {
                        "_op_type": "delete",
                        "_index": str(dataset_obj.id),
                        "_id": id_,
                    }
                    for id_ in index_ids
                ]
                try:
                    ret_ids = await async_bulk(
                        search_client, actions, request_timeout=1800
                    )
                except BulkIndexError as e:
                    logging.debug(f"ignore: Failed to delete search indexes ids: {e}")
            logging.info(
                f"Deleted search indexes ids for file {vector_file.id}: {ret_ids}"
            )
            return True
        else:
            logging.error("Failed to get OpenSearch client")
            raise ValueError("Failed to get OpenSearch client")
    except NotFoundError as e:
        logging.warning(f"{e}: ds: {str(dataset_obj.id)}, vf: {vector_file.id}")
        return True
    except Exception as e:
        logging.error(
            f"Failed to delete search indexes for file {vector_file.id}: {e}, {traceback.format_exc()}"
        )
        raise


async def delete_qdrant_index(vector_file: VectorFile) -> bool:
    """
    Delete qdrant index for vector_file

    Args:
        vector_file: The vector file to delete indexes for

    Returns:
        bool: True if deletion was successful, False otherwise
    """
    if not vector_file:
        logging.error(f"vector file is None")
        return False

    try:
        dataset_obj = await vector_file.dataset.get()
        embedding_params = dataset_obj.get_embedding_params()
        collection_name = get_collection_name(
            embedding_params.model_name,
            embedding_params.dimensions,
        )
        # 使用 filter 删除所有与该文件相关的向量
        filter_condition = Filter(
            must=[
                models.FieldCondition(
                    key="metadata.file_id",
                    match=models.MatchValue(value=StrictStr(vector_file.id)),
                ),
            ]
        )
        qdrant_client_delete_result = await async_qdrant_client.delete(
            collection_name=collection_name,
            points_selector=models.FilterSelector(filter=filter_condition),
        )
        logging.info(f"Qdrant delete result: {qdrant_client_delete_result}")
        index_ids = vector_file.index_ids

        if index_ids:
            qdrant_client_delete_result = await async_qdrant_client.delete(
                collection_name=collection_name,
                points_selector=index_ids,
            )
            logging.info(f"Qdrant delete index result: {qdrant_client_delete_result}")

        return True

    except Exception as e:
        logging.error(
            f"Failed to delete qdrant index for file {vector_file.id}: {e}, {traceback.format_exc()}"
        )
        raise
