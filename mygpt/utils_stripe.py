import calendar
import json
import re
from datetime import datetime, timedelta, timezone
from typing import Optional

import dateutil
import pytz
from fastapi import Depends, File, Security, UploadFile
from fastapi.security import HTT<PERSON><PERSON>earer
from loguru import logger as logging
from tortoise.functions import Sum

from mygpt.authorization import (
    depend_api_key_with_none,
    get_current_oauth_user_with_gbase_user,
    verify_robot,
    Auth0UserWGbaseUser,
)
from mygpt.enums import STRIPE_ORDER, AIConfigType, UserConfigType
from mygpt.error import ExceedLimitException, UnauthorizedException
from mygpt.models import (
    Dataset,
    Robot,
    SessionMessage,
    StripePayments,
    StripeProducts,
    User,
    VectorFile,
)
from mygpt.schemata import ApiKey, MyPlanOut
from mygpt.services.quota_service import QuotaService
from mygpt.settings import UNLIMITED_VERSION, RedisClient

MAX_BOT_COUNT_FOR_FREE = 1

http_bearer_scheme = HTTPBearer(auto_error=False)


async def verify_file_size(
    ai_id: str,
    file: UploadFile = File(...),
):
    if UNLIMITED_VERSION:
        return
    user = await User.get_or_none(robots__id=ai_id).prefetch_related("userconfigs")
    if user is not None and verify_account(user.email):
        package = await get_package(user)
        if file.size is not None and file.size > package.max_upload_file * 1024 * 1024:
            raise ExceedLimitException()


async def verify_robots(
    api_key: ApiKey = Depends(depend_api_key_with_none),
    user: Optional[Auth0UserWGbaseUser] = Security(
        get_current_oauth_user_with_gbase_user
    ),
):
    user_id = None
    if not user:
        if api_key:
            user_id = api_key.user.id
    else:
        user_id = user.user.id
    if not user_id:
        raise UnauthorizedException("Login or API key required.")
    await check_robots(user_id)


async def verify_robots_by_apikey(api_key: ApiKey = Depends(depend_api_key_with_none)):
    user_id = api_key.user_id
    if not user_id:
        raise UnauthorizedException("Login required.")
    await check_robots(user_id)


async def check_robots(user_id):
    if UNLIMITED_VERSION:
        return

    # Use the new quota service to check bot quota
    await QuotaService.check_bot_quota(user_id)


class UsedRedisClient:
    @classmethod
    async def get(self, key):
        rs = await RedisClient.get_client().get(key)
        if not rs:
            return None
        return json.loads(rs)

    @classmethod
    async def set(self, key, value):
        val = json.dumps(value)
        try:
            return await RedisClient.get_client().set(key, val, ex=60 * 60 * 24 * 7)
        except Exception as e:
            logging.error(f"set redis error:{e}")
            return None

    @classmethod
    async def unset(self, key):
        try:
            return await RedisClient.get_client().delete(key)
        except Exception as e:
            logging.error(f"unset redis error:{e}")
            return None


async def get_user_question(user_obj: User):
    # key = f"package_question_count_{user_obj.user_id}"
    # question_obj = await UsedRedisClient.get(key)
    # if question_obj:
    #     end_time = float(question_obj['end_at'])
    #     if end_time > datetime.now().timestamp():
    #         return question_obj
    #     else:
    #         # 超过结束时间，清除缓存
    #         await UsedRedisClient.unset(key)
    package = await get_package(user_obj)
    questions = await user_question_num_with_time_period(
        user_obj.user_id,
        package.created_at,
    )
    end_at = package.created_at + timedelta(days=package.month * 30)
    return {
        "count": questions,
        "max_questions": package.max_questions,
        "end_at": end_at.timestamp(),
    }


async def incr_user_count(user: User):
    try:
        question_obj = await get_user_question(user)
        question_obj["count"] += 1
        key = f"package_question_count_{user.user_id}"
        await UsedRedisClient.set(key, question_obj)
    except Exception as e:
        logging.error(f"incr_user_count error:{e}")


async def verify_user_count(user: User):
    try:
        question_obj = await get_user_question(user)
    except Exception as e:
        logging.error(f"verify_user_count error:{e}")
        raise ExceedLimitException()
    max_questions = question_obj["max_questions"]
    if isinstance(max_questions, str):
        max_questions = int(max_questions)
    if question_obj["count"] >= max_questions:
        raise ExceedLimitException()


async def get_robot_used_questions(ai_obj: Robot, user_id: str):
    # key = f"question_count_{ai_obj.id}"
    # question_obj = await UsedRedisClient.get(key)
    # if question_obj:
    #     return question_obj['count']
    # 如果设置了机器人的提问数，则仅查询机器人的提问数
    questions = await user_question_num_with_time_period(
        user_id,
        # 机器人的提问数量和套餐不同，每次增加提问数量都是累计增加，所以这里不需要设置时间限制，时间限制仅判断可用范围
        None,
        ai_id=str(ai_obj.id),
    )
    return questions


async def get_robot_question(ai_obj: Robot, user_id: str):
    max_questions = int(ai_obj.get_config(AIConfigType.MAX_QUESTIONS) or 0)
    start_date = ai_obj.get_config(AIConfigType.QUESTIONS_START_DATE)
    end_date = ai_obj.get_config(AIConfigType.QUESTIONS_END_DATE)

    # 获取当前时间并转换为 UTC 时间
    current_time_utc = datetime.utcnow().replace(tzinfo=pytz.utc)
    # 判断是套餐开始时间是否小于当前时间
    if start_date is not None and start_date != "":
        start_date = dateutil.parser.parse(start_date).astimezone(timezone.utc)
        # 还未到套餐开始时间
        if current_time_utc < start_date:
            max_questions = -1
    # 判断是套餐结束时间是否大于当前时间
    if end_date is not None and end_date != "":
        end_date = dateutil.parser.parse(end_date).astimezone(timezone.utc)
        if current_time_utc > end_date:
            max_questions = -1
    # 无限制，并且也没有设置时间限制
    if max_questions == 0:
        return None
    # 限制了时间，不在可用范围内
    if max_questions == -1:
        max_questions = 0

    count = await get_robot_used_questions(ai_obj, user_id)
    return {
        "count": count,
        "max_questions": max_questions,
    }


async def incr_robot_count(ai_obj: Robot, user_id: str):
    try:
        question_obj = await get_robot_question(ai_obj, user_id)
        if not question_obj:
            # 没有配置对robot的提问数限制，忽略计数增长
            return
        question_obj["count"] += 1
        key = f"question_count_{ai_obj.id}"
        await UsedRedisClient.set(key, question_obj)
    except Exception as e:
        logging.error(f"incr_robot_count error:{e}")


async def verify_robot_count(ai_obj: Robot, user_id: str):
    try:
        question_obj = await get_robot_question(ai_obj, user_id)
    except Exception as e:
        logging.error(f"verify_robot_count error:{e}")
        question_obj = None
    if not question_obj:
        # 没有配置对robot的提问数限制，忽略计数增长
        return
    if question_obj["count"] >= question_obj["max_questions"]:
        raise ExceedLimitException()


async def incr_user_and_robot_question_usage_count(ai_obj: Robot):
    if UNLIMITED_VERSION:
        return
    user = await User.get(robots__id=ai_obj.id).prefetch_related("userconfigs")
    # 增加单个bot的提问数
    await incr_robot_count(ai_obj, user.user_id)
    # 增加用户的提问数
    await incr_user_count(user)


async def verify_questions(ai_obj: Robot = Depends(verify_robot)):
    # if UNLIMITED_VERSION:
    #     return ai_obj
    # user = await User.get(robots__id=ai_obj.id).prefetch_related("userconfigs")
    # # 验证单个bot的提问数
    # await verify_robot_count(ai_obj, user.user_id)
    # # 验证用户的提问数
    # await verify_user_count(user)
    return ai_obj


async def clean_question_cache(
    ai_id: Optional[str] = None, user_id: Optional[str] = None
):
    if ai_id:
        key = f"question_count_{ai_id}"
        await UsedRedisClient.unset(key)
    if not user_id:
        user_obj = await User.get(robots__id=ai_id)
        user_id = user_obj.user_id
    key = f"package_question_count_{user_id}"
    await UsedRedisClient.unset(key)
    key = f"token_used_{user_id}"
    await UsedRedisClient.unset(key)


async def get_package(user_obj: User) -> StripeProducts:
    await user_obj.fetch_related("userconfigs")
    userconfig_dict = {
        userconfig.key: userconfig.value for userconfig in user_obj.userconfigs
    }
    max_token = userconfig_dict.get(UserConfigType.MAX_TOKEN)
    max_question = userconfig_dict.get(UserConfigType.MAX_QUESTION)
    max_upload_files = userconfig_dict.get(UserConfigType.MAX_UPLOAD_FILES)
    free_package = StripeProducts(
        name="Free",
        max_questions=max_question or 100,
        max_tokens=max_token or 100000,
        max_upload_file=max_upload_files or 3,
        month=1,
        created_at=get_current_month_first_day(),
    )
    if UNLIMITED_VERSION:
        max_tokens = 1200000000000000
        return StripeProducts(
            name="Enterprise",
            max_questions=max_tokens,
            max_tokens=max_tokens,
            max_upload_file=max_tokens,
            order=STRIPE_ORDER.ENTERPRISE,
            month=1,
            created_at=get_current_month_first_day(),
        )
    today = datetime.now().date()
    if not user_obj:
        return free_package
    if user_obj.corporate:
        return StripeProducts(
            name="Enterprise",
            max_questions=max_question or 40000,
            max_tokens=max_token or 24000000,
            max_upload_file=max_upload_files or 30,
            order=STRIPE_ORDER.ENTERPRISE,
            month=1,
            created_at=get_current_month_first_day(),
        )

    # 查询用户当前月度套餐
    one_month_ago = today - timedelta(days=30)
    package_monthly = (
        await StripePayments.filter(
            user_id=user_obj.id,
            stripe_products__month=1,
            created_at__gte=one_month_ago,
        )
        .prefetch_related("stripe_products")
        .first()
    )

    # 查询用户当前年度套餐
    one_year_ago = today - timedelta(days=365)
    package_yearly = (
        await StripePayments.filter(
            user_id=user_obj.id,
            stripe_products__month=12,
            created_at__gte=one_year_ago,
        )
        .prefetch_related("stripe_products")
        .first()
    )

    if package_monthly:
        package = package_monthly.stripe_products
        package.created_at = package_monthly.created_at
    if package_yearly:
        package = package_yearly.stripe_products
        package.created_at = package_yearly.created_at
    if package_monthly is None and package_yearly is None:
        package = free_package
    # 以管理员设置的提问数，token数为准
    package.max_questions = max_question or package.max_questions
    package.max_tokens = max_token or package.max_tokens
    package.max_upload_file = max_upload_files or package.max_upload_file
    return package


async def get_plan(user_obj: User, robot_id: str = None, gt_date=True) -> MyPlanOut:
    package = await get_package(user_obj)
    max_questions = (
        int(package.max_questions) if package.max_questions else package.max_questions
    )
    questions_start_date, questions_end_date = None, None
    if robot_id:
        robot = await Robot.get_or_none(id=robot_id).prefetch_related("robotconfigs")
        robot_max_questions = robot.get_config(AIConfigType.MAX_QUESTIONS)
        questions_start_date = robot.get_config(AIConfigType.QUESTIONS_START_DATE)
        questions_end_date = robot.get_config(AIConfigType.QUESTIONS_END_DATE)
        count = await get_robot_used_questions(robot, user_obj.user_id)
        used_questions = {
            "count": count,
        }
        if robot_max_questions is not None and robot_max_questions != "":
            max_questions = int(robot_max_questions)
        else:
            # 如果未设置机器人的提问数，则使用套餐的提问数 - 已使用的提问数
            user_used_questions = await get_user_question(user_obj)
            max_questions = max_questions - user_used_questions["count"]
    else:
        used_questions = await get_user_question(user_obj)
    used_tokens_info = await get_user_token(user_obj)
    plan = MyPlanOut(
        created_at=package.created_at,
        # end_at=package.end_at,
        name=package.name,
        max_questions=max_questions,
        used_questions=used_questions["count"],
        max_tokens=package.max_tokens,
        used_tokens=used_tokens_info["used_tokens"],
        max_upload_file=package.max_upload_file,
        param_ext=package.param_ext,
        order=package.order,
        month=package.month,
        questions_start_date=questions_start_date,
        questions_end_date=questions_end_date,
    )
    return plan


async def user_question_num_with_time_period(
    user_id: str,
    date: datetime = None,
    ai_id: str = None,
) -> int:
    if ai_id:
        ids = [ai_id]
    else:
        ids = await Robot.filter(
            user_id=user_id,
            deleted_at__isnull=True,
        ).values_list("id", flat=True)
    message_filter = {
        "session__robot_id__in": ids,
        "total_tokens__gt": 0,
        "is_test": False,
    }
    recommended_filter = {
        "robot_id__in": ids,
    }
    if date:
        message_filter["updated_at__gte"] = date
        recommended_filter["created_at__gte"] = date
    session_message_count = await SessionMessage.filter(**message_filter).count()
    # question_recommended_count = await QuestionRecommended.filter(**recommended_filter).count()
    # return session_message_count + question_recommended_count
    return session_message_count


async def used_tokens_with_time_period(user_id: str, date: datetime) -> int:
    datasets = await Dataset.filter(user_id=user_id)
    tokens = 0

    dataset_ids = [d.id for d in datasets]
    token_count_field = (
        await VectorFile.annotate(total_token_count=Sum("token_count"))
        .filter(
            dataset_id__in=dataset_ids,
            created_at__gte=date,
            file_status="complete",
        )
        .values("total_token_count")
    )

    for token_count in token_count_field:
        tokens += (
            token_count.get("total_token_count")
            if token_count.get("total_token_count")
            else 0
        )

    # metadata_field = (
    #     await VectorFile.filter(
    #         dataset_id__in=dataset_ids,
    #         created_at__gte=date,
    #         file_status="complete",
    #     )
    #     .annotate(token=RawSQL("""sum((metadata->'tokens')::integer)"""))
    #     .values("token")
    # )
    #
    # for token_count in metadata_field:
    #     tokens += token_count.get("token") if token_count.get("token") else 0

    return tokens


def verify_account(email: str):
    match = re.search(r"@([^.]+)\.", email)
    if match:
        if match.group(1) == "sparticle":
            return False
        return True
    else:
        return True


def get_current_month_first_day() -> datetime:
    today = datetime.now().date()
    return datetime(today.year, today.month, 1)


def get_current_month_last_day() -> datetime:
    now = datetime.now()
    last_day = calendar.monthrange(now.year, now.month)[1]
    return datetime(now.year, now.month, last_day)


async def get_user_token(user: User):
    key = f"token_used_{user.user_id}"
    used_token = await UsedRedisClient.get(key)
    if used_token:
        end_time = float(used_token["end_at"])
        if end_time < datetime.now().timestamp():
            await UsedRedisClient.unset(key)
        else:
            return used_token
    package = await get_package(user)
    # 查询当前用户的使用量
    used_tokens = await used_tokens_with_time_period(user.user_id, package.created_at)
    end_at = package.created_at + timedelta(days=package.month * 30)
    used_token = {
        "max_tokens": package.max_tokens,
        "used_tokens": used_tokens,
        "end_at": end_at.timestamp(),
    }
    await UsedRedisClient.set(key, used_token)
    return used_token
