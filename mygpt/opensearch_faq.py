import asyncio
import functools
import logging
import time
from typing import Any, Union

from lingua import Language, LanguageDetectorBuilder
from loguru import logger as logging
from opensearchpy import NotFoundError, OpenSearch
from opensearchpy.helpers import bulk
from opensearchpy.helpers.errors import BulkIndexError

from mygpt.indices.faq_questions import FAQ_QUESTIONS_INDEX
from mygpt.opensearch import retry
from mygpt.settings import OPENSEARCH_URL
from mygpt.utils import SingletonManager


class OpenSearchFaqClient:
    SUPPORTED_LANGUAGES = {
        "en": Language.ENGLISH,
        "ja": Language.JAPANESE,
        "zh": Language.CHINESE,
    }

    def __init__(
        self,
        server_url: str,
        verify_certs: bool = False,
    ) -> None:
        use_ssl = server_url.startswith("https://")
        self.os_client = OpenSearch(
            server_url,
            use_ssl=use_ssl,
            verify_certs=verify_certs,
            ssl_show_warn=False,
            timeout=2,
            max_retries=0,
            trust_env=True,
        )
        self.index = FAQ_QUESTIONS_INDEX
        self.lang_detector = (
            LanguageDetectorBuilder.from_languages(*self.SUPPORTED_LANGUAGES.values())
            .with_preloaded_language_models()
            .build()
        )

    @classmethod
    def get_instance(cls) -> Union["OpenSearchFaqClient", None]:
        if OPENSEARCH_URL:
            return SingletonManager.singleton(cls, OPENSEARCH_URL)
        return None

    def get_index(self):
        try:
            return self.os_client.indices.get(index=self.index.alias)
        except NotFoundError as e:
            return None

    @retry()
    def create_index(self):
        if self.get_index():
            return

        # Create the index with the specified settings and mappings
        self.os_client.indices.create(index=self.index.name, body=self.index.schema)

    def get_document(self, id: str):
        try:
            return self.os_client.get(index=self.index.alias, id=id)
        except NotFoundError as e:
            logging.info(f"index:{self.index.alias} not found")
            return None

    def build_document_body(self, dataset_id: str, faq_id: str, document: str):
        language: str | None = None
        try:
            detected_lang: Language | None = self.lang_detector.detect_language_of(
                document
            )
            if detected_lang:
                language = detected_lang.iso_code_639_1.name.lower()
        except Exception as _e:
            logging.info(f"Cannot detect language: {document}")

        contents = {
            "language": language,
            "supported": bool(language),
            "default": document,
        }
        if language:
            contents[language] = document

        document_body: dict[str, Any] = {
            "_index": self.index.alias,
            "_id": faq_id,
            "dataset_id": dataset_id,
            "contents": contents,
        }
        return document_body

    @retry()
    def add_documents(self, dataset_id: str, ids: list, documents: list):
        if not self.get_index():
            # 索引不存在时，创建索引
            self.create_index()
        if len(ids) != len(documents):
            raise Exception("ids and documents must be the same length")
        body = []
        for idx, document in enumerate(documents):
            document_body = self.build_document_body(dataset_id, ids[idx], document)
            body.append(document_body)
        return bulk(self.os_client, body, request_timeout=1800)

    def delete_documents(self, ids: list):
        body = []
        for id in ids:
            delete_body = {
                "_op_type": "delete",
                "_index": self.index.alias,
                "_id": id,
            }
            body.append(delete_body)
        try:
            return bulk(self.os_client, body, request_timeout=1800)
        except BulkIndexError as e:
            return None

    @retry()
    def search(
        self,
        dataset_ids: list[str],
        query,
        min_score,
        size=10,
    ):
        body = FAQ_QUESTIONS_INDEX.search(
            dataset_ids=dataset_ids,
            query=query,
            min_score=min_score,
            size=size,
        )
        try:
            res = self.os_client.search(index=self.index.alias, body=body)
            return res
        except NotFoundError as e:
            return None

    async def asearch(
        self,
        dataset_ids: list[str],
        query,
        min_score,
        size=10,
    ):
        return await asyncio.get_running_loop().run_in_executor(
            None, self.search, dataset_ids, query, min_score, size
        )

    async def adelete_documents(self, ids: list):
        return await asyncio.get_running_loop().run_in_executor(
            None, self.delete_documents, ids
        )

    async def aadd_documents(self, dataset_id: str, ids: list, documents: list):
        return await asyncio.get_running_loop().run_in_executor(
            None, self.add_documents, dataset_id, ids, documents
        )
