import re


class DocumentTranscoder:

    ENCODE_SPACES_REGEX = r"[\s\t]{6,}"
    DECODE_SPACES_REGEX = r"\[_s_:(\d+)\]"
    ENCODE_DASHES_REGEX = r"[-]{6,}"
    DECODE_DAHS_REGEX = r"\[_-_:(\d+)\]"

    def __init__(self, content: str) -> None:
        self.content = content

    def encode(self):
        rows = self.content.split("\n")
        for i in range(len(rows)):
            content = rows[i]
            content = self.encode_spaces(content)
            content = self.encode_dashes(content)
            rows[i] = content
        self.content = "\n".join(rows)
        return self.content

    def decode(self):
        rows = self.content.split("\n")
        for i in range(len(rows)):
            content = rows[i]
            content = self.decode_spaces(content)
            content = self.decode_dashes(content)
            rows[i] = content
        self.content = "\n".join(rows)
        return self.content

    def encode_spaces(self, content: str):
        return re.sub(
            DocumentTranscoder.ENCODE_SPACES_REGEX,
            lambda match: f"[_s_:{len(match.group(0))}]",
            content,
        )

    def decode_spaces(self, content: str):
        return re.sub(
            DocumentTranscoder.DECODE_SPACES_REGEX,
            lambda match: " " * int(match.group(1) or "0"),
            content,
        )

    def encode_dashes(self, content: str):
        return re.sub(
            DocumentTranscoder.ENCODE_DASHES_REGEX,
            lambda match: f"[_-_:{len(match.group(0))}]",
            content,
        )

    def decode_dashes(self, content: str):
        return re.sub(
            DocumentTranscoder.DECODE_DAHS_REGEX,
            lambda match: "-" * int(match.group(1) or "0"),
            content,
        )
