import logging
import re
import cssutils
from pydantic import BaseModel
from typing import List

LOGGER = logging.getLogger(__name__)

cssutils.log.setLevel(logging.CRITICAL)


class Outline(BaseModel):
    level: int
    text: str


OUTLINE_PREFIX = "Breadcrumb:"


class OutlineParser:

    DEFAULT_FONT_SIZE = 14

    def __init__(self, outlines: List[Outline | dict]) -> None:
        if outlines and isinstance(outlines[0], dict):
            outlines = [Outline(**outline) for outline in outlines]
        self.outlines = outlines

    def find_outline_full_path(self, text: str) -> str | None:
        try:
            path = ""
            for index in range(len(self.outlines)):
                outline = self.outlines[index]
                if text.strip().startswith(outline.text):
                    return self.get_outline_full_path(index)
            return path
        except Exception as e:
            LOGGER.error(f"find_outline_full_path error: {e}")
            return ""

    def get_outline_full_path(self, index: int) -> str | None:
        path = ""
        outline = self.outlines[index]
        if outline:
            path = outline.text
            prev_level = outline.level
            while index > 0:
                index -= 1
                outline = self.outlines[index]
                if outline.level >= prev_level:
                    continue
                path = outline.text + "/" + path
                if outline.level == 1:
                    break
        return path

    @staticmethod
    def extract_breadcrumb(original_string: str) -> str | None:
        match = re.search(rf"{re.escape(OUTLINE_PREFIX)}(.*?)\n\n", original_string)
        if match:
            return match.group(1)
        return None

    @staticmethod
    def delete_breadcrumb(original_string: str):
        return re.sub(rf"{re.escape(OUTLINE_PREFIX)}.*\n\n", "", original_string)

    @staticmethod
    def create_breadcrumb(string: str) -> str:
        return f"{OUTLINE_PREFIX}{string}\n\n"
