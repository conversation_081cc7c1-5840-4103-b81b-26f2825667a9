import os
from uuid import uuid4
from pypdf import PdfReader, PdfWriter
from mygpt.utils import get_file_tmp_location


def split_pdf_file(file_location: str, page_size: int):
    """
    按指定页数拆分为多个pdf文件
    """
    base_name, file_extension = os.path.splitext(os.path.basename(file_location))
    # 打开原始的PDF文件
    pdf_file = open(file_location, "rb")
    pdf_reader = PdfReader(pdf_file)
    outlines = pdf_reader.outline
    # 计算总页数
    total_pages = 0
    try:
        total_pages = len(pdf_reader.pages)
    except Exception:
        # 关闭原始PDF文件
        pdf_file.close()
        return [file_location]

    # 定义每个新PDF文件包含的页面数量
    pages_per_file = page_size

    # 计算需要创建的新PDF文件数量
    num_files = total_pages // pages_per_file + 1

    child_pdf_files: list[str] = []
    uuid = str(uuid4()).replace("-", "")
    # 逐个创建新的PDF文件
    for file_num in range(num_files):
        # 创建一个新的PDF文件来存储分割后的页面
        pdf_writer = PdfWriter()

        def copy_outline(_outlines, _parent=None):
            parent = None
            for outline in _outlines:
                if isinstance(outline, list):
                    copy_outline(outline, parent)
                else:
                    parent = pdf_writer.add_outline_item(
                        outline.title, None, parent=_parent
                    )

        # 计算要复制的页面范围
        start_page = file_num * pages_per_file
        end_page = min((file_num + 1) * pages_per_file, total_pages) - 1

        if (start_page - end_page) > 0:
            continue
        for page_num in range(start_page, end_page + 1):
            page = pdf_reader.pages[page_num]
            pdf_writer.add_page(page)
        if outlines and file_num == 0:
            copy_outline(outlines)
        # 保存新的PDF文件
        file_name = get_file_tmp_location(
            f"{base_name}_{uuid}_split_{file_num + 1}{file_extension}"
        )
        output_pdf = open(file_name, "wb")  # 使用递增的文件名
        pdf_writer.write(output_pdf)
        output_pdf.close()
        child_pdf_files.append(file_name)

    # 关闭原始PDF文件
    pdf_file.close()
    return child_pdf_files
