import os

import pandas as pd
from openpyxl.reader.excel import load_workbook
from pypdf import PdfReader

ALLOW_EXCEL_MERGED_CELLS = (
    os.getenv("ALLOW_EXCEL_MERGED_CELLS", "false").lower() == "true"
)


def check_excel_file(file_path: str):
    """
    检测excel文件是否符合要求
    1. 文件后缀必须是.xlsx或者.xls
    2. 行数不能超过10000
    3. 列数不能超过100
    4. 不能有合并单元格, 除非 ALLOW_EXCEL_MERGED_CELLS
    """
    if not file_path.endswith(".xlsx") and not file_path.endswith(".xls"):
        return

    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File {file_path} does not exist.")

    xls = pd.ExcelFile(file_path)
    for sheet_name in xls.sheet_names:
        df = pd.read_excel(xls, sheet_name)
        if len(df) > 2000:
            raise ValueError(
                f"Sheet {sheet_name} exceeds the maximum allowed rows (2,000)."
            )
        if len(df.columns) > 100:
            raise ValueError(
                f"Sheet {sheet_name} exceeds the maximum allowed columns (100)."
            )

        if not ALLOW_EXCEL_MERGED_CELLS:
            # 检查合并单元格
            workbook = load_workbook(file_path, data_only=True)
            worksheet = workbook[sheet_name]
            if worksheet.merged_cells.ranges:
                raise ValueError(
                    f"{sheet_name} contains merged cells, ranges: {worksheet.merged_cells.ranges}. Importing Excel files with merged cells is not allowed."
                )


def check_pdf_file(file_path: str):
    """
    检测pdf文件是否符合要求
    1. 文件后缀必须是.pdf
    2. 是否需要密码
    """
    if not file_path.endswith(".pdf"):
        return
    with open(file_path, "rb") as f:
        reader = PdfReader(f)
        if reader.is_encrypted:
            raise ValueError(
                "The PDF file is encrypted. We do not support encrypted PDF files."
            )


def is_ppt_file(file_path: str):
    """
    检查是否为ppt格式文件，是返回True, 否则False
    """
    if file_path.endswith(".ppt") or file_path.endswith(".pptx"):
        return True
    else:
        return False
