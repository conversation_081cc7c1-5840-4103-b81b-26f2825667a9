from enum import Enum
from urllib.parse import urlsplit


class UnstructuredType(str, Enum):
    PDF = "pdf"
    DOCX = "docx"
    XLSX = "xlsx"
    CSV = "csv"
    RTF = "rtf"
    HTML = "html"
    MARKDOWN = "md"
    TXT = "txt"
    URL = "url"
    PPTX = "pptx"
    UNKNOWN = "unknown"


class DocumentFormat(str, Enum):
    MARKDOWN = "markdown"
    TEXT = "text"
    HTML = "html"


def is_html(path: str):
    return path.endswith(".html") or path.endswith(".htm")


def filename_to_unstructured_type(filename: str) -> UnstructuredType:
    filename = filename.lower()
    if filename.endswith(".pdf"):
        return UnstructuredType.PDF
    elif filename.endswith(".docx") or filename.endswith(".doc"):
        return UnstructuredType.DOCX
    elif filename.endswith(".xlsx") or filename.endswith(".xls"):
        return UnstructuredType.XLSX
    elif filename.endswith(".csv"):
        return UnstructuredType.CSV
    elif filename.endswith(".rtf"):
        return UnstructuredType.RTF
    elif (
        is_html(filename)
        and not filename.startswith("http://")
        and not filename.startswith("https://")
    ):
        return UnstructuredType.HTML
    elif filename.endswith(".txt"):
        return UnstructuredType.TXT
    elif filename.endswith(".md"):
        return UnstructuredType.MARKDOWN
    elif filename.endswith(".pptx") or filename.endswith(".ppt"):
        return UnstructuredType.PPTX
    elif filename.startswith("http://") or filename.startswith("https://"):
        parsed_url = urlsplit(filename)
        url_path = parsed_url.path
        file_type = filename_to_unstructured_type(url_path)
        return (
            file_type
            if file_type != UnstructuredType.UNKNOWN
            and file_type != UnstructuredType.HTML
            else UnstructuredType.URL
        )
    else:
        return UnstructuredType.UNKNOWN


class DocumentParserPlugin(str, Enum):
    TABLE = "table"
    OUTLINE = "outline"
    METADATA = "metadata"
