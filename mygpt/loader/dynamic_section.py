from typing import List, Optional
import asyncio
from mygpt.openai_utils import aget_sections, arefine_sections
from mygpt.outputs import SectionOutput, SectionsOutput
from loguru import logger as logging


# 调用openai_utils进行大模型调用
async def acreate_sections(pages: List[dict], is_ppt: bool) -> List[SectionOutput]:
    try:
        tasks = [
            aget_sections(
                "page_num:" + str(page["num"]) + "\n" + page["content"], is_ppt
            )
            for page in pages
        ]
        results = await asyncio.gather(*tasks)
        if results is None or len(results) == 0:
            return None
        all_sections = [s for r in results for s in r]
        all_sections = await arefine_sections(all_sections, is_ppt)
        if all_sections is None or len(all_sections) == 0:
            return None
        # 针对all_sections中的每个section增加index键值对
        for i, section in enumerate(all_sections):
            section["index"] = i
        return all_sections
    except Exception as e:
        logging.warning(f"create dynamic sections exception: {e}")
        return None
