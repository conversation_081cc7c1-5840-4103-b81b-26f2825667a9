import threading
import logging

from mygpt.aes_util import AES<PERSON>ip<PERSON>
from mygpt.enums import AIConfigType
from mygpt.models import Robot, VectorFile, RobotConfig
from mygpt.openai_utils import language_detect
from mygpt.settings import AES_SECRETS

MAX_EMBEDDINGS_TASK_NUM = 6
MAX_TOKENS = 100000


encoder = AESCipher(AES_SECRETS)

# Create a set to store processed AI IDs
processed_ai_ids = set()

# Create a lock to ensure thread-safety
processed_ai_ids_lock = threading.Lock()


async def set_ai_source_language_if_need(ai_id: str, chunk: str):
    try:
        with processed_ai_ids_lock:
            if ai_id in processed_ai_ids:
                return

            processed_ai_ids.add(ai_id)

        has = await does_ai_has_source_lang(ai_id)
        if has:
            return
        lang, _ = await language_detect(chunk)
        if lang == "":
            with processed_ai_ids_lock:
                processed_ai_ids.remove(ai_id)
                return

        logging.info(f"update ai:{ai_id} language:{lang}, chunk:{repr(chunk)}")

        await update_ai_source_language(ai_id, lang)
        return lang
    except Exception as e:
        logging.error(f"fail to set_ai_source_language_if_need in error:{e}")
        return None


async def does_ai_has_source_lang(ai_id: str):
    robot_obj = await Robot.get_or_none(id=ai_id)
    if not robot_obj:
        return False

    config_obj = await RobotConfig.filter(robot_id=ai_id, key=AIConfigType.SOURCE_LANG)
    if not config_obj:
        return False

    return True


async def update_ai_source_language(ai_id: str, language: str):
    robot_obj = await Robot.get_or_none(id=ai_id)
    if not robot_obj:
        return

    await RobotConfig.update_or_create(
        robot=robot_obj,
        key=AIConfigType.SOURCE_LANG,
        defaults={"value": language},
    )


async def update_match_faq_min_score(ai_id: str, min_score: float):
    robot_obj = await Robot.get_or_none(id=ai_id)
    if not robot_obj:
        return

    await RobotConfig.update_or_create(
        robot=robot_obj,
        key=AIConfigType.MATCH_FAQ_MIN_SCORE,
        defaults={"value": min_score},
    )


async def user_used_tokens(user_id: str):
    ids = await Robot.filter(user_id=user_id).values("id")
    total_tokens = 0
    for id in ids:
        robot_tokens = await VectorFile.get_robot_tokens(str(id["id"]))
        total_tokens += robot_tokens
    return total_tokens
