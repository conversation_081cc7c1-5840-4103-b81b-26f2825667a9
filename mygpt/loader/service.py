import json
import time
import traceback
from typing import Any, List, Optional

import aiohttp
from langchain.docstore.document import Document
from loguru import logger as logging
from pydantic import BaseModel
from skywalking.decorators import trace

from mygpt import settings
from mygpt.loader.dynamic_section import acreate_sections
from mygpt.loader.enums import DocumentFormat, DocumentParserPlugin, UnstructuredType
from mygpt.loader.utils import check_excel_file, check_pdf_file, is_ppt_file
from mygpt.settings import (
    PARSER_TOKEN,
    PARSER_URL,
    PARSER_URLS,
    PARSER_TOKENS,
    RedisClient,
)
from mygpt.utils import async_timing_decorator


class ParserPlugin(BaseModel):
    plugin: DocumentParserPlugin
    params: Optional[dict] = None


import random
from typing import Tu<PERSON>


def get_next_parser() -> Tuple[str, str]:
    """随机选择一个 parser 服务"""
    if not PARSER_URLS:
        if not PARSER_URL or not PARSER_TOKEN:
            raise ValueError("Parser configuration is missing")
        return PARSER_URL, PARSER_TOKEN

    if len(PARSER_URLS) != len(PARSER_TOKENS):
        raise ValueError("Parser URLs and tokens count mismatch")

    # 随机选择索引
    index = random.randrange(len(PARSER_URLS))
    url = PARSER_URLS[index]
    token = PARSER_TOKENS[index]

    logging.info(
        f"get_next_parser, total parsers: {len(PARSER_URLS)}, selected index: {index}, url: {url}, token: {token[:20]}...{token[-20:]}"
    )
    return url, token


@async_timing_decorator
async def parser_service(
    api_url: str,
    payload: Any = None,
    json: Any = None,
    params: dict | None = None,
    type: str = "json",
    client: aiohttp.ClientSession = None,
    parser_url=None,
    parser_token=None,
):
    if not parser_url or not parser_token:
        logging.warning(
            f"No parser configured for parser_service {api_url}, using next parser, {parser_url}, {parser_token}"
        )
        parser = get_next_parser()
        parser_url, parser_token = parser
    url = f"{parser_url}/{api_url}"
    headers = {
        "Authorization": "Bearer " + parser_token,
    }

    async def send(client: aiohttp.ClientSession):
        logging.info(f"Sending {url}")
        async with client.post(
            url,
            data=payload,
            json=json,
            params=params,
            timeout=3600 * 2,
            headers=headers,
        ) as r:
            if r.status == 200:
                if type == "json":
                    data = await r.json()
                    if "code" in data and "message" in data:
                        logging.debug(f"parser_service: {data}")
                        raise ValueError(
                            data if isinstance(data, str) else data["message"]
                        )
                    return data
                elif type == "bytes":
                    return await r.read()
                else:
                    return await r.text()
            else:
                error_content = await r.text()
                detail = f"parser_service({url}): {r.status} {error_content}"
                logging.error(detail)
                raise ValueError(detail)

    try:
        if client:
            parser_result = await send(client)
        else:
            async with aiohttp.ClientSession() as session:
                parser_result = await send(session)
        return parser_result
    except Exception as e:
        logging.error(f"parser_service({url}): {e}, {traceback.format_exc()}")
        # raise ValueError(f"Parser service error: {e}")


async def download_from_parser_service(
    file_id: str,
    client: aiohttp.ClientSession = None,
):
    parser_conf = await RedisClient.get_client().get(
        file_id,
    )
    logging.info(
        f"download_from_parser_service, file_id: {file_id}, parser_conf: {parser_conf}"
    )
    if parser_conf:
        parser_conf = json.loads(parser_conf)
        PARSER_URL, PARSER_TOKEN = parser_conf
    else:
        PARSER_URL, PARSER_TOKEN = get_next_parser()

    url = f"{PARSER_URL}/document/download/{file_id}"
    headers = {
        "Authorization": "Bearer " + PARSER_TOKEN,
    }

    async def download(client: aiohttp.ClientSession):
        async with client.get(url, timeout=3600 * 2, headers=headers) as r:
            if r.status == 200:
                return await r.read()
            else:
                error_content = await r.text()
                detail = (
                    f"download_from_parser_service({url}): {r.status} {error_content}"
                )
                logging.error(detail)
                raise ValueError(detail)

    max_retries = 5
    attempt = 0

    while True:
        try:
            if client:
                return await download(client)
            else:
                async with aiohttp.ClientSession() as session:
                    return await download(session)
        except (aiohttp.ClientOSError, aiohttp.ServerDisconnectedError):
            if attempt < max_retries:
                attempt += 1
                logging.warning(f"download_from_parser_service({url}): retry {attempt}")
            else:
                raise


@trace()
@async_timing_decorator
async def load_file(
    file_path: str,
    type: UnstructuredType | None = None,
    learn_type: int = 0,
    src_name: str = "",
    format: DocumentFormat = DocumentFormat.HTML,
    plugins: List[ParserPlugin] | None = None,
    page_break: bool = True,
    max_images: int = 999,
    min_image_width: int = 100,
    min_image_height: int = 100,
    min_image_size: int = 1024,
    max_image_size: int = 1024 * 1024 * 10,
    client: aiohttp.ClientSession = None,
    parser_url=None,
    parser_token=None,
) -> List[Document]:
    max_retries = 5
    attempt = 0

    is_ppt = is_ppt_file(file_path)
    check_excel_file(file_path)
    check_pdf_file(file_path)
    if (
        learn_type > 0
        and len(
            [
                desc
                for desc in settings.ADVANCED_PARSER_DESC
                if desc[0] == learn_type and desc[2] == True
            ]
        )
        == 0
    ):
        raise ValueError("invalid learn_type")

    while True:
        try:
            with open(file_path, "rb") as file:
                payload = {"file": file, "plugins": json.dumps(plugins or [])}
                params = {
                    "learn_type": learn_type,
                    "src_name": src_name,
                    "page_size": 1,
                    "page_break": "true" if page_break else "false",
                    "max_images": max_images,
                    "min_image_width": min_image_width,
                    "min_image_height": min_image_height,
                    "min_image_size": min_image_size,
                    "max_image_size": max_image_size,
                    "ignore_images_count": 1000,
                    "use_file_storage": "true",
                }
                if type:
                    params["type"] = type.value
                if format:
                    params["format"] = format.value
                s_time = time.time()
                result = await parser_service(
                    "document/load_file",
                    payload=payload,
                    params=params,
                    client=client,
                    parser_url=parser_url,
                    parser_token=parser_token,
                )
                logging.info(
                    f"parser_service: {parser_url}, file: {file_path}, cost: {time.time() - s_time}, \
                             params: {params}, result: {result}"
                )
                if result is None:
                    logging.error(
                        f"Parser service error, result is None, api document/load_file, {parser_url=}, token: {parser_token[:20]}...{parser_token[-20:]}"
                    )
                    raise ValueError("Parser service error, result is None")
                file_id = result["file_id"]
                logging.info(
                    f"load_file result: {file_id}, {parser_url}, {parser_token}"
                )
                await RedisClient.get_client().set(
                    file_id,
                    json.dumps([parser_url, parser_token]),
                )
                content = await download_from_parser_service(
                    file_id,
                    client=client,
                )
                logging.info(
                    f"parser_service download: file: {file_path}, cost: {time.time() - s_time}"
                )
                trace_content = content
                content = content.decode("utf-8")
                docs = json.loads(content)
                if not isinstance(docs, list):
                    logging.error(
                        f"load_file error content: {content}, docs: {docs}, file: {file_path}, {parser_url=}, token: {parser_token[:20]}...{parser_token[-20:]}"
                    )
                    raise ValueError(f"Parser service error, load_file error: {docs}")
                docs = [Document(**doc) for doc in docs]
                for doc in docs:
                    doc.metadata["parser_file_id"] = result["file_id"]

                # 如果dynamic_section开关打开 且 有advanced_parser 信息，则进行动态章节提取，并将章节信息写入metadata
                if settings.DINAMIC_SECTION_EXTRACT:
                    for doc in docs:
                        if doc.metadata.get("advanced_parser"):
                            advanced_parser_ret = json.loads(
                                doc.metadata["advanced_parser"]
                            )
                            pages = advanced_parser_ret["pages"]
                            sections = await acreate_sections(
                                pages=pages, is_ppt=is_ppt
                            )
                            doc.metadata["sections"] = sections
                return docs
        except (aiohttp.ClientOSError, aiohttp.ServerDisconnectedError):
            if attempt < max_retries:
                attempt += 1
                logging.warning(f"parser_service(document/load_file): retry {attempt}")
            else:
                raise
        except Exception as e:
            raise


@trace()
@async_timing_decorator
async def load_url(
    url: str,
    type: UnstructuredType | None = None,
    format: DocumentFormat = DocumentFormat.HTML,
    plugins: List[ParserPlugin] | None = None,
    spa: bool = False,
    max_images: int = 0,
    min_image_width: int = 200,
    min_image_height: int = 200,
    min_image_size: int = 1024,
    max_image_size: int = 1024 * 1024 * 10,
    client: aiohttp.ClientSession = None,
    parser_url=None,
    parser_token=None,
) -> List[Document]:
    try:
        payload = {
            "plugins": json.dumps(plugins or []),
        }
        params = {
            "url": url,
            "spa": "true" if spa else "false",
            "max_images": max_images,
            "min_image_width": min_image_width,
            "min_image_height": min_image_height,
            "min_image_size": min_image_size,
            "max_image_size": max_image_size,
            "use_file_storage": "true",
        }
        if type:
            params["type"] = type.value
        if format:
            params["format"] = format.value

        result = await parser_service(
            "document/load_url",
            payload=payload,
            params=params,
            client=client,
            parser_url=parser_url,
            parser_token=parser_token,
        )
        if result is None:
            logging.error(
                f"Parser service error, result is None, api document/load_url, {parser_url=}, token: {parser_token[:20]}...{parser_token[-20:]}"
            )
            raise ValueError("Parser service error, result is None")
        if "file_id" not in result:
            logging.error(
                f"Parser service error, file_id not found, load_url error: {result}"
            )
            raise ValueError("Parser service error, file_id not returned")
        file_id = result["file_id"]
        logging.info(f"load_file result: {file_id}, {parser_url}, {parser_token}")
        await RedisClient.get_client().set(
            file_id,
            json.dumps([parser_url, parser_token]),
        )
        content = await download_from_parser_service(
            file_id,
            client=client,
        )
        content = content.decode("utf-8")
        docs = json.loads(content)
        docs = [Document(**doc) for doc in docs]
        for doc in docs:
            doc.metadata["parser_file_id"] = result["file_id"]
        return docs
    except Exception as e:
        raise e


@trace()
@async_timing_decorator
async def split(
    documents: List[Document],
    retain_original: bool = True,
    client: aiohttp.ClientSession = None,
) -> List[Document]:
    try:
        PARSER_URL, PARSER_TOKEN = get_next_parser()

        docs = await parser_service(
            "document/split",
            json=[document.dict() for document in documents],
            params={"retain_original": "true" if retain_original else "false"},
            client=client,
            parser_url=PARSER_URL,
            parser_token=PARSER_TOKEN,
        )
        if docs is None:
            logging.error(
                f"Parser service error, split error: {docs}, {PARSER_URL=}, {PARSER_TOKEN[:20]}...{PARSER_TOKEN[-20:]}"
            )
            raise ValueError("Parser service error, split error")
        return [Document(**doc) for doc in docs]
    except Exception as e:
        raise e


@trace()
@async_timing_decorator
async def split_by_file(
    file_id: str, retain_original: bool = True, client: aiohttp.ClientSession = None
) -> List[Document]:
    try:
        parser_conf = await RedisClient.get_client().get(
            file_id,
        )
        logging.info(f"split_by_file, file_id: {file_id}, parser_conf: {parser_conf}")
        if parser_conf:
            parser_conf = json.loads(parser_conf)
            parser_url, parser_token = parser_conf
        else:
            parser_url, parser_token = get_next_parser()

        result = await parser_service(
            "document/split_by_file",
            params={
                "file_id": file_id,
                "retain_original": "true" if retain_original else "false",
            },
            client=client,
            parser_url=parser_url,
            parser_token=parser_token,
        )
        file_id = result["file_id"]
        logging.info(f"load_file result: {file_id}, {parser_url}, {parser_token}")
        await RedisClient.get_client().set(
            file_id,
            json.dumps([parser_url, parser_token]),
        )
        content = await download_from_parser_service(
            file_id,
            client=client,
        )
        content = content.decode("utf-8")
        docs = json.loads(content)
        if not isinstance(docs, list):
            logging.error(
                f"split_by_file error content: {content}, docs: {docs}, file_id: {file_id}, {parser_url}, {parser_token[:20]}...{parser_token[-20:]}"
            )
            raise ValueError(f"Parser service error, split_by_file error: {docs}")
        return [Document(**doc) for doc in docs]
    except Exception as e:
        raise e


@trace()
async def word_to_pdf(file_path: str, client: aiohttp.ClientSession = None):
    try:
        with open(file_path, "rb") as file:
            payload = {"file": file}
            PARSER_URL, PARSER_TOKEN = get_next_parser()

            return await parser_service(
                "document/word_to_pdf",
                payload=payload,
                type="bytes",
                client=client,
                parser_url=PARSER_URL,
                parser_token=PARSER_TOKEN,
            )
    except Exception as e:
        raise e


@trace()
async def text_to_pdf(file_path: str, client: aiohttp.ClientSession = None):
    try:
        with open(file_path, "rb") as file:
            payload = {"file": file}
            PARSER_URL, PARSER_TOKEN = get_next_parser()

            return await parser_service(
                "document/text_to_pdf",
                payload=payload,
                type="bytes",
                client=client,
                parser_url=PARSER_URL,
                parser_token=PARSER_TOKEN,
            )
    except Exception as e:
        raise e
