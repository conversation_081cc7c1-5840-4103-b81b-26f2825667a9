import re
from typing import List
from bs4 import BeautifulSoup
from langchain.document_loaders.base import BaseLoader
from langchain.docstore.document import Document


class HTMLParserLoader(BaseLoader):
    """html解析器"""

    def __init__(self, content: str, url: str | None = None) -> None:
        self.content = content
        self.url = url
        super().__init__()

    def _get_soup(self):

        # TODO:使用Readability提取主要内容
        # doc = HTMLSummary(self.html)
        # main_content = doc.summary()

        if not hasattr(self, "soup"):
            self.soup = BeautifulSoup(self.content, "html.parser")
        return self.soup

    def load(self):
        text = self._get_soup().get_text()
        # 去除多个\n或者多个\t
        text = re.sub(r"(\n|\r\n){3,}", "\n", text)
        text = text.replace("\u3000", " ")
        return [Document(page_content=text, metadata=self.get_metadata())]

    def get_links(self) -> list:
        return [link for link in self._get_soup().find_all("a")]

    def get_title(self) -> str:
        soup = self._get_soup()
        title = soup.find("title")
        is_get_content = title is None
        if not title:
            title = soup.find("meta", {"name": "title"})
        if not title:
            title = soup.find("meta", {"property": "iframely:title"})
        value = ""
        if title:
            value = title.get("content") if is_get_content else title.get_text()
        # 替换掉所有换行符
        return value.replace("\n", "")

    def get_description(self) -> str:
        soup = self._get_soup()
        description = soup.find("meta", {"name": "description"})
        if not description:
            description = soup.find("meta", {"property": "iframely:description"})
        return description.get("content") if description else None

    def get_metadata(self) -> dict:
        return self._get_metadata()

    def _get_elements(self) -> List:
        return self._get_soup().find_all()

    def _get_metadata(self) -> dict:
        soup = self._get_soup()
        metadata = {}
        if self.url:
            metadata["source"] = self.url
        title = self.get_title()
        if title:
            metadata["title"] = title
        description = self.get_description()
        if description:
            metadata["description"] = description
        html_tag = soup.find("html")
        if html_tag:
            lang = html_tag.get("lang", "")
            if lang:
                metadata["language"] = lang
        return metadata
