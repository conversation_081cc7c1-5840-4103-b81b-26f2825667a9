from abc import ABC, abstractmethod
from typing import Optional, List, Dict, Any


class BaseStorage(ABC):
    """存储适配器基类"""

    @abstractmethod
    async def save_conversation(
        self, conversation_id: str, messages: List[Dict[str, Any]]
    ) -> None:
        """保存对话历史"""
        pass

    @abstractmethod
    async def get_conversation(
        self, conversation_id: str
    ) -> Optional[List[Dict[str, Any]]]:
        """获取对话历史"""
        pass

    @abstractmethod
    async def save_agent_state(self, agent_id: str, state: Dict[str, Any]) -> None:
        """保存Agent状态"""
        pass

    @abstractmethod
    async def save_agent_config(self, user_id: str, config: dict) -> str:
        """保存Agent配置并返回ID"""
        pass

    @abstractmethod
    async def get_agent_config(self, user_id: str, agent_id: str) -> Optional[dict]:
        """获取Agent配置"""
        pass

    @abstractmethod
    async def save_swarm_config(self, user_id: str, config: dict) -> str:
        """保存Swarm配置并返回ID"""
        pass

    @abstractmethod
    async def get_swarm_config(self, user_id: str, swarm_id: str) -> Optional[dict]:
        """获取Swarm配置"""
        pass
