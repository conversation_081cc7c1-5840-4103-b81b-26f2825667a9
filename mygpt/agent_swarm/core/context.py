from typing import Optional
from mygpt.agent_swarm.core.registry import ToolRegistry
from mygpt.agent_swarm.core.events import EventBus
from mygpt.agent_swarm.core.state_manager import StateManager
from mygpt.agent_swarm.llms.base import BaseLLM


class SwarmContext:
    """上下文管理器(单例模式)"""

    _instance = None

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        if SwarmContext._instance is not None:
            raise Exception("SwarmContext is a singleton!")

        SwarmContext._instance = self
        self.default_llm: Optional[BaseLLM] = None
        self.tool_registry = ToolRegistry()
        self.state_manager = StateManager()
        self.event_bus = EventBus()
