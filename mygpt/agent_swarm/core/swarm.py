import uuid
from typing import Any, Callable, Dict, List, Optional
from llama_index.core.agent import ReActAgentWorker
from llama_index.core.tools import QueryEngineTool, ToolMetadata
from mygpt.agent_swarm.core.agent import Agent
from mygpt.agent_swarm.core.context import Swarm<PERSON>ontext
from mygpt.agent_swarm.core.state_manager import AgentState, StateMessage


class Swarm:
    def __init__(
        self,
        name: str,
        description: str,
        instruction: str,
        agents: List[Agent],
        functions: Optional[List[Callable]] = None,
        swarm_id: Optional[str] = None,
        context: Optional[SwarmContext] = None,
    ):
        self.id = swarm_id or str(uuid.uuid4())
        self.name = name
        self.description = description
        self.instruction = instruction
        self.agents = {agent.name: agent for agent in agents}
        self.functions = functions or []
        self.context = context or SwarmContext()

        # 注册Swarm到上下文
        if context:
            context.register_agent(self.id, self)
            if functions:
                [context.register_tool(func.__name__, func) for func in functions]

        # 确保所有Agent共享同一个上下文
        for agent in agents:
            if agent.context != self.context:
                agent.context = self.context

    async def achat(
        self, message: str, agent_name: Optional[str] = None
    ) -> StateMessage:
        """与特定或默认Agent对话"""
        if agent_name and agent_name in self.agents:
            return await self.agents[agent_name].chat(message)

        # 默认使用第一个Agent
        default_agent = next(iter(self.agents.values()))
        return await default_agent.achat(message)

    async def astream_chat(self, message: str, agent_name: Optional[str] = None):
        """流式对话"""
        if agent_name and agent_name in self.agents:
            async for response in self.agents[agent_name].astream_chat(message):
                yield response
        else:
            default_agent = next(iter(self.agents.values()))
            async for response in default_agent.astream_chat(message):
                yield response

    def add_agent(self, agent: Agent):
        """添加新Agent"""
        if agent.name in self.agents:
            raise ValueError(f"Agent {agent.name} already exists")
        agent.context = self.context
        self.agents[agent.name] = agent

    def remove_agent(self, agent_name: str):
        """移除Agent"""
        if agent_name not in self.agents:
            raise ValueError(f"Agent {agent_name} not found")
        del self.agents[agent_name]
