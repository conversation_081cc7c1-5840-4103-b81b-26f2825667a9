from datetime import datetime
from typing import Any, Dict, List, Protocol, Optional
from dataclasses import dataclass, field
from llama_index.core.llms import ChatMessage, MessageRole


@dataclass
class RuntimeContext:
    """运行时上下文"""

    conversation_id: str
    user_id: Optional[str] = None
    agent_id: Optional[str] = None
    messages: List[ChatMessage] = field(default_factory=list)  # 直接使用ChatMessage
    metadata: Dict[str, Any] = field(default_factory=dict)

    def add_message(self, role: MessageRole, content: str):
        """添加新消息到历史记录"""
        self.messages.append(ChatMessage(role=role, content=content))

    def get_chat_history(self, include_last: bool = True) -> List[ChatMessage]:
        """获取对话历史
        Args:
            include_last: 是否包含最后一条消息
        """
        if include_last:
            return self.messages
        return self.messages[:-1]


@dataclass
class SwarmEvent:
    """事件基类"""

    event_type: str
    data: Any
    context: RuntimeContext
    timestamp: datetime = field(default_factory=datetime.now)


class EventHandler(Protocol):
    """事件处理器接口"""

    async def handle_event(self, event: SwarmEvent) -> None: ...


class EventBus:
    """事件总线"""

    def __init__(self):
        self.handlers: Dict[str, List[EventHandler]] = {}

    def subscribe(self, event_type: str, handler: EventHandler):
        if event_type not in self.handlers:
            self.handlers[event_type] = []
        self.handlers[event_type].append(handler)

    async def emit(self, event: SwarmEvent):
        handlers = self.handlers.get(event.event_type, [])
        for handler in handlers:
            await handler.handle_event(event)
