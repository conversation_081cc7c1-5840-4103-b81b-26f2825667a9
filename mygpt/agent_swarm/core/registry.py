from typing import Dict, Optional
from .tools import BaseTool


class ToolRegistry:
    """工具注册表"""

    def __init__(self):
        self.system_tools: Dict[str, BaseTool] = {}
        self.user_tools: Dict[str, Dict[str, BaseTool]] = {}

    def register_system_tool(self, tool: BaseTool) -> None:
        self.system_tools[tool.name] = tool

    def register_user_tool(self, user_id: str, tool: BaseTool) -> None:
        if user_id not in self.user_tools:
            self.user_tools[user_id] = {}
        self.user_tools[user_id][tool.name] = tool

    def get_available_tools(self, user_id: Optional[str] = None) -> Dict[str, BaseTool]:
        tools = self.system_tools.copy()
        if user_id and user_id in self.user_tools:
            tools.update(self.user_tools[user_id])
        return tools
