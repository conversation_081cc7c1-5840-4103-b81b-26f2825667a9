import uuid
from typing import Any, Callable, List, Optional
from enum import Enum

from llama_index.core.llms import Chat<PERSON><PERSON>age, MessageRole
from llama_index.core.tools import QueryEngineTool

from mygpt.agent_swarm.core.context import Swarm<PERSON>ontext
from mygpt.agent_swarm.core.state_manager import Agent<PERSON>tate, StateMessage
from mygpt.agent_swarm.core.tools import BaseTool, convert_to_llama_tool
from mygpt.agent_swarm.core.events import RuntimeContext, SwarmEvent


class AgentLifecycle(Enum):
    CREATED = "created"
    INITIALIZED = "initialized"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"


class Agent:
    def __init__(
        self,
        name: str,
        tool_names: List[str],
        user_id: Optional[str] = None,
        instruction: str = "",
        role: str = "",
        description: str = "",
        agent_id: Optional[str] = None,
        context: Optional[SwarmContext] = None,
    ):
        self.id = agent_id or str(uuid.uuid4())
        self.name = name
        self.instruction = instruction
        self.role = role
        self.description = description
        self.context = context or SwarmContext.get_instance()
        self.user_id = user_id
        self.lifecycle_state = AgentLifecycle.CREATED

        # 获取工具
        available_tools = self.context.tool_registry.get_available_tools(user_id)
        self.tools = []
        for tool_name in tool_names:
            if tool_name in available_tools:
                self.tools.append(available_tools[tool_name])

        self._setup_agent()

    def _setup_agent(self):
        """初始化Agent"""
        if not self.context.default_llm:
            raise ValueError("No default LLM provided in context")

        self._llm = self.context.default_llm
        # 转换工具格式
        llama_tools = [convert_to_llama_tool(tool) for tool in self.tools]
        self._llm.tools = llama_tools
        self._llm.instruction = self.instruction
        self._llm.init_agent(self.context.state_manager.callback_manager)

    async def achat(
        self, message: str, runtime_context: RuntimeContext
    ) -> StateMessage:
        """处理对话请求"""
        await self._emit_event(
            "chat_start", {"message": message, "agent_name": self.name}
        )

        # 添加用户消息到历史记录
        runtime_context.add_message(MessageRole.USER, message)

        await self.context.state_manager.transition(
            AgentState.THINKING, f"Agent {self.name} is thinking..."
        )

        # 获取历史记录(不包含当前消息)
        chat_history = runtime_context.get_chat_history(include_last=False)

        # 调用LLM
        response = await self._llm.agent.achat(message, chat_history=chat_history)

        # 添加助手回复到历史记录
        runtime_context.add_message(MessageRole.ASSISTANT, response.response)

        await self._emit_event("chat_end", {"response": response.response})

        return StateMessage(
            state=AgentState.RESPONDING,
            message=response.response,
            data={"source": self.name},
        )

    async def astream_chat(self, message: str, runtime_context: RuntimeContext):
        """流式处理对话"""
        await self._emit_event(
            "chat_start", {"message": message, "agent_name": self.name}
        )

        # 添加用户消息到历史记录
        runtime_context.add_message(MessageRole.USER, message)

        # 获取历史记录(不包含当前消息)
        chat_history = runtime_context.get_chat_history(include_last=False)

        response_content = []
        async for chunk in self._llm.agent.astream_chat(
            message, chat_history=chat_history
        ):
            response_content.append(chunk.response)
            yield StateMessage(
                state=AgentState.RESPONDING,
                message=chunk.response,
                data={"source": self.name},
            )

        # 添加完整的助手回复到历史记录
        runtime_context.add_message(MessageRole.ASSISTANT, "".join(response_content))

        await self._emit_event("chat_end", {})

    async def _emit_event(self, event_type: str, data: Any):
        runtime_context = RuntimeContext(
            conversation_id=str(uuid.uuid4()),  # 需要实际的conversation_id
            user_id=self.user_id,
            agent_id=self.id,
        )
        event = SwarmEvent(event_type, data, runtime_context)
        await self.context.event_bus.emit(event)
