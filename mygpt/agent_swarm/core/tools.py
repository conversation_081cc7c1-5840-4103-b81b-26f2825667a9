import inspect
from abc import ABC
from typing import Any, Callable, Dict, List, Optional, Union, Type, Coroutine
from dataclasses import dataclass
from enum import Enum
from inspect import signature, Parameter
from llama_index.core.tools import BaseTool, FunctionTool, ToolMetadata
from llama_index.core.base.response.schema import Response
import aiohttp
import json

from pydantic import create_model, BaseModel, Field


class ToolType(Enum):
    """工具类型"""

    SYSTEM = "system"  # 系统内置工具
    RAG = "rag"  # 检索增强工具
    API = "api"  # 外部API工具
    AGENT = "agent"  # Agent转换工具
    CUSTOM = "custom"  # 用户自定义工具


@dataclass
class ToolParameter:
    """工具参数的元数据"""

    name: str
    type: str
    description: str
    required: bool = True
    default: Any = None


@dataclass
class ApiConfig:
    """API工具配置"""

    url: str
    method: str = "POST"
    headers: Dict[str, str] = None
    auth_type: Optional[str] = None  # basic, bearer, custom等
    auth_config: Dict[str, str] = None
    timeout: int = 30
    retry: int = 3
    params: Dict[str, Any] = None
    body: Dict[str, Any] = None
    required_params: List[str] = None
    optional_params: List[str] = None
    description: str = ""


class ToolManager:
    def __init__(self):
        self.tools = {}
        self.metadata = {}

    def _parse_docstring_params(self, docstring: str) -> Dict[str, str]:
        """解析docstring中的参数描述

        Args:
            docstring: 函数的文档字符串

        Returns:
            Dict[str, str]: 参数名到描述的映射
        """
        param_docs = {}
        current_param = None

        for line in docstring.split("\n"):
            line = line.strip()

            # 检查Args部分
            if line.lower().startswith("args:"):
                continue

            # 解析参数
            if ":" in line and not line.startswith(":"):
                parts = line.split(":", 1)
                param_name = parts[0].strip()
                param_desc = parts[1].strip()
                param_docs[param_name] = param_desc
                current_param = param_name
            # 继续上一个参数的描述
            elif current_param and line:
                param_docs[current_param] += " " + line

        return param_docs

    def _create_schema_from_function(self, func: Callable) -> Type[BaseModel]:
        """从函数签名创建Pydantic模型

        主要步骤：
        1. 获取函数签名
        2. 解析参数类型和默认值
        3. 从docstring解析参数描述
        4. 创建Pydantic模型

        Args:
            func: 要创建schema的函数

        Returns:
            Type[BaseModel]: 生成的Pydantic模型类
        """
        # 获取函数签名
        sig = inspect.signature(func)

        # 获取函数文档字符串并解析
        docstring = inspect.getdoc(func) or ""
        param_docs = self._parse_docstring_params(docstring)

        # 准备字段定义
        fields = {}
        for name, param in sig.parameters.items():
            # 获取参数类型，默认为str
            field_type = (
                param.annotation if param.annotation != inspect.Parameter.empty else str
            )

            # 获取默认值
            if param.default != inspect.Parameter.empty:
                fields[name] = (field_type, param.default)
            else:
                fields[name] = (field_type, ...)  # ... 表示必需参数

            # 添加参数描述
            if name in param_docs:
                fields[name] = (field_type, Field(..., description=param_docs[name]))

        # 创建模型类
        schema_name = f"{func.__name__.title()}Schema"
        model = create_model(schema_name, **fields)
        model.__doc__ = docstring

        return model

    def _create_metadata(
        self, name: str, description: str, schema: Type[BaseModel]
    ) -> ToolMetadata:
        """创建工具元数据

        主要步骤：
        1. 处理名称和描述
        2. 从schema获取参数信息
        3. 创建ToolMetadata实例

        Args:
            name: 工具名称
            description: 工具描述
            schema: 参数schema

        Returns:
            ToolMetadata: 工具元数据对象
        """
        # 清理和验证名称
        clean_name = name.strip().lower().replace(" ", "_")

        # 处理描述
        clean_description = description.strip() if description else ""

        # 创建元数据
        metadata = ToolMetadata(
            name=clean_name, description=clean_description, fn_schema=schema
        )

        return metadata

    def register(
        self,
        name=None,
        description=None,
        return_direct=False,
        tool_metadata: Optional[ToolMetadata] = None,
        fn_schema: Optional[Type[BaseModel]] = None,
    ):
        """装饰器方式注册工具"""
        """
                fn: Optional[Callable[..., Any]] = None,
        name: Optional[str] = None,
        description: Optional[str] = None,
        return_direct: bool = False,
        fn_schema: Optional[Type[BaseModel]] = None,
        async_fn: Optional[AsyncCallable] = None,
        tool_metadata: Optional[ToolMetadata] = None,
        """

        def decorator(func: Union[Callable, Coroutine]):
            # 1. 检查函数是否已注册
            func_name = name or func.__name__
            if func_name in self.tools:
                raise ValueError(f"Tool with name '{func_name}' already registered")
            # 2. 检查是否是异步函数
            is_async = inspect.iscoroutinefunction(func)
            # 3. 准备函数schema
            schema = fn_schema
            if not schema:
                schema = self._create_schema_from_function(func)
            # 4. 准备元数据
            metadata = tool_metadata
            if not metadata:
                metadata = self._create_metadata(
                    name=func_name,
                    description=description or func.__doc__,
                    schema=schema,
                )
            # 5. 创建工具
            tool = FunctionTool.from_defaults(
                fn=None if is_async else func,
                async_fn=func if is_async else None,
                name=func_name,
                description=description or func.__doc__,
                return_direct=return_direct,
                fn_schema=schema,
                tool_metadata=metadata,
            )
            # 6. 注册工具
            self.tools[tool.metadata.name] = tool
            return func

        return decorator

    def get_tools(self, tool_type=None):
        """获取所有工具"""
        if tool_type is None:
            return list(self.tools.values())
        # 根据类型过滤
        return [t for t in self.tools.values() if isinstance(t, tool_type)]
