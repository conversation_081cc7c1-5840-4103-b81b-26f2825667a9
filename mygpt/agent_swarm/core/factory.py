from typing import List, Optional
from mygpt.agent_swarm.core.agent import Agent
from mygpt.agent_swarm.core.swarm import Swarm
from mygpt.agent_swarm.core.context import SwarmContext


class AgentFactory:
    @staticmethod
    def create_agent(
        name: str,
        tool_names: List[str],
        user_id: Optional[str] = None,
        instruction: str = "",
        role: str = "",
        description: str = "",
        context: Optional[SwarmContext] = None,
    ) -> Agent:
        return Agent(
            name=name,
            tool_names=tool_names,
            user_id=user_id,
            instruction=instruction,
            role=role,
            description=description,
            context=context,
        )


class SwarmFactory:
    @staticmethod
    def create_swarm(
        name: str,
        agents: List[Agent],
        description: str = "",
        instruction: str = "",
        context: Optional[SwarmContext] = None,
    ) -> Swarm:
        return Swarm(
            name=name,
            agents=agents,
            description=description,
            instruction=instruction,
            context=context or SwarmContext.get_instance(),
        )
