from enum import Enum
from typing import AsyncIterator, Optional, Any, List
from dataclasses import dataclass
from llama_index.core.callbacks import CallbackManager
from llama_index.core.callbacks.base import BaseCallbackHand<PERSON>, CBEventType


class AgentState(Enum):
    IDLE = "idle"
    THINKING = "thinking"
    CALLING_TOOL = "calling_tool"
    RESPONDING = "responding"


@dataclass
class StateMessage:
    state: AgentState
    message: str
    data: Optional[dict] = None


class SimpleCallbackHandler(BaseCallbackHandler):
    """简单的回调处理器，仅打印基本信息"""

    def __init__(self):
        super().__init__(
            event_starts_to_ignore=[], event_ends_to_ignore=[]  # 空列表而不是空集合
        )

    def on_event_start(self, *args: Any, **kwargs: Any):
        pass

    def on_event_end(self, *args: Any, **kwargs: Any):
        pass

    def start_trace(self, *args: Any, **kwargs: Any):
        pass

    def end_trace(self, *args: Any, **kwargs: Any):
        pass


class StateManager:
    def __init__(self):
        self.current_state: AgentState = AgentState.IDLE
        self.callback_handler = SimpleCallbackHandler()
        self.callback_manager = CallbackManager([self.callback_handler])

    async def transition(self, new_state: AgentState, message: str) -> StateMessage:
        self.current_state = new_state
        return StateMessage(state=new_state, message=message)

    async def stream_with_state(
        self, content_stream: AsyncIterator[str]
    ) -> AsyncIterator[StateMessage]:
        """包装内容流,添加状态信息"""
        async for chunk in content_stream:
            yield StateMessage(state=self.current_state, message=chunk)
