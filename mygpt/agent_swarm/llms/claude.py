from llama_index.core.agent import ReActAgentWorker
from .base import BaseLLM


class ClaudeLLM(BaseLLM):
    def init_agent(self, callback_manager=None):
        self.agent = ReActAgentWorker.from_tools(
            tools=self.tools,
            system_prompt=self.system_prompt,
            llm=self.llm,
            allow_parallel_tool_calls=False,
            tool_retriever=self.tool_retriever,
            callback_manager=callback_manager,
        ).as_agent()
