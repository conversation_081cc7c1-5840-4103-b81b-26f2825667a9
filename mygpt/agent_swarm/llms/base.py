from abc import ABC, abstractmethod
from typing import Any, Optional
from llama_index.core.agent import Agent<PERSON>unner
from llama_index.core.llms import LLM as LlamaLLM


class BaseLLM(ABC):
    """基础LLM接口类"""

    agent: AgentRunner
    llm: LlamaLLM

    def __init__(
        self,
        llm: Optional[LlamaLLM] = None,
        tools: Optional[list] = None,
        instruction: str = "",
        tool_retriever: Optional[Any] = None,
    ):
        self.llm = llm
        self.tools = tools or []
        self.tool_retriever = tool_retriever
        self.instruction = instruction
        self.system_prompt = self._get_system_prompt()

    def _get_system_prompt(self) -> str:
        return f"""You are a helpful AI assistant that is honest and direct. 
        Always answer as helpfully as possible while being truthful.
        
        You have access to tools that can help you answer questions.
        Always use the most appropriate tool when needed and explain your reasoning.
        
        Domain-specific instruction:
        {self.instruction}
        """

    @abstractmethod
    def init_agent(self, callback_manager=None) -> None:
        """初始化Agent"""
        pass
