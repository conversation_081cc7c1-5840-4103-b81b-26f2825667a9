from llama_index.core.agent import ReActAgentWorker
from llama_index.llms.openai import OpenAI
from .base import BaseLLM


class OpenAILLM(BaseLLM):
    def __init__(self, model: str = "gpt-3.5-turbo", **kwargs):
        llm = OpenAI(model=model, **kwargs)
        super().__init__(llm=llm)

    def init_agent(self, callback_manager=None):
        self.agent = ReActAgentWorker.from_tools(
            tools=self.tools,
            system_prompt=self.system_prompt,
            llm=self.llm,
            verbose=True,
            tool_retriever=self.tool_retriever,
            callback_manager=callback_manager,
        ).as_agent()


class OpenAIMultiModalLLM(BaseLLM):
    def init_agent(self, callback_manager=None):
        from llama_index.core.agent.react_multimodal.step import (
            MultimodalReActAgentWorker,
        )

        self.agent = MultimodalReActAgentWorker.from_tools(
            tools=self.tools,
            system_prompt=self.system_prompt,
            multi_modal_llm=self.llm,
            tool_retriever=self.tool_retriever,
            callback_manager=callback_manager,
        ).as_agent()
