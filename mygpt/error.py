import sentry_sdk
from fastapi import HTTPException
from skywalking.decorators import trace
from starlette.status import HTTP_401_UNAUTHORIZED

from mygpt.settings import ENABLE_SENTRY


class NotFoundException(HTTPException):
    """
    资源不存在
    """

    def __init__(self, detail: str = None):
        super().__init__(status_code=404, detail=detail)


class UnauthorizedException(HTTPException):
    """
    认证失败
    """

    def __init__(self, detail: str = "Unauthorized"):
        super().__init__(status_code=HTTP_401_UNAUTHORIZED, detail=detail)


class ForbiddenException(HTTPException):
    """
    没有权限
    """

    def __init__(self, detail: str = None):
        super().__init__(status_code=403, detail=detail)


class ServerErrorException(HTTPException):
    """
    服务器错误
    """

    def __init__(self, error: Exception = None):
        super().__init__(status_code=500, detail="Server Error.Please try again later.")
        if not error:
            error = Exception("Server Error.Please try again later.")
        sentry_sdk_capture_exception(error)


class ExceedLimitException(HTTPException):
    """
    额度超出限制
    """

    def __init__(self, detail: str = None, status_code: int = 402):
        if detail is None:
            detail = "Limit Exceeded. Please upgrade your package."
        super().__init__(status_code=status_code, detail=detail)


class InvalidParameterException(HTTPException):
    """
    参数错误
    """

    def __init__(self, detail: str = "Invalid Parameter.", status_code: int = 400):
        super().__init__(status_code=400, detail=detail)


class OperationFailedException(HTTPException):
    """
    操作失败,并附加错误信息
    """

    def __init__(self, detail: str = ""):
        super().__init__(status_code=400, detail=f"Operation Failed.{detail}")


class UnsupportedException(HTTPException):
    """
    不支持的资源/类型错误
    """

    def __init__(self, detail: str = ""):
        super().__init__(status_code=400, detail=f"Unsupported.{detail}")


class DatasetForzenException(HTTPException):
    """
    数据集已经冻结
    """

    def __init__(self):
        super().__init__(status_code=400, detail=f"The dataset is frozen.")


@trace()
def sentry_sdk_capture_exception(e: BaseException):
    if ENABLE_SENTRY:
        sentry_sdk.capture_exception(e)
