import asyncio
import json
import logging
import smtplib
from concurrent.futures import ThreadPoolExecutor
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from pathlib import Path
from typing import Optional

from jinja2 import Template
from fastapi.templating import Jin<PERSON>2Templates

from mygpt import settings
from mygpt.enums import AIConfigType
from mygpt.models import RobotConfig, Robot, SessionMessage

import os


async def send_single_mail_html(subject: str, recipients: list[str], html: str):
    username = settings.MAIL_USERNAME
    password = settings.MAIL_PASSWORD
    server = settings.MAIL_SERVER
    port = settings.MAIL_PORT

    if username is None or password is None or server is None or port is None:
        logging.error("Missing email configuration, skipping email notification.")
        return

    use_tls = True
    message = MIMEMultipart()
    message["From"] = settings.MAIL_FORM
    message["To"] = ", ".join(recipients)
    message["Subject"] = subject
    part = MIMEText(html, _subtype="html")
    message.attach(part)

    def _send_email_sync():
        try:
            with smtplib.SMTP(server, port) as smtp:
                if use_tls:
                    smtp.starttls()
                smtp.login(username, password)
                smtp.sendmail(message["From"], recipients, message.as_string())
        except Exception as e:
            logging.error(f"Failed to send email: {e}")

    with ThreadPoolExecutor() as executor:
        future = executor.submit(_send_email_sync)
        try:
            future.result(timeout=60)
        except TimeoutError:
            logging.error("Sending email timed out after 10 seconds.")


def render_template(name: str, context: Optional[dict] = None) -> str:
    base_path = Path(__file__).resolve().parent / "email_templates"
    template: Template = Jinja2Templates(directory=base_path).get_template(
        f"{name}.html"
    )
    if not context:
        context = {}
    return template.render(**context)


async def send_human_agent_notification_email(ai_id: int, message_id: int):
    try:
        # 获取是否有转人工配置
        robot_config = await RobotConfig.filter(
            robot_id=ai_id, key=AIConfigType.TRANSFER_TO_HUMAN
        ).first()
        robot_obj = json.loads(robot_config.value) if robot_config else None
        if not robot_obj or not robot_obj.get("sta", False):
            logging.error(
                "Transfer-to-human configuration not enabled or missing required data, skipping email notification."
            )
            return

        emails = robot_obj.get("emails", [])
        if not emails:
            logging.error(
                "No email addresses found in transfer-to-human configuration, skipping email notification."
            )
            return

        robot_info = await Robot.get_or_none(id=ai_id)
        if robot_info is None:
            logging.error(
                "No robot found for the given ID, skipping email notification."
            )
            return

        message_info = await SessionMessage.get_or_none(id=message_id)

        question_list = await SessionMessage.filter(
            session_id=message_info.session_id
        ).order_by("created_at")

        # 处理语言,支持中英日
        robot_language: Optional[str] = (
            await RobotConfig.filter(
                robot_id=ai_id, key=AIConfigType.SOURCE_LANG
            ).first()
        ).value
        # 判断如果不等于 en,zh-Hans,ja
        language = (
            robot_language
            if robot_language in ["en", "zh-Hans", "ja", "zh-Hant"]
            else "en"
        )
        if language == "zh-Hant":
            language = "zh-Hans"

        subject = {
            "en": f"「{robot_info.name}」 Chatbot received transfer to human response",
            "zh-Hans": f"「{robot_info.name}」 聊天机器人收到升级人工响应",
            "ja": f"「{robot_info.name}」 チャットボットが人間に転送された応答を受け取りました",
        }.get(
            language,
            f"「{robot_info.name}」 Chatbot received transfer to human response",
        )

        for email in emails:
            if email != "":
                await send_single_mail_html(
                    subject=subject,
                    recipients=[email],
                    html=render_template(
                        f"transfer_to_human_{language}",
                        {
                            "robot_name": robot_info.name,
                            "email": email,
                            "questions": question_list,
                            "session_url": f"{settings.ADMIN_DOMAIN}/bot/{str(ai_id)}/records?sessionId={message_info.session_id}",
                        },
                    ),
                )
                logging.info(f"Sending transfer-to-human notification email to {email}")

    except Exception as e:
        logging.error(f"Error sending notification email: {e}")


def _send_email(
    to_email: str,
    subject: str,
    body: str,
    from_email: Optional[str] = None,
    is_html: bool = False,
    config: dict = {},
) -> None:
    """
    发送邮件

    Args:
        to_email: 收件人邮箱
        subject: 邮件主题
        body: 邮件内容
        from_email: 发件人邮箱（可选，默认使用配置中的邮箱）
        is_html: 是否为HTML内容

    Raises:
        Exception: 发送失败时抛出异常
    """
    if not to_email:
        return

    user = config.get("user", "")
    password = config.get("password", "")
    host = config.get("host", "")
    port = config.get("port", 587)
    ssl = config.get("ssl", False)
    # 创建邮件对象
    msg = MIMEMultipart()
    msg["Subject"] = subject
    msg["From"] = from_email or user
    msg["To"] = to_email

    # 添加邮件内容
    content_type = "html" if is_html else "plain"
    msg.attach(MIMEText(body, content_type, "utf-8"))

    try:
        if ssl:
            server = smtplib.SMTP_SSL(host, port)
        else:
            server = smtplib.SMTP(host, port)
        # 连接SMTP服务器
        with server:
            if not ssl:
                server.starttls()  # 启用TLS加密
            server.login(user, password)
            server.send_message(msg)
    except Exception as e:
        logging.error(f"Failed to send email: {json.dumps(config)}")
        raise Exception(f"Failed to send email: {str(e)}")


def send_admin_alert_email(
    to_email: Optional[str], title: Optional[str], cause: Optional[str]
) -> None:
    """
    发送管理员告警邮件。

    参数:
        to_email (Optional[str]): 收件人邮箱地址。如果未提供，将使用 EMAIL_ADMIN_OUTGOING_NOTICE_LIST 环境变量。
        title (Optional[str]): 告警标题，将添加到邮件主题中。
        cause (Optional[str]): 告警原因，将包含在邮件正文中。

    返回:
        None
    """
    if not title:
        title = "gbase error"

    email_config = {}
    email_config["host"] = os.environ.get("EMAIL_ADMIN_OUTGOING_HOST", "")
    email_config["port"] = os.environ.get("EMAIL_ADMIN_OUTGOING_PORT", 465)
    email_config["user"] = os.environ.get("EMAIL_ADMIN_OUTGOING_USER", "")
    email_config["password"] = os.environ.get("EMAIL_ADMIN_OUTGOING_PASSWORD", "")
    email_config["ssl"] = os.environ.get("EMAIL_ADMIN_OUTGOING_SSL", True)
    if (
        not email_config
        or not email_config.get("host")
        or not email_config.get("user")
        or not email_config.get("password")
    ):
        logging.error("Email config not found or error")
        return

    email_subject = f"ALERT ON {title}"
    email_body = f""" ALERT 
                    {cause}
                """
    if not to_email:
        to_email = os.environ.get("EMAIL_ADMIN_OUTGOING_NOTICE_LIST", "")
    email_list = to_email.split(",")
    for email in email_list:
        _send_email(email, email_subject, email_body, config=email_config)
