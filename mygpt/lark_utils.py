import asyncio
import json
import math
import random
import traceback
from typing import List
from typing import Optional, <PERSON>ple

import lark_oapi as lark
from lark_oapi.api.drive.v1 import *
from loguru import logger as logging, logger
from pydantic import BaseModel

from mygpt.constant import lark_export_file_extension_dict
from mygpt.models import LarkFile
from mygpt.pydantic_rules import LarkShareUrlInput


async def get_tenant_access_token(app_id: str, app_secret: str) -> str:
    """
    获取租户访问凭证(tenant_access_token)

    Args:
        app_id (str): 应用的 App ID
        app_secret (str): 应用的 App Secret

    Returns:
        str: 租户访问凭证(tenant_access_token)，如果获取失败则返回空字符串
    """
    # 创建 client
    client = (
        lark.Client.builder()
        .app_id(app_id)
        .app_secret(app_secret)
        .log_level(lark.LogLevel.DEBUG)
        .build()
    )

    # 构造请求对象
    request = (
        lark.auth.v3.InternalTenantAccessTokenRequest.builder()
        .request_body(
            request_body=lark.auth.v3.InternalTenantAccessTokenRequestBody.builder()
            .app_id(app_id)
            .app_secret(app_secret)
            .build()
        )
        .build()
    )

    # 发起请求
    response = await client.auth.v3.tenant_access_token.ainternal(request)

    # 处理响应
    if response.raw and response.raw.content:
        try:
            decode = response.raw.content.decode("utf-8")
            content = json.loads(decode)
            if content.get("code") == 0:
                return content.get("tenant_access_token", "")
        except json.JSONDecodeError:
            logger.error(
                f"Failed to parse response content, content: {response.raw.content}, {traceback.format_exc()}"
            )

    # 处理失败返回
    lark.logger.error(
        f"获取tenant_access_token失败，错误码: {response.code}, 错误信息: {response.msg}, 日志ID: {response.get_log_id()}"
    )
    return ""


class FileInfo(BaseModel):
    token: str
    name: str
    type: str
    parent_token: str
    created_time: str
    modified_time: str
    owner_id: str
    url: str


async def check_rules_lark_file_exists(
    app_id: str, app_secret: str, share_urls: List[LarkShareUrlInput]
) -> bool:
    client = (
        lark.Client.builder()
        .app_id(app_id)
        .app_secret(app_secret)
        .log_level(lark.LogLevel.DEBUG)
        .build()
    )

    exists = False
    for share_url in share_urls:
        # 调用 Lark API 获取文件信息
        file_info = await check_file_list_exists(client, share_url.token)
        if file_info:
            exists = True
            break
    return exists


async def check_file_list_exists(client: lark.Client, folder_token: str) -> bool:
    req = (
        lark.drive.v1.ListFileRequest.builder().folder_token(folder_token).page_size(10)
    )
    req = req.build()
    resp = await client.drive.v1.file.alist(req)
    if not resp.success():
        logging.info(
            f"failed to get_file_list_exists: [{folder_token=}], {resp.code=}, {resp.msg=}, {resp.error=}"
        )
        return False
    return resp.data.files != []


async def check_any_lark_folder_exists(
    app_id: str, app_secret: str, share_urls: List[LarkShareUrlInput]
) -> [bool, bool]:
    client = (
        lark.Client.builder()
        .app_id(app_id)
        .app_secret(app_secret)
        .log_level(lark.LogLevel.DEBUG)
        .build()
    )
    err = False
    folders_exists = []
    for share_url in share_urls:
        folder_exists, err = await get_folder_exists(client, share_url.token)
        if not err:
            if folder_exists:
                return True, False
        else:
            err = True
        folders_exists.append(folder_exists)
    logger.info(f"folders_exists: {folders_exists}, err: {err}")
    return False, err


async def get_folder_exists(client: lark.Client, folder_token: str) -> [bool, bool]:
    """异步获取指定文件夹中的所有文件

    Args:
        client (lark.Client): 飞书API客户端
        folder_token (str): 文件夹的token
        recursive (bool, optional): 是否递归获取子文件夹内容. Defaults to False.

    Returns:
        List[FileInfo]: 文件信息列表
    """
    next_page_token = None
    max_retries = 10
    retries = 0
    recursive = False

    while True:
        req = (
            lark.drive.v1.ListFileRequest.builder()
            .folder_token(folder_token)
            .page_size(1)
        )
        req = req.build()

        resp = await client.drive.v1.file.alist(req)
        if resp.success():
            return True, False
        else:
            if resp.code == 99991400:
                reset_time = resp.raw.headers.get("x-ogw-ratelimit-reset")
                limit_time = resp.raw.headers.get("x-ogw-ratelimit-limit")
                retries += 1
                if retries >= max_retries:
                    logging.error(
                        f"达到获取lark文件列表最大重试次数 {max_retries=}, {retries=}，停止尝试。"
                    )
                    return True, True
                elif reset_time and int(reset_time) > 0 and limit_time:
                    actual_sleep_time = (
                        int(reset_time)
                        + math.pow(2, retries - 1)
                        + int(random.uniform(0, retries) * 3)
                    )
                    actual_sleep_time = min(actual_sleep_time, int(limit_time))
                    logging.warning(
                        f"请求频率受到限制。等待 {reset_time=} 秒后重试, actually: {actual_sleep_time=}: [{folder_token=}] | [{recursive=}], {resp.code=}, {resp.msg=}, {resp.error=}, {limit_time=}, {reset_time=}, {type(limit_time)=}, {type(reset_time)=}"
                    )
                    logging.info(f"actual sleep time is: {actual_sleep_time=}")
                    await asyncio.sleep(actual_sleep_time)
                    continue
                else:
                    fixed_reset_time = 3
                    logging.error(
                        f"未知原因未成功获取lark文件列表；疑似请求频率受到限制。等待 {fixed_reset_time=} 秒后重试, [{folder_token=}] | [{recursive=}], {resp.code=}, {resp.msg=}, {resp.error=}, {limit_time=}, {reset_time=}, {type(limit_time)=}, {type(reset_time)=}"
                    )
                    await asyncio.sleep(fixed_reset_time)
                    continue
            elif resp.code == 1061007:
                logging.error(
                    f"请求失败，文件夹已被删除，错误码：{resp.code}，错误信息：{resp.msg}, {folder_token=}] | [{recursive=}], {resp.code=}, {resp.msg=}, {resp.error=}"
                )
                return False, False
            else:
                logging.error(
                    f"未知请求失败，错误码：{resp.code}，错误信息：{resp.msg}, {folder_token=}] | [{recursive=}], {resp.code=}, {resp.msg=}, {resp.error=}"
                )
                return True, True
    return True, False


async def fetch_lark_file_list(
    app_id: str, app_secret: str, share_urls: List[LarkShareUrlInput]
) -> List[FileInfo]:
    client = (
        lark.Client.builder()
        .app_id(app_id)
        .app_secret(app_secret)
        .log_level(lark.LogLevel.DEBUG)
        .build()
    )

    lark_files = []
    for share_url in share_urls:
        # 调用 Lark API 获取文件信息
        file_info = await get_file_list(client, share_url.token, share_url.recursive)
        lark_files.extend(file_info)
    return lark_files


async def get_file_list(
    client: lark.Client, folder_token: str, recursive: bool = False
) -> List[FileInfo]:
    """异步获取指定文件夹中的所有文件

    Args:
        client (lark.Client): 飞书API客户端
        folder_token (str): 文件夹的token
        recursive (bool, optional): 是否递归获取子文件夹内容. Defaults to False.

    Returns:
        List[FileInfo]: 文件信息列表
    """
    files = []
    next_page_token = None
    max_retries = 10
    retries = 0

    while True:
        req = (
            lark.drive.v1.ListFileRequest.builder()
            .folder_token(folder_token)
            .page_size(200)  # 最大200
        )
        if next_page_token:
            req.page_token(next_page_token)
        req = req.build()
        max_retries = 10
        resp = None
        while max_retries > 0:
            max_retries -= 1
            try:
                resp = await client.drive.v1.file.alist(req)
                break
            except Exception as e:
                logging.error(f"Failed to get file list: {e}, {traceback.format_exc()}")
                await asyncio.sleep(3)
                continue
        if resp is None:
            raise ValueError("can not get lark file list after retries")
        if not resp.success():
            logging.info(
                f"resp.code: {resp.code}, {type(resp.code)}, {resp.code == 1061007}"
            )
            # rate limited
            if resp.code == 99991400:
                reset_time = resp.raw.headers.get("x-ogw-ratelimit-reset")
                limit_time = resp.raw.headers.get("x-ogw-ratelimit-limit")
                retries += 1
                if retries >= max_retries:
                    logging.error(
                        f"达到获取lark文件列表最大重试次数 {max_retries=}, {retries=}，停止尝试。"
                    )
                    raise ValueError(
                        f"达到获取lark文件列表最大重试次数 {max_retries=}, {retries=}，停止尝试。"
                    )
                    # return files
                elif reset_time and int(reset_time) > 0 and limit_time:
                    actual_sleep_time = (
                        int(reset_time)
                        + math.pow(2, retries - 1)
                        + int(random.uniform(0, retries) * 3)
                    )
                    actual_sleep_time = min(actual_sleep_time, int(limit_time))
                    logging.warning(
                        f"请求频率受到限制。等待 {reset_time=} 秒后重试, actually: {actual_sleep_time=}: [{folder_token=}] | [{recursive=}], {resp.code=}, {resp.msg=}, {resp.error=}, {limit_time=}, {reset_time=}, {type(limit_time)=}, {type(reset_time)=}"
                    )
                    logging.info(f"actual sleep time is: {actual_sleep_time=}")
                    await asyncio.sleep(actual_sleep_time)
                    continue
                else:
                    fixed_reset_time = 3
                    logging.error(
                        f"未知原因未成功获取lark文件列表；疑似请求频率受到限制。等待 {fixed_reset_time=} 秒后重试, [{folder_token=}] | [{recursive=}], {resp.code=}, {resp.msg=}, {resp.error=}, {limit_time=}, {reset_time=}, {type(limit_time)=}, {type(reset_time)=}"
                    )
                    await asyncio.sleep(fixed_reset_time)
                    continue
            elif resp.code == 1061007:
                logging.info(
                    f"文件夹已被删除，错误码：{resp.code}，错误信息：{resp.msg}, {folder_token=}] | [{recursive=}], {resp.code=}, {resp.msg=}, {resp.error=}"
                )
                return []
            else:
                logging.error(
                    f"未知请求失败，错误码：{resp.code}，错误信息：{resp.msg}, {folder_token=}] | [{recursive=}], {resp.code=}, {resp.msg=}, {resp.error=}"
                )
                raise ValueError(
                    f"未知请求失败，错误码：{resp.code}，错误信息：{resp.msg}, {folder_token=}] | [{recursive=}], {resp.code=}, {resp.msg=}, {resp.error=}"
                )
                # return files

        for file in resp.data.files:
            file_info = FileInfo(
                token=file.token,
                name=file.name,
                type=file.type,
                parent_token=folder_token,
                created_time=str(file.created_time),
                modified_time=str(file.modified_time),
                owner_id=file.owner_id,
                url=file.url,
            )
            files.append(file_info)

        if not resp.data.has_more:
            break
        next_page_token = resp.data.next_page_token

    if recursive:
        subfolder_tasks = []
        for file in files:
            if file.type == "folder":
                subfolder_tasks.append(
                    get_file_list(client, file.token, recursive=True)
                )

        if subfolder_tasks:
            subfolder_results = await asyncio.gather(*subfolder_tasks)
            for result in subfolder_results:
                files.extend(result)

    return files


async def export_and_download_single_file(
    app_id: str, app_secret: str, file: LarkFile
) -> Optional[bytes]:
    """
    Returns:
        Optional[bytes]: 导出文件的二进制数据，如果导出失败则返回None
    """
    client = (
        lark.Client.builder()
        .app_id(app_id)
        .app_secret(app_secret)
        .log_level(lark.LogLevel.DEBUG)
        .build()
    )
    target_extension = lark_export_file_extension_dict[file.type]
    if target_extension is None:
        raise ValueError(f"Unsupported file type for extension: {file.type}")

    # 步骤一：创建导出任务
    create_task_req = (
        lark.drive.v1.CreateExportTaskRequest.builder()
        .request_body(
            lark.drive.v1.ExportTaskBuilder()
            .file_extension(target_extension)  # 导出文件扩展名
            .token(file.token)  # 导出文档 token
            .type(file.type)  # 导出文档类型，可选值有：doc, docx, sheet, bitable
            .build()
        )
        .build()
    )

    create_task_resp = await client.drive.v1.export_task.acreate(create_task_req)
    if not create_task_resp.success():
        logging.info(
            f"创建导出任务失败: {create_task_resp.msg}，{create_task_resp.code}，{create_task_resp.error}"
        )
        return None

    ticket = create_task_resp.data.ticket

    # 步骤二：查询导出任务结果，一共等待 max_retries * sleep_interval 秒
    max_retries = 60
    sleep_interval = 2
    for _ in range(max_retries):
        get_task_req = (
            lark.drive.v1.GetExportTaskRequest.builder()
            .ticket(ticket)
            .token(file.token)
            .build()
        )

        get_task_resp = await client.drive.v1.export_task.aget(get_task_req)
        if not get_task_resp.success():
            logging.info(f"查询导出任务结果失败: {get_task_resp.msg}")
        job_status = get_task_resp.data.result.job_status
        if job_status == 0:
            file_token = get_task_resp.data.result.file_token
            break
        elif job_status in [1, 2]:  # 1：初始化，2：处理中
            logging.info(
                f"export task not finished yet, job_status: {get_task_resp.data.result.job_status}, {get_task_resp.data.result.job_error_msg}"
            )
            await asyncio.sleep(sleep_interval)
        else:
            logging.error(
                f"export task failed, job_status: {get_task_resp.data.result.job_status}, {get_task_resp.data.result.job_error_msg}"
            )
            return None
    else:
        logging.error(
            f"导出任务超时, {file.id}, {file.token}, {file.name}, {file.type}"
        )
        return None

    # 步骤三：下载导出文件
    download_req = (
        lark.drive.v1.DownloadExportTaskRequest.builder().file_token(file_token).build()
    )

    download_resp = await client.drive.v1.export_task.adownload(download_req)
    if not download_resp.success():
        logging.error(
            f"下载导出文件失败: {file.id}, {file.name}, {file.token}, {download_resp.msg}, {download_resp.code}, {download_resp.error}"
        )
        return None

    return download_resp.raw.content


async def download_file(client: lark.Client, file_token: str) -> Optional[bytes]:
    """
    下载飞书云空间中的文件

    Args:
        client (lark.Client): 飞书API客户端
        file_token (str): 要下载的文件token

    Returns:
        Optional[bytes]: 下载文件的二进制数据，如果下载失败则返回None
    """
    # 构造请求对象
    request = lark.drive.v1.DownloadFileRequest.builder().file_token(file_token).build()

    # 发起请求
    response = await client.drive.v1.file.adownload(request)

    # 处理响应
    if response.success():
        return response.file.data.content
    else:
        logging.info(f"下载文件失败: {response.msg}")
        return None


async def fetch_and_download_file(
    app_id: str, app_secret: str, file_token: str
) -> Optional[bytes]:
    """
    获取并下载飞书云空间中的文件

    Args:
        app_id (str): 应用的App ID
        app_secret (str): 应用的App Secret
        file_token (str): 要下载的文件token

    Returns:
        Optional[bytes]: 下载文件的二进制数据，如果下载失败则返回None
    """
    client = (
        lark.Client.builder()
        .app_id(app_id)
        .app_secret(app_secret)
        .log_level(lark.LogLevel.DEBUG)
        .build()
    )

    return await download_file(client, file_token)


async def download_file(client: lark.Client, file_token: str) -> Optional[bytes]:
    """
    下载飞书云空间中的文件

    Args:
        client (lark.Client): 飞书API客户端
        file_token (str): 要下载的文件token

    Returns:
        Optional[bytes]: 下载文件的二进制数据，如果下载失败则返回None
    """
    # 构造请求对象
    request = lark.drive.v1.DownloadFileRequest.builder().file_token(file_token).build()

    # 发起请求
    response = await client.drive.v1.file.download(request)

    # 处理响应
    if response.success():
        return response.data.content
    else:
        logging.info(f"下载文件失败: {response.msg}")
        return None


async def fetch_and_download_file(
    app_id: str, app_secret: str, file_token: str
) -> Optional[bytes]:
    """
    获取并下载飞书云空间中的文件

    Args:
        app_id (str): 应用的App ID
        app_secret (str): 应用的App Secret
        file_token (str): 要下载的文件token

    Returns:
        Optional[bytes]: 下载文件的二进制数据，如果下载失败则返回None
    """
    client = (
        lark.Client.builder()
        .app_id(app_id)
        .app_secret(app_secret)
        .log_level(lark.LogLevel.DEBUG)
        .build()
    )

    return await download_file(client, file_token)


async def download_single_file(
    app_id: str, app_secret: str, file_token: str
) -> Optional[bytes]:
    """
    统一下载单个文件，包括普通文件和云文档

    Args:
        app_id (str): 应用的App ID
        app_secret (str): 应用的App Secret
        file_token (str): 要下载的文件token

    Returns:
        Tuple[Optional[bytes], str]: 下载文件的二进制数据和文件类型，如果下载失败则返回(None, '')
    """
    client = (
        lark.Client.builder()
        .app_id(app_id)
        .app_secret(app_secret)
        .log_level(lark.LogLevel.DEBUG)
        .build()
    )

    request = lark.drive.v1.DownloadFileRequest.builder().file_token(file_token).build()
    response = await client.drive.v1.file.adownload(request)
    if not response.success():
        try:
            if json.loads(response.raw.content.decode("utf-8"))["code"] == 99991400:
                logging.warning(
                    f"lark download file rate limited, code: 99991400, retry soon"
                )
                await asyncio.sleep(10)
                return await download_single_file(app_id, app_secret, file_token)
        except Exception as e:
            logging.error(
                f"Failed to parse response content, content: {response.raw.content}, {traceback.format_exc()}"
            )
        logging.error(
            f"client.baike.v1.file.download failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
        )
        raise Exception(f"{response.msg}")
    else:
        # for empty files, i.e. empty.txt uploaded from local
        # response.raw.content == b''
        # response.raw.content.decode() == ''
        return response.raw.content


async def re_export_and_download_single_file(
    app_id: str, app_secret: str, file: LarkFile
) -> Optional[bytes]:
    """
    Returns:
        Optional[bytes]: 导出文件的二进制数据，如果导出失败则返回None
    """
    client = (
        lark.Client.builder()
        .app_id(app_id)
        .app_secret(app_secret)
        .log_level(lark.LogLevel.DEBUG)
        .build()
    )
    target_extension = lark_export_file_extension_dict[file.type]
    if target_extension is None:
        raise ValueError(f"Unsupported file type for extension: {file.type}")

    # 步骤一：创建导出任务
    create_task_req = (
        lark.drive.v1.CreateExportTaskRequest.builder()
        .request_body(
            lark.drive.v1.ExportTaskBuilder()
            .file_extension(target_extension)  # 导出文件扩展名
            .token(file.token)  # 导出文档 token
            .type(file.type)  # 导出文档类型，可选值有：doc, docx, sheet, bitable
            .build()
        )
        .build()
    )

    create_task_resp = await client.drive.v1.export_task.acreate(create_task_req)
    if not create_task_resp.success():
        logging.warning(
            f"创建导出任务失败: {create_task_resp.msg}，{create_task_resp.code}，{create_task_resp.error}"
        )
        raise Exception(f"{create_task_resp.msg}")

    ticket = create_task_resp.data.ticket

    # 步骤二：查询导出任务结果，一共等待 max_retries * sleep_interval 秒
    max_retries = 60
    sleep_interval = 2
    for _ in range(max_retries):
        get_task_req = (
            lark.drive.v1.GetExportTaskRequest.builder()
            .ticket(ticket)
            .token(file.token)
            .build()
        )

        get_task_resp = await client.drive.v1.export_task.aget(get_task_req)
        if not get_task_resp.success():
            logging.info(f"查询导出任务结果失败: {get_task_resp.msg}")
        job_status = get_task_resp.data.result.job_status
        if job_status == 0:
            file_token = get_task_resp.data.result.file_token
            break
        elif job_status in [1, 2]:  # 1：初始化，2：处理中
            logging.info(
                f"export task not finished yet, job_status: {get_task_resp.data.result.job_status}, {get_task_resp.data.result.job_error_msg}"
            )
            await asyncio.sleep(sleep_interval)
        else:
            logging.warning(
                f"export task failed, job_status: {get_task_resp.data.result.job_status}, {get_task_resp.data.result.job_error_msg}"
            )
            raise Exception(
                f"export task failed, job_status: {get_task_resp.data.result.job_status}, {get_task_resp.data.result.job_error_msg}"
            )
    else:
        logging.warning(
            f"导出任务超时, {file.id}, {file.token}, {file.name}, {file.type}"
        )
        raise Exception(f"Export task timed out")

    # 步骤三：下载导出文件
    download_req = (
        lark.drive.v1.DownloadExportTaskRequest.builder().file_token(file_token).build()
    )

    download_resp = await client.drive.v1.export_task.adownload(download_req)
    if not download_resp.success():
        logging.warning(
            f"下载导出文件失败: {file.id}, {file.name}, {file.token}, {download_resp.msg}, {download_resp.code}, {download_resp.error}"
        )
        raise Exception(f"{download_resp.msg}")

    return download_resp.raw.content


# 这一步可以批量操作
async def get_file_metadata(
    app_id: str, app_secret: str, file_token: str, file_type: str
) -> Optional[FileInfo]:
    """
    Retrieve file metadata from Lark using file token

    Args:
        app_id: Lark application ID
        app_secret: Lark application secret
        file_token: The token of the file to retrieve

    Returns:
        dict: File metadata response from Lark API
    """

    client = (
        lark.Client.builder()
        .app_id(app_id)
        .app_secret(app_secret)
        .log_level(lark.LogLevel.DEBUG)
        .build()
    )

    request: BatchQueryMetaRequest = (
        BatchQueryMetaRequest.builder()
        .request_body(
            MetaRequest.builder()
            .request_docs(
                [RequestDoc.builder().doc_token(file_token).doc_type(file_type).build()]
            )
            .with_url(True)
            .build()
        )
        .build()
    )
    # 发起请求
    response = await client.drive.v1.meta.abatch_query(request)

    # 处理失败返回
    if not response.success():
        lark.logger.warn(
            f"client.drive.v1.meta.batch_query failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}"
        )
        raise Exception(f"{response.msg}")

    # lark.logger.info(lark.JSON.marshal(response.data, indent=4))

    if len(response.data.metas) == 0 and len(response.data.failed_list) > 0:
        raise Exception("Empty file metadata")

    file = response.data.metas[0]
    file_info = FileInfo(
        token=file.doc_token,
        name=file.title,
        type=file.doc_type,
        parent_token="",
        created_time=str(file.create_time),
        modified_time=str(file.latest_modify_time),
        owner_id=file.owner_id,
        url=file.url,
    )

    return file_info


async def main():
    file_content = await download_single_file(app_id, app_secret, file_token)
    if file_content:
        with open("downloaded_file.txt", "wb") as f:
            f.write(file_content)
        print("File downloaded successfully.")
    else:
        print("Failed to download the file.")


if __name__ == "__main__":
    asyncio.run(main())
