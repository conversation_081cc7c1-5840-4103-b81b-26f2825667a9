from pydantic import BaseModel, Field
from typing import List, Optional
import re
import json


# Custom parser
def extract_json(text: str) -> List[dict]:
    """Extracts JSON content from a string where JSON is embedded between \`\`\`json and \`\`\` tags.

    Parameters:
        text (str): The text containing the JSON content.

    Returns:
        list: A list of extracted JSON strings.
    """
    # Define the regular expression pattern to match JSON blocks
    pattern = r"\`\`\`json(.*?)\`\`\`"

    # Find all non-overlapping matches of the pattern in the string
    matches = re.findall(pattern, text, re.DOTALL)

    # Return the list of matched JSON strings, stripping any leading or trailing whitespace
    try:
        return [json.loads(match.strip()) for match in matches]
    except Exception:
        raise ValueError(f"Failed to parse: {text}")


class SectionOutput(BaseModel):
    """The metadata for a given section. Includes the section name, title, page that it starts on, and more."""

    section_title: str = Field(
        ...,
        description="The current section title (e.g. section_title='3.2 Experimental Results')",
    )
    start_page_number: int = Field(..., description="The start page number.")
    is_subsection: bool = Field(
        ...,
        description="True if it's a subsection (e.g. Section 3.2). False if it's not a subsection (e.g. Section 3)",
    )
    description: Optional[str] = Field(
        None,
        description="The extracted line from the source text that indicates this is a relevant section.",
    )


class SectionsOutput(BaseModel):
    """A list of all sections."""

    sections: List[SectionOutput]
