import asyncio
import functools
import json
import logging
import time
import traceback
from typing import Any, Dict, List, Union, Optional

import loguru
from loguru import logger as logging
from opensearchpy import (
    NotFoundError,
    OpenSearch,
    AsyncOpenSearch,
    RequestsHttpConnection,
)
from opensearchpy.helpers import bulk
from opensearchpy.helpers.errors import BulkIndexError

from mygpt.loader.transcoder import DocumentTranscoder
from mygpt.schemata import Embeddings
from mygpt.settings import OPENSEARCH_URL


def retry(max_retries=3, delay=1):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    retries += 1
                    logging.warning(f"Exception caught: {e}")
                    logging.warning(f"Retrying... ({retries}/{max_retries})")
                    time.sleep(delay)
            return None

        return wrapper

    return decorator


class OpenSearchClient:
    def __init__(
        self, server_url: str, use_ssl: bool = False, verify_certs: bool = False
    ) -> None:
        use_ssl = server_url.startswith("https://")
        self.os_client = OpenSearch(
            hosts=server_url,
            use_ssl=use_ssl,
            verify_certs=verify_certs,
            ssl_show_warn=False,
            timeout=60,
            max_retries=0,
            trust_env=True,
            connection_class=RequestsHttpConnection,
        )

    def get_index(self, index_name: str):
        try:
            return self.os_client.indices.get(index=index_name)
        except NotFoundError as e:
            return None

    @retry()
    def create_index(self, index_name: str, analyzer: str = "standard"):
        if self.get_index(index_name):
            return
        mapping = {
            "mappings": {
                "properties": {
                    "content": {
                        "type": "text",
                        "analyzer": analyzer,
                        "fields": {"keyword_field": {"type": "keyword"}},
                    },
                    "metadata": {
                        "properties": {
                            "doc_id": {"type": "keyword"},
                            "file_id": {"type": "keyword"},
                            "chunk_index": {"type": "integer"},
                            "outlines": {
                                "type": "nested",
                                "properties": {
                                    "level": {"type": "integer"},
                                    "text": {"type": "text"},
                                },
                            },
                            "parent_index": {"type": "keyword"},
                            "short_chunk": {"type": "boolean"},
                            "title": {"type": "keyword"},
                            "source": {"type": "keyword"},
                            "page_numbers": {
                                "type": "integer",
                            },
                            "breadcrumb": {"type": "text"},
                        }
                    },
                }
            },
            "settings": {"index": {"number_of_shards": "1", "number_of_replicas": "0"}},
        }
        # Create the index with the specified settings and mappings
        self.os_client.indices.create(index=index_name, body=mapping)

    def get_document(self, index_name: str, id: str):
        try:
            return self.os_client.get(index=index_name, id=id)
        except NotFoundError as e:
            logging.info(f"index:{index_name} not found")
            return None

    def add_documents(
        self, index_name: str, ids: list, documents: list, analyzer: str = "standard"
    ):
        if not self.get_index(index_name):
            # 索引不存在时，创建索引
            self.create_index(index_name, analyzer)
        if len(ids) != len(documents):
            raise Exception("ids and documents must be the same length")
        body = []
        for idx, document in enumerate(documents):
            insert_body: dict[str, Any] = {
                "_index": index_name,
                "_id": ids[idx],
                "content": document.page_content,
            }
            # index both short chunk & large chunk
            # a doc is either a short chunk or large chunk
            short_chunk = document.metadata.get("short_chunk")
            if short_chunk is True:
                insert_body["metadata"] = {
                    "short_chunk": True,
                    "parent_index": document.metadata["parent_index"],
                    "source": document.metadata.get("source"),
                    "outlines": document.metadata.get("outlines"),
                    "file_id": document.metadata.get("file_id"),
                    "title": document.metadata.get("title"),
                    "page_numbers": document.metadata.get("page_numbers"),
                }
                if document.metadata.get("faq_id") is not None:
                    insert_body["metadata"]["faq_id"] = document.metadata.get("faq_id")
            else:
                # 'doc_id' and 'chunk_index' should be in document.metadata for large chunks
                insert_body["metadata"] = {
                    "short_chunk": False,
                    "doc_id": document.metadata["doc_id"],
                    "chunk_index": document.metadata["chunk_index"],
                    "source": document.metadata.get("source"),
                    "outlines": document.metadata.get("outlines"),
                    "file_id": document.metadata.get("file_id"),
                    "title": document.metadata.get("title"),
                    "page_numbers": document.metadata.get("page_numbers"),
                }
                if document.metadata.get("faq_id") is not None:
                    insert_body["metadata"]["faq_id"] = document.metadata.get("faq_id")

            body.append(insert_body)
        return bulk(self.os_client, body, request_timeout=1800)

    # def delete_documents(self, index_name: str, ids: list):
    #     body = []
    #     for id in ids:
    #         delete_body = {
    #             "_op_type": "delete",
    #             "_index": index_name,
    #             "_id": id,
    #         }
    #         body.append(delete_body)
    #     return bulk(self.os_client, body, request_timeout=1800)

    def delete_documents_safe(
        self, index_name: str, ids: list, file_ids: Optional[list] = None
    ):
        if file_ids is None:
            file_ids = []

        ret_ids = None
        try:
            if ids:
                actions = [
                    {
                        "_op_type": "delete",
                        "_index": index_name,
                        "_id": id_,
                    }
                    for id_ in ids
                ]
                ret_ids = bulk(self.os_client, actions, request_timeout=1800)

            for file_id in file_ids:
                # convert uuid to str
                file_id = str(file_id)
                # 每个 file_id 独立调用 delete_by_query
                query = {"term": {"metadata.file_id": file_id}}
                self.os_client.delete_by_query(
                    index=index_name, body={"query": query}, timeout=1800
                )

            return ret_ids
        except BulkIndexError as e:
            loguru.logger.error(traceback.format_exc())
            return None
        except Exception as e:
            loguru.logger.error(traceback.format_exc())
            return None

    def update_document_metadata(self, index_name: str, ids: list, metadatas: list):
        body = []
        for idx, metadata in enumerate(metadatas):
            short_chunk = metadata.get("short_chunk")
            if short_chunk is True:
                update_metadata = {
                    "short_chunk": True,
                    "parent_index": metadata["parent_index"],
                    "source": metadata.get("source"),
                    "outlines": metadata.get("outlines"),
                    "file_id": metadata.get("file_id"),
                    "title": metadata.get("title"),
                }
            else:
                # 'doc_id' and 'chunk_index' should be in document.metadata for large chunks
                update_metadata = {
                    "short_chunk": False,
                    "doc_id": metadata["doc_id"],
                    "chunk_index": metadata["chunk_index"],
                    "source": metadata.get("source"),
                    "outlines": metadata.get("outlines"),
                    "file_id": metadata.get("file_id"),
                    "title": metadata.get("title"),
                }
            update_body: dict[str, Any] = {
                "_op_type": "update",
                "_index": index_name,
                "_id": ids[idx],
                "doc": {"metadata": update_metadata},
            }
            body.append(update_body)
        return bulk(self.os_client, body, request_timeout=1800)

    def _build_match_clauses_query(
        self,
        analyzer: str,
        query_list: List[str],
        minimum_should_match: str = "1<40%",
        is_faq: bool = False,
    ) -> Dict:
        match_clauses = [
            {"match_phrase": {"content": {"query": query}}} for query in query_list
        ]

        if is_faq:
            simple_query = {
                "query": {
                    "bool": {
                        "should": match_clauses,
                        "filter": [
                            {"term": {"metadata.short_chunk": False}},
                        ],
                        "must": [
                            {"exists": {"field": "metadata.faq_id"}},
                        ],
                        "minimum_should_match": minimum_should_match,
                    }
                }
            }
        else:
            simple_query = {
                "query": {
                    "bool": {
                        "should": match_clauses,
                        "filter": [
                            {"term": {"metadata.short_chunk": False}},
                        ],
                        "must_not": [
                            {"exists": {"field": "metadata.faq_id"}},
                        ],
                        "minimum_should_match": minimum_should_match,
                    }
                }
            }
        return simple_query

    def _analyze_query(
        self, index_name: str, analyzer: str, query: str
    ) -> Union[List[str], None]:
        try:
            analyzed_query = self.os_client.indices.analyze(
                index=index_name, body={"analyzer": analyzer, "text": query}
            )
            return [token["token"] for token in analyzed_query["tokens"]]
        except NotFoundError as e:
            logging.info(f"index:{index_name} not found")
            return None

    @retry()
    def custom_search(
        self,
        index_name: str,
        analyzer: str,
        query: str,
        size: int = 10,
        minimum_should_match: str = "1",
        is_faq: bool = False,
    ):
        logging.info(
            f"index_name: {index_name}, analyzer: {analyzer}, query:{query}, size: {size}"
        )
        # 結心会 会員 数量 TO [結, 心, 会, 会員, 数量], bad case
        # analyzed_query_list = self._analyze_query(index_name, analyzer, query)
        analyzed_query_list = query.split(" ")
        if analyzed_query_list:
            body = self._build_match_clauses_query(
                analyzer, analyzed_query_list, minimum_should_match, is_faq
            )
            body["size"] = size
            logging.info(
                f"index_name: {index_name}, analyzer: {analyzer}, query:{query}, size: {size}, body: {body}"
            )
            try:
                res = self.os_client.search(index=index_name, body=body)
                return res
            except NotFoundError as e:
                logging.info(f"index:{index_name} not found")
                return None
        else:
            return None

    async def async_query_search(
        self, index_name, analyzer, query, size=10, get_short_chunk=False
    ):
        start = time.time()
        max_retry = 3
        result = None

        while max_retry > 0:
            max_retry -= 1
            try:
                result = await asyncio.get_event_loop().run_in_executor(
                    None,
                    self.search,
                    index_name,
                    analyzer,
                    query,
                    size,
                    get_short_chunk,
                )
            except Exception as e:
                logging.warning(
                    f"index:{index_name},query:{query} excutor opensearch query error:{e}"
                )
                result = None
        end = time.time()
        elapsed_time = end - start
        if result is None:
            logging.info(
                f"The async_search function took {elapsed_time} seconds to complete."
            )
        else:
            logging.info(
                f"The async_search function took {elapsed_time} seconds to complete. es internal took: {result['took']}ms"
            )
        return result

    def search(self, index_name, analyzer, query, size=10, get_short_chunk=True):
        body = {
            "query": {
                "bool": {
                    "must": {
                        "match": {
                            "content": {
                                "query": query,
                                # "analyzer": analyzer,
                            }
                        }
                    }
                },
            },
            "_source": ["content", "metadata"],
            "size": size,
        }

        if not get_short_chunk:
            body["query"]["bool"]["must_not"] = [
                {"term": {"metadata.short_chunk": True}},
            ]
        try:
            res = self.os_client.search(index=index_name, body=body)
            return res
        except NotFoundError as e:
            logging.info(f"index:{index_name} not found")
            return None

    async def check_index_metadata_exist(self, index_name: str):
        try:
            result = await asyncio.get_event_loop().run_in_executor(
                None, self.get_index, index_name
            )
            if result is None:
                return False

            metadata = result[index_name]["mappings"]["properties"].get("metadata")
            return metadata is not None
        except Exception as e:
            logging.warning(f"read metadata error:{e}")
            return False

    async def async_clauses_query_search(
        self, index_name, analyzer, query, size=10, is_faq=False
    ):
        start = time.time()
        try:
            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.custom_search(
                    index_name=index_name,
                    analyzer=analyzer,
                    query=query,
                    size=size,
                    is_faq=is_faq,
                ),
            )
        except Exception as e:
            logging.warning(
                f"index:{index_name},query:{query} executor opensearch query error:{e}"
            )
            result = None
        end = time.time()
        elapsed_time = end - start
        if result is None:
            logging.info(
                f"The async_clauses_query_search function took {elapsed_time} seconds to complete. res: None. No data found."
            )
        else:
            logging.info(
                f"The async_clauses_query_search function took {elapsed_time} seconds to complete. es internal took: {result['took']}ms"
            )
        return result

    # async def async_delete_documents(self, index_name: str, ids: list):
    #     return await asyncio.get_event_loop().run_in_executor(
    #         None, self.delete_documents, index_name, ids
    #     )

    async def async_delete_documents_safe(
        self, index_name: str, ids: list, file_ids: list = None
    ):
        return await asyncio.get_event_loop().run_in_executor(
            None, self.delete_documents_safe, index_name, ids, file_ids
        )

    async def async_update_document_metadata(
        self, index_name: str, ids: list, metadatas: list
    ):
        return await asyncio.get_event_loop().run_in_executor(
            None, self.update_document_metadata, index_name, ids, metadatas
        )

    async def async_add_documents(self, index_name: str, ids: list, documents: list):
        return await asyncio.get_event_loop().run_in_executor(
            None, self.add_documents, index_name, ids, documents
        )


open_search_client = None


def get_open_search_client() -> OpenSearchClient:
    global open_search_client
    if open_search_client is None and OPENSEARCH_URL:
        logging.info(f"opensearch init in url:{OPENSEARCH_URL}")
        # 根据URL是否以https开头来决定use_ssl的值
        use_ssl = OPENSEARCH_URL.startswith("https://")
        open_search_client = OpenSearchClient(
            OPENSEARCH_URL, use_ssl=use_ssl, verify_certs=False
        )

    return open_search_client


async_open_search = None


def get_async_open_search() -> AsyncOpenSearch:
    global async_open_search
    if async_open_search is None and OPENSEARCH_URL:
        logging.info(f"opensearch init in url:{OPENSEARCH_URL}")
        use_ssl = OPENSEARCH_URL.startswith("https://")
        async_open_search = AsyncOpenSearch(
            OPENSEARCH_URL,
            use_ssl=use_ssl,
            verify_certs=False,
            trust_env=True,
        )
    return async_open_search


async def fetch_from_opensearch(collection_name, to_fetch):
    if len(to_fetch) == 0:
        return []

    os_client = get_open_search_client()
    if not os_client:
        return []

    search_query = {
        "query": {
            "bool": {
                "should": [
                    {
                        "bool": {
                            "filter": [
                                {"term": {"metadata.doc_id": item[0]}},
                                {"term": {"metadata.chunk_index": item[1]}},
                            ]
                        }
                    }
                    for item in to_fetch
                ]
            }
        }
    }
    logging.info(
        f"Fetching chunks in es, query is:\n{json.dumps(search_query, ensure_ascii=False)}"
    )
    try:
        result = await asyncio.to_thread(
            os_client.os_client.search,
            index=collection_name,
            body=search_query,
        )
    except Exception as e:
        logging.warning(f"opensearch query error:{e}")
        return []
    hits = result["hits"]["hits"]
    matches = []
    for hit in hits:
        content = hit["_source"]["content"]
        document_transcoder = DocumentTranscoder(content)
        content = document_transcoder.decode()

        match = Embeddings(
            id=hit["_id"],
            text=content,
            score=hit["_score"],
            metadata=hit["_source"]["metadata"],
            dataset_id=collection_name,
        )
        matches.append(match)
    return matches
