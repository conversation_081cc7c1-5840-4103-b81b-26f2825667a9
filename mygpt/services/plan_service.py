import logging
from datetime import datetime, timezone, timedelta
from typing import Optional, List
from uuid import UUID

from dateutil.relativedelta import relativedelta
from fastapi import HTTPException

from mygpt.models import (
    Plan,
    PlanSubscription,
    User,
    PlanType,
    DurationUnit,
)


async def get_plan(
    plan_id: Optional[UUID] = None, plan_type: Optional[PlanType] = None
) -> Plan:
    """
    Get a plan by ID or type.

    Args:
        plan_id: Optional ID of the plan to retrieve
        plan_type: Optional type of the plan to retrieve

    Returns:
        The plan object

    Raises:
        HTTPException: If the plan is not found
    """
    if plan_id:
        plan = await Plan.get_or_none(id=plan_id, deleted_at__isnull=True)
        if not plan:
            logging.error(f"Plan with ID {plan_id} not found")
            raise HTTPException(
                status_code=404,
                detail=f"Plan with ID {plan_id} not found.",
            )
        return plan

    if plan_type:
        plan = await Plan.get_or_none(plan_type=plan_type, deleted_at__isnull=True)
        if not plan:
            logging.error(f"Plan with type {plan_type} not found")
            raise HTTPException(
                status_code=404,
                detail=f"Plan with type {plan_type} not found.",
            )
        return plan

    raise HTTPException(
        status_code=400,
        detail="Either plan_id or plan_type must be provided.",
    )


async def get_plans(limit: int = 100, offset: int = 0) -> List[Plan]:
    """
    Get a list of plans.

    Args:
        limit: Maximum number of plans to return
        offset: Number of plans to skip

    Returns:
        List of plan objects
    """
    return await Plan.filter(deleted_at__isnull=True).limit(limit).offset(offset).all()


async def create_plan(plan_data: dict) -> Plan:
    """
    Create a new plan.

    Args:
        plan_data: Dictionary containing plan data

    Returns:
        The created plan object

    Raises:
        HTTPException: If the plan creation fails
    """
    try:
        plan = await Plan.create(**plan_data)
        return plan
    except Exception as e:
        logging.error(f"Failed to create plan: {e}")
        raise HTTPException(status_code=400, detail=f"Failed to create plan: {e}")


async def auto_subscribe_trial_plan(user: User) -> Optional[PlanSubscription]:
    """
    Automatically subscribe a user to the trial plan if they are eligible.

    Args:
        user: The user to subscribe

    Returns:
        The created plan subscription object, or None if the user is not eligible

    Raises:
        HTTPException: If the subscription creation fails
    """
    # Corporate users don't get trial plans
    if user.corporate:
        return None

    # Check if the user already has an active subscription
    has_subscription = await PlanSubscription.filter(
        user_id=user.id,
        cancelled_at__isnull=True,
        expires_at__gt=datetime.now(timezone.utc),
        deleted_at__isnull=True,
    ).exists()

    if has_subscription:
        return None

    # Get the trial plan
    trial_plan = await get_plan(plan_type=PlanType.TRIAL)

    # Create the subscription
    subscription_data = {
        "user_id": user.id,
        "plan_id": trial_plan.id,
        "start_at": datetime.now(timezone.utc),
    }

    # Calculate expiration date based on plan duration
    if trial_plan.duration_unit == DurationUnit.DAY:
        subscription_data["expires_at"] = subscription_data["start_at"] + timedelta(
            days=trial_plan.duration_length
        )
    elif trial_plan.duration_unit == DurationUnit.MONTH:
        subscription_data["expires_at"] = subscription_data["start_at"] + relativedelta(
            months=trial_plan.duration_length
        )
    else:
        logging.error(f"Unsupported duration unit: {trial_plan.duration_unit}")
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported duration unit: {trial_plan.duration_unit}",
        )

    try:
        subscription = await PlanSubscription.create(**subscription_data)
        return subscription
    except Exception as e:
        logging.error(f"Failed to create trial subscription for user {user.id}: {e}")
        raise HTTPException(
            status_code=400, detail=f"Failed to create trial subscription: {e}"
        )


async def subscribe_plan(user_id: UUID, plan_id: UUID) -> PlanSubscription:
    """
    Subscribe a user to a plan.

    Args:
        user_id: The ID of the user to subscribe
        plan_id: The ID of the plan to subscribe to

    Returns:
        The created plan subscription object

    Raises:
        HTTPException: If the subscription creation fails
    """
    # Validate User exists
    user = await User.get_or_none(id=user_id, deleted_at__isnull=True)
    if not user:
        logging.error(f"User with ID {user_id} not found")
        raise HTTPException(
            status_code=404,
            detail=f"User with ID {user_id} not found.",
        )

    # Validate Plan exists
    plan = await get_plan(plan_id=plan_id)

    # Check if the user has an active subscription
    active_subscription = await PlanSubscription.filter(
        user_id=user_id,
        cancelled_at__isnull=True,
        expires_at__gt=datetime.now(timezone.utc),
        deleted_at__isnull=True,
    ).first()

    # If there's an active subscription, cancel it
    if active_subscription:
        await active_subscription.cancel()

    # Create the new subscription
    subscription_data = {
        "user_id": user_id,
        "plan_id": plan_id,
        "start_at": datetime.now(timezone.utc),
    }

    # Calculate expiration date based on plan duration
    if plan.duration_unit == DurationUnit.DAY:
        subscription_data["expires_at"] = subscription_data["start_at"] + timedelta(
            days=plan.duration_length
        )
    elif plan.duration_unit == DurationUnit.MONTH:
        subscription_data["expires_at"] = subscription_data["start_at"] + relativedelta(
            months=plan.duration_length
        )
    else:
        logging.error(f"Unsupported duration unit: {plan.duration_unit}")
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported duration unit: {plan.duration_unit}",
        )

    try:
        subscription = await PlanSubscription.create(**subscription_data)
        return subscription
    except Exception as e:
        logging.error(f"Failed to create subscription for user {user_id}: {e}")
        raise HTTPException(
            status_code=400, detail=f"Failed to create subscription: {e}"
        )


async def cancel_plan_subscription(user_id: UUID, subscription_id: UUID) -> bool:
    """
    Cancel a plan subscription.

    Args:
        user_id: The ID of the user who owns the subscription
        subscription_id: The ID of the subscription to cancel

    Returns:
        True if the subscription was successfully cancelled

    Raises:
        HTTPException: If the subscription is not found
    """
    subscription = await PlanSubscription.get_or_none(
        id=subscription_id, user_id=user_id, deleted_at__isnull=True
    )
    if not subscription:
        raise HTTPException(
            status_code=404,
            detail=f"Plan subscription with ID {subscription_id} not found for user {user_id}.",
        )

    # Use the model's cancel method
    await subscription.cancel()
    return True


async def get_plan_subscriptions(
    user_id: Optional[UUID] = None, limit: int = 100, offset: int = 0
) -> List[PlanSubscription]:
    """
    Get a list of plan subscriptions.

    Args:
        user_id: Optional user ID to filter subscriptions by
        limit: Maximum number of subscriptions to return
        offset: Number of subscriptions to skip

    Returns:
        List of plan subscription objects
    """
    query = PlanSubscription.filter(deleted_at__isnull=True).prefetch_related("plan")

    if user_id:
        query = query.filter(user_id=user_id)

    return await query.limit(limit).offset(offset).all()
