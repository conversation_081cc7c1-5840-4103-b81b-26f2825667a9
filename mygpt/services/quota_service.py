import asyncio
import logging
import traceback
from datetime import datetime, timezone
from typing import Op<PERSON>, <PERSON><PERSON>, List, NamedTuple
from uuid import UUID

from dateutil.relativedelta import relativedelta
from tortoise.functions import Sum
from tortoise.transactions import in_transaction

from mygpt.enums import VectorFileStatus, VectorFileType
from mygpt.models import (
    User,
    UsageLog,
    UsageType,
    QuotaGrantType,
    PlanSubscription,
    AddOnSubscription,
    Plan,
    Robot,
    PlanField,
    VectorFile,
)
from mygpt.settings import IS_USE_LOCAL_VLLM, QUOTA_CHECK

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


from mygpt.exceptions import QuotaException


class QuotaSource(NamedTuple):
    """Represents a single source of consumable quota (Plan or AddOn)."""

    subscription_id: UUID
    source_type: str  # 'plan' or 'addon'
    granted_amount: Optional[
        int
    ]  # Amount provided by *this* source (None for unlimited)
    grant_type: Optional[QuotaGrantType]  # TOTAL or MONTHLY (relevant for Plan)
    start_time: datetime  # Subscription start time (relevant for MONTHLY)
    expiry_time: datetime  # Subscription expiry time


class QuotaService:
    """
    Handles calculation, checking, and consumption of user *consumable* quotas
    (e.g., messages, web page learns, multimodal parses) based on their active
    plan and add-on subscriptions.
    Implements prioritization based on earliest expiry time.
    Handles monthly resets for relevant plan quotas.

    Note: Concurrent limits (storage, bot count) are typically checked directly
    via methods on the User model or specific resource creation logic, not this service.
    """

    @staticmethod
    async def _get_consumable_quota_sources(
        user_id: str | UUID, resource_type: UsageType
    ) -> List[QuotaSource]:
        """Gets all relevant *consumable* quota sources (plan + addons) sorted by expiry time."""
        sources = []
        is_plan_unlimited = False
        plan: Optional[Plan] = None

        # Get the user to check subscription history and corporate status
        user = await User.get_or_none(id=user_id)
        if not user:
            logger.error(f"User {user_id} not found for quota source check.")
            return sources

        # Check if user has any subscription history to the new plan system
        has_plan_history = await user.has_any_plan_subscription_history()

        # If user has subscription history, use new plan system and ignore corporate attribute
        if has_plan_history:
            logger.info(
                f"User {user_id}: Has subscription history, using new plan system."
            )
            # 1. Get Active Plan Subscription
            plan_sub = await QuotaService._get_active_plan_subscription(user_id)
            if not plan_sub:
                logger.warning(
                    f"User {user_id}: No active plan subscription. Cannot provide quota for {resource_type}."
                )
            else:
                plan = await plan_sub.plan
        else:
            # No subscription history, check corporate attribute
            if user.corporate:
                logger.info(
                    f"User {user_id}: Corporate user with no subscription history, providing unlimited quota."
                )
                # Create a virtual unlimited quota source
                now = datetime.now(timezone.utc)
                future = now + relativedelta(years=100)  # Far future expiry
                sources.append(
                    QuotaSource(
                        subscription_id=UUID(
                            "00000000-0000-0000-0000-000000000000"
                        ),  # Dummy UUID
                        source_type="corporate",
                        granted_amount=None,  # None means unlimited
                        grant_type=None,
                        start_time=now,
                        expiry_time=future,
                    )
                )
                return sources
            else:
                # Not corporate, use regular plan system
                logger.info(
                    f"User {user_id}: Non-corporate user with no subscription history, using active plan."
                )
                plan_sub = await QuotaService._get_active_plan_subscription(user_id)
                if not plan_sub:
                    logger.warning(
                        f"User {user_id}: No active plan subscription. Cannot provide quota for {resource_type}."
                    )
                else:
                    plan = await plan_sub.plan

        if plan:
            quota_attr = None
            grant_type_attr = None

            # Map UsageType to Plan model attributes
            if resource_type == UsageType.MESSAGE_SENT:
                quota_attr = "message_quota"
                grant_type_attr = "message_quota_grant_type"
            elif resource_type == UsageType.WEB_PAGE_LEARNED:
                quota_attr = "web_page_quota"
                grant_type_attr = "web_page_quota_grant_type"  # Web page quota uses the same grant type as message quota
            elif resource_type == UsageType.MULTI_MODAL_PARSING:
                quota_attr = "multi_modal_parsing_quota"
                grant_type_attr = "multi_modal_parsing_quota_grant_type"  # Multi-modal parsing quota uses the same grant type as message quota
            # Add other consumable types here

            if quota_attr and grant_type_attr:
                base_quota = getattr(plan, quota_attr, None)
                grant_type = getattr(plan, grant_type_attr, None)

                if base_quota is None:  # None means unlimited
                    is_plan_unlimited = True
                    granted = None
                    logger.debug(
                        f"User {user_id}: Plan {plan.name} grants UNLIMITED quota for {resource_type}."
                    )
                elif base_quota is not None and grant_type is not None:
                    granted = int(base_quota)
                    if granted == 0:
                        logger.debug(
                            f"User {user_id}: Plan {plan.name} grants 0 quota for {resource_type}. Ignoring source."
                        )
                        granted = None  # Treat 0 grant as no source for this step
                    else:
                        logger.debug(
                            f"User {user_id}: Plan {plan.name} grants {granted} ({grant_type}) for {resource_type}."
                        )
                else:
                    # If quota attribute exists but grant type doesn't, it's likely a configuration error or not a consumable quota
                    logger.debug(
                        f"User {user_id}: Plan {plan.name} attribute exists but grant type missing or quota is None for {resource_type}. Quota: {base_quota}, GrantType: {grant_type}"
                    )
                    granted = None

                if granted is not None or is_plan_unlimited:
                    sources.append(
                        QuotaSource(
                            subscription_id=plan_sub.id,
                            source_type="plan",
                            granted_amount=granted,
                            grant_type=grant_type,  # Include grant type
                            start_time=plan_sub.start_at,  # Include start time
                            expiry_time=plan_sub.expires_at,
                        )
                    )
            else:
                # This usage type is not defined as a consumable quota in the Plan model
                logger.debug(
                    f"User {user_id}: Plan {plan.name} does not define consumable quota attributes for {resource_type}."
                )
        else:
            logger.debug(
                f"User {user_id}: No active plan found, skipping plan quota source check for {resource_type}."
            )

        # If plan is unlimited for this resource, addons are irrelevant for *this* resource type
        if is_plan_unlimited:
            logger.debug(
                f"User {user_id}: Plan provides unlimited {resource_type}, skipping addons."
            )
            unlimited_plan_source = next(
                (s for s in sources if s.source_type == "plan"), None
            )
            return [unlimited_plan_source] if unlimited_plan_source else []

        # 2. Get Active Add-On Subscriptions providing this resource type
        add_on_subs = await QuotaService._get_active_add_on_subscriptions(
            user_id, resource_type
        )
        for sub in add_on_subs:
            add_on = (
                await sub.add_on
                if hasattr(sub, "add_on") and callable(getattr(sub, "add_on"))
                else getattr(sub, "add_on", None)
            )
            if not add_on:
                logger.warning(
                    f"User {user_id}: AddOn relation not loaded for subscription {sub.id}. Skipping."
                )
                continue

            # Addons grant a specific amount, typically treated as TOTAL for their duration
            # 从 value 字段中获取数值
            try:
                addon_value = int(add_on.value) if add_on.value is not None else None
            except (ValueError, TypeError):
                logger.error(
                    f"User {user_id}: AddOn {add_on.id} has invalid value: {add_on.value}. Treating as None. {traceback.format_exc()}"
                )
                addon_value = None
            if addon_value is not None and addon_value > 0:
                expiry = sub.expires_at
                if expiry:
                    sources.append(
                        QuotaSource(
                            subscription_id=sub.id,
                            source_type="addon",
                            granted_amount=addon_value,
                            grant_type=QuotaGrantType.TOTAL,  # Addons are typically total grant
                            start_time=sub.start_at,
                            expiry_time=expiry,
                        )
                    )
                    logger.debug(
                        f"User {user_id}: Added AddOn {add_on.name} ({addon_value}) for {resource_type}."
                    )
                else:
                    logger.warning(
                        f"User {user_id}: Could not determine expiry for AddOn sub {sub.id}"
                    )
            elif addon_value == 0:
                logger.debug(
                    f"User {user_id}: AddOn {add_on.name} grants 0 quota, ignoring source."
                )
            elif addon_value is None:
                logger.warning(
                    f"User {user_id}: AddOn {add_on.name} (Sub ID: {sub.id}) has None numeric_value for target {add_on.target_field}. Effect data: '{add_on.effect_data}'. Skipping."
                )

        # Sort sources by expiry time (earliest first)
        logger.info(
            f"[DEBUG-QUOTA] Collected {len(sources)} sources before final sort for user {user_id}, resource {resource_type}."
        )
        for idx, src in enumerate(sources):
            logger.info(
                f"[DEBUG-QUOTA] _get_consumable_quota_sources - Before final sort [{idx}]: id={src.subscription_id}, type={src.source_type}, expiry={src.expiry_time}"
            )
        sources.sort(key=lambda s: s.expiry_time)
        for idx, src in enumerate(sources):
            logger.info(
                f"[DEBUG-QUOTA] _get_consumable_quota_sources - After final sort [{idx}]: id={src.subscription_id}, type={src.source_type}, expiry={src.expiry_time}"
            )
        return sources

    @staticmethod
    async def _calculate_consumed_for_source(
        user_id: str | UUID, resource_type: UsageType, source: QuotaSource
    ) -> int:
        """Calculates usage logged specifically against a given source within its relevant period."""

        now = datetime.now(timezone.utc)
        usage_query = UsageLog.filter(
            user_id=user_id,
            usage_type=resource_type,
            source_subscription_id=source.subscription_id,
            source_subscription_type=source.source_type,
            deleted_at__isnull=True,
        )

        # --- Handle Monthly Reset ---
        if source.source_type == "plan" and source.grant_type == QuotaGrantType.MONTHLY:
            # Calculate start of the *current* monthly cycle for this subscription
            cycle_start = source.start_time
            while (
                cycle_start + relativedelta(months=1) <= now
                and cycle_start < source.expiry_time
            ):
                cycle_start += relativedelta(months=1)

            # Ensure cycle_start doesn't go past the subscription expiry
            cycle_start = min(cycle_start, source.expiry_time)
            # Ensure cycle start is not in the future relative to now (edge case)
            cycle_start = min(cycle_start, now)

            # Filter usage logs from the start of the current cycle
            usage_query = usage_query.filter(created_at__gte=cycle_start)
            logger.debug(
                f"User {user_id}: Calculating MONTHLY consumption for plan {source.subscription_id} from cycle start: {cycle_start}"
            )
        # For TOTAL grant type (Plan TOTAL or AddOn), sum all historical usage for that source
        # Safely aggregate consumption
        aggregation = await usage_query.annotate(total_consumed=Sum("amount")).values(
            "total_consumed"
        )
        total_consumed = (
            int(aggregation[0]["total_consumed"] or 0)
            if aggregation and aggregation[0]["total_consumed"] is not None
            else 0
        )

        logger.debug(
            f"User {user_id}: Source {source.source_type} {source.subscription_id} ({source.grant_type}) consumed: {total_consumed} of {resource_type}"
        )
        return total_consumed

    @classmethod
    async def get_quota_details(
        cls, user_id: str | UUID, resource_type: UsageType
    ) -> Tuple[Optional[int], Optional[int]]:
        """
        Calculates the total *available* consumable quota across all sources.

        Returns:
            Tuple[Optional[int], Optional[int]]: (total_available_quota, total_granted_in_period)
                - (None, None) if unlimited.
                - (Available amount, Total granted amount considering monthly resets) for limited.
                - (0, 0) if no active sources or zero granted.
        """
        logger.info(
            f"User {user_id}: Getting available consumable quota for: {resource_type}"
        )
        sources = await cls._get_consumable_quota_sources(user_id, resource_type)

        if not sources:
            logger.warning(
                f"User {user_id}: No active consumable quota sources for {resource_type}. Returning (0, 0)."
            )
            return 0, 0

        # Check for unlimited source
        unlimited_source = next((s for s in sources if s.granted_amount is None), None)
        if unlimited_source:
            logger.debug(f"User {user_id}: Unlimited quota found for {resource_type}.")
            return None, None

        # Calculate total available from all limited sources, considering resets
        total_available = 0
        total_granted_in_period = 0

        consumption_tasks = [
            cls._calculate_consumed_for_source(user_id, resource_type, source)
            for source in sources  # Already filtered for limited sources if unlimited check passed
        ]
        consumed_amounts = await asyncio.gather(*consumption_tasks)

        for i, source in enumerate(sources):
            consumed = consumed_amounts[i]
            granted = source.granted_amount  # Should not be None here

            if granted is None:  # Should not happen, but safeguard
                logger.error(
                    f"User {user_id}: Unexpected None granted_amount in limited source calculation: {source}"
                )
                continue

            # Available from this specific source in its current period
            available_from_source = max(0, granted - consumed)
            total_available += available_from_source
            total_granted_in_period += granted  # Summing up the granted amounts
            logger.debug(
                f"User {user_id}: Source {source.source_type} {source.subscription_id} ({source.grant_type}, Expires: {source.expiry_time}): Granted={granted}, Consumed={consumed}, Available={available_from_source}"
            )

        logger.info(
            f"User {user_id}: Total available {resource_type}: {total_available} (Total granted in period: {total_granted_in_period})"
        )
        return total_available, total_granted_in_period

    @classmethod
    async def get_total_consumed_count_current_period(
        cls, user_id: str | UUID, resource_type: UsageType
    ) -> int:
        """
        Get the total consumed amount for a user and resource_type.
        For unlimited quotas:
        - If from a MONTHLY plan, only counts usage in the current monthly cycle
        - If from a TOTAL plan, counts all historical usage
        """
        # 1. 检查用户是否有活跃订阅及其计划类型
        sources = await cls._get_consumable_quota_sources(user_id, resource_type)
        plan_source = next((s for s in sources if s.source_type == "plan"), None)

        # 默认查询所有历史记录
        query = UsageLog.filter(user_id=user_id, usage_type=resource_type)

        # 如果有计划源且是MONTHLY类型，则只查询当前周期
        if plan_source and plan_source.grant_type == QuotaGrantType.MONTHLY:
            # 计算当前周期开始时间
            now = datetime.now(timezone.utc)
            cycle_start = plan_source.start_time
            while (
                cycle_start + relativedelta(months=1) <= now
                and cycle_start < plan_source.expiry_time
            ):
                cycle_start += relativedelta(months=1)

            # 确保周期开始不超过订阅到期或当前时间
            cycle_start = min(cycle_start, plan_source.expiry_time)
            cycle_start = min(cycle_start, now)

            # 过滤当前周期的记录
            query = query.filter(created_at__gte=cycle_start)
            logger.debug(
                f"User {user_id}: Calculating MONTHLY consumption from {cycle_start}"
            )

        # 执行查询并汇总
        result = await query.annotate(total_consumed=Sum("amount")).values(
            "total_consumed"
        )

        # 从结果中提取值
        total_consumed = result[0]["total_consumed"] if result else None
        return int(total_consumed or 0)  # 将None转换为0，然后转为int

    @classmethod
    async def check_quota(
        cls, user_id: str | UUID, resource_type: UsageType, amount_to_consume: int = 1
    ) -> bool:
        """Checks if the user has enough *available* consumable quota across all sources."""
        if amount_to_consume <= 0:
            return True

        total_available, _ = await cls.get_quota_details(user_id, resource_type)

        if total_available is None:  # Unlimited
            logger.debug(
                f"User {user_id}: Quota check PASSED ({resource_type}, Unlimited)."
            )
            return True

        if resource_type == UsageType.WEB_PAGE_LEARNED:
            # 减去正在学习的网页学习配额
            web_page_in_process = await VectorFile.filter(
                dataset__user__id=user_id,
                file_status=VectorFileStatus.PROCESS,
                file_type__in=[
                    VectorFileType.HTML,
                    VectorFileType.SITEMAP,
                    VectorFileType.GITBOOK,
                ],
                deleted_at__isnull=True,
            ).count()
            total_available -= web_page_in_process
        elif resource_type == UsageType.MULTI_MODAL_PARSING:
            # 减去正在学习的多模态解析配额
            multi_modal_in_process = await VectorFile.filter(
                dataset__user__id=user_id,
                file_status=VectorFileStatus.PROCESS,
                learn_type__gte=1,
                learn_type__lte=99,
                file_type__in=[VectorFileType.UPLOAD, VectorFileType.INTEGRATION],
                deleted_at__isnull=True,
            ).count()
            total_available -= multi_modal_in_process

        has_enough = total_available >= amount_to_consume
        if has_enough:
            logger.debug(
                f"User {user_id}: Quota check PASSED ({resource_type}). Available: {total_available}, Needed: {amount_to_consume}."
            )
        else:
            logger.warning(
                f"User {user_id}: Quota check FAILED ({resource_type}). Available: {total_available}, Needed: {amount_to_consume}."
            )
        return has_enough

    @classmethod
    async def get_effective_quota(
        cls, user_id: str | UUID, resource_type: UsageType
    ) -> Tuple[Optional[int], Optional[int], Optional[int]]:
        """
        Get effective quota information including available, total, and used amounts.

        Returns:
            Tuple[Optional[int], Optional[int], Optional[int]]: (available_quota, total_quota, used_quota)
                - If unlimited, all values will be None
                - If no quota sources, returns (0, 0, 0)
        """
        logger.info(f"User {user_id}: Getting effective quota for: {resource_type}")

        try:
            # Get basic quota details
            available, total = await cls.get_quota_details(user_id, resource_type)

            # If unlimited, return None for all values
            if available is None and total is None:
                return None, None, None

            # Calculate used quota
            used = total - available if total is not None else 0

            logger.info(
                f"User {user_id}: Effective quota for {resource_type}: Available={available}, Total={total}, Used={used}"
            )
            return available, total, used
        except Exception as e:
            logger.error(f"Error getting effective quota: {e}")
            return 0, 0, 0

    @classmethod
    async def consume_quota(
        cls, user_id: str | UUID, resource_type: UsageType, amount_to_consume: int = 1
    ):
        """
        Consumes *consumable* quota, prioritizing sources with the earliest expiry time.
        Logs consumption against the specific source(s) used.

        Raises:
            QuotaException: If the user does not have enough quota.
            ValueError: If amount_to_consume is not positive.
        """
        logger.info(
            f"User {user_id}: Attempting to consume {amount_to_consume} of {resource_type}"
        )
        if amount_to_consume <= 0:
            raise ValueError("Amount to consume must be positive.")

        sources = await cls._get_consumable_quota_sources(user_id, resource_type)

        if not sources:
            raise QuotaException(
                detail=f"User {user_id} has no active quota sources for {resource_type}. Consumption failed.",
            )

        # Use transaction inside the method
        async with in_transaction() as connection:
            # Handle unlimited case first
            unlimited_source = next(
                (s for s in sources if s.granted_amount is None), None
            )
            if unlimited_source:
                logger.info(
                    f"User {user_id}: Consuming from unlimited source {unlimited_source.source_type} {unlimited_source.subscription_id}."
                )
                try:
                    await UsageLog.create(
                        user_id=user_id,
                        usage_type=resource_type,
                        amount=amount_to_consume,
                        source_subscription_id=unlimited_source.subscription_id,
                        source_subscription_type=unlimited_source.source_type,
                        using_db=connection,
                    )
                    logger.info(
                        f"User {user_id}: Logged consumption of {amount_to_consume} {resource_type} against unlimited source."
                    )
                    return  # Consumption successful
                except Exception as e:
                    logger.error(
                        f"User {user_id}: Failed to log consumption against unlimited source: {e}"
                    )
                    raise QuotaException(
                        detail=f"Failed to log quota consumption: {e}",
                    )

            # --- Handle Limited Sources --- #
            amount_remaining_to_consume = amount_to_consume
            logs_to_create = []
            total_available_calculated = 0  # For final check

            # 重要: 确保我们只处理有限额度的sources，并且按过期时间排序
            limited_sources = [s for s in sources if s.granted_amount is not None]
            logger.info(
                f"[DEBUG-QUOTA] consume_quota: Initial limited_sources (before sort): {limited_sources}"
            )
            limited_sources.sort(key=lambda s: s.expiry_time)
            logger.info(
                f"[DEBUG-QUOTA] consume_quota: Sorted limited_sources: {limited_sources}"
            )

            consumed_amounts = {}
            for source_calc_idx, source_calc in enumerate(limited_sources):
                # logger.info(f"[DEBUG-QUOTA] consume_quota: About to calculate consumed for source_calc [{source_calc_idx}]: id={source_calc.subscription_id}, type={source_calc.source_type}")
                consumed = await cls._calculate_consumed_for_source(
                    user_id, resource_type, source_calc
                )
                consumed_amounts[source_calc.subscription_id] = consumed
            logger.info(
                f"[DEBUG-QUOTA] consume_quota: Calculated consumed_amounts: {consumed_amounts}"
            )

            for i, source in enumerate(limited_sources):
                consumed = consumed_amounts[source.subscription_id]
                granted = source.granted_amount

                if granted is None:
                    logger.warning(
                        f"[DEBUG-QUOTA] consume_quota: Source {source.subscription_id} has None grant, skipping."
                    )
                    continue

                available_from_this_source = max(0, granted - consumed)
                total_available_calculated += available_from_this_source
                logger.info(
                    f"[DEBUG-QUOTA] consume_quota - Evaluating for consumption [{i}]: source id={source.subscription_id}, type={source.source_type}, expiry={source.expiry_time}, granted={granted}, consumed={consumed}, available={available_from_this_source}, amount_remaining_to_consume={amount_remaining_to_consume}"
                )

                if available_from_this_source > 0 and amount_remaining_to_consume > 0:
                    consume_from_this = min(
                        amount_remaining_to_consume, available_from_this_source
                    )
                    amount_remaining_to_consume -= consume_from_this
                    logger.info(
                        f"[DEBUG-QUOTA] Consumed {consume_from_this} from source id={source.subscription_id}, remaining to consume: {amount_remaining_to_consume}"
                    )
                    logs_to_create.append(
                        UsageLog(
                            user_id=user_id,
                            usage_type=resource_type,
                            amount=consume_from_this,  # Log the amount consumed from *this* source
                            source_subscription_id=source.subscription_id,
                            source_subscription_type=source.source_type,
                            using_db=connection,
                        )
                    )
                else:
                    logger.debug(
                        f"User {user_id}: Skipping {source.source_type} {source.subscription_id} - "
                        f"Available={available_from_this_source}, AmountRemaining={amount_remaining_to_consume}"
                    )

                if amount_remaining_to_consume == 0:
                    logger.info(
                        f"[DEBUG-QUOTA] All {amount_to_consume} units for {resource_type} have been accounted for."
                    )
                    break  # Stop when the required amount is fully covered

            # Final check: Ensure enough was actually available based on calculation
            if amount_remaining_to_consume > 0:
                logger.error(
                    f"User {user_id}: Insufficient total available quota for {resource_type}. Calculated Available: {total_available_calculated}, Needed: {amount_to_consume}. Consumption failed."
                )
                # Raise QuotaException even if check_quota passed due to potential race conditions or calculation nuances
                raise QuotaException(
                    detail=f"Quota exceeded for {resource_type}. Available: {total_available_calculated}, Needed: {amount_to_consume}.",
                )

            # Create usage logs if any consumption happened
            if logs_to_create:
                try:
                    await UsageLog.bulk_create(logs_to_create, using_db=connection)
                    consumed_details = ", ".join(
                        [
                            f"{log.amount} from {log.source_subscription_type} {log.source_subscription_id}"
                            for log in logs_to_create
                        ]
                    )
                    logger.info(
                        f"User {user_id}: Successfully logged consumption of {amount_to_consume} {resource_type}. Details: {consumed_details}"
                    )
                except Exception as e:
                    logger.error(
                        f"User {user_id}: Failed to bulk create usage logs during consumption: {e}"
                    )
                    # Transaction should rollback automatically
                    raise QuotaException(
                        detail=f"Failed to log quota consumption: {e}",
                    )
            else:
                # Should not happen if amount_to_consume > 0 and initial check passed
                logger.warning(
                    f"User {user_id}: No usage logs created for consuming {amount_to_consume} {resource_type}, despite sufficient quota calculation."
                )

    # --- Methods for Concurrent Limits (Example Stubs - Implementation might vary) ---
    # These might not belong strictly in QuotaService if checked elsewhere, but shown for completeness.

    @staticmethod
    async def _get_total_concurrent_limit(
        user_id: str | UUID, limit_type: UsageType
    ) -> Optional[int]:
        """
        Calculates the total concurrent limit for a user (e.g., bot count, storage).
        Considers the active plan and relevant active add-ons.

        Returns:
            Optional[int]: The total limit, or None if unlimited. Returns 0 if no plan/addons grant limit.
        """
        logger.debug(
            f"User {user_id}: Calculating total concurrent limit for {limit_type}"
        )
        total_limit: Optional[int] = 0  # Start with 0, None means unlimited
        is_plan_unlimited = False
        plan_base_limit: Optional[int] = 0

        # 1. Get Active Plan Subscription and its limit
        plan_sub = await QuotaService._get_active_plan_subscription(user_id)
        if not plan_sub:
            logger.warning(
                f"User {user_id}: No active plan subscription found for concurrent limit check."
            )
            # Proceed to check addons, maybe they provide the base limit
            # Set plan_base_limit to 0 explicitly
            plan_base_limit = 0
        else:
            plan = await plan_sub.plan  # Ensure plan is loaded
            if not plan:
                logger.error(
                    f"User {user_id}: Plan relation not loaded for active subscription {plan_sub.id}."
                )
                plan_base_limit = 0  # Treat as 0 limit if plan data unavailable
            else:
                limit_attr = None
                if limit_type == UsageType.BOT_CREATED:
                    limit_attr = PlanField.BOT_QUOTA
                elif limit_type == UsageType.MESSAGE_SENT:
                    limit_attr = PlanField.MESSAGE_QUOTA
                elif limit_type == UsageType.WEB_PAGE_LEARNED:
                    limit_attr = PlanField.WEB_PAGE_QUOTA
                elif limit_type == UsageType.MULTI_MODAL_PARSING:
                    limit_attr = PlanField.MULTI_MODAL_PARSING_QUOTA
                if limit_attr:
                    plan_base_limit = getattr(
                        plan, limit_attr, 0
                    )  # Default to 0 if attr doesn't exist
                    if plan_base_limit is None:
                        logger.debug(
                            f"User {user_id}: Plan {plan.name} grants UNLIMITED {limit_type}."
                        )
                        is_plan_unlimited = True
                    else:
                        plan_base_limit = int(plan_base_limit)
                        logger.debug(
                            f"User {user_id}: Plan {plan.name} base limit for {limit_type}: {plan_base_limit}"
                        )
                else:
                    logger.error(
                        f"User {user_id}: Plan {plan.name} does not define an attribute for concurrent limit type {limit_type}."
                    )
                    plan_base_limit = 0  # Treat as 0 if not defined

        # If plan is unlimited, total limit is unlimited
        if is_plan_unlimited:
            return None

        # Start total limit with the plan's limit (which is 0 if no plan or not defined)
        total_limit = plan_base_limit

        # 2. Get Active Add-On Subscriptions providing this limit type
        add_on_subs = await QuotaService._get_active_add_on_subscriptions(
            user_id, limit_type
        )
        addon_increase = 0
        for sub in add_on_subs:
            add_on = (
                await sub.add_on
                if hasattr(sub, "add_on") and callable(getattr(sub, "add_on"))
                else getattr(sub, "add_on", None)
            )
            if not add_on:
                logger.warning(
                    f"User {user_id}: AddOn relation not loaded for subscription {sub.id} during concurrent limit check. Skipping."
                )
                continue

            # Addons increase the *base* limit
            addon_value = add_on.value
            if addon_value is not None and addon_value > 0:
                addon_increase += addon_value
                logger.debug(
                    f"User {user_id}: AddOn {add_on.name} adds {addon_value} to {limit_type} limit."
                )
            elif addon_value is None:
                logger.warning(
                    f"User {user_id}: AddOn {add_on.name} (Sub ID: {sub.id}) has None numeric_value for target {limit_type}. Skipping."
                )

        total_limit += addon_increase
        logger.debug(
            f"User {user_id}: Total calculated concurrent limit for {limit_type}: {total_limit} (Plan: {plan_base_limit}, Addons: {addon_increase})"
        )
        return total_limit

    @classmethod
    async def check_concurrent_limit(
        cls, user_id: str | UUID, limit_type: UsageType
    ) -> bool:
        """Checks concurrent limits like bot count or storage based on plan + addons."""
        logger.info(f"User {user_id}: Checking concurrent limit for {limit_type}")
        user = await User.get_or_none(id=user_id)
        if not user:
            logger.error(f"User {user_id} not found for concurrent check.")
            return False

        # Calculate the total limit from plan + addons
        total_limit = await cls._get_total_concurrent_limit(user_id, limit_type)

        # Unlimited case
        if total_limit is None:
            logger.info(
                f"User {user_id}: Concurrent limit check PASSED ({limit_type}, Unlimited)."
            )
            return True

        # Limited case
        if limit_type == UsageType.BOT_CREATED:
            # Get current bot count (assuming User model has this method)
            try:
                # Assuming Robot model has user relation and deleted_at field
                current_count = await Robot.filter(
                    user_id=user.user_id, deleted_at__isnull=True
                ).count()
                # current_count = await user.get_active_bot_count() # Alternative if method exists on User
            except AttributeError:
                logger.error(
                    f"User {user_id}: Cannot get bot count. `Robot` model query failed or `user.get_active_bot_count()` not found."
                )
                return False  # Fail safe

            has_capacity = current_count < total_limit
            if has_capacity:
                logger.info(
                    f"User {user_id}: Bot limit check PASSED. Current: {current_count}, Limit: {total_limit}."
                )
            else:
                logger.warning(
                    f"User {user_id}: Bot limit check FAILED. Current: {current_count}, Limit: {total_limit}."
                )
            return has_capacity
        else:
            logger.warning(
                f"User {user_id}: Unsupported concurrent limit type check: {limit_type}"
            )
            return True

    @classmethod
    async def check_bot_quota(cls, user_id: str | UUID) -> bool:
        """
        Checks if a user can create a new bot based on their subscription plan and quota.

        Args:
            user_id: The user's UUID

        Returns:
            bool: True if the user can create a new bot, False otherwise

        Raises:
            QuotaException: If there's an issue with quota checking or the user has exceeded their bot quota
        """
        # Skip quota check for local deployment
        if not QUOTA_CHECK:
            logger.info(f"quota check disabled, skipping bot quota check.")
            return True
        if IS_USE_LOCAL_VLLM:
            logger.info(
                f"User {user_id}: Local deployment detected, skipping bot quota check."
            )
            return True

        # Get the user
        user = await User.get_or_none(id=user_id)
        if not user:
            logger.error(f"User {user_id} not found for bot quota check.")
            raise QuotaException(
                detail=f"User {user_id} not found for bot quota check.",
            )

        # Check if user has any subscription history to the new plan system
        has_plan_history = await user.has_any_plan_subscription_history()

        # If user has subscription history, use new plan system and ignore corporate attribute
        if has_plan_history:
            logger.info(
                f"User {user_id}: Has subscription history, using new plan system for bot quota."
            )
            # Continue to check_concurrent_limit below
        else:
            # No subscription history, check corporate attribute
            if user.corporate:
                logger.info(
                    f"User {user_id}: Corporate user with no subscription history, unlimited bot quota."
                )
                return True

        # Check concurrent limit for bot creation
        has_capacity = await cls.check_concurrent_limit(user_id, UsageType.BOT_CREATED)
        if not has_capacity:
            # Get bot quota details for better error message
            current_count, total_limit = await cls.get_bot_quota_details(user_id)
            # Format message based on whether limit is None (unlimited) or int
            limit_str = "unlimited" if total_limit is None else str(total_limit)
            logger.warning(
                f"User {user_id}: Bot quota exceeded. Current: {current_count}, Limit: {limit_str}"
            )
            # Ensure total_limit is not None before using in f-string if needed, but check_concurrent_limit should only fail if limit is NOT None.
            if total_limit is None:
                # This case should ideally not happen if has_capacity is False
                logger.error(
                    f"User {user_id}: Inconsistent state - has_capacity is False but total_limit is None."
                )
                raise QuotaException(
                    detail="Bot quota check failed due to an inconsistent state.",
                )
            else:
                raise QuotaException(
                    detail=f"Bot quota exceeded. You have {current_count} bots out of a maximum of {total_limit}.",
                )

        return True

    @classmethod
    async def get_bot_quota_details(
        cls, user_id: str | UUID
    ) -> Tuple[int, Optional[int]]:
        """
        Gets the bot quota details for a user.

        Args:
            user_id: The user's UUID

        Returns:
            Tuple[int, Optional[int]]: (current_bot_count, total_bot_limit)
                - If unlimited, total_bot_limit will be None.
                - If no plan/user found, returns (0, 0).
        """
        # Skip quota check for local deployment
        if IS_USE_LOCAL_VLLM:
            # Need current count even if unlimited
            user = await User.get_or_none(id=user_id)
            current_count = 0
            if user:
                current_count = await Robot.filter(
                    user_id=user.user_id, deleted_at__isnull=True
                ).count()
            return current_count, None  # Unlimited for local deployment

        # Get the user
        user = await User.get_or_none(id=user_id)
        if not user:
            raise QuotaException(
                detail=f"User {user_id} not found for bot quota details."
            )

        # Get current bot count first
        current_count = await Robot.filter(
            user_id=user.user_id, deleted_at__isnull=True
        ).count()

        # Check if user has any subscription history to the new plan system
        has_plan_history = await user.has_any_plan_subscription_history()

        # If user has subscription history, use new plan system and ignore corporate attribute
        if has_plan_history:
            logger.info(
                f"User {user_id}: Has subscription history, using new plan system for bot quota details."
            )
            # Continue to get total_limit below
        else:
            # No subscription history, check corporate attribute
            if user.corporate:
                logger.info(
                    f"User {user_id}: Corporate user with no subscription history, unlimited bot quota details."
                )
                return current_count, None  # Unlimited for corporate users

        # Get the total bot limit (which can be None for unlimited)
        total_limit = await cls._get_total_concurrent_limit(
            user_id, UsageType.BOT_CREATED
        )

        # total_limit is already Optional[int], so just return it
        return current_count, total_limit

    @staticmethod
    async def _get_active_plan_subscription(
        user_id: str | UUID,
    ) -> Optional[PlanSubscription]:
        """Placeholder: Fetches the user's active plan subscription."""
        # Implementation needed here, e.g.:
        # return await PlanSubscription.filter(
        #     user_id=user_id,
        #     is_active=True,
        #     start_at__lte=datetime.now(timezone.utc),
        #     expires_at__gte=datetime.now(timezone.utc),
        # ).prefetch_related("plan").first()
        # Attempt to get user to retrieve plan (assuming User model has a method)
        user = await User.get_or_none(id=user_id).prefetch_related(
            "plan_subscriptions__plan"
        )
        if user:
            return await user.get_active_plan_subscription()
        return None

    @staticmethod
    async def _get_active_add_on_subscriptions(
        user_id: str | UUID, resource_type: UsageType
    ) -> List[AddOnSubscription]:
        # Attempt to get user to retrieve addons (assuming User model has a method)
        user = await User.get_or_none(id=user_id).prefetch_related(
            "add_on_subscriptions__add_on"
        )
        if not user:
            raise QuotaException(detail="User not found.")
        all_active_add_on_subscriptions = await user.get_active_add_on_subscription()
        filtered_add_on_subscriptions = []

        for subscription in all_active_add_on_subscriptions:
            add_on = await subscription.add_on

            # 获取 add_on 的 target_field 并检查是否与资源类型匹配
            target_field = add_on.target_field

            # 使用映射函数或映射字典来检查 target_field 是否对应于 resource_type
            if target_field_matches_resource_type(target_field, resource_type):
                filtered_add_on_subscriptions.append(subscription)

        logger.info(
            f"Found {len(filtered_add_on_subscriptions)} active add-on subscriptions for user {user_id} and resource type {resource_type}"
        )
        return filtered_add_on_subscriptions


def target_field_matches_resource_type(
    target_field: str, resource_type: UsageType
) -> bool:
    """检查目标字段是否与资源类型匹配。"""
    mapping = {
        "message_quota": UsageType.MESSAGE_SENT,
        "bot_quota": UsageType.BOT_CREATED,
        "web_page_quota": UsageType.WEB_PAGE_LEARNED,
        "multi_modal_parsing_quota": UsageType.MULTI_MODAL_PARSING,
    }
    return mapping.get(target_field) == resource_type
