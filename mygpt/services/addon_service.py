import logging
from datetime import datetime, timezone, timedelta
from typing import <PERSON><PERSON>, <PERSON><PERSON>, List
from uuid import UUID

from dateutil.relativedelta import relativedelta
from fastapi import HTTPException

from mygpt.models import (
    AddOn,
    AddOnSubscription,
    User,
)


async def get_addon(addon_id: UUID) -> AddOn:
    """
    Get an add-on by ID.

    Args:
        addon_id: The ID of the add-on to retrieve

    Returns:
        The add-on object

    Raises:
        HTTPException: If the add-on is not found
    """
    addon = await AddOn.get_or_none(id=addon_id, deleted_at__isnull=True)
    if not addon:
        logging.error(f"AddOn with ID {addon_id} not found")
        raise HTTPException(
            status_code=404,
            detail=f"AddOn with ID {addon_id} not found.",
        )
    return addon


async def get_addons(limit: int = 100, offset: int = 0) -> List[AddOn]:
    """
    Get a list of add-ons.

    Args:
        limit: Maximum number of add-ons to return
        offset: Number of add-ons to skip

    Returns:
        List of add-on objects
    """
    return await AddOn.filter(deleted_at__isnull=True).limit(limit).offset(offset).all()


async def create_addon(addon_data: dict) -> AddOn:
    """
    Create a new add-on.

    Args:
        addon_data: Dictionary containing add-on data

    Returns:
        The created add-on object

    Raises:
        HTTPException: If the add-on creation fails
    """
    try:
        addon = await AddOn.create(**addon_data)
        return addon
    except Exception as e:
        logging.error(f"Failed to create add-on: {e}")
        raise HTTPException(status_code=400, detail=f"Failed to create add-on: {e}")


async def subscribe_addon(
    user_id: UUID,
    addon_id: UUID,
    start_at: datetime = None,
    duration_unit: str = None,
    duration_length: int = None,
) -> AddOnSubscription:
    """
    Subscribe a user to an add-on.

    Args:
        user_id: The ID of the user to subscribe
        addon_id: The ID of the add-on to subscribe to
        start_at: Optional start time for the subscription (defaults to now)
        duration_unit: Optional duration unit (day or month) for the subscription
        duration_length: Optional duration length for the subscription

    Returns:
        The created add-on subscription object

    Raises:
        HTTPException: If the subscription creation fails
    """
    # Validate User exists
    logging.info(f"Validating user with ID {user_id}")
    user = await User.get_or_none(id=user_id, deleted_at__isnull=True)
    if not user:
        logging.error(f"User with ID {user_id} not found")
        raise HTTPException(
            status_code=404,
            detail=f"User with ID {user_id} not found.",
        )

    # Validate Add-on exists
    logging.info(f"Validating add-on with ID {addon_id}")
    add_on = await AddOn.get_or_none(id=addon_id, deleted_at__isnull=True)
    if not add_on:
        logging.error(f"AddOn with ID {addon_id} not found")
        raise HTTPException(
            status_code=404,
            detail=f"AddOn with ID {addon_id} not found.",
        )

    try:
        logging.info(
            f"Creating subscription for user {user.id} with add-on {add_on.id}"
        )

        # Use provided start_at or default to now
        if start_at is None:
            start_at = datetime.now(timezone.utc)

        subscription_data = {
            "user_id": user_id,
            "add_on_id": addon_id,
            "start_at": start_at,
        }

        # Use provided duration values or fallback to add-on's values
        effective_duration_unit = duration_unit or add_on.duration_unit
        effective_duration_length = duration_length or add_on.duration_length

        # Calculate expiration date based on duration
        if effective_duration_unit == "day":
            subscription_data["expires_at"] = start_at + timedelta(
                days=effective_duration_length
            )
            logging.info(
                f"Setting expiration date to {subscription_data['expires_at']} ({effective_duration_length} days)"
            )
        elif effective_duration_unit == "month":
            subscription_data["expires_at"] = start_at + relativedelta(
                months=effective_duration_length
            )
            logging.info(
                f"Setting expiration date to {subscription_data['expires_at']} ({effective_duration_length} months)"
            )
        else:
            logging.error(f"Unsupported duration unit: {effective_duration_unit}")
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported duration unit: {effective_duration_unit}",
            )

        # Create the subscription
        logging.info("Creating add-on subscription in database")
        addon_subscription = await AddOnSubscription.create(**subscription_data)
        logging.info(
            f"Successfully created add-on subscription with ID {addon_subscription.id}"
        )

        return addon_subscription

    except Exception as e:
        logging.error(f"Error creating addon subscription for user {user_id}: {e}")
        # Add more detailed error information
        import traceback

        logging.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=400, detail=f"Failed to create add-on subscription: {e}"
        )


async def cancel_addon_subscription(subscription_id: UUID) -> bool:
    """
    Cancel an add-on subscription.

    Args:
        subscription_id: The ID of the subscription to cancel

    Returns:
        True if the subscription was successfully cancelled

    Raises:
        HTTPException: If the subscription is not found
    """
    subscription = await AddOnSubscription.get_or_none(
        id=subscription_id, deleted_at__isnull=True
    )
    if not subscription:
        raise HTTPException(
            status_code=404,
            detail=f"Add-on subscription with ID {subscription_id} not found.",
        )

    # Use the model's cancel method
    await subscription.cancel()
    return True


async def get_addon_subscriptions(
    user_id: Optional[UUID] = None, limit: int = 100, offset: int = 0
) -> List[AddOnSubscription]:
    """
    Get a list of add-on subscriptions.

    Args:
        user_id: Optional user ID to filter subscriptions by
        limit: Maximum number of subscriptions to return
        offset: Number of subscriptions to skip

    Returns:
        List of add-on subscription objects
    """
    query = AddOnSubscription.filter(deleted_at__isnull=True).prefetch_related("add_on")

    if user_id:
        query = query.filter(user_id=user_id)

    return await query.limit(limit).offset(offset).all()
