import asyncio
import logging
import os
import traceback
from datetime import datetime
from uuid import uuid4

from pypdf import PdfReader, PdfWriter

from mygpt.endpoints.dataset import init_dataset
from mygpt.enums import VectorFileStatus, VectorStorageType
from mygpt.loader.document import DocumentLoader
from mygpt.loader.enums import filename_to_unstructured_type, UnstructuredType
from mygpt.loader.split import split_pdf_file
from mygpt.models import VectorFile
from mygpt.utils import semaphore_old, lock_old, semaphore_adv, lock_adv


async def process_file(session, dataset_obj, file_obj, file_location):
    from mygpt.core.embedding import file_embeddings

    lock = lock_old if file_obj.learn_type == 0 else lock_adv
    semaphore = semaphore_old if file_obj.learn_type == 0 else semaphore_adv
    async with semaphore:
        vector_file = None
        original_file_location = None
        pdf_decryption_happened = False
        try:
            try:
                lock.release()
            except Exception as e:
                logging.error(
                    f"unexpected error when release lock, error: {e}; {lock}; {traceback.format_exc()}"
                )
            logging.debug(f"process_file: {file_location}, {file_obj}, {semaphore}")
            filename = os.path.basename(file_location)
            unstructured_type = filename_to_unstructured_type(filename)
            child_files = [file_location]

            # 旧文档学习需要按90页拆分pdf，高级文档学习不要拆分，拆分了高级文档解析的缓存策略就无效了
            if unstructured_type == UnstructuredType.PDF and file_obj.learn_type == 0:
                child_files = await asyncio.to_thread(split_pdf_file, file_location, 90)
                if len(child_files) != 1 or child_files[0] != file_location:
                    os.remove(file_location)
            if unstructured_type == UnstructuredType.PDF and file_obj.learn_type != 0:
                child_files, file_location, original_file_location = await decrypt_pdf_empty_pwd(child_files, file_location)
                pdf_decryption_happened = True
            doc_id = str(uuid4())
            resources = []

            # 获取vector_file对象
            vector_file = await VectorFile.filter(
                id=file_obj.id, deleted_at__isnull=True
            ).first()
            if not vector_file:
                logging.warning(f"file_obj not found: {file_obj.id}")
                return

            vector_file.file_status = VectorFileStatus.PROCESS
            vector_file.updated_at = datetime.now()
            await vector_file.save(update_fields=["file_status", "updated_at"])

            for index, child_file in enumerate(child_files):
                try:
                    vector_file = await VectorFile.filter(
                        id=file_obj.id, deleted_at__isnull=True
                    ).first()
                    if not vector_file:
                        logging.warning(f"file_obj not found: {file_obj.id}")
                        break

                    document_loader = DocumentLoader(child_file)
                    documents = await document_loader.wait_for_load(
                        client=session,
                        learn_type=vector_file.learn_type,
                        src_name=vector_file.filename,
                    )

                    for document in documents:
                        document.metadata["title"] = file_obj.filename

                    for document in documents:
                        document_resources = document.metadata.get("resources", [])
                        resources.extend(document_resources)
                        if document_resources:
                            del document.metadata["resources"]

                    vector_file.resources = resources
                    await vector_file.save(update_fields=["resources"])

                    if len(documents) == 0:
                        failed_reason = "No content found in file."
                        vector_file.file_status = VectorFileStatus.FAIL
                        vector_file.failed_reason = failed_reason
                        vector_file.updated_at = datetime.now()
                        await vector_file.save(
                            update_fields=[
                                "file_status",
                                "failed_reason",
                                "updated_at",
                            ]
                        )
                except Exception as e:
                    logging.error(
                        f"fail to upload file in error 222: {e}, {traceback.format_exc()}"
                    )
                    if vector_file is None:
                        vector_file = await VectorFile.filter(id=file_obj.id).first()
                    if not vector_file:
                        logging.warning(f"file_obj not found: {file_obj.id}")
                    else:
                        vector_file.file_status = VectorFileStatus.FAIL
                        vector_file.failed_reason = str(e)
                        vector_file.updated_at = datetime.now()
                        await vector_file.save(
                            update_fields=[
                                "file_status",
                                "failed_reason",
                                "updated_at",
                            ]
                        )
                    break
                finally:
                    os.remove(child_file)

                if not documents:
                    continue

                dataset_obj = await init_dataset(str(dataset_obj.id), documents)
                vector_storage_type = VectorStorageType(
                    dataset_obj.metadata.get(
                        "vector_storage", VectorStorageType.PINECONE
                    )
                )

                if vector_storage_type == VectorStorageType.QDRANT:
                    vector_file.file_status = VectorFileStatus.FAIL
                    vector_file.failed_reason = (
                        "Upgrade vector storage type to qdrant_one_collection"
                    )
                    vector_file.updated_at = datetime.now()
                    await vector_file.save(
                        update_fields=["file_status", "failed_reason", "updated_at"]
                    )
                embedding_params = dataset_obj.get_embedding_params()

                metadata = documents[0].metadata
                metadata["doc_id"] = doc_id
                await file_embeddings(
                    vector_storage_type,
                    str(dataset_obj.id),
                    dataset_obj.collection_name,
                    str(file_obj.id),
                    documents,
                    metadata,
                    embedding_model=embedding_params.provider,
                    embedding_model_name=embedding_params.model_name,
                    embedding_dimensions=embedding_params.dimensions,
                    is_first=index == 0,
                    is_last=index == len(child_files) - 1,
                )

                # 如果是解密后的文件，处理完成后删除原始加密文件
                if pdf_decryption_happened and original_file_location is not None and file_location != original_file_location and os.path.exists(original_file_location):
                    try:
                        os.remove(original_file_location)
                        logging.debug(f"Removed original encrypted file: {original_file_location}")
                    except OSError as e:
                        logging.error(f"Error removing original file: {e}")

        except Exception as e:
            if vector_file is None:
                vector_file = await VectorFile.filter(id=file_obj.id).first()
            if vector_file:
                vector_file.file_status = VectorFileStatus.FAIL
                vector_file.failed_reason = str(e)
                await vector_file.save()
            logging.error(f"Exception in process_file inner block: {e}")
            logging.error(traceback.format_exc())


async def decrypt_pdf_empty_pwd(child_files: list, file_location: str) -> tuple[list, str, str]:
    original_path = file_location
    with open(file_location, "rb") as src_file:
        reader = PdfReader(src_file)
        # 如果是加密文件，需要处理
        if reader.is_encrypted:
            # 尝试解密逻辑
            decrypted = False
            for password in ["", "\x00"]:  # 空密码和空字符
                try:
                    if reader.decrypt(password) > 0:
                        # 深度验证：尝试读取首页内容
                        try:
                            reader.pages[0].extract_text()
                            decrypted = True
                            logging.debug(f"PDF解密成功: {file_location}")
                            break
                        except Exception as e:
                            logging.debug(f"PDF解密后无法读取内容: {file_location}, 错误: {str(e)}")
                            continue  # 解密成功但内容不可读
                except Exception as e:
                    logging.debug(f"PDF解密尝试失败: {file_location}, 密码: {password}, 错误: {str(e)}")
                    continue

            if decrypted:
                # 创建新的解密文件
                decrypted_path = os.path.join(
                    os.path.dirname(file_location),
                    f"decrypted_{str(uuid4())[:8]}_{os.path.basename(file_location)}"
                )
                logging.debug(f"创建解密文件: {decrypted_path}")
                original_child_files = child_files.copy()  # 保存原始child_files
                try:
                    # 写入新文件
                    with open(decrypted_path, "wb") as dest_file:
                        writer = PdfWriter()

                        # 复制元数据，但移除加密相关信息
                        if reader.metadata:
                            filtered_metadata = reader.metadata.copy()
                            # 移除加密相关的metadata（包括可能的大小写变体）
                            base_encryption_keys = {'encrypt', 'encryptmetadata', 'filter', 'subfilter',
                                                    'v', 'length', 'cf', 'stmf', 'strf', 'r', 'o', 'u',
                                                    'p', 'eff', 'perms'}

                            # 移除所有匹配的键，不区分大小写
                            keys_to_remove = set()
                            for key in filtered_metadata:
                                # 移除开头的 '/' 并转换为小写进行比较
                                key_lower = key.lstrip('/').lower()
                                if key_lower in base_encryption_keys:
                                    keys_to_remove.add(key)

                            for key in keys_to_remove:
                                filtered_metadata.pop(key, None)

                            writer.add_metadata(filtered_metadata)

                        # 复制所有页面
                        for page in reader.pages:
                            writer.add_page(page)

                        writer.write(dest_file)

                    # 使用新的解密文件继续处理
                    file_location = decrypted_path
                    child_files = [file_location]
                    logging.debug(f"PDF解密文件创建成功: {decrypted_path}")
                except Exception as e:
                    # 清理解密文件
                    if os.path.exists(decrypted_path):
                        os.remove(decrypted_path)
                        logging.error(f"PDF解密失败，清理临时文件: {decrypted_path}, 错误: {str(e)}")
                    # 恢复原始文件路径和child_files
                    file_location = original_path
                    child_files = original_child_files
                    logging.error(f"PDF解密失败，恢复原始文件: {file_location}")
            else:
                # 如果不能解密，抛出异常
                logging.error(f"PDF文件无法解密: {file_location}")
                raise ValueError("The PDF file is encrypted. We do not support encrypted PDF files.")
    return child_files, file_location, original_path
