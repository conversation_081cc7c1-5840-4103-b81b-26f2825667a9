import asyncio
import json

from mygpt.core.cohere_utils import AsyncClientSession
from mygpt.settings import COHERE_API_KEY

co = AsyncClientSession(
    COHERE_API_KEY, api_url="http://35.229.203.197:9000", timeout=600
)


async def cohere_rerank(query: str, documents: list, model_name: str):
    # The endpoint will error if len(documents) * max_chunks_per_doc >1,000, so max_chunks_per_doc is set to 8
    response = await co.rerank(query, documents, model_name, max_chunks_per_doc=8)
    return response


async def eval():
    f = "output_directory/huge_json/cases.json"
    cases = json.loads(open(f).read())

    for dataset_case in cases:
        print(f'\n\n{dataset_case["dataset_id"]} start')
        data = json.loads(
            open(
                "output_directory/huge_json/" + dataset_case["dataset_id"] + ".json"
            ).read()
        )
        for index, case in enumerate(dataset_case["cases"]):
            print("case", index)
            documents = list(data.values())
            query = case["query"]
            await add_test_doc(documents)
            # results = await asyncio.gather(
            #     cohere_rerank(
            #         query, documents, "hotchpotch/japanese-bge-reranker-v2-m3-v1"
            #     ),
            #     cohere_rerank(
            #         query, documents, "hotchpotch/japanese-bge-reranker-v2-m3-v1"
            #     ),
            #     cohere_rerank(
            #         query, documents, "hotchpotch/japanese-bge-reranker-v2-m3-v1"
            #     ),
            #     cohere_rerank(
            #         query, documents, "hotchpotch/japanese-bge-reranker-v2-m3-v1"
            #     ),
            #     cohere_rerank(
            #         query, documents, "hotchpotch/japanese-bge-reranker-v2-m3-v1"
            #     ),
            #     cohere_rerank(
            #         query, documents, "hotchpotch/japanese-bge-reranker-v2-m3-v1"
            #     ),
            # )
            # continue
            res = await cohere_rerank(
                query, documents, "hotchpotch/japanese-bge-reranker-v2-m3-v1"
            )

            for rank_index, result in enumerate(res.results, start=1):
                score = result.relevance_score
                index = result.index
                document = result.document
                file_id = document["metadata"]["file_id"]
                if file_id in case["file_ids"]:
                    print(rank_index)
                    break


async def add_test_doc(documents):
    last_added = documents[-1].copy()
    last_added[
        "text"
    ] = """Content-related illustrations: !(https://prd-mygpt.s3.ap-northeast-1.amazonaws.com/parser_file/74802a8063a54e5e8bed9199255bd553.jpeg)

            カロリーゼロ カフェインレス 砂糖ゼロ ダイエットウォーター デザートティー

            DozoFreesh Fruits Tea Set Of 6Bags

            DozoFreeshフルーツティー オリジナル3種+新たに3種類セット（全6種） 無添加 食べられるお茶 砂糖不使用 カロリーゼロ カフェインレス ノンカフェイン ティーバッグ 水分補給 プレゼント ギフト 贈り物 （紅茶 ルイボスティー ウーロン茶 龍井茶 プーアル茶 アッサムティー）

            ¥ 2,334（税込）

            1896

            132

            たっぷりのドライフルーツと選び抜かれたお茶を使った新感覚フルーツティー オリジナル3種+新たに3種類セット（全6種）

            SHARE

            Content-related illustrations: !(https://prd-mygpt.s3.ap-northeast-1.amazonaws.com/parser_file/4bdb6a56631c4748998a23a5e21e5a7a.svg) リンクをコピー

            Content-related illustrations: !(https://prd-mygpt.s3.ap-northeast-1.amazonaws.com/parser_file/357982cff584423e808a4529498ed4e2.svg) Instagramでシェア: https://www.instagram.com/dozofreesh_official Content-related illustrations: !(https://prd-mygpt.s3.ap-northeast-1.amazonaws.com/parser_file/5d0a912165864481b9eba9bd7f6df4a4.svg) Facebookでシェア: https://www.facebook.com/share.php?u=https://www.freesh.com/shop/shop/detail?id=76 Content-related illustrations: !(https://prd-mygpt.s3.ap-northeast-1.amazonaws.com/parser_file/dd579011e81a4c16a0e09c4f0c851c2b.svg) Twitterでシェア: https://twitter.com/share?text=DozoFreeshフルーツティー オリジナル3種+新たに3種類セット（全6種） 無添加 食べられるお茶"""
    last_added["metadata"]["file_id"] = "be6e240c-9d11-44da-ac3b-6212f494a53e"
    documents.append(last_added)


if __name__ == "__main__":
    asyncio.run(eval())
