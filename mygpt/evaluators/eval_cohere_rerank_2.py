import asyncio
import json

from mygpt.core.cohere_utils import AsyncClientSession
from mygpt.settings import COHERE_API_KEY

co = AsyncClientSession(COHERE_API_KEY) if COHERE_API_KEY else None


async def cohere_rerank(query: str, documents: list, model_name: str):
    # The endpoint will error if len(documents) * max_chunks_per_doc >1,0000, so max_chunks_per_doc is set to 50
    response = await co.rerank(query, documents, model_name, max_chunks_per_doc=50)
    return response


async def eval():
    f = "output_directory/huge_json/cases.json"
    cases = json.loads(open(f).read())

    for dataset_case in cases:
        print(f'\n\n{dataset_case["dataset_id"]} start')
        data = json.loads(
            open(
                "output_directory/huge_json/" + dataset_case["dataset_id"] + ".json"
            ).read()
        )
        for case in dataset_case["cases"]:
            documents = list(data.values())
            query = case["query"]
            res = await cohere_rerank(query, documents, "rerank-multilingual-v2.0")
            for rank_index, result in enumerate(res.results, start=1):
                score = result.relevance_score
                index = result.index
                document = result.document
                file_id = document["metadata"]["file_id"]
                if file_id in case["file_ids"]:
                    print(rank_index)
                    break


if __name__ == "__main__":
    asyncio.run(eval())
