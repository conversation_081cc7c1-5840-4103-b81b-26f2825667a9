import json
import time

import httpx


def eval():
    f = "output_directory/huge_json/cases.json"
    cases = json.loads(open(f).read())

    for dataset_case in cases:
        print(f'\n\n{dataset_case["dataset_id"]} start')
        data = json.loads(
            open(
                "output_directory/huge_json/" + dataset_case["dataset_id"] + ".json"
            ).read()
        )
        for case in dataset_case["cases"]:
            data_values = list(data.values())
            # docs = [
            #     {"text": d["text"], "file_id": d["metadata"]["file_id"]}
            #     for d in data_values
            # ]
            documents = list(data.values())
            query = case["query"]
            data = {
                "query": query,
                "documents": documents[:10],
                "model": "maidalun1020/bce-reranker-base_v1",
            }
            t0 = time.time()
            res = httpx.post(
                "http://34.146.198.179:9000/rerank", json=data, timeout=100
            )
            d1 = res.json()
            print("dt", len(data["documents"]), time.time() - t0, "real t", d1["time"])
            continue
            for rank_index, result in enumerate(d1["results"], start=1):
                # result is a dict with keys: index, document, relevance_score
                score = result["relevance_score"]
                index = result["index"]
                document = result["document"]
                file_id = document["metadata"]["file_id"]
                if file_id in case["file_ids"]:
                    print(rank_index)
                    break
            # print(d1)


if __name__ == "__main__":
    eval()
