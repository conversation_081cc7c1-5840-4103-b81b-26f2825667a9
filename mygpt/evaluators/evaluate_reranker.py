import json
import os
import shutil
import time
import uuid
from collections import defaultdict
from typing import Optional

import loguru
import pandas as pd
import qdrant_client
from langchain_core.documents import Document
from llama_index.core.base.base_retriever import BaseRetriever
from llama_index.core.evaluation import (
    EmbeddingQAFinetuneDataset,
    RetrieverEvaluator,
    get_retrieval_results_df,
    generate_question_context_pairs,
)
from llama_index.core.llms.utils import LLM
from llama_index.core.schema import TextNode
from qdrant_client.conversions.conversion import GrpcToRest
from qdrant_client.http.models import Filter, FieldCondition, MatchValue
from tortoise import Tortoise

from mygpt import settings
from mygpt.core.embedding import EmbeddingFactory
from mygpt.core.retriever import Retriever
from mygpt.core.utils import handle_decode, get_collection_name
from mygpt.core.vector_storage import VectorStorage, VectorStorageFactory
from mygpt.enums import LLM_PROVIDER
from mygpt.models import Dataset, VectorFile
from mygpt.settings import TORTOISE_ORM

QA_FILE_ID_GENERATE_PROMPT_TMPL = """\
Please formulate questions based on the following documents and provide answers. I hope that each question and answer corresponds to a unique document UUID4. Do not provide questions that can be answered by multiple documents; a question should only be answerable by the content of a specific document. Include the corresponding document's file ID.

If there is sufficient information, please provide 20 or more questions along with their corresponding file IDs.

Here are the documents:
====================================
{documents}
====================================
"""


def filter_qa_dataset(
    qa_dataset,
):
    """
    Filters out queries from the qa_dataset that contain certain phrases and the corresponding
    entries in the relevant_docs, and creates a new EmbeddingQAFinetuneDataset object with
    the filtered data.

    :param qa_dataset: An object that has 'queries', 'corpus', and 'relevant_docs' attributes.
    :return: An EmbeddingQAFinetuneDataset object with the filtered queries, corpus and relevant_docs.
    """

    def check_for_phrases_in_dict_values(query_string: str):
        phrase_arrs = ["Here are 2", "Here are two"]
        return any(phrase in query_string for phrase in phrase_arrs)

    # Extract keys from queries and relevant_docs that need to be removed
    queries_relevant_docs_keys_to_remove = {
        k for k, v in qa_dataset.queries.items() if check_for_phrases_in_dict_values(v)
    }

    # Filter queries and relevant_docs using dictionary comprehensions
    filtered_queries = {
        k: v
        for k, v in qa_dataset.queries.items()
        if k not in queries_relevant_docs_keys_to_remove
    }
    filtered_relevant_docs = {
        k: v
        for k, v in qa_dataset.relevant_docs.items()
        if k not in queries_relevant_docs_keys_to_remove
    }

    # Create a new instance of EmbeddingQAFinetuneDataset with the filtered data
    return EmbeddingQAFinetuneDataset(
        queries=filtered_queries,
        corpus=qa_dataset.corpus,
        relevant_docs=filtered_relevant_docs,
    )


async def generate_dataset(
    dataset_id: str,  # 数据集ID
    collection_name: str = "gptbase_col",  # 集合名称，默认为"gptbase_col"
    # data: Optional[list] = None,  # 可选的数据列表，默认为None
    # save_path: str = './data',  # 保存路径，默认为'./data'
    offset: Optional[str] = None,  # 可选的偏移量，默认为None
    limit: int = 1000,  # 限制获取的数据量，默认为100
):
    """
    异步函数，用于生成数据集的CSV文件
    """
    # 获取VectorStorageFactory的实例
    storage = VectorStorageFactory.get_instance()

    # 定义过滤器
    filters = {"must": [{"key": "group_id", "match": {"any": [dataset_id]}}]}

    # 从存储中索引点
    result = await storage.index_points(
        collection_name=collection_name,
        offset=offset,
        limit=limit,
        filters=filters,
        with_vectors=False,
    )

    # 将结果的下一页偏移量转换为字符串
    try:
        next_page_offset = (
            GrpcToRest.convert_point_id(result.next_page_offset)
            if result.next_page_offset
            else None
        )
    except Exception as e:
        next_page_offset = None

    # 如果结果的下一页偏移量等于当前偏移量
    if not next_page_offset or next_page_offset == offset:
        return []

    # 将结果中的点转换为记录
    points = [GrpcToRest.convert_record(point) for point in result.result]

    # 将点的数据添加到数据列表中
    d = [
        {
            "id": doc.id,
            "content": doc.payload["page_content"],
            "doc_id": doc.payload["metadata"]["doc_id"],
            "chunk_index": doc.payload["metadata"]["chunk_index"],
            "source": doc.payload["metadata"]["source"],
            "file_id": doc.payload["metadata"]["file_id"],
            "metadata": doc.payload["metadata"],
        }
        for doc in points
    ]

    # 递归调用自身，以获取下一页的数据
    sub_data = await generate_dataset(
        dataset_id,
        collection_name=collection_name,
        offset=next_page_offset,
        limit=limit,
    )
    return d + sub_data


class Evaluation:
    def __init__(self, dataset_id: str):
        self.dataset_id = dataset_id

    async def generate_qa_pairs(
        self,
        llm: LLM,
        pair_num: int = 100,
        csv_path: Optional[str] = None,
    ):
        """
        生成问答对
        """
        if not csv_path:
            csv_path = f"./data/{self.dataset_id}.csv"
        # 判断是否存在CSV文件
        try:
            data = pd.read_csv(csv_path)
        except FileNotFoundError:
            # 生成数据集
            print("Generating dataset...")
            rs = await generate_dataset(self.dataset_id)
            data = pd.DataFrame(rs)
            data.to_csv(csv_path, index=False)
        dataset_save_path = f"./data/{self.dataset_id}_qa_pairs.json"
        try:
            old_qa_dataset = EmbeddingQAFinetuneDataset.from_json(dataset_save_path)
        except FileNotFoundError:
            old_qa_dataset = EmbeddingQAFinetuneDataset(
                queries={}, corpus={}, relevant_docs={}
            )

        try:
            qa_dataset = EmbeddingQAFinetuneDataset(
                queries={}, corpus={}, relevant_docs={}
            )
            for idx, item in data.iterrows():
                if idx >= pair_num:
                    break
                print(f"starting with idx:{idx}")
                if old_qa_dataset.corpus.get(item["id"]):
                    continue
                # 生成问题-上下文对
                nodes = [
                    TextNode(
                        id_=item["id"],
                        text=item["content"],
                    )
                ]

                tmp = generate_question_context_pairs(
                    nodes,
                    llm=llm,
                    qa_generate_prompt_tmpl=QA_GENERATE_PROMPT_TMPL,
                    num_questions_per_chunk=2,
                )
                qa_dataset = EmbeddingQAFinetuneDataset(
                    queries={**qa_dataset.queries, **tmp.queries},
                    corpus={**qa_dataset.corpus, **tmp.corpus},
                    relevant_docs={**qa_dataset.relevant_docs, **tmp.relevant_docs},
                )
            qa_dataset = filter_qa_dataset(qa_dataset)

        finally:
            # 合并新旧问答对
            qa_dataset = EmbeddingQAFinetuneDataset(
                queries={**old_qa_dataset.queries, **qa_dataset.queries},
                corpus={**old_qa_dataset.corpus, **qa_dataset.corpus},
                relevant_docs={
                    **old_qa_dataset.relevant_docs,
                    **qa_dataset.relevant_docs,
                },
            )
            qa_dataset.save_json(dataset_save_path)

    async def save_database(
        self,
        dataset_path: str,
        storage: VectorStorage,
        model_name: Optional[str] = None,
        collection_name: str = "gptbase_col",
    ):
        qa_dataset = EmbeddingQAFinetuneDataset.from_json(dataset_path)
        docs, doc_ids = [], []
        for k, v in qa_dataset.corpus.items():
            docs.append(
                Document(
                    page_content=v,
                    metadata={},
                )
            )
            doc_ids.append(k)

        embeddings = EmbeddingFactory.get_instance(
            provider=LLM_PROVIDER.OPENAI,
            model_name=model_name,
        )
        storage.from_documents(
            embeddings=embeddings,
            collection_name=collection_name,
            group_id=self.dataset_id,
            documents=docs,
            ids=doc_ids,
        )

    async def save_csv_database(
        self,
        csv_path: str,
        storage: VectorStorage,
    ):
        data = pd.read_csv(csv_path)
        docs, doc_ids = [], []
        for idx, item in data.iterrows():
            docs.append(
                Document(
                    page_content=item["content"],
                    metadata={},
                )
            )
            doc_ids.append(item["id"])

        embeddings = EmbeddingFactory.get_instance(
            provider=LLM_PROVIDER.OPENAI, model_name="text-embedding-3-large"
        )
        storage.from_documents(
            embeddings=embeddings,
            collection_name="gptbase_col",
            group_id=self.dataset_id,
            documents=docs,
            ids=doc_ids,
        )

    async def aevaluate_dataset(
        self,
        dataset_path: str,
        retriever: BaseRetriever,
        save_path: Optional[str] = None,
    ):
        retriever_evaluator = RetrieverEvaluator.from_metric_names(
            ["mrr", "hit_rate"],
            retriever=retriever,
        )
        qa_dataset = EmbeddingQAFinetuneDataset.from_json(dataset_path)
        eval_results = []
        idx = 20
        while idx > 0:
            try:
                item_idx = 0
                for query_id, query in qa_dataset.queries.items():
                    item_idx += 1
                    if item_idx <= len(eval_results):
                        continue
                    expected_ids = qa_dataset.relevant_docs[query_id]
                    # expected_ids=['108fa63692cf471aafad9858d3af0a00']
                    # 将hex格式的id转换为字符串 expected_ids=['108fa636-92cf-471a-afad-9858d3af0a00']
                    expected_ids = [
                        uuid.UUID(hex=id).urn[9:] if len(id) == 32 else id
                        for id in expected_ids
                    ]
                    eval_result = await retriever_evaluator.aevaluate(
                        query,
                        expected_ids=expected_ids,
                    )
                    eval_results.append(eval_result)
                    print(f"query:{query} done. total finished {len(eval_results)}")
                break
            except Exception as e:
                idx -= 1
                print(
                    f"query:{query} Error: {e} retrying in 1 second,remaining {idx} times"
                )
                time.sleep(1)
        data = get_retrieval_results_df(
            names=["retriever"],
            results_arr=[eval_results],
            metric_keys=["mrr", "hit_rate"],
        )
        if not save_path:
            save_path = f"./data/{self.dataset_id}_retrieval_results.csv"
        data.to_csv(save_path, index=False)

    async def retriever_speed_check(
        self,
        queries: list[str],
        retriever: Retriever,
        collection_name: str = "gptbase_col",
        model_name: Optional[str] = None,
        limit: int = 10,
    ):
        retriever_result = []
        start_time = time.time()
        embedding_time = 0
        for query in queries:
            ed_start_time = time.time()
            embeddings = await EmbeddingFactory.concurrent_embedding(
                [query], model_name
            )
            ed_end_time = time.time()
            embedding_time += ed_end_time - ed_start_time
            result = await retriever.retrieve(
                [self.dataset_id],
                query,
                collection_name=collection_name,
                model_name=model_name,
                embeddings=embeddings,
                limit=limit,
            )
            retriever_result.append(result)
        end_time = time.time()
        total_time = end_time - start_time
        average_time = total_time / len(queries)  # seconds
        embedding_average_time = embedding_time / len(queries)
        print(
            f"average_time:{average_time} seconds | embedding_average_time:{embedding_average_time} seconds | query_num:{len(queries)}"
        )
        return retriever_result, average_time, embedding_average_time


async_qdrant_client = qdrant_client.AsyncQdrantClient(
    host=settings.VECTOR_STORAGE_QDRANT_HOST,
    port=settings.VECTOR_STORAGE_QDRANT_HTTP_PORT,
    grpc_port=settings.VECTOR_STORAGE_QDRANT_GRPC_PORT,
    timeout=60,
)


def merge_chunks(full_dict_list):
    file_dict = defaultdict(lambda: {"metadata": None, "text": ""})

    # 先对full_dict_list按照file_id和chunk_index排序
    sorted_full_dict_list = sorted(
        full_dict_list,
        key=lambda x: (
            x["payload"]["metadata"]["file_id"],
            x["payload"]["metadata"]["chunk_index"],
        ),
    )

    for item in sorted_full_dict_list:
        payload = item["payload"]
        file_id = payload["metadata"]["file_id"]
        chunk_index = payload["metadata"]["chunk_index"]
        page_content = payload["page_content"]

        if not file_dict[file_id]["metadata"]:
            file_dict[file_id]["metadata"] = payload["metadata"]

        # 拼接文本内容
        file_dict[file_id]["text"] += page_content

    return file_dict


async def get_all_file_dict(dataset_id: str) -> dict:
    """
    生成数据集文本
    """
    # use new his-mobile
    start_time = time.time()

    dataset = await Dataset.get(id=dataset_id)
    storage_type = dataset.metadata["vector_storage"]
    if storage_type == "qdrant":
        loguru.logger.info(f"not support qdrant storage for dataset {dataset_id}")
        return {}
    # vector_file = await VectorFile.filter(dataset_id=dataset_id)
    # filters = {"must": [{"key": "group_id", "match": {"value": dataset_id}}]}
    # Construct the filter for search
    filter = Filter(
        must=[FieldCondition(key="group_id", match=MatchValue(value=dataset_id))],
        must_not=[
            FieldCondition(key="metadata.short_chunk", match=MatchValue(value=True))
        ],
    )
    collection_name = "gptbase_col"
    if dataset.metadata.get("embedding_model_name"):
        collection_name = get_collection_name(dataset.metadata["embedding_model_name"])
    full_record_list = []
    # Perform the search. Assuming you have a collection named appropriately.
    response = await async_qdrant_client.scroll(
        collection_name=collection_name, scroll_filter=filter, limit=100
    )
    full_record_list.extend(response[0])
    while response[1]:
        response = await async_qdrant_client.scroll(
            collection_name=collection_name,
            scroll_filter=filter,
            limit=500,
            offset=response[1],
        )
        for index in response[0]:
            text, metadata = handle_decode(
                index.payload["page_content"], index.payload["metadata"]
            )
            index.payload["page_content"] = text
            index.payload["metadata"] = metadata
        full_record_list.extend(response[0])
    full_dict_list = [it.dict() for it in full_record_list]
    if full_dict_list and "file_id" not in full_dict_list[0]["payload"]["metadata"]:
        vector_files = await VectorFile.filter(dataset_id=dataset_id)
        for file in full_dict_list:
            for vector_file in vector_files:
                if vector_file.filename == file["payload"]["metadata"]["source"]:
                    file["payload"]["metadata"]["file_id"] = str(vector_file.id)
                    break
    file_dict = merge_chunks(full_dict_list)
    print(response, len(full_record_list))
    end_time = time.time()
    print(f"Execution time: {end_time - start_time} seconds")
    return file_dict


async def generate_dataset_text(dataset_id: str) -> None:
    file_dict = await get_all_file_dict(dataset_id)
    save_file_dict(file_dict, "output_directory")


def to_valid_filename(filename):
    illegal_chars = '<>:"/\\|?*'
    for char in illegal_chars:
        filename = filename.replace(char, "_")  # 将非法字符替换为下划线
    return filename


async def generate_huge_json(
    dataset_id: str, output_dir="output_directory/huge_json"
) -> None:
    file_dict = await get_all_file_dict(dataset_id)
    loguru.logger.info(
        f"Saving {len(file_dict)} files as one file to output directory {output_dir}"
    )
    os.makedirs(output_dir, exist_ok=True)

    file_name = f"{dataset_id}.json"
    file_path = os.path.join(output_dir, file_name)
    with open(file_path, "w", encoding="utf-8") as new_file:
        json.dump(file_dict, new_file, ensure_ascii=False, indent=4)


async def generate_and_save_merged_huge_file(
    dataset_id: str, output_dir="output_directory/huge_file"
) -> None:
    file_dict = await get_all_file_dict(dataset_id)
    loguru.logger.info(
        f"Saving {len(file_dict)} files as one file to output directory {output_dir}"
    )
    clear_directory(output_dir)
    content = ""
    for file_id, value in file_dict.items():
        metadata = value["metadata"]
        content_item = ""
        content_item += (
            "==================== seperator for documents ====================\n"
        )
        content_item += f"### Metadata\n"
        content_item += f"file_id: {file_id}\n"
        content_item += f"title: {metadata.get('title')}\n"
        content_item += f"source: {metadata.get('source')}\n"
        content_item += f"\n\n"
        content_item += f"### Content\n{value['text']}\n\n\n"
        content += content_item
    file_name = f"full_doc_{uuid.uuid4()}.txt"
    file_path = os.path.join(output_dir, file_name)

    with open(file_path, "w") as f:
        f.write(QA_FILE_ID_GENERATE_PROMPT_TMPL.format(documents=content))


def save_file_dict(file_dict: dict, output_dir) -> None:
    loguru.logger.info(
        f"Saving {len(file_dict)} files to output directory {output_dir}"
    )
    clear_directory(output_dir)

    for file_id, value in file_dict.items():
        title = to_valid_filename(value["metadata"]["title"])
        file_name = f"{title}__{file_id}.txt"
        file_path = os.path.join(output_dir, file_name)

        with open(file_path, "w") as f:
            f.write(value["text"])


def clear_directory(dir_path):
    # 检查目录是否存在
    if os.path.exists(dir_path):
        # 遍历目录中的所有内容
        for filename in os.listdir(dir_path):
            file_path = os.path.join(dir_path, filename)
            try:
                # 如果是文件或链接，直接删除
                if os.path.isfile(file_path) or os.path.islink(file_path):
                    os.unlink(file_path)
                # 如果是目录，使用shutil.rmtree来递归删除
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
            except Exception as e:
                print(f"Failed to delete {file_path}. Reason: {e}")
    else:
        # 目录不存在，创建它
        os.makedirs(dir_path)


# async def generate_huge_file_in_one(dataset_id: str):
#     file_dict = await get_all_file_dict(dataset_id)
#     generate_and_save_merged_huge_file(file_dict, "output_directory/huge_file")


# 调用函数


# async def get_dataset_id(robot_id: str):
#     robot = await Robot.get(id=robot_id)
#     return robot.datasets

# )
#     qdrant.
#     rs = await generate_dataset(dataset_id)
#     data = pd.DataFrame(rs)
#     data.to_csv(f"./data/{dataset_id}.csv", index=False)
#     jsonl file
#     {"id": "$uuid4 of file_id", "text": ""}

if __name__ == "__main__":
    import asyncio

    Tortoise.init_models(["mygpt.models"], "models")
    asyncio.run(Tortoise.init(config=TORTOISE_ORM))

    # dataset for robot b42a24f3-381a-47c2-8446-24f00a6975b5
    # asyncio.run(generate_dataset_text("bcddcb88-f5cd-45b0-add8-96672d5235df"))
    # asyncio.run(
    #     generate_and_save_merged_huge_file("bcddcb88-f5cd-45b0-add8-96672d5235df")
    # )
    # asyncio.run(generate_huge_json("bcddcb88-f5cd-45b0-add8-96672d5235df"))
    asyncio.run(generate_huge_json("6df1673e-3483-4206-868a-2f9539ec4abe"))
