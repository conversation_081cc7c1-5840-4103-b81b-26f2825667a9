import json

url_list = json.loads(open("data/his-mobile-web.json").read())
existing_list = json.loads(
    open("output_directory/huge_json/bcddcb88-f5cd-45b0-add8-96672d5235df.json").read()
)

new_json = {}
for item in existing_list:
    v = existing_list[item]
    if v["metadata"]["source"] in url_list:
        new_json[item] = v

with open(
    "output_directory/huge_json/bcddcb88-f5cd-45b0-add8-96672d5235df-filtered.json", "w"
) as f:
    json.dump(new_json, f, ensure_ascii=False, indent=4)
