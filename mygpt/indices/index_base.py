import json
import string


class IndexBase:
    MULTI_LANGUAGE_ANALYSIS = """
{
  "analyzer": {
    "ik_max_word_t2s": {
      "tokenizer": "ik_max_word",
      "char_filter": [
        "char_filter_t2s"
      ]
    },
    "ik_smart_t2s": {
      "tokenizer": "ik_smart",
      "char_filter": [
        "char_filter_t2s"
      ]
    },
    "sudachi_search": {
      "type": "custom",
      "tokenizer": "sudachi_tokenizer_search",
      "filter": [
        "sudachi_filter_search",
        "sudachi_ja_stop"
      ]
    },
    "sudachi_index": {
      "type": "custom",
      "tokenizer": "sudachi_tokenizer_index",
      "filter": [
        "sudachi_filter_index",
        "sudachi_ja_stop"
      ]
    }
  },
  "tokenizer": {
    "sudachi_tokenizer_search": {
      "type": "sudachi_tokenizer",
      "split_mode": "B",
      "discard_punctuation": true
    },
    "sudachi_tokenizer_index": {
      "type": "sudachi_tokenizer",
      "split_mode": "A",
      "discard_punctuation": true
    }
  },
  "char_filter": {
    "char_filter_t2s": {
      "type": "stconvert",
      "keep_both": "false",
      "convert_type": "t2s"
    }
  },
  "filter": {
    "sudachi_filter_search": {
      "type": "sudachi_split",
      "mode": "search"
    },
    "sudachi_filter_index": {
      "type": "sudachi_split",
      "mode": "extended"
    },
    "sudachi_part_of_speech_filter": {
      "type": "sudachi_part_of_speech",
      "stoptags": [
        "助詞",
        "助動詞",
        "補助記号,句点",
        "補助記号,読点"
      ]
    },
    "sudachi_ja_stop_filter": {
      "type": "sudachi_ja_stop",
      "stopwords": [
        "は",
        "です",
        "を",
        "の",
        "に",
        "と",
        "で",
        "が"
      ]
    }
  }
}
    """

    MULTI_LANGUAGE_CONTENTS = """
{
  "properties": {
    "language": {
      "type": "keyword"
    },
    "supported": {
      "type": "boolean"
    },
    "default": {
      "type": "text",
      "fields": {
        "icu": {
          "type": "text",
          "analyzer": "icu_analyzer"
        }
      }
    },
    "en": {
      "type": "text",
      "analyzer": "english"
    },
    "ja": {
      "type": "text",
      "analyzer": "sudachi_index",
      "search_analyzer": "sudachi_search"
    },
    "zh": {
      "type": "text",
      "analyzer": "ik_max_word_t2s",
      "search_analyzer": "ik_smart_t2s"
    }
  }
}
    """

    def __init__(self, *, name, alias, schema: str, search_template: None | str = None):
        self.name = name
        self.alias = alias
        self.schema = schema
        self.__search_template = (
            string.Template(search_template) if search_template else None
        )

    def __encode_data(self, data: dict) -> dict:
        json_data = {k: json.dumps(v, default=str) for k, v in data.items()}
        return json_data

    def search(self, **data) -> str:
        if not hasattr(self.__search_template, "substitute"):
            raise NotImplementedError

        encoded_data = self.__encode_data(data)
        return self.__search_template.substitute(encoded_data)

    def query(self, **data):
        raise NotImplementedError