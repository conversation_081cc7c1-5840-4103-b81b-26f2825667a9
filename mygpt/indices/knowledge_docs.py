import string
import json
from typing import Union, List

from mygpt.indices.index_base import IndexBase

KNOWLEDGE_DOCS_MULTI_LANGUAGE_ANALYSIS = """
{
  "analysis": {
    "analyzer": {
      "icu_multilingual": {
        "type": "custom",
        "tokenizer": "icu_tokenizer",
        "filter": [
          "icu_folding",
          "icu_normalizer",
          "icu_collation"
        ]
      }
    },
    "filter": {
      "icu_normalizer": {
        "type": "icu_normalizer",
        "name": "nfkc_cf"
      },
      "icu_collation": {
        "type": "icu_collation",
        "language": "root",
        "strength": "primary"
      }
    }
  }
}
"""


class KnowledgeDocsIndex(IndexBase):
    schema = string.Template(
        """
{
  "aliases": {
    "knowledge_docs_index": {}
  },
  "settings": {
    "analysis": ${knowledge_docs_multi_language_analysis}
  },
  "mappings": {
    "dynamic": "strict",
    "properties": {
      "doc_id": {
        "type": "keyword" 
      },
      "project_name": {
        "type": "text",
        "analyzer": "icu_multilingual",
        "fields": {
          "keyword": { "type": "keyword" }
        }
      },
      "project_id": {
        "type": "keyword"
      },
      "title": {
        "type": "text",
        "analyzer": "icu_multilingual",
        "fields": {
          "keyword": { "type": "keyword" }
        }
      },
      "content": {
        "type": "text", 
        "analyzer": "icu_multilingual" 
      },
      "summary": {
        "type": "text"
      },
      "language": {
        "type": "keyword" 
      },
      "created_at": {
        "type": "date" 
      },
      "updated_at": {
        "type": "date"
      },
      "tags": {
        "type": "keyword" 
      },
      "topics": {
        "type": "keyword"
      }
    }
  }
}
"""
    ).substitute(
        knowledge_docs_multi_language_analysis=KNOWLEDGE_DOCS_MULTI_LANGUAGE_ANALYSIS,
    )

    def __init__(self):
        super().__init__(name="knowledge_docs_index", alias="knowledge_docs_index", schema=self.schema)

    def query(
        self,
        is_terms: bool = False,
        term: str = None,
        value: Union[List[str], str] = None,
        source: list = None,
        **kwargs
    ) -> str:
        if is_terms:
            term_key = "terms"
            if not isinstance(value, list):
                raise ValueError("value must be a list when is_terms is True")
            value = json.dumps(value, default=str, ensure_ascii=False)
        else:
            term_key = "term"
            if not isinstance(value, str):
                raise ValueError("value must be a string when is_terms is False")
            value = f'"{value}"'
        if not term:
            raise ValueError("term is required")
        if not source:
            source = []
        if not isinstance(source, list):
            raise ValueError("source must be a list")
        source_str = json.dumps(source, default=str, ensure_ascii=False)
        size = kwargs.get("size", 100)
        query_template = string.Template("""{
    "size": ${size},
    "query": {
        "bool":{
            "filter": {
                "${term_key}": {
                    "${term}": ${value}
                }                
            }
        }
    },
    "_source": ${source}
}""")
        query = query_template.substitute(
            term_key=term_key,
            term=term,
            value=value,
            size=kwargs.get("size", 10),
            source=source_str,
        )
        return query


KNOWLEDGE_DOCS_INDEX = KnowledgeDocsIndex()
