import string

from mygpt.indices.index_base import IndexBase

FAQ_QUESTIONS_INDEX = IndexBase(
    name="faq_questions",
    alias="faq_questions_index",
    schema=string.Template(
        """
{
  "aliases": {
    "faq_questions_index": {}
  },
  "settings": {
    "analysis": ${multi_language_analysis}
  },
  "mappings": {
    "dynamic": "strict",
    "properties": {
      "contents": ${multi_language_contents},
      "dataset_id": {
        "type": "keyword"
      }
    }
  }
}
    """
    ).substitute(
        multi_language_analysis=IndexBase.MULTI_LANGUAGE_ANALYSIS,
        multi_language_contents=IndexBase.MULTI_LANGUAGE_CONTENTS,
    ),
    search_template="""
{
  "min_score": ${min_score},
  "size": ${size},
  "query": {
    "bool": {
      "filter": [
        {
          "terms": {
            "dataset_id": ${dataset_ids}
          }
        }
      ],
      "must": [
        {
          "multi_match": {
            "query": ${query},
            "type": "best_fields",
            "fields": [
              "contents.ja",
              "contents.zh",
              "contents.en",
              "contents.default^0.2"
            ]
          }
        }
      ]
    }
  },
  "highlight": {
    "fields": {
      "contents.ja": {},
      "contents.zh": {},
      "contents.en": {},
      "contents.default": {}
    }
  },
  "_source": [
    "contents"
  ],
  "explain": false
}
    """,
)
