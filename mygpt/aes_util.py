from Crypto.Cipher import AES
import base64
import os


class AESCipher:
    def __init__(self, key):
        self.key = key
        self.block_size = AES.block_size

    def encrypt(self, raw):
        raw = self._pad(raw)
        iv = os.urandom(self.block_size)
        cipher = AES.new(self.key, AES.MODE_CBC, iv)
        return base64.b64encode(iv + cipher.encrypt(raw))

    def decrypt(self, enc):
        enc = base64.b64decode(enc)
        iv = enc[: self.block_size]
        cipher = AES.new(self.key, AES.MODE_CBC, iv)
        return self._unpad(cipher.decrypt(enc[self.block_size :]))

    def _pad(self, s):
        pad_value = self.block_size - len(s) % self.block_size
        return s + pad_value.to_bytes(1, "big") * pad_value

    def _unpad(self, s):
        return s[: -ord(s[len(s) - 1 :])]


# 使用示例：
# key = b'mysecretpassword'
# cipher = AESCipher(key)
# text = 'Hello, world!'
# encrypted_text = cipher.encrypt(text.encode('utf-8')).decode('utf-8')
# decrypted_text = cipher.decrypt(encrypted_text).decode('utf-8')
# print('Original text:', text)
# print('Encrypted text:', encrypted_text)
# print('Decrypted text:', decrypted_text)
