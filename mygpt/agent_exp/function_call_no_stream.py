import os
import time

# import openai
from dotenv import load_dotenv, find_dotenv

_ = load_dotenv(find_dotenv())  # read local .env file
# openai.api_key = os.environ["OPENAI_API_KEY"]

import json


def get_current_weather(location, unit="fahrenheit"):
    """Get the current weather in a given location"""
    weather_info = {
        "location": location,
        "temperature": "72",
        "unit": unit,
        "forecast": ["sunny", "windy"],
    }
    return json.dumps(weather_info)


# define a function
functions = [
    {
        "type": "function",
        "function": {
            "name": "get_current_weather",
            "description": "Get the current weather in a given location",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The city and state, e.g. San Francisco, CA",
                    },
                    "unit": {"type": "string", "enum": ["celsius", "fahrenheit"]},
                },
                "required": ["location"],
            },
        },
    }
]

messages = [
    {
        "role": "user",
        "content": "What's the weather like in Boston? also tell me a joke",
    }
]


def call(type="openai"):
    from openai import OpenAI

    client = OpenAI(
        # This is the default and can be omitted
        api_key=(
            os.environ.get("OPENAI_API_KEY")
            if type == "openai"
            else os.environ.get("CLAUDE_API_KEY")
        )
    )

    response = client.chat.completions.create(
        messages=messages,
        model="gpt-4o" if type == "openai" else "claude-3-5-sonnet-20240620",
        # functions=functions,
        tools=functions,
        # stream=True,
    )

    # response_message = response["choices"][0]["message"]
    # response_message = response["choices"][0]["message"]
    # print(
    #     response_message["content"]
    # )  # there will be no content at all, but i expected a joke
    # print(response_message["function_call"])  # correct function call
    print(response.json(indent=2))


if __name__ == "__main__":
    t0 = time.monotonic()
    print(call("openai"))
    print(f"Time taken: {time.monotonic() - t0:.2f} seconds")
    print("========================")
    t0 = time.monotonic()
    print(call("claude"))
    print(f"Time taken: {time.monotonic() - t0:.2f} seconds")


# example output
# {
#   "id": "chatcmpl-9yBFq2DcktXrn5O3a3zGtxODyO86J",
#   "choices": [
#     {
#       "finish_reason": "tool_calls",
#       "index": 0,
#       "logprobs": null,
#       "message": {
#         "content": null,
#         "role": "assistant",
#         "function_call": null,
#         "tool_calls": [
#           {
#             "id": "call_fKR16aq73m1kTm67xSUhGDSM",
#             "function": {
#               "arguments": "{\"location\":\"Boston, MA\"}",
#               "name": "get_current_weather"
#             },
#             "type": "function"
#           }
#         ],
#         "refusal": null
#       }
#     }
#   ],
#   "created": 1724129970,
#   "model": "gpt-4o-2024-08-06",
#   "object": "chat.completion",
#   "system_fingerprint": "fp_2a322c9ffc",
#   "usage": {
#     "completion_tokens": 17,
#     "prompt_tokens": 84,
#     "total_tokens": 101
#   }
# }
# None
# Time taken: 2.86 seconds
# ========================
# {
#   "id": "chatcmpl-msg_01DVon7pTBz367juFWWQ5EYD",
#   "choices": [
#     {
#       "finish_reason": "tool_calls",
#       "index": 0,
#       "logprobs": null,
#       "message": {
#         "content": "Certainly! I'll get the weather information for Boston and then share a joke with you.\n\nFirst, let's check the weather in Boston:",
#         "role": "assistant",
#         "function_call": null,
#         "tool_calls": [
#           {
#             "id": "toolu_01Yb5b9C6yBPro7x73LWrXQp",
#             "function": {
#               "arguments": "{\"location\":\"Boston, MA\"}",
#               "name": "get_current_weather"
#             },
#             "type": "function"
#           }
#         ]
#       }
#     }
#   ],
#   "created": 1724129976,
#   "model": "claude-3-5-sonnet-20240620",
#   "object": "chat.completion",
#   "system_fingerprint": null,
#   "usage": {
#     "completion_tokens": 87,
#     "prompt_tokens": 410,
#     "total_tokens": 497
#   }
# }
# None
# Time taken: 5.66 seconds
