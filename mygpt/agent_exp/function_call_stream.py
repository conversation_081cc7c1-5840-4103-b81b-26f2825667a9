import os
import time

# import openai
from dotenv import load_dotenv, find_dotenv

_ = load_dotenv(find_dotenv())  # read local .env file
# openai.api_key = os.environ["OPENAI_API_KEY"]

import json


def get_current_weather(location, unit="fahrenheit"):
    """Get the current weather in a given location"""
    weather_info = {
        "location": location,
        "temperature": "72",
        "unit": unit,
        "forecast": ["sunny", "windy"],
    }
    return json.dumps(weather_info)


# define a function
functions = [
    {
        "type": "function",
        "function": {
            "name": "get_current_weather",
            "description": "Get the current weather in a given location",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The city and state, e.g. San Francisco, CA",
                    },
                    "unit": {"type": "string", "enum": ["celsius", "fahrenheit"]},
                },
                "required": ["location"],
            },
        },
    }
]

messages = [
    {
        "role": "user",
        "content": "What's the weather like in Boston? also tell me a joke",
    }
]


def call(type="openai"):
    from openai import OpenAI

    client = OpenAI(
        # This is the default and can be omitted
        api_key=(
            os.environ.get("OPENAI_API_KEY")
            if type == "openai"
            else os.environ.get("CLAUDE_API_KEY")
        )
    )

    response = client.chat.completions.create(
        messages=messages,
        model="gpt-4o" if type == "openai" else "claude-3-5-sonnet-20240620",
        # functions=functions,
        tools=functions,
        stream=True,
    )

    # response_message = response["choices"][0]["message"]
    # response_message = response["choices"][0]["message"]
    # print(
    #     response_message["content"]
    # )  # there will be no content at all, but i expected a joke
    # print(response_message["function_call"])  # correct function call
    for res in response:
        print(res)
    #     for choice in ["choices"]:
    #         for tool in choice["message"]["tool_calls"]:
    #             if tool["function"]["name"] == "get_current_weather":
    #                 return get_current_weather(**json.loads(tool["function"]["arguments"]))


if __name__ == "__main__":
    # t0 = time.monotonic()
    # print(call("openai"))
    # print(f"Time taken: {time.monotonic() - t0:.2f} seconds")
    # print("========================")
    t0 = time.monotonic()
    print(call("claude"))
    print(f"Time taken: {time.monotonic() - t0:.2f} seconds")


# example output
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content='', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content='', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content='Certainly', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content='! I', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content="'d be happy to check", function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=' the weather in', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=' Boston for', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=' you and tell you a', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=' joke as', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=' well.', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=' Let', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content="'s", function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=' start with the weather.', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content='\n\nTo', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=' get the current weather in', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=" Boston, I'll", function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=' nee', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content='d to use', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=' the available', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=' weather tool', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=". Here's", function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=' how', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=' we', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content="'ll", function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=' do that:', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content='', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, role='assistant', tool_calls=[ChoiceDeltaToolCall(index=None, id='toolu_01Np2NeBa77QRKZweUk5aHD4', function=ChoiceDeltaToolCallFunction(arguments='', name='get_current_weather'), type='function')]), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, role='assistant', tool_calls=[ChoiceDeltaToolCall(index=None, id=None, function=ChoiceDeltaToolCallFunction(arguments='', name=None), type=None)]), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, role='assistant', tool_calls=[ChoiceDeltaToolCall(index=None, id=None, function=ChoiceDeltaToolCallFunction(arguments='{"loc', name=None), type=None)]), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, role='assistant', tool_calls=[ChoiceDeltaToolCall(index=None, id=None, function=ChoiceDeltaToolCallFunction(arguments='ati', name=None), type=None)]), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, role='assistant', tool_calls=[ChoiceDeltaToolCall(index=None, id=None, function=ChoiceDeltaToolCallFunction(arguments='on": "Bo', name=None), type=None)]), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, role='assistant', tool_calls=[ChoiceDeltaToolCall(index=None, id=None, function=ChoiceDeltaToolCallFunction(arguments='ston, M', name=None), type=None)]), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, role='assistant', tool_calls=[ChoiceDeltaToolCall(index=None, id=None, function=ChoiceDeltaToolCallFunction(arguments='A"}', name=None), type=None)]), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content='', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content='', function_call=None, role='assistant', tool_calls=None), finish_reason='tool_calls', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# ChatCompletionChunk(id='chatcmpl-msg_01M4JvYiRqWVEba79bC6fE76', choices=[Choice(delta=ChoiceDelta(content='', function_call=None, role='assistant', tool_calls=None), finish_reason='', index=0, logprobs=None)], created=1724133711, model='claude-3-5-sonnet-20240620', object='chat.completion.chunk', system_fingerprint=None)
# None
# Time taken: 5.39 seconds
#
