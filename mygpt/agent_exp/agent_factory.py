import os
import re
from typing import <PERSON>ple

import yaml
import Agent<PERSON>
from mygpt.agent_exp.core.chat_model_base import <PERSON>t<PERSON>penA<PERSON>
from mygpt.agent_exp.core.prompt_manager import PromptManager
from mygpt.agent_exp.core.react import ReActAgent
from mygpt.agent_exp.core.agently_wapper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mygpt.agent_exp.tools.Tools import ToolManager
from mygpt.agent_exp.utils.callback_handlers import <PERSON>lyCallBackHandler
from mygpt.enums import OpenAIModel, AgentType
from mygpt.models import Robot, Session
from mygpt.schemata import QuestionIn


def parse_user_prompt(user_prompt: str) -> Tuple[str, str]:
    pattern = r"# Role\s*(.*?)\s*# General\s*(.*)"
    matches = re.search(pattern, user_prompt, re.DOTALL)
    if matches:
        role = matches.group(1)
        general = matches.group(2)
        return role, general
    else:
        return "", user_prompt


class AgentFactory:
    @staticmethod
    def __create_agently_agent(robot: Robot):
        # 读取robot中的prompt信息
        ori_prompt = robot.prompt
        if not ori_prompt:
            raise Exception(f"robot {robot.id} has no prompt.")
        role, general = parse_user_prompt(ori_prompt)
        agently_agent = (
            Agently.create_agent()
            .set_settings("current_model", "OAIClient")
            .set_settings("model.OAIClient.url", os.environ["OPENAI_BASE_URL"])
            .set_settings(
                "model.OAIClient.auth", {"api_key": os.environ["OPENAI_API_KEY"]}
            )
            .set_settings("model.OAIClient.options", {"model": "gpt-4o"})
        )
        # agently_agent = AgentFactory.open_ai_agent_factory.create_agent()
        # 填充prompt
        if role:
            agently_agent.role(role)
        agently_agent.general(general)
        # 注册工具
        tools = ToolManager.get_agently_tools(
            question_in=QuestionIn(question="test", session_id="asdf")
        )
        for tool in tools:
            agently_agent.register_tool(**tool)
        return agently_agent

    @staticmethod
    def create_agent(
        robot: Robot,
        question_in: QuestionIn,
        chat_history=None,
        max_thought_steps=4,
        agent_type=AgentType.AGENTLY,
        callback=None,
        verbose=False,
    ):
        if agent_type == AgentType.AGENTLY:
            # llm = ChatOpenAI(model=OpenAIModel.GPT_4_OMNI)
            # llm.validate_environment()
            if not callback:
                callback = AgentlyCallBackHandler(
                    question_in=question_in, robot=robot, chat_history=chat_history
                )
            # 创建基于Agently的Agent实例
            agently_agent = AgentFactory.__create_agently_agent(robot)
            agent = AgentlyWapper(
                agently_agent,
                robot=robot,
                max_thought_steps=max_thought_steps,
                verbose=verbose,
                callback=callback,
            )
            return agent
        elif agent_type == AgentType.FUNCTIONCALL:
            pass

        # 创建Agent对象需要的参数: llm, 工具, 顶一个agent的prompt, 最大思考轮数
        llm = ChatOpenAI(model=OpenAIModel.GPT_4_OMNI)
        llm.validate_environment()  # 我们现在pydantic库的版本太低, langchain-openai原始代码不能直接用, 实例化之后必须手动执行以下这个方法, 进行一些初始化
        # 定义工具, 需要有自己的工具, 工具看实际需求, 需要就如Rag, 目前可以先写一些简单的工具跑通流程.
        if not PromptManager.main_prompt:
            PromptManager.init_prompt()
            if (
                not PromptManager.main_prompt
                or not PromptManager.meta_prompt
                or not PromptManager.main_agently_prompt
            ):
                raise Exception(
                    "PromptManager init failed. please check the prompt file and config file."
                )
        tools = ToolManager.get_tools(
            llm,
            question_in=question_in.question,
            chat_history=chat_history,
            verbose=verbose,
        )
        agent = ReActAgent(
            llm=llm,
            tools=tools,
            robot=robot,
            question_in=question_in,
            max_thought_steps=max_thought_steps,
            verbose=verbose,
        )
        return agent
