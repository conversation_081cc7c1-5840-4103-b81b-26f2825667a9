import asyncio
from loguru import logger as logging
from typing import Optional, Union, Any, Dict, List
from uuid import UUID

from langchain_core.callbacks import BaseCallbackHandler
from langchain.callbacks import AsyncIteratorCallbackHandler
from langchain_core.outputs import GenerationChunk, ChatGenerationChunk, LLMResult

from mygpt.agent_exp.core.storage import agent_reply_postprocess_handler
from mygpt.agent_exp.utils.print_utils import *
from mygpt.enums import StreamingMessageDataFormat, StreamingMessageDataType
from mygpt.models import Robot
from mygpt.schemata import StreamOut, QuestionIn


class ColoredPrintHandler(BaseCallbackHandler):
    def __init__(self, color: str):
        BaseCallbackHandler.__init__(self)
        self._color = color

    def on_llm_new_token(
        self,
        token: str,
        *,
        chunk: Optional[Union[GenerationChunk, ChatGenerationChunk]] = None,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        **kwargs: Any,
    ) -> Any:
        color_print(token, self._color, end="")
        return token

    def on_llm_end(self, response: LLMResult, **kwargs: Any) -> Any:
        color_print("\n", self._color, end="")
        return response

    def on_tool_end(self, output: Any, **kwargs: Any) -> Any:
        """Run when tool ends running."""
        print()
        color_print("\n[Tool Return]", RETURN_COLOR)
        color_print(output, OBSERVATION_COLOR)
        return output

    @staticmethod
    def on_thought_start(index: int, **kwargs: Any) -> Any:
        """自定义事件，非继承自BaseCallbackHandler"""
        color_print(f"\n[Thought: {index}]", ROUND_COLOR)
        return index


class AgentStreamCallbackHanlder(AsyncIteratorCallbackHandler):
    """
    langchain回调处理器 - 处理流式输出
    """

    def __init__(
        self,
        message_id=None,
        tokens: int = 0,
        stream_format=StreamingMessageDataFormat.JSON_AST,
        origin_question: str = "",
    ):
        super().__init__()
        self.message_id = message_id
        self.tokens = tokens
        self.format = stream_format
        self.pending_texts = []
        self.origin_question = origin_question

    def create_stream_out(
        self, message_type: StreamingMessageDataType, content: str | List | dict
    ):
        return StreamOut(
            message_id=self.message_id,
            message_type=message_type,
            content=content,
            use_faq=False,
            tokens=self.tokens,
        )

    async def on_llm_new_token(self, token: str, **kwargs: Any) -> None:
        # 处理流式返回内容
        if token is None or token == "":
            return
        if self.format == StreamingMessageDataFormat.JSON_AST:
            rs = self.create_stream_out(
                message_type=StreamingMessageDataType.PLAIN_TEXT,
                content=token,
            ).json(ensure_ascii=False)
            await super().on_llm_new_token(rs + "\n", **kwargs)
        else:
            await super().on_llm_new_token(token, **kwargs)

    async def on_error(
        self, error: Exception | KeyboardInterrupt, **kwargs: Any
    ) -> None:
        # 大模型接口流式返回异常
        if self.format == StreamingMessageDataFormat.JSON_AST:
            rs = self.create_stream_out(
                message_type=StreamingMessageDataType.ERROR,
                content=str(error),
            ).json(ensure_ascii=False)
            await super().on_llm_new_token(rs + "\n", **kwargs)
            return
        await super().on_llm_new_token(str(error) + "\n", **kwargs)

    async def on_llm_end(self, response, **kwargs):
        # 大模型接口流式返回结束
        answer = response.generations[0][0].message.content
        llm_can_answer = True
        if answer == "" or answer == " " or answer is None:
            logging.error(
                f"empty answer [{answer}], message: {response.generations[0][0].to_json()}"
            )
        if self.format == StreamingMessageDataFormat.JSON_AST:
            llm_can_answer_rs = self.create_stream_out(
                message_type=StreamingMessageDataType.LLM_CAN_ANSWER,
                content=str(llm_can_answer).lower(),
            ).json(ensure_ascii=False)
            await super().on_llm_new_token(llm_can_answer_rs + "\n", **kwargs)


class AgentThoughtLoggingHandler(BaseCallbackHandler):
    """
    用于记录思考过程的回调处理器, 主要用于分析思考过程和debug工作
    """

    def __init__(self, user_id, question_in: QuestionIn, robot: Robot):
        BaseCallbackHandler.__init__(self)
        self.user_id = user_id
        self.question_in = question_in
        self.robot = robot

    def on_llm_new_token(
        self,
        token: str,
        *,
        chunk: Optional[Union[GenerationChunk, ChatGenerationChunk]] = None,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        **kwargs: Any,
    ) -> Any:
        color_print(token, self._color, end="")
        return token

    def on_llm_end(self, response: LLMResult, **kwargs: Any) -> Any:
        color_print("\n", self._color, end="")
        return response

    def on_tool_end(self, output: Any, **kwargs: Any) -> Any:
        """Run when tool ends running."""
        print()
        color_print("\n[Tool Return]", RETURN_COLOR)
        color_print(output, OBSERVATION_COLOR)
        return output

    @staticmethod
    def on_thought_start(index: int, **kwargs: Any) -> Any:
        """自定义事件，非继承自BaseCallbackHandler"""
        color_print(f"\n[Thought: {index}]", ROUND_COLOR)
        return index


class AgentlyCallBackHandler:
    def __init__(
        self,
        question_in: QuestionIn,
        robot: Robot,
        chat_history,
        is_faq=False,
        tokens=0,
        format=None,
        reference_list=None,
        recommend_list=None,
        resources=None,
        event=None,
        origin_question: str = "",
        user_intent=None,
        faq_child_node_list=None,
    ):
        self.question_in = question_in
        self.robot = robot
        self.message_id = question_in.session_id
        self.chat_history = chat_history
        self.is_faq = is_faq
        self.tokens = tokens
        self.format = format
        self.pending_texts = []
        self.reference_list = reference_list
        self.recommend_list = recommend_list
        self.resources = resources or {}
        self.event = event
        self.origin_question = origin_question
        self.cache_links = []
        self.user_intent = user_intent
        self.faq_child_node_list = faq_child_node_list
        self.queue = asyncio.Queue()

    def create_stream_out(
        self, message_type: StreamingMessageDataType, content: str | List | dict
    ):
        return StreamOut(
            message_id=self.message_id,
            message_type=message_type,
            content=content,
            use_faq=self.is_faq,
            tokens=self.tokens,
        )

    async def on_delta(self, delta: str, **kwargs):

        if delta is None or delta == "":
            return
        if self.format == StreamingMessageDataFormat.JSON_AST:
            rs = self.create_stream_out(
                message_type=StreamingMessageDataType.PLAIN_TEXT, content=delta
            ).json(ensure_ascii=False)
            return rs
        else:
            return delta

    async def on_done(self, done):
        if self.format == StreamingMessageDataFormat.JSON_AST:
            rs = StreamOut(
                message_id=self.message_id,
                message_type=StreamingMessageDataType.DONE,
                content=done,
                use_faq=self.is_faq,
                tokens=self.tokens,
            ).json(ensure_ascii=False)
            await self.event.set()
            self.pending_texts.append(rs)
        else:
            await self.event.set()
            self.pending_texts.append(done)

    async def on_finally(self, *args, **kwargs):
        reply = args[0].get("reply")
        # 处理数据写库等操作
        await agent_reply_postprocess_handler(
            self.question_in, reply, self.chat_history
        )

    async def on_realtime(self, data):
        pass
