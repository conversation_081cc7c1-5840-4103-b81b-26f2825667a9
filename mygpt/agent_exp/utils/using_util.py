from typing import List, Union, AsyncIterator

from langchain_community.chat_message_histories.in_memory import ChatMessageHistory
from mygpt.agent_exp.core.mod.langchain_core_messages_ai import AIMessage
from langchain_core.messages.base import BaseMessage
from mygpt.agent_exp.core.mod.langchain_core_messages_human import HumanMessage
from mygpt.enums import StreamingMessageDataFormat, StreamingMessageDataType
from mygpt.schemata import StreamOut


def covert_mod_messages(ori_messages) -> ChatMessageHistory:
    """
    由于Agent模块中需要使用langchain-openai库, 但是目前库冲突比较严重, 只能进行手动把用到的langchain-openai库的东西拷贝过来并修改
    依赖的langchain-core的版本也需要比较高, 目前将langchain-core的高版本代码进行了融合.
    总结来说, 使用当前的Agent模块需要将现在版本的BaseMessage换成Agent模块中的使用的BaseMessage
    """
    message_history = ChatMessageHistory()
    for ori_message in ori_messages:
        if ori_message.type == "human":
            message = HumanMessage(ori_message.content)
        elif ori_message.type == "ai":
            message = AIMessage(ori_message.content)
        else:
            raise ValueError(f"message type ")
        message_history.add_message(message)
    return message_history


def convert_base_messages_to_openai_format(
    messages: Union[ChatMessageHistory, List[BaseMessage]]
):
    if isinstance(messages, ChatMessageHistory):
        messages = messages.messages
    elif messages and isinstance(messages[0], dict):
        # 已经是openai格式的消息
        return messages
    openai_messages = []
    for message in messages:
        if message.type == "ai":
            role = "assistant"
        elif message.type == "human":
            role = "user"
        elif message.type == "system":
            role = "system"
        else:
            raise ValueError("Unsupported message type")
        openai_message = {"role": role, "content": message.content}
        openai_messages.append(openai_message)
    return openai_messages


def format_chat_chunk_response(
    token: Union[str, None],
    message_id,
    message_type: str = StreamingMessageDataType.PLAIN_TEXT,
    format=StreamingMessageDataFormat.JSON_AST,
):
    if format == StreamingMessageDataFormat.JSON_AST:
        chunk = (
            StreamOut(
                message_id=message_id,
                message_type=message_type,
                content=token,
                use_faq=False,
                tokens=None,
            ).json(ensure_ascii=False)
            + "\n"
        )
        return chunk
    else:
        token
    return token


async def prepare_answer2chatchunk(reply: str, message_id) -> AsyncIterator:
    """
    将大模型的字符串回复转换成多个chunk, 返回一个异步迭代器
    """
    for char in reply:
        chunk = format_chat_chunk_response(
            char,
            message_id,
            message_type=StreamingMessageDataFormat.PLAIN_TEXT,
        )
        yield chunk
    chunk = format_chat_chunk_response(
        None, message_id, message_type=StreamingMessageDataType.REFERENCE_LIST
    )
    yield chunk
    chunk = format_chat_chunk_response(
        None, message_id, message_type=StreamingMessageDataType.RECOMMEND_LIST
    )
    yield chunk
    chunk = format_chat_chunk_response(
        None, message_id, message_type=StreamingMessageDataType.FAQ_CHILD_NODE
    )
    yield chunk
    chunk = format_chat_chunk_response(
        "false", message_id, message_type=StreamingMessageDataType.SHOW_REFERENCE_BUTTON
    )
    yield chunk
    chunk = format_chat_chunk_response(
        "true", message_id, message_type=StreamingMessageDataType.LLM_CAN_ANSWER
    )
    yield chunk
