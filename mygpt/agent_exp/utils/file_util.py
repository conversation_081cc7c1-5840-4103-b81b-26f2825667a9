import os
import orjson
from pathlib import Path
from typing import Any, Dict, List, Union, Optional, Iterator


class FileUtil:
    @staticmethod
    def read_json(file_path: Union[str, Path]) -> Any:
        """
        读取json文件
        """
        if isinstance(file_path, Path):
            file_path = str(file_path)
        try:
            with open(file_path, "r", encoding="utf-8") as fp:
                data = orjson.loads(fp.read())
            return data
        except FileNotFoundError as e:
            raise FileNotFoundError(f"文件不存在: {file_path}") from e
        except Exception as e:
            raise Exception(f"读取文件失败: {file_path}") from e

    @staticmethod
    def write_json(
        data: Union[Dict, List],
        file_path: str,
        indent: Optional[int] = None,
        sort_keys: bool = False,
        overwrite: bool = False,
    ) -> None:
        """
        将数据写入 JSON 文件。
        :param data: 要写入的数据
        :param file_path: JSON 文件的路径
        :param indent: 指定缩进层级，可以为 None（不缩进）或整数（表示缩进层级）
        :param sort_keys: 指定是否进行排序和格式化输出
        :param overwrite: 指定是否覆盖已有文件
        :return: 写入成功返回 True，否则返回 False
        """
        if not overwrite and os.path.exists(file_path):
            raise FileExistsError(f"文件已经存在: {file_path}")
        options = 0
        if indent is not None:
            options |= orjson.OPT_INDENT_2
        if sort_keys:
            options |= orjson.OPT_SORT_KEYS
        try:
            json_str = orjson.dumps(data, option=options).decode("utf-8")
            with open(file_path, "w", encoding="utf-8") as fp:
                fp.write(json_str)
        except Exception as e:
            raise Exception(f"写入文件失败: {file_path}") from e

    @staticmethod
    def read_jsonl(
        file_path: str, use_iterator: bool = False
    ) -> Union[List[Any], Iterator[Any]]:
        """
        读取 jsonl 文件
        """

        def json_iterator(file_path_: str) -> Iterator[Any]:
            try:
                with open(file_path_, "r", encoding="utf-8") as fp_:
                    for line_ in fp_:
                        yield orjson.loads(line_)
            except FileNotFoundError as e:
                raise FileNotFoundError(f"文件不存在: {file_path_}") from e
            except Exception as e:
                raise Exception(f"读取文件失败: {file_path_}") from e

        if use_iterator:
            return json_iterator(file_path)
        else:
            data = []
            try:
                with open(file_path, "r", encoding="utf-8") as fp:
                    for line in fp:
                        data.append(orjson.loads(line))
                return data
            except FileNotFoundError as e:
                raise FileNotFoundError(f"文件不存在: {file_path}") from e
            except Exception as e:
                raise Exception(f"读取文件失败: {file_path}") from e

    @staticmethod
    def write_jsonl(
        data: List[Dict], file_path: str, overwrite: bool = False, mode="a"
    ) -> None:
        """
        将数据写入 jsonl 文件
        :param data: 要写入的数据列表
        :param file_path: JSONL 文件的路径
        :param overwrite: 指定是否覆盖已有文件
        :param mode: 写入模式，默认为追加模式 ("a")，也可以指定为覆盖模式 ("w")
        :return: 写入成功返回 True，否则返回 False
        """
        if not overwrite and os.path.exists(file_path):
            raise FileExistsError(f"文件已经存在: {file_path}")
        try:
            with open(file_path, mode, encoding="utf-8") as fp:
                for row in data:
                    fp.write(orjson.dumps(row).decode("utf-8") + "\n")
        except Exception as e:
            raise Exception(f"写入文件失败: {file_path}") from e

    @staticmethod
    def read_text(file_path: str, encoding="utf-8") -> str:
        """
        读取文本文件
        """
        try:
            with open(file_path, "r", encoding=encoding) as fp:
                content = fp.read()
            return content
        except FileNotFoundError as e:
            raise FileNotFoundError(f"文件不存在: {file_path}") from e
        except Exception as e:
            raise Exception(f"读取文件失败: {file_path}") from e

    @staticmethod
    def write_text(text: str, file_path: str, overwrite: bool = False) -> None:
        """
        将文本写入文件
        """
        if not overwrite and os.path.exists(file_path):
            raise FileExistsError(f"文件已经存在: {file_path}")
        try:
            with open(file_path, "w", encoding="utf-8") as fp:
                fp.write(text)
        except Exception as e:
            raise Exception(f"写入文件失败: {file_path}") from e
