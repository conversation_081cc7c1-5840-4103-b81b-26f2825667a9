info信息
```
{
    "name": {
        "value": none,
        "customer_explation": [],
        "data_type": "String",
        "optional": false
    },
    "phone": {
        "value": none,
        "customer_explation": [],
        "data_type": "String",
        "optional": flase
    },
    "is_alcohol_residue": {
        "value": none,
        "customer_explation": [],
        "data_type": "Boolean",
        "optional": false
    }
}
```

function_call信息
```
[
    {
        "function_name": "save_alcohol_detection_result",
        "description": "酒精检测信息收集完成后, 将最终结果写入数据库的方法",
        "parameters": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string",
                    "description": "用户的名字"
                },
                "phone": {
                    "type": "string",
                    "description": "用户的手机号"
                },
                "is_alcohol_residue": {
                    "type": "bool",
                    "description": "用户身上是否有酒精残留"
                }
            },
            "required": ["name", "phone", "is_alcohol_residue"]
        }
    }
]
```