import os
import json
from openai import OpenAI
from dotenv import load_dotenv, find_dotenv

_ = load_dotenv(find_dotenv(filename=".test.env"))
from mygpt.agent_exp.utils.file_util import FileUtil


META_PROMPT = FileUtil.read_text("./meta_prompt_v0.2.txt").strip()


def gen_prompt_by_meta_prompt(task_or_prompt):
    client = OpenAI()
    completion = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {
                "role": "system",
                "content": META_PROMPT,
            },
            {
                "role": "user",
                "content": "Task, Goal, or Current Prompt:\n" + task_or_prompt,
            },
        ],
    )

    return completion.choices[0].message.content


if __name__ == "__main__":
    question = "我想希望有一个能帮我收集用户满意度的机器人, 我需要手机用户的姓名, 联系方式, 对餐厅服务的满意度, 以及餐品的满意度. 请生成一个中文的信息收集类的prompt"
    prompt = gen_prompt_by_meta_prompt(question)
    print(prompt)
