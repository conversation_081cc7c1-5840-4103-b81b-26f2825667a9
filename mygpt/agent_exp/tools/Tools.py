from langchain.tools import StructuredTool
from .FinishTool import finish
from .InterfaceCallTool import InterfaceCallToolEngine
from .RAGTool import ask_document
from .InfoUpdateTool import info_update


document_qa_tool = StructuredTool.from_function(
    func=ask_document,
    name="AskDocument",
    description="检索知识库工具, 调用后会返回执行是否成功, 以及执行结果标记, 如果成功, 请将执行结果标记给作为FINISHI工具的参数.",
)

info_update_tool = StructuredTool.from_function(
    func=info_update,
    name="InfoUpdateTool",
    description="更新info信息工具, 使用时请完成传递info中的槽位信息, 不要进行删减",
)


finish_placeholder = StructuredTool.from_function(
    func=finish, name="FINISH", description="结束任务，将最终答案返回"
)

# 子Agent工具需要以对象的形式进行使用, 所以需要实例化, 所以需要单独管理, 每次获取工具的时候需要额外的实例化步骤
sub_agents_tool = [
    # 外部接口调用工具
    InterfaceCallToolEngine
]

from mygpt.agent_exp.tools.AgentlyTools import AgentlyTool


class ToolManager:
    static_tools = [
        document_qa_tool,
        info_update_tool,
        finish_placeholder,
    ]

    @staticmethod
    def get_tools(llm, question_in, chat_history, verbose):
        sub_agents_tools = []
        for tool in sub_agents_tool:
            sub_agents_tools.append(
                tool(
                    llm=llm,
                    question_in=question_in,
                    chat_history=chat_history,
                    verbose=verbose,
                ).as_tool()
            )
        return ToolManager.static_tools + sub_agents_tools

    @staticmethod
    def get_agently_tools(question_in):
        tool_info = {
            "tool_name": "save_message",
            "desc": "存储用户对话信息中提取的信息, 将用户的名称, 编号和是否有酒精残留存储到数据库中.",
            "args": {
                "name": ("str", "[*Required] 用户姓名"),
                "number": ("str", "[*Required] 用户编号"),
                "alcohol": ("str", "[*Required] 是否有酒精残留"),
            },
            "func": AgentlyTool(question_in).save_message,
        }
        return [tool_info]
