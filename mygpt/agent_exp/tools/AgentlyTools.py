from mygpt.schemata import QuestionIn


class AgentlyTool:
    def __init__(self, question_in):
        self.question_in: QuestionIn = question_in

    async def save_message(self, name: str, number: str, alcohol: bool):
        print("-----------------")
        print(self.question_in.question)
        print(f"姓名: {name}, 编号: {number}, 是否有酒精残留: {alcohol}")
        print("-----------------")
        result = "信息保存成功"
        return result
