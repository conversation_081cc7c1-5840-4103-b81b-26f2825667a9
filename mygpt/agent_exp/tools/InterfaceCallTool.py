import asyncio
import json
import time
from typing import Union
from loguru import logger as logging

from langchain_core.language_models.chat_models import BaseChatModel, BaseLanguageModel
from langchain.tools import StructuredTool
from langchain.schema.output_parser import StrOutputParser

from mygpt.agent_exp.core.prompt_manager import PromptManager
from mygpt.agent_exp.utils.callback_handlers import ColoredPrintHandler
from mygpt.agent_exp.utils.print_utils import CODE_COLOR
from mygpt.endpoints.function_call_api import parse_function_call, function_call_api
from mygpt.models import Robot
from mygpt.openai_utils import question_answer_turbo_16k_with_function_call
from mygpt.schemata import QuestionIn
from mygpt.settings import RedisClient


system_message = """
如果用户提供的信息不足以调用函数，请不要调用function_call。可以向用户询问更多信息.
不要假设要向函数中插入什么值。如果用户意图明确，调用function_call，否则提示缺少的信息。
The current time zone time is: {time}
"""


class InterfaceCallToolEngine:
    """
    根据传入的方法名称和任务描述，调用对应的外部接口，执行对应的功能。
    需要方法名称, 任务描述以及可能作为参数的数据, 参数数据可以使用自然语言描述.
    """

    @staticmethod
    def __get_function_call_system_message():
        return system_message.format(
            time=time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        )

    def __init__(
        self,
        llm: Union[BaseLanguageModel, BaseChatModel],
        question_in: QuestionIn,
        chat_history=None,
        verbose=False,
        query_key_words_in_ai_language="",
        response_language="",
    ):
        self.prompt = PromptManager.function_call_prompt
        self.llm = llm
        self.question_in = question_in
        self.chat_history = chat_history
        self.query_key_words_in_ai_language = query_key_words_in_ai_language
        self.response_language = response_language
        self.verbose = verbose
        self.verbose_handler = ColoredPrintHandler(CODE_COLOR)

    def _perpare_messages(self, information):
        if not isinstance(information, str):
            information = json.dumps(information, ensure_ascii=False)
        messages = [
            {"role": "system", "content": self.__get_function_call_system_message()},
            {"role": "user", "content": information},
        ]
        logging.info(f"【InterfaceCallTool】 messages: {messages}")
        return messages

    def _sanitize_input(self, name, information):
        if name.startswith("[") and name.endswith("]"):
            # 去掉字符串两边的中括号
            name = name[1:-1]
        return name, information

    async def interface_call(self, name, information, env=None):
        thought_message = env.get("thought_message")
        thought_message.is_interface_call = True
        # 参数鲁棒性处理
        name, information = self._sanitize_input(name, information)
        request = env.get("request")
        start_time = time.time()
        # 根据工具名称获取api接口对象
        robot: Robot = env.get("robot")
        target_api = None
        for api in robot.apis.related_objects:
            if name == api.function_call_name:
                target_api = api
                break
        if target_api is None:
            return f"Error: 未找到名为 {name} 对应的API接口"
        # INFO ---- 记录当前可能要调用的api接口
        thought_message.agent_function_call_api_id = target_api.id
        # 获取api的参数信息
        function_call_description = (
            target_api.function_call_description
        )  # 外部工具的描述信息
        method = target_api.method  # 外部工具的请求方法, 是GET还是POST
        server_url = (
            target_api.server_url
        )  # 外部工具的请求地址 (主地址, 还需要进一步拼接路径)
        path = target_api.path  # 外部工具的请求路径
        body = target_api.body  # 外部工具的请求体定义信息, 需要哪些参数, 以及参数的类型
        body_required = (
            target_api.body_required
        )  # 外部工具的请求体中的参数哪些是必须的, 通常为列表类型
        function_call_list = [target_api]
        # 解析可使用的外部工具信息
        # tasks = [parse_function_call(function_call) for function_call in function_call_list]
        tasks = [parse_function_call(target_api)]
        results = await asyncio.gather(*tasks)
        function_descriptions = [result["function_description"] for result in results]
        operations = {
            result["operation"]["function_call_name"]: result["operation"]["operation"]
            for result in results
        }
        function_call_infos = {
            result["operation"]["function_call_name"]: result["operation"][
                "function_call_info"
            ]
            for result in results
        }
        if function_descriptions is None:
            logging.info("【InterfaceCallTool】 function_descriptions is None")
            raise

        # 日志计时
        end_time = time.time()
        logging.info(
            f"【InterfaceCallTool】 parse function_call time: {end_time - start_time}"
        )
        messages = self._perpare_messages(information)
        start_time = time.time()
        gpt_response = await question_answer_turbo_16k_with_function_call(
            messages=messages,
            functions=function_descriptions,
            function_call="auto",
            max_tokens=4096,
            stream=False,
        )
        end_time = time.time()
        logging.info(
            f"【InterfaceCallTool】 gpt_response time: {end_time - start_time}"
        )
        logging.info(f"【InterfaceCallTool】 gpt_response: {gpt_response}")
        # 解析GPT返回的结果
        if gpt_response and gpt_response.additional_kwargs:
            start_time = time.time()
            # 进行网络请求, 返回网络请求的原生结果
            (answer_dic, _, request_info) = await function_call_api(
                gpt_response=gpt_response,
                operations=operations,
                function_call_infos=function_call_infos,
                request=request,
                query_key_words_in_ai_language="",
                response_language="",
                question=self.question_in,
            )
            end_time = time.time()
            agent_function_call_duration = end_time - start_time
            response = answer_dic.get("answer", "")
            logging.info(
                f"【InterfaceCallTool】 function_call_api time: {agent_function_call_duration}"
            )
            logging.info(
                f"【InterfaceCallTool】 function call is enabled, response: {response}"
            )
            # 存储外部接口调用的耗时
            thought_message.agent_function_call_duration = agent_function_call_duration
            if not isinstance(response, str):
                res_template = "接口返回数据为json格式:\n```\n{response}\n```"
                response_stringfied = json.dumps(response, ensure_ascii=False, indent=2)
                response = res_template.format(response=response_stringfied)
            thought_message.agent_function_call_return = response
            # 存储接口调用后的结果信息
            thought_message.agent_function_call_request = request_info
        else:
            # 返回大模型的回答信息
            logging.info(
                f"【InterfaceCallTool】 function call is disabled, response: {gpt_response.content}"
            )
            res_template = (
                "接口未调用, 以下是工具给出的可能的提示:\n```\n{response}\n```"
            )
            response = res_template.format(response=gpt_response.content)
        return response

    def as_tool(self):
        return StructuredTool.from_function(
            func=self.interface_call,
            name="InterfaceCallTool",
            description=self.__class__.__doc__.replace("\n", ""),
        )
