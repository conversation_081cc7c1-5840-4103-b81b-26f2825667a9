import json

from mygpt.agent_exp.core.storage import set_redis_storage, get_redis_storage


async def info_update(info: str, env=None) -> str:
    """
    更新info信息, 写入redis
    """
    question_in = env.get("question_in")
    session_id = str(question_in.session_id)
    robot = env.get("robot")
    # 先读取redis中的info信息
    storaged_info = await get_redis_storage(session_id)
    # 如果redis中没有槽位信息, 则读取robot中定义的模板信息
    if not storaged_info:
        # todo - subject_name临时存储槽位信息
        storaged_info = robot.subject_name
    ori_info = json.loads(storaged_info)
    # 新的info数据可能不全, 所以需要遍历更新
    updated_solts = json.loads(info)
    for key, value in updated_solts.items():
        if not isinstance(value, dict):
            raise TypeError(
                "info中元素的value应该是一个字典, 其中包括value, optional, data_type, comments属性"
            )
        ori_info[key] = value
    new_info = json.dumps(ori_info, ensure_ascii=False, indent=2)
    res = await set_redis_storage(session_id=session_id, value=new_info)
    if res:
        reply = {"status": "Success", "message": "info update success"}
    else:
        reply = {"status": "Failed", "message": "info update failed"}
    return json.dumps(json.dumps(reply, ensure_ascii=False), ensure_ascii=False)
