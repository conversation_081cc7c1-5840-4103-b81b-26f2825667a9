from inspect import signature


def render_text_description(tools):
    """
    要整理成的形式:

    AskDocument(filename: str, query: str) -> str - 根据一个Word或PDF文档的内容，回答一个问题。考虑上下文信息，确保问题对相关概念的定义表述完整。
    GenerateDocument(query: str, verbose=False) - 根据需求描述生成一篇正式文档
    SendEmail(to: str, subject: str, body: str, cc: str = None, bcc: str = None) -> str - 给指定的邮箱发送邮件。确保邮箱地址是***********的格式。多个邮箱地址以';'分割。
    InspectExcel(filename: str, n: int = 3) -> str - 探查表格文件的内容和结构，展示它的列名和前n行，n默认为3
    ListDirectory(path: str) -> str - 探查文件夹的内容和结构，展示它的文件名和文件夹名
    FINISH(the_final_answer: str) -> str - 结束任务，将最终答案返回
    AnalyseExcel(query, filename) - 通过程序脚本分析一个结构化文件（例如excel文件）的内容。    输人中必须包含文件的完整路径和具体分析方式和分析依据，阈值常量等。
    """
    descriptions = []
    for tool in tools:
        if tool.description.startswith(tool.name):
            descriptions.append(tool.description)
            continue
        if hasattr(tool, "func") and tool.func:
            sig = signature(tool.func)
            description = f"{tool.name}{sig} - {tool.description}"
        else:
            description = f"{tool.name} - {tool.description}"

        descriptions.append(description)
    return "\n".join(descriptions)
