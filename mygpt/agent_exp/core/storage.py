import asyncio
from uuid import UUID
from typing import Union, List
from loguru import logger as logging
from langchain_community.chat_message_histories.in_memory import ChatMessageHistory

from mygpt.openai_utils import (
    RedisChatMessageHistory,
    update_message_to_finished,
    task_done_exception_logging_callback,
)
from mygpt.schemata import QuestionIn
from mygpt.service.quality_assessment import save_to_quality_assessment_by_message_id
from mygpt.settings import RedisClient
from langchain_core.messages.base import BaseMessage
from langchain_core.messages.ai import AIMessage
from langchain_core.messages.human import HumanMessage
from mygpt.utils import num_tokens_for_langchain_messages, num_tokens_from_string

AGENT_INFO_STORAGE_PRIFIX = "agent_info_storage"


async def get_redis_storage(session_id: Union[str, UUID]):
    session_id = str(session_id)
    key = f"{AGENT_INFO_STORAGE_PRIFIX}:{session_id}"
    redis = RedisClient.get_client()
    res = await redis.get(key)
    return res


async def set_redis_storage(session_id: Union[str, UUID], value: str):
    session_id = str(session_id)
    key = f"{AGENT_INFO_STORAGE_PRIFIX}:{session_id}"
    redis = RedisClient.get_client()
    res = await redis.set(key, value)
    return res


async def agent_reply_postprocess_handler(
    question_in: QuestionIn, reply: str, chat_history: ChatMessageHistory
):
    """
    对Agent返回的答案进行处理
    针对非流式情况
    """
    session_id = question_in.session_id
    message_id = question_in.message_id
    question = question_in.question
    # 更新长时记忆 - 正常更新长时记忆应该在Agent内部进行, 但是为了兼容rag的stream方式, 所以有所拆分
    # 同时长时记忆放在数据库的sessionmessage表中, 以及redis中, 写库的操作就相当于更新长时记忆了
    chat_history.add_message(HumanMessage(content=question))
    chat_history.add_message(AIMessage(content=reply))
    # 聊天记录缓存
    chat_memory = RedisChatMessageHistory(ttl=3600)
    await chat_memory.add_message(session_id, chat_history.messages[-2])
    await chat_memory.add_message(session_id, chat_history.messages[-1])
    # 更新 关系型数据 - 更新 sessionvaluate 表
    # 计算token使用数量 todo - 这个计算并不准确, 目前就是为了写库 (之后需要计算的准确一些)
    tokens = num_tokens_for_langchain_messages(chat_history.messages)
    asyncio.create_task(
        update_message_to_finished(
            message_id=str(message_id), answer=reply, total_tokens=tokens
        )
    ).add_done_back(task_done_exception_logging_callback)


async def save_message(
    question_in: QuestionIn,
    answer: str,
    chat_history: Union[ChatMessageHistory, List[BaseMessage]],
):
    if not question_in.message_id:
        return
    if not isinstance(chat_history, list):
        chat_history = chat_history.messages
    total_tokens = 0
    session_id = question_in.session_id
    message_id = question_in.message_id
    try:
        if not answer:
            logging.warning(
                f"empty answer for session_id:{question_in.session_id} message_id: {question_in.message_id}"
            )
            return
        # 聊天记录缓存
        chat_memory = RedisChatMessageHistory(ttl=3600)
        await chat_memory.add_message(
            session_id, HumanMessage(content=question_in.question)
        )
        await chat_memory.add_message(session_id, AIMessage(content=answer))
        total_tokens += num_tokens_for_langchain_messages(chat_history)
        tokens = num_tokens_from_string(answer)
        total_tokens += tokens
        asyncio.create_task(save_to_quality_assessment_by_message_id(message_id))
    finally:
        asyncio.create_task(
            update_message_to_finished(
                message_id=str(message_id), answer=answer, total_tokens=total_tokens
            )
        ).add_done_callback(task_done_exception_logging_callback)
