import asyncio
import os
import Agently

# import uuid
# from datetime import datetime
# from typing import Any, List, Optional, Union
# from uuid import UUID
#
# from pydantic import AnyHttpUrl, BaseModel, Field
# from tortoise import Tortoise
# from tortoise.contrib.pydantic import pydantic_model_creator
#
# from mygpt.enums import (
#     EMBEDDINGS_MODEL,
#     FAQ_TYPE,
#     FaqSourceType,
#     AIModel,
#     AIType,
#     OpenAIModel,
#     StreamingMessageDataType,
#     StripeModel,
#     VectorStorageType,
#     StreamingMessageDataFormat,
#     RobotType,
# )
#
#
#
#
# import base64
# import hashlib
# import json
# import os
# import uuid
# from datetime import datetime
# from typing import Any, List, Optional
#
# from cryptography.hazmat.backends import default_backend
# from cryptography.hazmat.primitives import serialization
# from cryptography.hazmat.primitives.asymmetric import rsa
# from fastapi.encoders import jsonable_encoder
# from mygpt.aes_util import AESCipher
# from mygpt.enums import (
#     DATASET_STATUS,
#     EMBEDDINGS_MODEL,
#     FAQ_TYPE,
#     PROMPT_TYPE,
#     AIConfigType,
#     AIModel,
#     AIStatus,
#     AIType,
#     ExcelTestTaskStatus,
#     FaqSourceType,
#     FileSource,
#     FileType,
#     OpenAIModel,
#     OpenaiApikeyStatus,
#     QuestionType,
#     ResourceType,
#     SessionMessageStatus,
#     StripeModel,
#     VectorFileStatus,
#     VectorFileType,
#     VectorStatus,
#     VectorTextType,
#     UserConfigType,
#     MESSAGE_COMES_FROM,
#     MessagePageInfo,
#     FileStorage,
#     RobotAccessType,
#     RobotType,
#     VectorFileSourceType,
#     IntegrationRuleType,
#     IntegrationRuleFileListSyncingStatus,
# )
# from mygpt.prompt import (
#     digital_human_default_prompt,
#     get_unknown_text,
#     digital_human_robot_default_prompt,
# )
# from mygpt.pydantic_rules import LarkIntegrationRuleInput
# from pydantic import BaseModel
# from tortoise import fields, models
# from tortoise.exceptions import MultipleObjectsReturned
#
#
# import asyncio
# import logging
# import os
# import secrets
# import ssl
# import string
# from urllib.parse import quote, urlencode
#
# import loguru
# import redis.asyncio as redis
# from dotenv import load_dotenv
# from loguru import logger as _logging
# from pydantic import BaseModel
# from redis.exceptions import ConnectionError
#
# from mygpt.enums import EMBEDDINGS_MODEL, OpenAIModel, VectorStorageType
#
# # ssl._create_default_https_context = ssl._create_unverified_context
#
# import mygpt.settings
#
# # todo - 目前来看是settings的问题 --- 下面是排查过的
# # from mygpt import models
# # Tortoise.init_models(["mygpt.models"], "models")
# # from mygpt.settings import AES_SECRETS, AWS_S3_BUCKET_NAME


import nest_asyncio

nest_asyncio.apply()

role = """你是Sparticle公司的一名客服人员, 你的工作职责是与员工进行沟通.
旨在为员工进行每日酒精检查。需要按照特定顺序询问一系列问题并记录回答。
请以清晰、专业、友好的方式交流。
在整个对话中使用礼貌的中文。"""

general = """对话流程:
1. 开始问候
“感谢您的来电。我是酒精检查中心的YUMI。请告诉我您的姓名和法人号码。”
问题列表:
用户回答法人号码和姓名
例：“法人号码〇的<姓名/>。” 聊天机器人复述并确认。
例：“〇号码的〇〇先生/女士，对吧？” 聊天机器人询问当前酒精残留情况。 “现在是否没有酒精残留？”
用户回答“没有残留”。
2. 当所有信息收集完成后, 使用工具完成信息的写入.
3. 信息写入完成后, 请礼貌表示感谢, 并结束当前对话任务.
"""

agent = (
    (
        Agently.create_agent()
        .set_settings("current_model", "OAIClient")
        .set_settings("model.OAIClient.url", os.environ["OPENAI_BASE_URL"])
        .set_settings("model.OAIClient.auth", {"api_key": os.environ["OPENAI_API_KEY"]})
        .set_settings("model.OAIClient.options", {"model": "gpt-4o"})
    )
    .role(role)
    .general(general)
)


# 读取Agent的配置信息, 配置信息固定在`../config/agent_config.yaml`文件中


def print_handler(data):
    print(data, end="")


# def save_message(name, number, alcohol):
#     print("-----------------")
#     print(f"姓名: {name}, 编号: {number}, 是否有酒精残留: {alcohol}")
#     print("-----------------")


# tool_info = {
#     "tool_name": "save_message",
#     "desc": "存储用户对话信息中提取的信息, 将用户的名称, 编号和是否有酒精残留存储到数据库中.",
#     "args": {
#         "name": (
#             "str",
#             "[*Required] 用户姓名"
#         ),
#         "number": (
#             "str",
#             "[*Required] 用户编号"
#         ),
#         "alcohol": (
#             "str",
#             "[*Required] 是否有酒精残留"
#         )
#     },
#     "func": save_message
# }

# agent.register_tool(
#     **tool_info
# )


async def run_agent(question, chat_history=None):
    if not chat_history:
        chat_history = []
    result = (
        agent.chat_history(chat_history)
        # input: 和本次请求相关的输入信息
        .input(question)
        # start: 用于开始本次主要交互请求
        .on_delta(print_handler).start()
    )
    return result


if __name__ == "__main__":
    question = "没有残留"
    chat_history = []
    loop = asyncio.get_event_loop()
    reply = loop.run_until_complete(run_agent(question, chat_history))
    print(reply)
