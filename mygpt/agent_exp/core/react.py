import os
import re
import time

import yaml
import asyncio
from datetime import datetime
from inspect import signature
from typing import List, Tu<PERSON>, Optional

from langchain_community.chat_message_histories.in_memory import ChatMessageHistory
from langchain_core.language_models.chat_models import BaseChatModel
from langchain.output_parsers import PydanticOutputParser, OutputFixingParser
from langchain.schema.output_parser import StrOutputParser
from langchain.tools.base import BaseTool
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder

from mygpt.agent_exp.core.mod.langchain_core_tools import render_text_description
from pydantic import ValidationError
from langchain_core.prompts import HumanMessagePromptTemplate

from mygpt.agent_exp.core.action import Action
from mygpt.agent_exp.core.prompt_manager import PromptManager
from mygpt.agent_exp.core.storage import get_redis_storage
from mygpt.agent_exp.utils.callback_handlers import *
from mygpt.models import Robot, AgentThoughtMessage
from mygpt.openai_utils import <PERSON><PERSON>allback<PERSON>an<PERSON>, ChainResponseAsyncHandler
from mygpt.schemata import QuestionIn


class ReActAgent:

    @staticmethod
    def __format_thought_observation(
        thought: str, action: Action, observation: str
    ) -> str:
        """将短时记忆拼装成一个字符串, 为了往prompt中放的方便"""
        # 将全部JSON代码块替换为空
        ret = re.sub(r"```json(.*?)```", "", thought, flags=re.DOTALL)
        ret += "\n" + str(action) + "\n返回结果:\n" + observation
        return ret

    @staticmethod
    def __extract_json_action(text: str) -> str | None:
        # 匹配最后出现的JSON代码块
        json_pattern = re.compile(r"```json(.*?)```", re.DOTALL)
        matches = json_pattern.findall(text)
        if matches:
            last_json_str = matches[-1]
            return last_json_str
        return None

    @staticmethod
    def __parse_user_prompt(user_prompt: str) -> Tuple[str, str]:
        pattern = r"# Role\s*(.*?)\s*# General\s*(.*)"
        matches = re.search(pattern, user_prompt, re.DOTALL)
        if matches:
            role = matches.group(1)
            general = matches.group(2)
            return role, general
        else:
            return "", user_prompt

    def __init__(
        self,
        llm: BaseChatModel,
        tools: List[BaseTool],
        robot: Robot,
        question_in: QuestionIn,
        max_thought_steps: Optional[int] = 10,
        thought_message_handler=None,
        verbose=False,
    ):
        self.llm = llm
        self.tools = tools
        self.robot = robot
        self.question_in: QuestionIn = question_in
        self.verbose = verbose
        self.max_thought_steps = max_thought_steps

        # OutputFixingParser： 如果输出格式不正确，尝试修复
        self.output_parser = PydanticOutputParser(pydantic_object=Action)
        self.robust_parser = OutputFixingParser.from_llm(
            parser=self.output_parser, llm=llm
        )

        self.main_prompt = PromptManager.main_prompt

        self._role_prompt, self._general_prompt = self.__parse_user_prompt(
            self.robot.prompt
        )
        self.__init_prompt_templates()
        self.__init_chains()

        self.verbose_handler = ColoredPrintHandler(color=THOUGHT_COLOR)
        self.thought_messages = []

    def _format_interface_call_infomation(self) -> str:
        """
        将接口调用信息格式化成自然语言的描述信息, 格式如下:
        [weather] - 获取天气信息
        """
        if not self.robot.apis.related_objects:
            return ""
        ret = []
        for api in self.robot.apis.related_objects:
            record = f"[{api.function_call_name}] - {api.function_call_description}"
            ret.append(record)
        return "\n".join(ret)

    def __init_prompt_templates(self):
        robot_prompt = self.robot.prompt

        # 多轮对话模板
        self.prompt = ChatPromptTemplate.from_messages(
            [
                # 历史记录(长时记忆) 用语言模型本身的多轮历史
                MessagesPlaceholder(variable_name="chat_history"),
                # 短时记忆自己拼装模板
                HumanMessagePromptTemplate.from_template(self.main_prompt),
            ]
        ).partial(  # 填充恒定值到模板中.
            role=self._role_prompt,
            general=self._general_prompt,
            # 可用工具集
            tools=render_text_description(self.tools),
            # 可使用的工具的名称
            tool_names=",".join([tool.name for tool in self.tools]),
            format_instructions=self.output_parser.get_format_instructions(),
            # 外部接口调用信息
            interface=self._format_interface_call_infomation(),
        )

    def __init_chains(self):
        # 主流程的chain
        self.main_chain = self.prompt | self.llm | StrOutputParser()

    def __find_tool(self, tool_name: str) -> Optional[BaseTool]:
        for tool in self.tools:
            if tool.name == tool_name:
                return tool
        return None

    def _thought_message_handler(
        self,
        thought: str,
        iteration: int,
        action,
        response,
        iteration_duration,
        cur_thought_message: AgentThoughtMessage,
    ):
        """思考过程中的消息处理"""
        if "env" in action.args:
            action.args.pop("env")
        cur_thought_message.robot_id = self.robot.id
        cur_thought_message.session_id = self.question_in.session_id
        cur_thought_message.message_id = self.question_in.message_id
        cur_thought_message.user_input = self.question_in.question
        cur_thought_message.iteration = iteration
        cur_thought_message.thought_message = thought
        cur_thought_message.action_name = action.name
        cur_thought_message.action_args = action.args
        cur_thought_message.tool_return = response
        cur_thought_message.iteration_duration = iteration_duration
        self.thought_messages.append(cur_thought_message)

    async def __step(
        self, task, short_term_memory, chat_history, cur_datetime=None, info=""
    ) -> Tuple[Action, str]:
        if not cur_datetime:
            cur_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        """执行一步思考"""
        inputs = {
            # 当前的任务
            "input": task,
            # 当前的短时记忆
            "agent_scratchpad": "\n".join(short_term_memory),
            # 历史的长时记忆
            "chat_history": chat_history.messages,
            # 当前时间
            "current_datetime": cur_datetime,
            # 当前槽位信息
            "info": info,
        }

        config = {"callbacks": [self.verbose_handler] if self.verbose else []}
        response_chars = []
        for s in self.main_chain.stream(inputs, config=config):
            response_chars.append(s)
        response = "".join(response_chars)
        # 提取JSON代码块
        json_action = self.__extract_json_action(response)
        # 带容错的解析
        action = self.robust_parser.parse(json_action if json_action else response)
        return action, response

    async def __exec_action(self, action: Action, env=None) -> str:
        # 查找工具
        tool = self.__find_tool(action.name)
        if tool is None:
            observation = (
                f"Error: 找不到工具或指令 '{action.name}'. "
                f"请从提供的工具/指令列表中选择，请确保按对顶格式输出。"
            )
        else:
            try:
                if tool.name == "FINISH":
                    print()
                # 执行工具
                action.args["env"] = env
                result = await tool.arun(action.args)
                if isinstance(result, str):
                    observation = result
                else:
                    observation = await asyncio.gather(result)
                    observation = observation[0]
            except ValidationError as e:
                # 工具的入参异常
                if "env" in action.args:
                    del action.args["env"]
                observation = f"Validation Error in args: {str(e)}, args: {action.args}"
            except Exception as e:
                # 工具执行异常
                if "env" in action.args:
                    del action.args["env"]
                observation = (
                    f"Error: {str(e)}, {type(e).__name__}, args: {action.args}"
                )
                print(e)
        return observation

    async def run(
        self,
        question: QuestionIn,
        chat_history: ChatMessageHistory,
        env=None,
    ) -> Tuple[str, int]:
        """
        运行智能体
        :param question: 用户的一些信息, 包括session_id, question等
        :param chat_history: 对话上下文（长时记忆）
        :param env: 用于传递环境信息, 字典类型
            字典中需要的信息包括:
                "robot": robot,
                "question_in": question,
                "question_record_obj": question_record_obj,
                "chat_history": chat_history,
                "event": event,
                "user_id": user_id
        """
        if not env:
            env = {}
        # 初始化短时记忆: 记录推理过程
        short_term_memory = []

        # 思考步数
        thought_step_count = 0

        reply = ""

        # 开始逐步思考
        while thought_step_count < self.max_thought_steps:
            if self.verbose:
                self.verbose_handler.on_thought_start(
                    thought_step_count
                )  # 打印现在第几轮思考
            start_time = time.time()
            # 创建轮次信息记录对象
            thought_message = AgentThoughtMessage()
            env["thought_message"] = thought_message
            # 获取info槽位信息
            res = await get_redis_storage(question.session_id)
            # todo - 这里暂时验证可行性, 取的是robot的subject_name. 如果可行, 需要新创建字段存储robot的槽位定义信息
            info = res if res else self.robot.subject_name
            if info is None:
                info = ""

            # 执行一步思考
            # 每一步会产生一个action, 生成一大段文本.
            action, thought = await self.__step(
                task=question.question,
                short_term_memory=short_term_memory,
                chat_history=chat_history,
                info=info,
            )

            # 如果是结束指令，执行最后一步
            if action.name == "FINISH":
                try:
                    finish_coroutine = self.__exec_action(action, env=None)
                    reply = await asyncio.gather(finish_coroutine)
                    reply = reply[0]
                    end_time = time.time()
                    iteration_duration = end_time - start_time
                    self._thought_message_handler(
                        thought=thought,
                        iteration=thought_step_count,
                        action=action,
                        response=reply,
                        iteration_duration=iteration_duration,
                        cur_thought_message=thought_message,
                    )
                    break
                except Exception as e:
                    print(e)
                    raise e

            # 执行动作
            observation = await self.__exec_action(action, env=env)
            if (
                observation
                == "<|||bos|||>e1c3af5b-2caf-4763-9ff5-a48f2ab395a5<|||eos|||>"
            ):
                # 这里给一个判断, 用一个特殊字符串表示指令, 收到这个指令则结束循环, 不走finish了. 返回空字符串, 最终的结果需要到env中的resp获取
                reply = ""
                break
            if self.verbose:
                self.verbose_handler.on_tool_end(observation)

            # 更新短时记忆
            short_term_memory.append(
                self.__format_thought_observation(thought, action, observation)
            )
            end_time = time.time()
            iteration_duration = end_time - start_time
            # 记录当前轮次信息 - todo
            self._thought_message_handler(
                thought=thought,
                iteration=thought_step_count,
                action=action,
                response=observation,
                iteration_duration=iteration_duration,
                cur_thought_message=thought_message,
            )
            thought_step_count += 1
        if thought_step_count >= self.max_thought_steps:
            # 如果思考步数达到上限，返回错误信息
            action, response = await self.__step(
                task="任务的思考轮数达到最大, 根据用户问题礼貌地解释自己未能完成用户的所要求的任务, 分析失败原因并向用户解释. 使用FINISH工具",
                short_term_memory=short_term_memory,
                chat_history=chat_history,
                info=info,
            )
            if action.name != "FINISH":
                reply = "抱歉，我没能完成您的任务。"
            else:
                reply = self.__exec_action(action, env=env)

        # 更新长时记忆
        # chat_history.add_user_message(task)
        # chat_history.add_ai_message(reply)
        # 存储thought信息到数据库
        if not self.thought_messages:
            logging.info("ReActAgent - run - thought_messages is empty.")
        # await AgentThoughtMessage.bulk_create(self.thought_messages)
        asyncio.create_task(self.save_thought_messages())
        return reply, thought_step_count

    async def save_thought_messages(self):
        try:
            await AgentThoughtMessage.bulk_create(self.thought_messages)
        except Exception as e:
            # Handle exceptions as needed (e.g., logging)
            logging.error(f"Error saving thought messages: {e}")
