import os
import asyncio

from mygpt.models import Robot
from mygpt.schemata import QuestionIn
from langchain_community.chat_message_histories.in_memory import ChatMessageHistory

from typing import List, Optional, Tuple
import Agently


def print_handler(data: str):
    print(data, end="")


role = """你是Sparticle公司的一名客服人员, 你的工作职责是与员工进行沟通.
旨在为员工进行每日酒精检查。需要按照特定顺序询问一系列问题并记录回答。
请以清晰、专业、友好的方式交流。
在整个对话中使用礼貌的中文。"""

general = """对话流程:
1. 开始问候
“感谢您的来电。我是酒精检查中心的YUMI。请告诉我您的姓名和法人号码。”
问题列表:
用户回答法人号码和姓名
例：“法人号码〇的<姓名/>。” 聊天机器人复述并确认。
例：“〇号码的〇〇先生/女士，对吧？” 聊天机器人询问当前酒精残留情况。 “现在是否没有酒精残留？”
用户回答“没有残留”。
2. 当所有信息收集完成后, 使用工具完成信息的写入.
3. 信息写入完成后, 请礼貌表示感谢, 并结束当前对话任务.
"""


class AgentlyWapper:
    def __init__(
        self,
        agently_agent,
        robot: Robot,
        max_thought_steps: Optional[int] = 10,
        thought_message_handler=None,
        callback=None,
        verbose=False,
    ):
        # AgentFactory中创建Agently的Agent实例, 传递过来
        self.agently_agent = agently_agent
        self.robot = robot
        self.max_thought_steps = max_thought_steps
        self.thought_message_handler = thought_message_handler
        self.callback = callback
        self.verbose = verbose

    def process_stream_token(self, iterable_obj):
        for event, data in iterable_obj:
            # token处理逻辑
            if event == "response:start":
                yield "Start"
            elif event == "response:delta":
                yield data + "\n"
            elif event == "response:done":
                yield "Done"
        return "Done"

    def convert_chat_history(self, chat_history: ChatMessageHistory):
        messages = []
        for message in chat_history.messages:
            if message.type == "human":
                messages.append({"role": "user", "content": message.content})
            elif message.type == "ai":
                messages.append({"role": "assistant", "content": message.content})
            else:
                messages.append({"role": "system", "content": message.content})
        return messages

    async def run(
        self,
        question_in: QuestionIn,
        chat_history,
        env=None,
    ) -> Tuple[str, int]:
        if not chat_history:
            chat_history = []
        if isinstance(chat_history, ChatMessageHistory):
            chat_history = self.convert_chat_history(chat_history)
        result = (
            self.agently_agent.chat_history(chat_history)
            # input: 和本次请求相关的输入信息
            .input(question_in.question)
            .on_delta(print_handler)
            .on_finally(self.callback.on_finally)
            .get_generator()
        )
        resp = self.process_stream_token(result)
        return resp


if __name__ == "__main__":
    history = [
        {"role": "user", "content": "你好"},
        {
            "role": "assistant",
            "content": "感谢您的来电。我是酒精检查中心的YUMI。请告诉我您的姓名和法人号码。",
        },
        {"role": "user", "content": "张飒, 3419"},
        {
            "role": "assistant",
            "content": "3419号码的张飒先生，对吧？现在是否没有酒精残留？",
        },
    ]
    agent = (
        (
            Agently.create_agent()
            .set_settings("current_model", "OAIClient")
            .set_settings("model.OAIClient.url", os.environ["OPENAI_BASE_URL"])
            .set_settings(
                "model.OAIClient.auth", {"api_key": os.environ["OPENAI_API_KEY"]}
            )
            .set_settings("model.OAIClient.options", {"model": "gpt-4o"})
        )
        .role(role)
        .general(general)
        .register_tool(**tool_info)
    )
    tools = []
    robot = None
    question_in = QuestionIn(question="没有残留", session_id="123456")
    wapper = AgentlyWapper(agent=agent, tools=tools, robot=robot)
    loop = asyncio.get_event_loop()
    reply = loop.run_until_complete(
        wapper.run(chat_history=history, question_in=question_in)
    )
    print(reply)
    # loop = asyncio.get_event_loop()
    # reply = loop.run_until_complete(run_agent(question_in, history))
