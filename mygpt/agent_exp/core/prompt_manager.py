import os
import yaml
from mygpt.settings import PROJECT_PATH
from mygpt.agent_exp.utils.file_util import FileUtil


class PromptManager:
    meta_prompt = None
    main_prompt = None
    main_agently_prompt = None
    function_call_prompt = None

    @staticmethod
    def init_prompt():
        # 读取配置文件, 初始化工厂
        with open(
            os.path.join(PROJECT_PATH, "agent_exp", "config", "agent_config.yaml"),
            "r",
            encoding="utf-8",
        ) as f:
            config = yaml.safe_load(f)
            meta_prompt_file_path = os.path.join(
                PROJECT_PATH, config["prompts"]["meta_prompt"]
            )
            main_prompt_file_path = os.path.join(
                PROJECT_PATH, config["prompts"]["main_prompt"]
            )
            main_agently_prompt_file_path = os.path.join(
                PROJECT_PATH, config["prompts"]["main_agently_prompt"]
            )
            function_call_prompt_file_path = os.path.join(
                PROJECT_PATH, config["prompts"]["function_call_prompt"]
            )
            PromptManager.meta_prompt = FileUtil.read_text(meta_prompt_file_path)
            PromptManager.main_prompt = FileUtil.read_text(main_prompt_file_path)
            PromptManager.main_agently_prompt = FileUtil.read_text(
                main_agently_prompt_file_path
            )
            PromptManager.function_call_prompt = FileUtil.read_text(
                function_call_prompt_file_path
            )
