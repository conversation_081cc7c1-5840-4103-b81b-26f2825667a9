import asyncio
import uuid
from typing import Async<PERSON>tera<PERSON>, Awaitable
from loguru import logger as logging
from langchain_community.chat_models import AzureChatOpenAI, ChatOpenAI
from langchain.callbacks import AsyncIteratorCallbackHandler
from mygpt.agent_exp.agent_factory import AgentFactory
from mygpt.agent_exp.core.prompt_manager import PromptManager
from mygpt.agent_exp.utils.callback_handlers import (
    ColoredPrintHandler,
    AgentStreamCallbackHanlder,
)
from mygpt.agent_exp.utils.print_utils import THOUGHT_COLOR
from mygpt.enums import OpenAIModel, LLM_PROVIDER, StreamingMessageDataType
from mygpt.openai_utils import get_chat_model, chat_async_iterator
from mygpt.schemata import GenPromptIn, StreamOut
from langchain.prompts.chat import (
    ChatPromptTemplate,
    HumanMessagePromptTemplate,
    SystemMessagePromptTemplate,
)
from langchain.schema import (
    BaseMessage,
    AIMessage,
    HumanMessage,
    SystemMessage,
    get_buffer_string,
)


async def chat_async_iterator(
    model: Chat<PERSON>penA<PERSON>,
    messages: list[BaseMessage],
    callback: AsyncIteratorCallbackHandler,
    message_id: str,
) -> AsyncIterator[str]:
    """
    langchain chat模型异步迭代器
    """
    openai_chat_model = get_chat_model(
        model=OpenAIModel.GPT_4_OMNI,
        llm_provider=LLM_PROVIDER.OPENAI,
        max_tokens=model.max_tokens,
        temperature=model.temperature,
        max_retries=model.max_retries,
        request_timeout=model.request_timeout,
        callbacks=[callback],
    )
    ans = ""
    async for chunk in openai_chat_model.astream(messages):
        if not chunk.content:
            continue
        ans += chunk.content
        rs = StreamOut(
            message_id=message_id,
            message_type=StreamingMessageDataType.PLAIN_TEXT,
            content=chunk.content,
            use_faq=False,
            tokens=-1,
        ).json(ensure_ascii=False)
        yield rs + "\n"
    logging.debug(f"gen_prompt_service, ans: {ans}")


async def gen_prompt_by_meta_prompt(gen_prompt_in: GenPromptIn):
    if not gen_prompt_in.message_id:
        gen_prompt_in.message_id = str(uuid.uuid4())
    stream_callback_handler = AgentStreamCallbackHanlder(
        message_id=gen_prompt_in.message_id,
        origin_question=gen_prompt_in.prompt_in,
    )
    llm: ChatOpenAI = get_chat_model(
        OpenAIModel.GPT_4_OMNI_2024_11_20, max_tokens=8192, request_timeout=60
    )
    # 获取meta_prompt
    messages = [
        SystemMessage(content=PromptManager.meta_prompt),
        HumanMessage(content=gen_prompt_in.prompt_in),
    ]
    if gen_prompt_in.stream:
        resp = chat_async_iterator(
            llm, messages, stream_callback_handler, message_id=gen_prompt_in.message_id
        )
    else:
        resp = await llm.ainvoke(messages)
    return resp
