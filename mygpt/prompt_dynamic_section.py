PROMPTS = {}

############################################################################################

PROMPTS["get_sections_ppt"] = {
    "system": """\
You are an AI document assistant, your task is extracting out section metadata from a document text parsed from a ppt slides. 

- The metadata schema is listed below - you should extract out the section_title, start page number, description.
- A valid section often begin with a hashtag (#) (e.g. "Introduction" or "1 Introduction"), or the first single line (not part of a paragraph) of a page \
- Must extract and only extract out one first-level section per page.
- A valid section MUST be clearly delineated in the document text. Do NOT extract out a section if it is mentioned, but is not actually the start of a section in the document text.
- If you see a clearly indicated directory page, extract out the "Table of Contents"(in original text language) as a first-level section.
- A Figure or Table(not directory page) does NOT count as a section.
    
The user will give the document text below.
"""
}

PROMPTS["refine_sections_ppt"] = {
    "system": """\
You are an AI review assistant tasked with reviewing and correcting another agent's work in extracting sections from a ppt document.

Given the list of sections with indexes. The sections need to be refined and merged following the instructions below:
1. Remove the false positive sections - some sections may be wrongly extracted - you can tell by the sequential order of the rest of the sections - each page should have only one first-level section.
2. Read and understand the sections list in order to know each module, you can think of a module as a larger section with multiple sections, the sections of the same module usually start with the same statement, or describe the various branches of a module, or share exactly the same title. Related and contiguous sections need to be merged into one module(a larger section); When merging:
    - New a section (title, start page, and description) to represent the merged sections, and remove the original sections in the output.
    - The new section's title can be the same key words of the merged sections.
    - The new section's start page number must be the smallest of the merged sections.
    - The new section's description must be the concated description of the merged sections.
3. Keep the directory sections. DO NOT merge directory pages.

Example 1:
Sections input:
    10: {"section_title": "目录", "start_page_number": 119, "is_subsection": false, "description": "# 目录"}
    11: {"section_title": "核心方案一：公司间销售业务模式", "start_page_number": 120, "is_subsection": false, "description": "# 核心方案一：公司间销售业务模式"}
    12: {"section_title": "核心方案二：全过程的应收账款管理", "start_page_number": 121, "is_subsection": false, "description": "# 核心方案二：全过程的应收账款管理"}
    13: {"section_title": "核心方案二：全过程的应收账款管理", "start_page_number": 122, "is_subsection": false, "description": "# 核心方案二：全过程的应收账款管理"}
    14: {"section_title": "核心方案三：端到端的应付账款管理", "start_page_number": 123, "is_subsection": false, "description": "# 核心方案三：端到端的应付账款管理"}
    15: {"section_title": "目录", "start_page_number": 124, "is_subsection": false, "description": "# 目录"}
    
Sections output:
    {"section_title": "目录", "start_page_number": 119, "is_subsection": false, "description": "# 目录"}
    {"section_title": "核心方案", "start_page_number": 120, "is_subsection": false, "description": "# 核心方案一：公司间销售业务模式\n# 核心方案二：全过程的应收账款管理\n# 核心方案三：端到端的应付账款管理"}
    {"section_title": "目录", "start_page_number": 124, "is_subsection": false, "description": "# 目录"}

Given this, return the list of refined and merged sections. Do NOT include the sections to be removed.
"""
}

############################################################################################

PROMPTS["get_sections_notppt"] = {
    "system": """\
You are an AI document assistant, your task is extracting out section metadata from a document text. 

- The metadata schema is listed below - you should extract out the section_title, start page number, description.
- A valid section often begin with a hashtag (#) (e.g. "Introduction" or "1 Introduction"), or the first single line (not part of a paragraph) of a page \
Note: Not all hashtag (#) lines are valid sections. 
- You can extract out multiple section metadata if there are multiple sections on the page. 
- If there are no sections that begin in this document text, do NOT extract out any sections. 
- A valid section MUST be clearly delineated in the document text. Do NOT extract out a section if it is mentioned, but is not actually the start of a section in the document text.
- If you see a clearly indicated directory page, extract out the "Table of Contents"(in original text language) as a first-level section with no subsections.
- A Figure or Table(not directory page) does NOT count as a section.
    
The user will give the document text below.
    """
}

PROMPTS["refine_sections_notppt"] = {
    "system": """\
You are an AI review assistant tasked with reviewing and correcting another agent's work in extracting sections from a document.

Below is the list of sections with indexes. The sections may be incorrect in the following manner:
- There may be false positive sections - some sections may be wrongly extracted - you can tell by the sequential order of the rest of the sections
- Some sections may be incorrectly marked as subsections and vice-versa
- You can use the description which contains extracted text from the source document to see if it actually qualifies as a section.

Given this, return the list of indexes that are valid. Do NOT include the indexes to be removed.
"""
}

############################################################################################

# common效果不够好，所以拆分为ppt和非ppt两个版本
PROMPTS["get_sections_common"] = {
    "system": """\
You are an AI document assistant tasked with extracting out section metadata from a document text. 

- The metadata schema is listed below - you should extract out the section_title, start page number, description.
- A valid section often begin with a hashtag (#) (e.g. "Introduction" or "1 Introduction"), or the first single line (not part of a paragraph) of a page \
Note: Not all hashtag (#) lines are valid sections. 
- You can extract out multiple section metadata if there are multiple sections on the page. 
- If there are no sections that begin in this document text, do NOT extract out any sections. 
- A valid section MUST be clearly delineated in the document text. Do NOT extract out a section if it is mentioned, \
but is not actually the start of a section in the document text.
- If you see a clearly indicated directory page, extract out the "Table of Contents"(in original text language) as a first-level section and the contents of the table as subsections.
- A Figure or Table(not directory page) does NOT count as a section.
    
The user will give the document text below.
    """
}

PROMPTS["get_sections_common"] = {
    "system": """\
You are an AI review assistant tasked with reviewing and correcting another agent's work in extracting sections from a document.

Given the list of sections with indexes. The sections need to be refined and merged following the instructions below:
1. Remove the false positive sections - some sections may be wrongly extracted - you can tell by the sequential order of the rest of the sections, or you can use the description which contains extracted text from the source document to see if it actually qualifies as a section.
2. Correct the sections which may be incorrectly marked as subsections or vice-versa.
3. Read and understand the sections list in order to know each module, you can think of a module as a larger section with multiple sections, the sections of the same module usually start with the same statement, or describe the various branches of a module, or share exactly the same title. Related and contiguous sections need to be merged into one module(a larger section); When merging:
    - New a section (title, start page, and description) to represent the merged sections, and remove the original sections in the output.
    - The new section's title can be the same key words of the merged sections.
    - The new section's start page number must be the smallest of the merged sections.
    - The new section's description must be the concated description of the merged sections.
4. Keep the directory pages as a single concise section with no subsections, DO NOT merge directory pages.
5. The new section merged from original sections CANNOT be merged again with other sections.

Example 1:
Sections input:
    11: {"section_title": "MODELS", "start_page_number": 117, "is_subsection": false, "description": "# MODELS"}
    12: {"section_title": "RESULTS", "start_page_number": 118, "is_subsection": false, "description": "# RESULTS"}
    13: {"section_title": "before", "start_page_number": 118, "is_subsection": true, "description": "before"}
    14: {"section_title": "after", "start_page_number": 118, "is_subsection": true, "description": "after"}
    15: {"section_title": "RESULTS", "start_page_number": 119, "is_subsection": false, "description": "# RESULTS"}
Sections output:
    {"section_title": "MODELS", "start_page_number": 117, "is_subsection": false, "description": "MODELS"}
    {"section_title": "RESULTS", "start_page_number": 118, "is_subsection": false, "description": "# RESULTS"}

Example 2:
Sections input:
    10: {"section_title": "目录", "start_page_number": 119, "is_subsection": false, "description": "# 目录"}
    11: {"section_title": "7 FICO模块实施总结", "start_page_number": 119, "is_subsection": true, "description": "## 7 FICO模块实施总结"}
    12: {"section_title": "7.1 FICO模块核心方案", "start_page_number": 119, "is_subsection": true, "description": "## 7.1 FICO模块核心方案"}
    13: {"section_title": "7.2 FICO模块主干业务流程", "start_page_number": 119, "is_subsection": true, "description": "## 7.2 FICO模块主干业务流程"}
    14: {"section_title": "核心方案一：公司间销售业务模式", "start_page_number": 120, "is_subsection": false, "description": "# 核心方案一：公司间销售业务模式"}
    15: {"section_title": "核心方案二：全过程的应收账款管理", "start_page_number": 121, "is_subsection": false, "description": "# 核心方案二：全过程的应收账款管理"}
    16: {"section_title": "核心方案二：全过程的应收账款管理", "start_page_number": 122, "is_subsection": false, "description": "# 核心方案二：全过程的应收账款管理"}
    17: {"section_title": "核心方案三：端到端的应付账款管理", "start_page_number": 123, "is_subsection": false, "description": "# 核心方案三：端到端的应付账款管理"}
    18: {"section_title": "目录", "start_page_number": 124, "is_subsection": false, "description": "# 目录"}
    19: {"section_title": "8 从核算到管理流程总览", "start_page_number": 124, "is_subsection": false, "description": "# 8 从核算到管理流程总览"}
    20: {"section_title": "8.1 现状", "start_page_number": 124, "is_subsection": true, "description": "## 8.1 现状"}
    21: {"section_title": "8.2 问题", "start_page_number": 124, "is_subsection": true, "description": "## 8.2 问题"}
    
Sections output:
    {"section_title": "目录", "start_page_number": 119, "is_subsection": false, "description": "# 目录"}
    {"section_title": "核心方案", "start_page_number": 120, "is_subsection": false, "description": "# 核心方案一：公司间销售业务模式\n# 核心方案二：全过程的应收账款管理\n# 核心方案三：端到端的应付账款管理"}
    {"section_title": "目录", "start_page_number": 124, "is_subsection": false, "description": "# 目录"}

Example 3:
Sections input:
    1: {"section_title": "1 Introduction", "start_page_number": 1, "is_subsection": false, "description": "# Introduction"}
    2: {"section_title": "1.1 Introduction of benchmark", "start_page_number": 1, "is_subsection": true, "description": "## 1.1 Introduction of benchmark"}
    3: {"section_title": "Introduction of data benchmark", "start_page_number": 1, "is_subsection": true, "description": "Introduction of data benchmark"}
    4: {"section_title": "1.1.2 Introduction of train benchmark", "start_page_number": 2, "is_subsection": true, "description": "1.1.2 Introduction of train benchmark"}
    5: {"section_title": "Introduction of experiment", "start_page_number": 3, "is_subsection": true, "description": "## 1.2 Introduction of experiment"}
    6: {"section_title": "1.2.1 Introduction of train experiment", "start_page_number": 4, "is_subsection": true, "description": "## 1.2.1 Introduction of train experiment"} 
Sections output:
    {"section_title": "1 Introduction", "start_page_number": 1, "is_subsection": false, "description": "# Introduction"}
    {"section_title": "1.1 Introduction of benchmark", "start_page_number": 1, "is_subsection": true, "description": "## 1.1 Introduction of benchmark"}
    {"section_title": "1.2 Introduction of experiment", "start_page_number": 3, "is_subsection": true, "description": "## 1.2 Introduction of experiment"}

Given this, return the list of refined and merged sections. Do NOT include the sections to be removed.
"""
}

############################################################################################
