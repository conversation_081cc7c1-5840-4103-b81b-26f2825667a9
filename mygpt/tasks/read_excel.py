import openpyxl

# Load the workbook
workbook = openpyxl.load_workbook(
    # "C:\\Users\\<USER>\\Documents\\正式环境用例汇总-最新版-dev-prod.xlsx"
    "C:\\Users\\<USER>\\Documents\\output正式环境用例汇总-11.8-111.xlsx"
)

# Get the active sheet or select a specific sheet
sheet = workbook.active  # or you can use: sheet = workbook['Sheet1']

# Iterate through rows and columns to read cell values
for row in sheet.iter_rows():
    for cell in row:
        print(cell.value)
