import os
import time
import traceback
from datetime import datetime

import loguru
import qdrant_client
from dateutil.parser import parse
from qdrant_client.http.models import Filter, FieldCondition, MatchValue
from tortoise import Tortoise
from tortoise.expressions import Q

from mygpt import settings
from mygpt.enums import VectorFileStatus
from mygpt.models import VectorFile
from mygpt.opensearch import get_async_open_search
from mygpt.settings import TORTOISE_ORM

async_opensearch = get_async_open_search()
async_qdrant_client = qdrant_client.AsyncQdrantClient(
    host=settings.VECTOR_STORAGE_QDRANT_HOST,
    port=settings.VECTOR_STORAGE_QDRANT_HTTP_PORT,
    grpc_port=settings.VECTOR_STORAGE_QDRANT_GRPC_PORT,
    timeout=60,
)

LAST_PROCESSED_FILE = "data/last_processed_file.txt"


async def cleanup():
    last_processed_created_at = None

    if os.path.exists(LAST_PROCESSED_FILE):
        with open(LAST_PROCESSED_FILE, "r") as f:
            last_processed_created_at = parse(f.read())
    t0 = time.time()
    # 获取待处理文件
    if last_processed_created_at:
        files_to_process = await VectorFile.filter(
            (
                Q(file_status=VectorFileStatus.PROCESS)
                | Q(file_status=VectorFileStatus.FAIL)
                | Q(file_status=VectorFileStatus.DELETED)
                | Q(deleted_at__isnull=False)
            ),
            created_at__lte=datetime(2024, 6, 10),
            created_at__gt=last_processed_created_at,
        ).order_by("created_at")
    else:
        files_to_process = await VectorFile.filter(
            (
                Q(file_status=VectorFileStatus.PROCESS)
                | Q(file_status=VectorFileStatus.FAIL)
                | Q(file_status=VectorFileStatus.DELETED)
                | Q(deleted_at__isnull=False)
            ),
            created_at__lte=datetime(2024, 6, 10),
        ).order_by("created_at")
    loguru.logger.info(f"total: {len(files_to_process)}, time: {time.time() - t0}")
    for index, file in enumerate(files_to_process):
        file_id_str = str(file.id)
        try:
            t0 = time.time()
            res = await async_opensearch.delete_by_query(
                index=str(file.dataset_id),
                body={"query": {"term": {"metadata.file_id": file_id_str}}},
            )
            loguru.logger.info(f"es del {index}: {res}, time: {time.time() - t0}")
        except Exception as e:
            loguru.logger.error(
                f"es del {index} err: {traceback.format_exc()}, time: {time.time() - t0}"
            )

        try:
            # search_results = await async_qdrant_client.search(
            #     collection_name="gptbase_local_bce",
            #     query_vector=[1.0] * 768,
            #     query_filter=Filter(
            #         must=[FieldCondition(key='metadata.file_id', match=MatchValue(value=file_id_str))]
            #     ),
            #     limit=1,  # 只需要知道数量，可以设置较小的 limit
            #     with_payload=False,  # 不需要实际的向量数据，只需要统计数量
            #     with_vectors=False
            # )
            # if search_results:
            #     vector_count = search_results['result']['count']
            #     loguru.logger.info(f"qdrant index count {index}: {vector_count}")
            t0 = time.time()
            res = await async_qdrant_client.delete(
                collection_name="gptbase_local_bce",
                points_selector=Filter(
                    must=[
                        FieldCondition(
                            key="metadata.file_id", match=MatchValue(value=file_id_str)
                        )
                    ]
                ),
            )
            loguru.logger.info(f"qdrant del {index}: {res}, time: {time.time() - t0}")
        except Exception as e:
            loguru.logger.error(
                f"qdrant del {index} err: {traceback.format_exc()}, time: {time.time() - t0}"
            )
        with open(LAST_PROCESSED_FILE, "w") as f:
            f.write(file.created_at.isoformat())


# 初始化 Tortoise-ORM
async def init():
    await Tortoise.init(config=TORTOISE_ORM)


# 运行代码
if settings.IS_USE_LOCAL_VLLM:
    import asyncio

    asyncio.run(init())
    asyncio.run(cleanup())
