"""
Load docs from database into the `faq_questions` index.
Because OpenSearch does not support inference,
language detection needs to be handled in Python.
Therefore, can not use the OpenSearch to automatically reindex.
"""

import asyncio

from opensearchpy.helpers import bulk
from tortoise import Tortoise

from mygpt.indices.faq_questions import FAQ_QUESTIONS_INDEX
from mygpt.models import Faqs
from mygpt.opensearch_faq import OpenSearchFaqClient
from mygpt.settings import TORTOISE_ORM, OPENSEARCH_URL


class LoadFaqQuestionsIndexTask:
    def __init__(self):
        self.batch_size = 100
        self.index = FAQ_QUESTIONS_INDEX
        self.faq_client = OpenSearchFaqClient(server_url=OPENSEARCH_URL)

    def insert_indices(self, faqs):
        batch_document_bodies = []
        for faq in faqs:
            document_body = self.faq_client.build_document_body(
                faq.dataset_id, faq.id, faq.question
            )
            batch_document_bodies.append(document_body)
            try:
                if faq.similar_questions:
                    for idx, similar_question in enumerate(faq.similar_questions):
                        document_body = self.faq_client.build_document_body(
                            faq.dataset_id,
                            faq.similar_questions_ids[idx],
                            similar_question,
                        )
                        batch_document_bodies.append(document_body)
            except Exception as e:
                print(f"Failed to build similar questions for faq: {faq.id}\n{e}")

        result = bulk(self.faq_client.os_client, batch_document_bodies)
        return result[0]

    async def load(self):
        if not self.faq_client.get_index():
            self.faq_client.os_client.indices.create(
                index=self.index.name, body=FAQ_QUESTIONS_INDEX.schema
            )

        index_count = 0
        offset = 0
        while True:
            faqs = await (
                Faqs.filter(deleted_at__isnull=True)
                .filter(is_search=True)
                .order_by("id")
                .offset(offset)
                .limit(self.batch_size)
            )
            if len(faqs) == 0:
                break

            index_count += self.insert_indices(faqs)
            offset += self.batch_size

        return index_count


async def main():
    await Tortoise.init(config=TORTOISE_ORM)
    index_count = await LoadFaqQuestionsIndexTask().load()
    print(
        "=" * 20,
        f"Loaded {index_count} docs to index `faq_questions`",
        "=" * 20,
    )


if __name__ == "__main__":
    print(
        "=" * 20,
        "Load faq_questions index task",
        "=" * 20,
    )
    asyncio.run(main())
