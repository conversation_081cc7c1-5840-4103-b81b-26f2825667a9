import asyncio
import json
import os
import random
import tempfile
import time
from datetime import datetime, timedelta
from io import By<PERSON><PERSON>
from typing import <PERSON>ple
from urllib.parse import urlparse
from uuid import uuid4

import aiohttp
import httpx
import numpy as np
import openpyxl
from aiohttp import FormData
from celery import Celery
from celery.schedules import crontab
from loguru import logger as logging
from openpyxl.styles import Font
from openpyxl.utils import column_index_from_string, get_column_letter
from openpyxl.utils.exceptions import InvalidFileException
from pytz import timezone as pytz_timezone
from tortoise import Tortoise

from mygpt.enums import ExcelTestTaskStatus
from mygpt.models import ExcelTestTask, TimedExcelTestFile
from mygpt.settings import (
    CELERY_BROKER_URL,
    CELERY_BACKEND_URL,
    TORTOISE_ORM,
    STATISTICS_KEY,
)
from mygpt.utils import download_file_s3, upload_file_to_s3, download_file

celery_app = Celery(
    "mygpt.tasks.excel_test_task_cl",
    broker=CELERY_BROKER_URL,
    backend=CELERY_BACKEND_URL,
)

celery_app.conf.task_routes = {
    "mygpt.tasks.excel_test_task.*": {"queue": "excel_test_task_queue"},
}

celery_app.conf.beat_schedule = {
    # "test_excel_files_5AM": {
    #     "task": "mygpt.tasks.excel_test_task.test_timed_excel_files",
    #     "schedule": crontab(hour="5", minute="0"),
    #     "options": {"queue": "scheduled_tasks"},
    #     "kwargs": {"env": "PROD"},
    # },
    # "test_excel_files_6PM": {
    #     "task": "mygpt.tasks.excel_test_task.test_timed_excel_files",
    #     "schedule": crontab(hour="18", minute="0"),
    #     "options": {"queue": "scheduled_tasks"},
    #     "kwargs": {"env": "PROD"},
    # },
    # "test_excel_files_every_8PM": {
    #     "task": "mygpt.tasks.excel_test_task.test_timed_excel_files",
    #     "schedule": crontab(hour="20", minute="0"),
    #     "options": {"queue": "scheduled_tasks"},
    #     "kwargs": {"env": "TEST"},
    # },
    "statistic_task_9AM00": {
        "task": "mygpt.tasks.excel_test_task.message_statistics",
        "schedule": crontab(hour="9", minute="00"),
        "options": {"queue": "scheduled_tasks"},
        "kwargs": {"env": ""},
    },
}

celery_app.conf.worker_mingle = False

# celery_app.conf.timezone = "UTC+8"
celery_app.conf.timezone = "Asia/Shanghai"

# print("Initializing excel_test_task module")


# print("Registered tasks excel_test_task:", app.tasks)


async def connect_db() -> None:
    await Tortoise.init(config=TORTOISE_ORM)


async def disconnect_db() -> None:
    await Tortoise.close_connections()


async def wrap_db_ctx(func, *args, **kwargs) -> None:
    try:
        await connect_db()
        await func(*args, **kwargs)
    finally:
        await disconnect_db()


def async_to_sync(func, *args, **kwargs) -> None:
    asyncio.run(wrap_db_ctx(func, *args, **kwargs))


def _check_answer(expect_answer_keywords_str, answer):
    # Split the input into 'AND' groups
    and_groups = expect_answer_keywords_str.split("\n")
    # Convert answer to casefold for better language support
    answer_casefold = answer.casefold()

    for and_group in and_groups:
        # Split the 'AND' group into 'OR' keywords
        or_keywords = and_group.split("||")

        group_condition_met = False

        for keyword in or_keywords:
            keyword = keyword.strip().casefold()

            # 'NOT' condition
            if keyword.startswith("/="):
                not_keyword = keyword[2:]
                # If the 'NOT' keyword is found in the answer, return False immediately
                if not_keyword in answer_casefold:
                    return False
                else:
                    group_condition_met = True
                    break
            else:
                if keyword in answer_casefold:
                    group_condition_met = True
                    break

        if not group_condition_met:
            return False
    return True


if __name__ == "__main__":
    assert _check_answer("9899", "") is False
    assert _check_answer("/=周二", "周二可以上思想品德课。") is False
    assert (
        _check_answer("周二\n/=周三\n/=周四\n/=周五\n/=周一", "周二可以上思想品德课。")
        is True
    )
    assert _check_answer("/=Hello", "Hell") is True
    assert _check_answer("/=Hello", "Would Hello") is False
    assert _check_answer("/=Hello", "Would Hell") is True
    assert _check_answer("/=Hello", "Hello") is False
    assert _check_answer("Hello\nWorld", "Hello") is False
    assert _check_answer("Hello\nWorld", "Helloworld") is True
    print("pass")


def _transform_url(input_url: str) -> str:
    if "dev.gbase.ai" in input_url:
        base_url = "https://api-dev.gbase.ai/v1/question/"
    elif "gbase.ai" in input_url:
        base_url = "https://api.gbase.ai/v1/question/"
    else:
        raise ValueError("Invalid input URL")

    uuid = input_url.split("/")[-1]
    output_url = base_url + uuid
    return output_url


def _parse_s3_url(s3_url) -> Tuple[str, str]:
    """
    Parse an S3 URL and return a tuple containing the bucket name and the object key.

    Args:
        s3_url (str): The S3 URL to parse.
        f'https://{bucket_name}.s3.{AWS_S3_REGION}.amazonaws.com/{blob_s3_key}'

    Returns:
        tuple: A tuple containing the bucket name and the object key.
    """
    parsed_url = urlparse(s3_url)
    domain_parts = parsed_url.netloc.split(".")
    bucket_name = domain_parts[0]
    object_key = parsed_url.path.lstrip("/")
    return bucket_name, object_key


async def async_process_excel_test_task(task_id: str):
    logging.info("tid %s", task_id)
    excel_test_task = await ExcelTestTask.get(id=task_id)
    if excel_test_task is None:
        logging.error("celery excel task %s not found", task_id)
        return

    logging.info("celery excel task %s start %s", task_id, excel_test_task)
    input_file_url = excel_test_task.input_file_url
    s3_bucket, s3_key = _parse_s3_url(input_file_url)
    output_filename = s3_key.replace("input_", "output_", 1)

    logging.info("downloading: %s / %s", s3_key, s3_bucket)
    # Download the Excel file content
    file_content = await download_file_s3(s3_key, s3_bucket)
    logging.info("downloaded: %s / %s", s3_key, s3_bucket)

    if file_content is None:
        logging.error("Failed to download the Excel file")
        return
    # Load the workbook
    try:
        workbook = openpyxl.load_workbook(BytesIO(file_content))
    except InvalidFileException as e:
        excel_test_task.status = ExcelTestTaskStatus.FAILED
        await excel_test_task.save()
        logging.error("Invalid file format: %s", e)
        raise e
    except ValueError as e:
        excel_test_task.status = ExcelTestTaskStatus.FAILED
        await excel_test_task.save()
        logging.error("failed to load file 1: %s", e)
        raise e
    except Exception as e:
        excel_test_task.status = ExcelTestTaskStatus.FAILED
        await excel_test_task.save()
        logging.error("failed to load file 2: %s", e)
        raise e

    # Get the active worksheet (assuming data is in the first first_sheet)
    first_sheet = workbook.active

    # 在工作簿中创建一个新的工作表
    no_history_sheet = workbook.create_sheet("no-history")

    # 复制第一个工作表的所有内容到新的工作表
    for row in first_sheet.iter_rows():
        for cell in row:
            new_cell = no_history_sheet.cell(
                row=cell.row, column=cell.column, value=cell.value
            )
            if cell.has_style:
                new_cell.font = cell.font.copy()
                new_cell.border = cell.border.copy()
                new_cell.fill = cell.fill.copy()
                new_cell.number_format = cell.number_format
                new_cell.protection = cell.protection.copy()
                new_cell.alignment = cell.alignment.copy()

    # await process_sheet(excel_test_task, first_sheet, task_id, single_session=True)
    await process_sheet(
        excel_test_task, no_history_sheet, task_id, single_session=False
    )

    # Save the processed Excel file to a local temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx")
    workbook.save(temp_file.name)
    temp_file.close()

    # Upload the processed Excel file back to S3
    with open(temp_file.name, "rb") as f:
        output_file_url = await upload_file_to_s3(
            output_filename,
            f,
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.first_sheet",
            bucket_name=s3_bucket,
        )

    # Delete the local temporary file
    os.remove(temp_file.name)

    # Update the task status in the database
    excel_test_task.output_file_url = output_file_url
    excel_test_task.status = ExcelTestTaskStatus.FINISHED
    await excel_test_task.save()


async def process_sheet(excel_test_task, sheet, task_id, single_session):
    # 读取文件
    cols = {
        "ai",
        "question",
        "expect_answer_keywords",
        "real_answer",
        "result",
        "use_faq",
        "delay_of_first_char",
        "response_time",
    }
    # Get the index of columns by their names
    column_indices = {
        cell.value: cell.column for cell in sheet[1] if cell.value in cols
    }
    # Add missing columns
    for col_name in cols:
        if col_name not in column_indices:
            # Find the first empty column index
            first_empty_col_idx = sheet.max_column + 1
            # Insert a new column at the first empty column index
            sheet.insert_cols(first_empty_col_idx)

            # Add the new column header
            new_col_letter = get_column_letter(first_empty_col_idx)
            sheet[f"{new_col_letter}1"] = col_name

            # Update the column_indices dictionary
            column_indices[col_name] = new_col_letter
    # Get new index of columns by their names after inserted
    column_indices = {
        cell.value: cell.column for cell in sheet[1] if cell.value in cols
    }
    # Convert column letters to column indices (if needed)
    for col_name in column_indices.keys():
        if col_name in column_indices and isinstance(column_indices[col_name], str):
            column_indices[col_name] = column_index_from_string(
                column_indices[col_name]
            )
    cnt = 0
    for row in sheet.iter_rows(min_row=2):
        ai = row[column_indices["ai"] - 1].value
        question = row[column_indices["question"] - 1].value
        expect_answer_keywords = row[column_indices["expect_answer_keywords"] - 1].value
        if not ai or not question or not expect_answer_keywords:
            continue
        cnt += 1
    # 2 sheets
    task_total_count = excel_test_task.total_count
    task_total_count += cnt
    excel_test_task.total_count = task_total_count
    await excel_test_task.save()
    task_processed_count = excel_test_task.processed_count
    task_pass_count = excel_test_task.pass_count
    task_fail_count = excel_test_task.fail_count
    red_font = Font(color="FF0000")
    green_font = Font(color="00FF00")
    sheet_total_count = 0
    sheet_pass_count = 0
    sheet_fail_count = 0
    response_time_sum = 0
    sum_delay_of_first_char = 0
    session_map = {}
    first_char_delay_list = []
    async with httpx.AsyncClient(timeout=120) as client:
        excel_test_task.status = ExcelTestTaskStatus.PROCESSING
        await excel_test_task.save()
        # test without session
        for row in sheet.iter_rows(min_row=2):
            if task_processed_count % 10 == 0:
                logging.info(f"processed row {task_processed_count}")
            # Extract cell values by column name
            ai = row[column_indices["ai"] - 1].value
            question = row[column_indices["question"] - 1].value
            expect_answer_keywords = row[
                column_indices["expect_answer_keywords"] - 1
            ].value
            use_faq = (
                False
                if str(row[column_indices["use_faq"] - 1].value).strip().lower()
                == "false"
                else True
            )
            row[column_indices["use_faq"] - 1].value = use_faq
            # session_id = row[column_indices['session_id'] - 1].value
            if not ai or not question or not expect_answer_keywords:
                logging.info(
                    f"ai or question or expect_answer_keywords is None {ai} {question} {expect_answer_keywords}"
                )
                continue
            if single_session:
                session_id_key = f"{ai}"
                if session_map.get(session_id_key) == None:
                    session_map[session_id_key] = (
                        "excel_test_task" + "__" + str(uuid4())
                    )
                session_id = session_map[session_id_key]
            else:
                session_id = str(uuid4())
            url = _transform_url(ai)
            headers = {
                "Content-Type": "application/json",
            }
            data = {
                "session_id": f"{session_id}",
                "stream": True,
                "simple_question": True,
                "with_source": True,
                "question": question,
                "use_faq": use_faq,
                "is_test": True,
            }

            MAX_RETRIES = 5
            BACKOFF_FACTOR = 2
            JITTER_FACTOR = 0.2
            INITIAL_SLEEP_TIME = 30

            i = 0
            while i <= MAX_RETRIES:
                fail = False
                # Record the start time using perf_counter
                start_time = time.perf_counter()
                try:
                    async with client.stream(
                        "POST", url, headers=headers, json=data
                    ) as response:
                        first_char_received = False
                        answer = ""
                        async for chunk in response.aiter_bytes():
                            if not first_char_received:
                                first_char_received_time = time.perf_counter()
                                first_char_delay = first_char_received_time - start_time

                                # Write the first character delay to the delay_of_first_char field with one decimal place precision
                                row[column_indices["delay_of_first_char"] - 1].value = (
                                    f"{first_char_delay:.1f}"
                                )
                                first_char_received = True

                            # real answer
                            answer += chunk.decode("utf-8")
                        response_time = time.perf_counter() - start_time
                        # total response time
                        row[column_indices["response_time"] - 1].value = (
                            f"{response_time:.1f}"
                        )
                        break
                except Exception as e:
                    print("e", e)
                    logging.info(f"error happened {e}")
                    i += 1
                    sleep_time = INITIAL_SLEEP_TIME * (BACKOFF_FACTOR ** (i - 1))

                    # Adding jitter to sleep time
                    jitter = sleep_time * JITTER_FACTOR
                    sleep_time = sleep_time + random.uniform(-jitter, jitter)

                    await asyncio.sleep(sleep_time)
                    fail = True
            if fail:
                task_fail_count += 1
                sheet_fail_count += 1
                task_processed_count += 1
                excel_test_task.pass_count = task_pass_count
                excel_test_task.fail_count = task_fail_count
                excel_test_task.processed_count = task_processed_count
                await excel_test_task.save()
                continue

            row[column_indices["real_answer"] - 1].value = answer
            expect_answer_keywords_str = str(expect_answer_keywords)
            if _check_answer(
                expect_answer_keywords_str=expect_answer_keywords_str, answer=answer
            ):
                sheet_pass_count += 1
                task_pass_count += 1
                row[column_indices["result"] - 1].value = "pass"
                row[column_indices["result"] - 1].font = green_font
            else:
                row[column_indices["result"] - 1].value = "fail"
                row[column_indices["result"] - 1].font = red_font
                sheet_fail_count += 1
                task_fail_count += 1
            sheet_total_count += 1
            sum_delay_of_first_char += first_char_delay
            first_char_delay_list.append(first_char_delay)
            task_processed_count += 1
            response_time = float(row[column_indices["response_time"] - 1].value)
            if not response_time:
                logging.error("too many err retries? no response_time")
                response_time = response_time_sum / task_processed_count
            response_time_sum += float(response_time)
            excel_test_task.pass_count = task_pass_count
            excel_test_task.fail_count = task_fail_count
            excel_test_task.processed_count = task_processed_count
            await excel_test_task.save()
    logging.info("process excel finished, total: %s", sheet_total_count)
    average_response_time = (
        response_time_sum / sheet_total_count if sheet_total_count > 0 else 0
    )
    average_delay_of_first_char = (
        sum_delay_of_first_char / sheet_total_count if sheet_total_count > 0 else 0
    )
    excel_test_task.average_response_time = average_delay_of_first_char
    excel_test_task.median_response_time = np.median(first_char_delay_list)
    # Append the results to the end of the worksheet
    # Find the first empty row index
    first_empty_row_idx = sheet.max_row + 1
    for idx, row_cells in enumerate(sheet.iter_rows(), start=1):
        if all(cell.value is None for cell in row_cells) and all(
            cell.value is None
            for cells in sheet.iter_rows(min_row=idx + 1)
            for cell in cells
        ):
            first_empty_row_idx = idx
            break
    results_row = first_empty_row_idx
    sheet.cell(row=results_row, column=1, value="Pass:")
    sheet.cell(row=results_row, column=2, value=sheet_pass_count).font = green_font
    results_row += 1
    sheet.cell(row=results_row, column=1, value="Fail:")
    sheet.cell(row=results_row, column=2, value=sheet_fail_count).font = red_font
    results_row += 1
    sheet.cell(row=results_row, column=1, value="Total:")
    sheet.cell(row=results_row, column=2, value=sheet_total_count)
    results_row += 1
    sheet.cell(row=results_row, column=1, value="Average response time:")
    sheet.cell(row=results_row, column=2, value=f"{average_response_time:.1f}")
    results_row += 1
    sheet.cell(row=results_row, column=1, value="Average delay of first char:")
    sheet.cell(row=results_row, column=2, value=f"{average_delay_of_first_char:.1f}")
    results_row += 1
    sheet.cell(row=results_row, column=1, value="Median delay of first char:")
    sheet.cell(
        row=results_row, column=2, value=f"{excel_test_task.median_response_time:.1f}"
    )
    results_row += 1
    sheet.cell(row=results_row, column=1, value="Pass rate:")
    p_rate = (
        0 if sheet_total_count == 0 else sheet_pass_count / sheet_total_count
    ) * 100
    sheet.cell(row=results_row, column=2, value=f"{p_rate:.1f}%")
    results_row += 1
    excel_test_task = await ExcelTestTask.get(id=task_id)
    # Get the submit and finish times from the ExcelTestTask object
    submit_time = excel_test_task.created_at
    finish_time = excel_test_task.updated_at
    # Convert the times to Beijing and Tokyo time zones
    bj_tz = pytz_timezone("Asia/Shanghai")
    tk_tz = pytz_timezone("Asia/Tokyo")
    submit_time_bj = submit_time.astimezone(bj_tz)
    submit_time_tk = submit_time.astimezone(tk_tz)
    finish_time_bj = finish_time.astimezone(bj_tz)
    finish_time_tk = finish_time.astimezone(tk_tz)
    # Format the times as desired (e.g., 'YYYY-MM-DD HH:mm:ss')
    time_format = "%Y-%m-%d %H:%M:%S"
    submit_time_bj_str = submit_time_bj.strftime(time_format)
    submit_time_tk_str = submit_time_tk.strftime(time_format)
    finish_time_bj_str = finish_time_bj.strftime(time_format)
    finish_time_tk_str = finish_time_tk.strftime(time_format)
    # Write the times to the appropriate cells
    sheet.cell(row=results_row, column=1, value="Test submit time (hk/tk):")
    sheet.cell(row=results_row, column=2, value=submit_time_bj_str)
    sheet.cell(row=results_row, column=3, value=submit_time_tk_str)
    results_row += 1
    sheet.cell(row=results_row, column=1, value="Test completion time (hk/tk):")
    sheet.cell(row=results_row, column=2, value=finish_time_bj_str)
    sheet.cell(row=results_row, column=3, value=finish_time_tk_str)
    for column_cells in sheet.columns:
        length = max(len(str(cell.value)) for cell in column_cells)
        sheet.column_dimensions[column_cells[0].column_letter].width = length + 1


@celery_app.task(bind=True)
def process_excel_test(task, task_id: str):
    logging.info("--------------Starting process_excel_test_task %s %s", task_id, task)
    async_to_sync(async_process_excel_test_task, task_id=task_id)
    # asyncio.run(async_process_excel_test_task(task_id))
    logging.info("--------------Finished process_excel_test_task %s", task_id)


@celery_app.task(bind=True)
def test_timed_excel_files(self, env, send_message_to_lark=True):
    logging.info("---------test_timed_excel_files--------------%s", self)
    async_to_sync(
        async_test_timed_excel_files, env=env, send_message_to_lark=send_message_to_lark
    )
    logging.info("---------test_timed_excel_files--------------finish")


async def async_test_timed_excel_files(env, send_message_to_lark: bool = True):
    file = (
        await TimedExcelTestFile.all()
        .filter(deleted_at=None, env=env)
        .order_by("-created_at")
        .first()
    )
    if not file:
        logging.warning("not file")
        return

    excel_test_task = await ExcelTestTask.create(is_scheduled=True)
    excel_test_task.input_file_url = file.s3_file_url

    file_content = await download_file(file.s3_file_url)

    if file_content is None:
        logging.error("Failed to download the Excel file")
        # TODO send fail message
        return
    # Load the workbook
    try:
        workbook = openpyxl.load_workbook(BytesIO(file_content))
    except InvalidFileException as e:
        excel_test_task.status = ExcelTestTaskStatus.FAILED
        await excel_test_task.save()
        logging.error("Invalid file format: %s", e)
        raise e
    except ValueError as e:
        excel_test_task.status = ExcelTestTaskStatus.FAILED
        await excel_test_task.save()
        logging.error("failed to load file 1: %s", e)
        raise e
    except Exception as e:
        excel_test_task.status = ExcelTestTaskStatus.FAILED
        await excel_test_task.save()
        logging.error("failed to load file 2: %s", e)
        raise e

    # Get the active worksheet (assuming data is in the first first_sheet)
    first_sheet = workbook.active

    # 在工作簿中创建一个新的工作表
    no_history_sheet = workbook.create_sheet("no-history")

    # 复制第一个工作表的所有内容到新的工作表
    for row in first_sheet.iter_rows():
        for cell in row:
            new_cell = no_history_sheet.cell(
                row=cell.row, column=cell.column, value=cell.value
            )
            if cell.has_style:
                new_cell.font = cell.font.copy()
                new_cell.border = cell.border.copy()
                new_cell.fill = cell.fill.copy()
                new_cell.number_format = cell.number_format
                new_cell.protection = cell.protection.copy()
                new_cell.alignment = cell.alignment.copy()

    await process_sheet(
        excel_test_task, first_sheet, excel_test_task.id, single_session=False
    )
    # await process_sheet(
    #     excel_test_task, no_history_sheet, excel_test_task.id, single_session=True
    # )

    # Save the processed Excel file to a local temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx")
    workbook.save(temp_file.name)
    temp_file.close()

    input_file_url = excel_test_task.input_file_url
    s3_bucket, s3_key = _parse_s3_url(input_file_url)

    logging.info("downloading: %s / %s", s3_key, s3_bucket)
    # Download the Excel file content
    file_content = await download_file_s3(s3_key, s3_bucket)
    logging.info("downloaded: %s / %s", s3_key, s3_bucket)

    output_filename = (
        "output__" + datetime.now().strftime("%Y%m%d-%H%M%S-%f") + "__" + s3_key
    )

    # Upload the processed Excel file back to S3
    with open(temp_file.name, "rb") as f:
        output_file_url = await upload_file_to_s3(
            output_filename,
            f,
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.first_sheet",
            bucket_name=s3_bucket,
        )

    # Delete the local temporary file
    os.remove(temp_file.name)

    # Update the task status in the database
    excel_test_task.output_file_url = output_file_url
    excel_test_task.status = ExcelTestTaskStatus.FINISHED
    await excel_test_task.save()

    message_url = "https://open.larksuite.com/open-apis/bot/v2/hook/380feb82-4743-49f7-b877-68d60eb5494b"
    headers = {"Content-Type": "application/json"}
    # 任务id: {excel_test_task.id}
    # 测试文件名: {file.name}
    text_content = f"""\
测试结果
    环境：{env}
    任务id：{excel_test_task.id}
    测试输入文件url: {excel_test_task.input_file_url}
    测试输出文件url: {excel_test_task.output_file_url}
    测试用例总数: {excel_test_task.total_count}
    通过: {excel_test_task.pass_count}
    失败: {excel_test_task.fail_count}
    平均首字符响应时间: {excel_test_task.average_response_time}
    中位数首字符响应时间: {excel_test_task.median_response_time}
    测试通过率: {excel_test_task.pass_count / excel_test_task.total_count * 100}%
    """
    text_data = {"msg_type": "text", "content": {"text": text_content}}

    async def send_request(url, headers, data):
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url, headers=headers, data=json.dumps(data)
            ) as response:
                print(await response.text())

    async def upload():
        url = "https://open.larksuite.com/open-apis/im/v1/files"
        form = FormData()
        form.add_field("file_type", "stream")
        form.add_field("file_name", "text.txt")
        form.add_field(
            "file",
            open("path/text.txt", "rb"),
            filename="text.txt",
            content_type="text/plain",
        )

        headers = {
            "Authorization": "Bearer xxx",  # 获取tenant_access_token, 需要替换为实际的token
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, data=form) as response:
                print(response.headers["X-Tt-Logid"])  # for debug or oncall
                print(await response.text())  # Print Response

    # Press the green button in the gutter to run the script.
    if send_message_to_lark:
        await send_request(url=message_url, headers=headers, data=text_data)
    else:
        logging.info(
            text_content
        )  # file_data = {"msg_type": "file", "content": {"text": text_content}}  # await send_request(url=message_url, headers=headers, data=file_data)  # await upload()


@celery_app.task(bind=True)
def message_statistics(self, env, send_message_to_lark=True):
    logging.info("---------message_statistics--------------%s", self)
    # 获取昨天年月日
    yesterday = datetime.today() - timedelta(days=1)
    logging.info(yesterday.strftime("%Y-%m-%d"))
    async_to_sync(
        async_message_statistics,
        start_date=yesterday.strftime("%Y-%m-%d"),
        end_date=yesterday.strftime("%Y-%m-%d"),
        send_message_to_lark=send_message_to_lark,
    )
    logging.info("---------message_statistics--------------finish")


async def async_message_statistics(
    start_date, end_date, send_message_to_lark: bool = True
):
    # 发送http请求
    url = "https://api.gbase.ai" + "/session/metrics/statistics"
    query = {
        "start_date": start_date,
        "end_date": end_date,
        "lark_send": send_message_to_lark,
        "key": STATISTICS_KEY,
    }
    async with httpx.AsyncClient(timeout=120) as client:
        response = await client.get(url, params=query)
        logging.info(response.text)
        if response.status_code == 200:
            logging.info("success")
        else:
            logging.error("fail")
