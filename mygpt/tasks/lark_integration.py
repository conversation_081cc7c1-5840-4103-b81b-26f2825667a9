import asyncio
import os
import shutil
import traceback
from datetime import datetime, timezone
from uuid import uuid4

import aiohttp
import psutil
import redis
from billiard.exceptions import Terminated
from celery import Celery
from celery.exceptions import MaxRetriesExceededError, TaskRevokedError, WorkerTerminate
from celery.exceptions import SoftTimeLimitExceeded
from loguru import logger as logging
from tortoise import Tortoise
from tortoise.transactions import in_transaction

from mygpt import settings
from mygpt.constant import lark_export_file_extension_dict
from mygpt.core.embedding import file_embeddings
from mygpt.enums import VectorStorageType, IntegrationRuleFileListSyncingStatus
from mygpt.lark_utils import (
    download_single_file,
    fetch_lark_file_list,
    export_and_download_single_file,
    FileInfo,
)
from mygpt.loader.document import DocumentLoader
from mygpt.loader.enums import filename_to_unstructured_type, UnstructuredType
from mygpt.loader.service import text_to_pdf
from mygpt.loader.split import split_pdf_file
from mygpt.models import (
    LarkFile,
    LarkIntegrationRule,
    VectorFile,
    VectorFileSourceType,
    VectorFileStatus,
    VectorFileType,
    IntegrationRule,
    IntegrationRuleVectorFile,
)
from mygpt.pydantic_rules import LarkShareUrlInput
from mygpt.settings import MAX_FILE_SIZE
from mygpt.utils import upload_binary, get_file_tmp_location

# 创建 Celery 应用
celery_app = Celery(
    "lark_integration_processing",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_BACKEND_URL,
)

TIME_LIMIT_SECONDS = 6 * 60 * 60
MAX_RETRIES = 2

# celery_app.conf.task_routes = {
#     'process_lark_file': {
#         'queue': 'lark_file.123'
#     }
# }

celery_app.conf.update(
    broker_heartbeat=0,
    broker_pool_limit=None,
    broker_transport_options={
        "visibility_timeout": TIME_LIMIT_SECONDS * (MAX_RETRIES + 1) + 3600
    },
    result_expires=48 * 3600,
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    broker_connection_retry_on_startup=True,  # 处理 Celery 6.0 警告
)


# @signals.worker_ready.connect
# def setup_consumers(signal, sender, **kwargs):
#     worker_name = sender.hostname  # correct ?
#     logging.info("Worker is set up. Setting up consumers.")
#     if worker_name != "celery@lark_file_worker":
#         logging.info(f"sender: {sender}, no add consumers")
#         return
#     logging.info(f"Setting up consumers for worker: {sender}")
#     try:
#         add_existing_consumers(worker_name)
#         # 验证队列是否成功绑定
#         inspector = celery_app.control.inspect([worker_name])
#         active_queues = inspector.active_queues()
#         logging.info(f"Active queues after add existing consumers: {active_queues}")
#         if active_queues and worker_name in active_queues:
#             logging.info(f"Active queues after setup: {active_queues[worker_name]}")
#         else:
#             logging.error("Failed to verify active queues")
#     except Exception as e:
#         logging.error(f"Failed to start add_existing_consumers: {e}")
#
#
def add_existing_consumers(worker_name: str):
    logging.info("add_existing_consumers Setting up consumers 2")
    try:
        logging.info("Connecting to Redis...")
        redis_client = redis.Redis.from_url(settings.CELERY_BROKER_URL)

        # 使用 SCAN 命令避免 KEYS 命令的性能问题
        cursor = 0
        queue_pattern = "lark_file.*"
        queue_names = set()

        logging.info("Starting SCAN for queues...")
        while True:
            cursor, keys = redis_client.scan(
                cursor=cursor, match=queue_pattern, count=100
            )
            for key in keys:
                key_str = key.decode("utf-8")
                # 验证队列是否存在且包含任务
                queue_length = redis_client.llen(key_str)
                logging.info(f"Queue {key_str} length: {queue_length}")
                if queue_length > 0:
                    queue_names.add(key_str)
            if cursor == 0:
                logging.info("Completed SCAN for queues.")
                break

        logging.info(f"Found {len(queue_names)} queues: {queue_names}")

        # process_lark_file.apply_async(
        #     args=["test"],
        #     queue="lark_file.123",
        # )

        for queue_name in queue_names:
            try:
                logging.info(
                    f"Adding consumer for queue: {queue_name} to worker: {worker_name}"
                )
                reply = celery_app.control.add_consumer(
                    queue=queue_name, destination=[worker_name], reply=True
                )
                if reply:
                    logging.info(
                        f"Successfully added consumer for {queue_name}: {reply}"
                    )
                else:
                    logging.warning(
                        f"Failed to add consumer for {queue_name}: empty reply: {reply}"
                    )
            except Exception as e:
                logging.error(
                    f"Failed to add consumer for {queue_name}: {e}, {traceback.format_exc()}"
                )

        redis_client.close()
        logging.info("Redis connection closed.")

    except Exception as e:
        logging.error(f"Error in add_existing_consumers: {e}, {traceback.format_exc()}")


tortoise_initialized = False  # 全局变量，标记是否已初始化


def log_resource_usage(task_name: str):
    process = psutil.Process()
    mem_info = process.memory_info()
    if os.name == "nt":
        fd_count = len(process.open_files())
    else:
        fd_count = process.num_fds()  # 打开的文件描述符数量

    logging.info(
        f"""{task_name} resource usage:
    Memory usage: {mem_info.rss / 1024 / 1024:.2f} MB
    File descriptors: {fd_count}
    """
    )


async def connect_db() -> None:
    await Tortoise.init(config=settings.TORTOISE_ORM)


async def disconnect_db() -> None:
    await Tortoise.close_connections()


async def wrap_db_ctx(func, *args, **kwargs) -> None:
    try:
        await connect_db()
        await func(*args, **kwargs)
    finally:
        await disconnect_db()


# def async_to_sync(func, *args, **kwargs) -> None:
#     try:
#         loop = asyncio.get_event_loop()
#     except RuntimeError:
#         loop = asyncio.new_event_loop()
#         asyncio.set_event_loop(loop)
#     if loop.is_closed():
#         loop = asyncio.new_event_loop()
#         asyncio.set_event_loop(loop)

#     try:
#         return loop.run_until_complete(wrap_db_ctx(func, *args, **kwargs))
#     finally:
#         loop.close()
#         asyncio.set_event_loop(None)


def async_to_sync(func, *args, **kwargs):
    try:
        return asyncio.run(wrap_db_ctx(func, *args, **kwargs))
    except RuntimeError as e:
        if "cannot schedule new futures after interpreter shutdown" in str(e):
            # 处理解释器关闭时的特殊情况
            logging.warning("Caught interpreter shutdown error, handling gracefully")
            return None
        raise


def get_vector_file_name_for_lark_file(file_info: FileInfo):
    if file_info.type == "file":
        return file_info.name
    elif lark_export_file_extension_dict.get(file_info.type):
        return file_info.name + "." + lark_export_file_extension_dict[file_info.type]
    else:
        return file_info.name + "." + file_info.type


redis_client = redis.asyncio.Redis.from_url(settings.CELERY_BROKER_URL)

TASK_ID_KEY_PREFIX = "lark_file_task:"


async def store_task_id(vector_file_id: str, task_id: str):
    """存储任务 ID 到 Redis"""
    key = f"{TASK_ID_KEY_PREFIX}{vector_file_id}"
    await redis_client.set(key, task_id, ex=864000)  # 设置 240 小时过期时间


@celery_app.task(bind=True)
def process_lark_integration_rule(self, integration_rule_id: str):
    """
    新增lark_integration_rule，获取和储存文件列表，并分发实际学习任务到 celery@lark_file_worker
    """

    async def run():
        integration_rule = None
        try:
            log_resource_usage(
                "process_lark_integration_rule start"
            )  # 任务开始时记录资源使用

            # await init_tortoise()

            # Fetch the integration rule from the database
            lark_integration_rule = await LarkIntegrationRule.get(
                integration_rule_id=integration_rule_id
            ).select_related("integration_rule")
            if not lark_integration_rule:
                raise ValueError(f"IntegrationRule {integration_rule_id} not found")
            integration_rule = await IntegrationRule.get(id=integration_rule_id)
            integration_rule.file_list_sync_status = (
                IntegrationRuleFileListSyncingStatus.SYNCING
            )
            await integration_rule.save()
            # integration_rule = lark_integration_rule.integration_rule.first()
            # if integration_rule:
            # from mygpt.enums import IntegrationRuleFileListSyncingStatus

            # integration_rule.file = IntegrationRuleFileListSyncingStatus.SYNCING
            await lark_integration_rule.integration_rule.fetch_related(
                "dataset", "dataset__user"
            )
            dataset = lark_integration_rule.integration_rule.dataset
            if not dataset:
                raise ValueError(
                    f"Dataset not found for IntegrationRule {integration_rule_id}"
                )
            share_urls = await lark_integration_rule.share_urls.all()

            # Convert LarkShareUrl instances to LarkShareUrlInput instances
            share_url_inputs = [
                LarkShareUrlInput(url=share_url.url, recursive=share_url.recursive)
                for share_url in share_urls
            ]

            # Now pass the list of LarkShareUrlInput instances to the function
            lark_files = await fetch_lark_file_list(
                app_id=lark_integration_rule.app_id,
                app_secret=lark_integration_rule.app_secret,
                share_urls=share_url_inputs,
            )
            lark_files_filtered = []
            for file in lark_files:
                if file.type == "folder":
                    continue
                elif file.type in lark_export_file_extension_dict:
                    lark_files_filtered.append(file)
                elif file.type == "file":
                    if (
                        filename_to_unstructured_type(file.name)
                        != UnstructuredType.UNKNOWN
                    ):
                        lark_files_filtered.append(file)
                else:
                    logging.info(f"unsupported type: {file.type}")
            logging.info(
                f"get total lark files: {len(lark_files)}, after filter: {len(lark_files_filtered)}"
            )
            if lark_files_filtered:
                vector_files_data = []
                lark_files_data = []

                for file_info in lark_files_filtered:
                    vector_file_id = uuid4()

                    # Prepare VectorFile data
                    vector_files_data.append(
                        VectorFile(
                            id=vector_file_id,
                            dataset=dataset,
                            filename=get_vector_file_name_for_lark_file(file_info),
                            title=file_info.name,
                            file_type=VectorFileType.INTEGRATION,
                            source_type=VectorFileSourceType.LARK,
                            file_status=VectorFileStatus.READY,
                        )
                    )

                    # Prepare LarkFile data
                    lark_files_data.append(
                        LarkFile(
                            vector_file_id=vector_file_id,
                            name=file_info.name,
                            type=file_info.type,
                            token=file_info.token,
                            parent_token=file_info.parent_token,
                            created_time=file_info.created_time,
                            modified_time=file_info.modified_time,
                            owner_id=file_info.owner_id,
                            url=file_info.url,
                        )
                    )

                # Bulk create within a transaction
                async with in_transaction():
                    created_vector_files = await VectorFile.bulk_create(
                        vector_files_data
                    )
                    await LarkFile.bulk_create(lark_files_data)
                    integration_rule_vector_files = [
                        IntegrationRuleVectorFile(
                            integration_rule_id=integration_rule.id,
                            vectorfile_id=vector_file.id,
                        )
                        for vector_file in created_vector_files
                    ]
                    await IntegrationRuleVectorFile.bulk_create(
                        integration_rule_vector_files
                    )
                user_id = str(dataset.user.id)
                queue_name = f"lark_file.{user_id}"
                # routing_key = f"lark_file.{user_id}"
                reply = celery_app.control.add_consumer(
                    queue=queue_name,
                    reply=True,
                    destination=["celery@lark_file_worker"],
                )
                logging.info(f"add_consumer {queue_name=}; reply: {reply=}")
                for vector_file in vector_files_data:
                    task_result = process_lark_file.apply_async(
                        args=[str(vector_file.id), str(integration_rule_id)],
                        queue=queue_name,
                        # routing_key=routing_key,
                    )
                    try:
                        await store_task_id(str(vector_file.id), task_result.id)
                    except Exception as e:
                        logging.error(
                            f"Failed to store task id for {vector_file.id}: {e}; {traceback.format_exc()}"
                        )
            else:
                logging.warning(
                    f"No Lark files found for integration rule {integration_rule_id}"
                )
            integration_rule.file_list_sync_status = (
                IntegrationRuleFileListSyncingStatus.COMPLETED
            )
            await integration_rule.save()
        except (TaskRevokedError, WorkerTerminate, Terminated) as e:
            # 任务被撤销
            logging.info(
                f"Task {self.request.id}, {integration_rule_id=} was revoked: {e}, {traceback.format_exc()}"
            )
        except Exception as e:
            logging.error(
                f"Error processing Lark integration rule {integration_rule_id}: {e}, {traceback.format_exc()}"
            )
            log_resource_usage(
                "process_lark_integration_rule exception"
            )  # 任务开始时记录资源使用

            # try:
            #     await self.retry(exc=e, countdown=15)
            # except MaxRetriesExceededError:
            #     logging.error(
            #         f"Max retries exceeded for integration rule {integration_rule_id}"
            #     )
            if integration_rule:
                integration_rule.file_list_sync_status = (
                    IntegrationRuleFileListSyncingStatus.FAILED
                )
                await integration_rule.save()
            raise

    async_to_sync(run)


# @worker_process_shutdown.connect
# def shutdown(*args, **kwargs):
#     logging.info("Worker process shutdown")
#     pass


@celery_app.task(bind=True, max_retries=MAX_RETRIES, soft_time_limit=TIME_LIMIT_SECONDS)
def process_lark_file(self, vector_file_id: str, integration_rule_id: str):
    """
    params:
    vector_file_id: str, the vector file id to process
    integration_rule_id: str, the integration rule id triggers this task

    this task syncs the lark file data in db with remote one
    """

    async def run():
        # await init_tortoise()
        log_resource_usage("process_lark_file start")  # 任务开始时记录资源使用

        vector_file = None
        try:
            # Fetch data from the database
            vector_file = await VectorFile.get_or_none(
                id=vector_file_id, deleted_at__isnull=True
            )
            if not vector_file:
                raise ValueError(f"VectorFile {vector_file_id} not found")

            if vector_file.file_status == VectorFileStatus.COMPLETE:
                logging.info(
                    f"VectorFile {vector_file_id} already processed for integration rule {integration_rule_id}"
                )
                return

            lark_file = await LarkFile.get_or_none(
                vector_file_id=vector_file_id, deleted_at__isnull=True
            )
            if not lark_file:
                raise ValueError(f"LarkFile for VectorFile {vector_file_id} not found")

            dataset = await vector_file.dataset
            if not dataset:
                raise ValueError(f"Dataset for vector_file {vector_file.id} not found")

            integration_rule = await IntegrationRule.get(id=integration_rule_id)
            if not integration_rule:
                raise ValueError(
                    f"IntegrationRule not found for VectorFile {vector_file_id}"
                )
            lark_integration_rule = await LarkIntegrationRule.get(
                integration_rule_id=integration_rule.id, deleted_at__isnull=True
            )
            if not lark_integration_rule:
                raise ValueError(
                    f"LarkIntegrationRule not found for IntegrationRule {integration_rule.id}"
                )
            if lark_file.type == "file":
                file_bytes = await download_single_file(
                    lark_integration_rule.app_id,
                    lark_integration_rule.app_secret,
                    lark_file.token,
                )
            elif lark_file.type in lark_export_file_extension_dict:
                logging.info(
                    f"non file type: {lark_file.type}, {lark_file.name}, export_and_download"
                )
                file_bytes = await export_and_download_single_file(
                    lark_integration_rule.app_id,
                    lark_integration_rule.app_secret,
                    lark_file,
                )
            else:
                logging.error(f"unexpected unsupported type: {lark_file.type}")
                vector_file.file_status = VectorFileStatus.FAIL
                vector_file.failed_reason = (
                    f"Cannot export '{lark_file.type}' file '{lark_file.name}' as Lark only "
                    f"supports doc, sheet, bitable, and docx formats."
                )
                await vector_file.save()
                return

            if file_bytes is None:
                raise ValueError("Failed to download Lark file")

            if len(file_bytes) > MAX_FILE_SIZE:
                raise ValueError(
                    f"File {vector_file.filename} size {len(file_bytes)} exceeds the limit of {MAX_FILE_SIZE} bytes"
                )
            if len(file_bytes) == 0:
                raise ValueError(f"Empty file not allowed: {vector_file.filename}")
            vector_file.file_size = len(file_bytes)
            await vector_file.save()

            file_location = get_file_tmp_location(
                f"{vector_file_id}__{vector_file.filename}"
            )
            with open(file_location, "wb") as f:
                f.write(file_bytes)

            unstructured_type = filename_to_unstructured_type(vector_file.filename)
            if unstructured_type == UnstructuredType.UNKNOWN:
                raise ValueError(
                    f"File extension not supported for file {vector_file.filename}"
                )

            # Update file status to COMPLETE
            await VectorFile.filter(id=vector_file.id).update(
                file_status=VectorFileStatus.COMPLETE
            )
            # with open(file_location, "wb") as w_file:
            #     shutil.copyfileobj(file, w_file)
            name, suffix = os.path.splitext(vector_file.filename)
            unstructured_type = filename_to_unstructured_type(vector_file.filename)
            if unstructured_type == UnstructuredType.TXT:
                pdf_content = await text_to_pdf(file_location)
                file_location = get_file_tmp_location(f"{vector_file.id}__{name}.pdf")
                suffix = ".pdf"
                with open(file_location, "wb") as f:
                    f.write(pdf_content)
            upload_key = f"{vector_file.id.hex}{suffix}"
            try:
                await upload_binary(upload_key, file_location)
            except Exception as e:
                logging.warning(
                    f"fail to upload file {vector_file_id}, error:{e}; {traceback.format_exc()}"
                )
                vector_file.file_status = VectorFileStatus.FAIL
                vector_file.failed_reason = str(e)
                await vector_file.save()
                return
            await VectorFile.filter(id=vector_file.id).update(
                key=upload_key,
            )

            logging.info(f"process_file: {file_location}, {vector_file}")
            failed_reason = None
            filename = os.path.basename(file_location)
            unstructured_type = filename_to_unstructured_type(filename)
            child_files = [file_location]

            if unstructured_type == UnstructuredType.PDF:
                child_files = await asyncio.to_thread(split_pdf_file, file_location, 90)

            doc_id = str(uuid4())
            resources = []

            vector_file = await VectorFile.filter(
                id=vector_file.id, deleted_at__isnull=True
            ).first()
            if not vector_file:
                logging.warning(f"vector_file not found: {vector_file.id}")
                return

            vector_file.file_status = VectorFileStatus.PROCESS
            vector_file.updated_at = datetime.now(timezone.utc)
            await vector_file.save(update_fields=["file_status", "updated_at"])
            # while True:
            #     logging.info(f"process_lark_file: {lark_file} sleep 1s")
            #     await asyncio.sleep(1)
            for index, child_file in enumerate(child_files):
                try:
                    if unstructured_type == UnstructuredType.PDF:
                        os.remove(file_location)
                        shutil.move(child_file, file_location)

                    vector_file = await VectorFile.filter(
                        id=vector_file.id, deleted_at__isnull=True
                    ).first()
                    if not vector_file:
                        logging.warning(f"vector_file not found: {vector_file.id}")
                        break

                    document_loader = DocumentLoader(file_location)
                    async with aiohttp.ClientSession() as session:
                        unstructured_type = filename_to_unstructured_type(filename)
                        logging.info(
                            f"filename: {filename}, unstructured_type: {unstructured_type}"
                        )
                        documents = await document_loader.wait_for_load(
                            client=session, type=unstructured_type
                        )

                    for document in documents:
                        document.metadata["title"] = vector_file.filename
                        document_resources = document.metadata.get("resources", [])
                        resources.extend(document_resources)
                        if document_resources:
                            del document.metadata["resources"]

                    vector_file.resources = resources
                    await vector_file.save(update_fields=["resources"])

                    if len(documents) == 0:
                        failed_reason = "No content found in file."
                        vector_file.file_status = VectorFileStatus.FAIL
                        vector_file.failed_reason = failed_reason
                        vector_file.updated_at = datetime.now(timezone.utc)
                        await vector_file.save(
                            update_fields=[
                                "file_status",
                                "failed_reason",
                                "updated_at",
                            ]
                        )
                except Exception as e:
                    logging.error(
                        f"fail to upload file in error 111: {vector_file.filename}, {vector_file.file_type}, {e}, {traceback.format_exc()}"
                    )
                    vector_file = await VectorFile.filter(id=vector_file.id).first()
                    if not vector_file:
                        logging.warning(f"vector_file not found: {vector_file.id}")
                    else:
                        vector_file.file_status = VectorFileStatus.FAIL
                        vector_file.failed_reason = str(e)
                        vector_file.updated_at = datetime.now(timezone.utc)
                        await vector_file.save(
                            update_fields=[
                                "file_status",
                                "failed_reason",
                                "updated_at",
                            ]
                        )
                    break

                if not documents:
                    return

                # dataset_obj = await init_dataset(str(dataset_obj.id), documents)
                vector_storage_type = VectorStorageType(
                    dataset.metadata.get("vector_storage", VectorStorageType.PINECONE)
                )

                if vector_storage_type == VectorStorageType.QDRANT:
                    vector_file.file_status = VectorFileStatus.FAIL
                    vector_file.failed_reason = (
                        "Upgrade vector storage type to qdrant_one_collection"
                    )
                    vector_file.updated_at = datetime.now(timezone.utc)
                    await vector_file.save(
                        update_fields=["file_status", "failed_reason", "updated_at"]
                    )
                embedding_params = dataset.get_embedding_params()

                metadata = documents[0].metadata
                metadata["doc_id"] = doc_id
                vector_file = await file_embeddings(
                    vector_storage_type,
                    str(dataset.id),
                    dataset.collection_name,
                    str(vector_file.id),
                    documents,
                    metadata,
                    embedding_model=embedding_params.provider,
                    embedding_model_name=embedding_params.model_name,
                    embedding_dimensions=embedding_params.dimensions,
                    is_first=index == 0,
                    is_last=index == len(child_files) - 1,
                )
        except (TaskRevokedError, WorkerTerminate, Terminated) as e:
            # 任务被撤销
            logging.info(
                f"Task {self.request.id} was revoked, task canceled or terminated: {e}, {vector_file_id}, {traceback.format_exc()}"
            )
            if vector_file:
                vector_file.file_status = VectorFileStatus.FAIL
                vector_file.failed_reason = "task canceled or terminated"
                await vector_file.save()
        except SoftTimeLimitExceeded:
            logging.error(
                f"Task process_lark_file for VectorFile {vector_file_id} exceeded time limit."
            )
            if vector_file:
                vector_file.file_status = VectorFileStatus.FAIL
                vector_file.failed_reason = "exceeded process time limit"
                await vector_file.save()
        except ValueError as e:
            logging.error(
                f"Error processing file {vector_file_id}: {e}, {traceback.format_exc()}"
            )
            if vector_file:
                vector_file.file_status = VectorFileStatus.FAIL
                vector_file.failed_reason = str(e)
                await vector_file.save()
        except (KeyboardInterrupt, InterruptedError, SystemExit) as e:
            logging.error(
                f"Task process_lark_file for VectorFile {vector_file_id} was interrupted., {e}"
            )
            if vector_file:
                vector_file.file_status = VectorFileStatus.FAIL
                vector_file.failed_reason = "task interrupted"
                await vector_file.save()
            raise
        except (WorkerTerminate, Terminated) as e:
            logging.error(
                f"Task process_lark_file for VectorFile {vector_file_id} was terminated., {e}"
            )
            if vector_file:
                vector_file.file_status = VectorFileStatus.FAIL
                vector_file.failed_reason = "task terminated"
                await vector_file.save()
            raise
        except Exception as e:
            logging.error(f"Exception in process_file inner block: {e}")
            logging.error(traceback.format_exc())
            logging.error(
                f"Error processing file {vector_file_id}: {e}, {traceback.format_exc()}"
            )
            if vector_file:
                vector_file.file_status = VectorFileStatus.FAIL
                vector_file.failed_reason = str(e)
                await vector_file.save()
            try:
                await self.retry(exc=e, countdown=30)
            except MaxRetriesExceededError:
                # Update file status to FAILED
                if vector_file:
                    await VectorFile.filter(id=vector_file.id).update(
                        file_status=VectorFileStatus.FAIL, failed_reason=str(e)
                    )
                    logging.error(f"Max retries exceeded for file {vector_file_id}")
            raise
        except asyncio.CancelledError as e:
            logging.error(
                f"Task process_lark_file for VectorFile {vector_file_id} was cancelled: {e}, {traceback.format_exc()}"
            )
            if vector_file:
                vector_file.file_status = VectorFileStatus.FAIL
                vector_file.failed_reason = "task cancelled"
                await vector_file.save()
            raise
        finally:
            # if vector file status is not settled, set as failed
            if (
                vector_file
                and vector_file.file_status != VectorFileStatus.COMPLETE
                and vector_file.file_status != VectorFileStatus.FAIL
            ):
                logging.error(
                    f"finally: vector_file {vector_file_id} is still not settled, mark it as FAIL, reason: Unknown Error"
                )
                vector_file.file_status = VectorFileStatus.FAIL
                vector_file.failed_reason = "Unknown Error"
                await vector_file.save()
            log_resource_usage("process_lark_file final")  # 任务开始时记录资源使用

    async_to_sync(run)


@celery_app.task(bind=True, max_retries=MAX_RETRIES, soft_time_limit=TIME_LIMIT_SECONDS)
def delete_integration_rule_task(self, integration_rule_id: str):
    async def run():
        try:
            integration_rule = await IntegrationRule.get(id=integration_rule_id)
            if not integration_rule:
                raise ValueError(f"IntegrationRule {integration_rule_id} not found")

            lark_integration_rule = await LarkIntegrationRule.get(
                integration_rule_id=integration_rule_id
            )
            if not lark_integration_rule:
                raise ValueError(
                    f"LarkIntegrationRule for IntegrationRule {integration_rule_id} not found"
                )

            # Delete the LarkIntegrationRule
            await lark_integration_rule.delete()

            # Delete the IntegrationRule
            await integration_rule.delete()

            vector_files = await IntegrationRuleVectorFile.filter(
                integration_rule_id=integration_rule_id
            ).values("vectorfile_id")
        except Exception as e:
            logging.error(
                f"Error deleting integration rule {integration_rule_id}: {e}, {traceback.format_exc()}"
            )
            try:
                await self.retry(exc=e, countdown=30)
            except MaxRetriesExceededError:
                logging.error(
                    f"Max retries exceeded for delete_integration_rule_task {integration_rule_id}"
                )
            raise

    async_to_sync(run)


@celery_app.task(bind=True, max_retries=MAX_RETRIES, soft_time_limit=TIME_LIMIT_SECONDS)
def update_vector_file_and_its_index(self, vector_file_id: str):
    pass


@celery_app.task(bind=True, max_retries=MAX_RETRIES, soft_time_limit=TIME_LIMIT_SECONDS)
def delete_vector_file(self, vector_file_id: str, integration_rule_id: str):
    async def run():
        try:
            vector_file = await VectorFile.get(id=vector_file_id)
            if not vector_file:
                raise ValueError(f"VectorFile {vector_file_id} not found")

            # Delete the VectorFile
            await vector_file.delete()

            # Delete the LarkFile
            lark_file = await LarkFile.get(vector_file_id=vector_file_id)
            if lark_file:
                await lark_file.delete()

            # Delete the IntegrationRuleVectorFile
            await IntegrationRuleVectorFile.filter(
                integration_rule_id=integration_rule_id, vectorfile_id=vector_file_id
            ).delete()
        except Exception as e:
            logging.error(
                f"Error deleting vector file {vector_file_id}: {e}, {traceback.format_exc()}"
            )
            try:
                await self.retry(exc=e, countdown=30)
            except MaxRetriesExceededError:
                logging.error(
                    f"Max retries exceeded for delete_vector_file {vector_file_id}"
                )
            raise
