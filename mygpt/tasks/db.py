from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import String, DateTime, Text, JSON, Uuid, Column, Integer, func
from sqlalchemy.orm import sessionmaker
from sqlalchemy.orm import scoped_session
from sqlalchemy import create_engine

from mygpt.enums import VectorFileStatus

CONNECTION_STRING = "postgresql+psycopg2://postgres:postgres@localhost:5432/mygpt"


class Base(DeclarativeBase):
    pass


class Url(Base):
    __tablename__ = "url"
    id: Mapped[str] = mapped_column(primary_key=True)
    created_at: Mapped[str] = mapped_column(DateTime())
    url: Mapped[str] = mapped_column(String(255))
    status: Mapped[str] = mapped_column(String(20))
    failed_reason: Mapped[str] = mapped_column(Text())
    metadata_: Mapped[dict[str, any]] = mapped_column("metadata", JSON)
    robot_id: Mapped[str] = mapped_column(Uuid)

    def __repr__(self) -> str:
        return f"Url(id={self.id!r}, created_at={self.created_at!r}, url={self.url!r}, status={self.status!r}, failed_reason={self.failed_reason!r}, metadata={self.metadata_!r}, robot_id={self.robot_id!r})"


class VectorFile(Base):
    __tablename__ = "vectorfile"
    id: Mapped[str] = mapped_column(
        primary_key=True,
    )
    created_at: Mapped[str] = mapped_column(
        DateTime,
        server_default=func.now(),
    )
    updated_at: Mapped[str] = mapped_column(
        DateTime,
        server_default=func.now(),
        onupdate=func.now(),
    )
    robot_id: Mapped[str] = mapped_column(Uuid)
    key: Mapped[str] = mapped_column(String(100))
    filename: Mapped[str] = mapped_column(String(255))
    file_type: Mapped[str] = mapped_column(String(20))
    file_status: Mapped[str] = mapped_column(String(20))
    file_lang: Mapped[str] = mapped_column(String(50))
    failed_reason: Mapped[str] = mapped_column(Text)
    metadata_: Mapped[dict[str, any]] = Column("metadata", JSON)

    def __repr__(self) -> str:
        return f"VectorFile(id={self.id!r}, created_at={self.created_at!r}, robot_id={self.robot_id!r}, key={self.key!r}, filename={self.filename!r}, file_type={self.file_type!r}, file_status={self.file_status!r}, file_lang={self.file_lang!r}, failed_reason={self.failed_reason!r}, metadata={self.metadata_!r})"

    @classmethod
    def get_known_urls(cls, robot_id: str):
        urls = (
            db_session.query(VectorFile)
            .filter(
                VectorFile.robot_id == robot_id,
                VectorFile.file_status != VectorFileStatus.FAIL,
            )
            .all()
        )
        known_urls = set()
        for url in urls:
            known_urls.add(url.filename)
        return known_urls


class Vector(Base):
    __tablename__ = "vector"
    id: Mapped[str] = mapped_column(primary_key=True)
    created_at: Mapped[str] = mapped_column(DateTime)
    robot_id: Mapped[str] = mapped_column(Uuid)
    vector_text: Mapped[str] = mapped_column(Text)
    text_origin: Mapped[str] = mapped_column(Text)
    index_id: Mapped[str] = mapped_column(String(100))
    text_type: Mapped[str] = mapped_column(String(20))
    tokens: Mapped[str] = mapped_column(Integer)
    fingerprint: Mapped[str] = mapped_column(String(100))
    vector_file_id: Mapped[str] = mapped_column(Uuid)
    status: Mapped[str] = mapped_column(String(50))
    metadata_: Mapped[dict[str, any]] = Column("metadata", JSON)

    def __repr__(self) -> str:
        return f"Vector(id={self.id!r}, created_at={self.created_at!r}, robot_id={self.robot_id!r}, vector_text={self.vector_text!r}, text_origin={self.text_origin!r}, index_id={self.index_id!r}, text_type={self.text_type!r}, tokens={self.tokens!r}, fingerprint={self.fingerprint!r}, vector_file_id={self.vector_file_id!r}, status={self.status!r}, metadata={self.metadata_!r})"


engine = create_engine(CONNECTION_STRING, pool_recycle=3600, pool_size=10)
db_session = scoped_session(
    sessionmaker(autocommit=False, autoflush=False, bind=engine)
)


# user = db_session.query(VectorFile).filter(
#     VectorFile.id == 'aeabd780-1228-46b8-9a96-4c7c18456b20').one()
# print(user)
# user = db_session.query(Vector).filter(
#     Vector.vector_file_id == 'aeabd780-1228-46b8-9a96-4c7c18456b20').all()
# print(user)
# db_session.remove()
