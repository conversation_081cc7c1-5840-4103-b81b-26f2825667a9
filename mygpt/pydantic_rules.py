# 加个新文件解决循环依赖
# prevent circular dependencies between models.py and schemata.py
import re
from typing import List
from urllib.parse import urlparse

from pydantic import (
    BaseModel,
    HttpUrl,
    EmailStr,
    Field,
    root_validator,
    validator,
)


class LarkShareUrlInput(BaseModel):
    url: HttpUrl = Field(..., description="Lark file sharing link")
    recursive: bool = Field(
        ..., description="Whether to recursively fetch contents of subfolders"
    )
    token: str = Field(None, exclude=True)

    @validator("url", pre=True)
    def strip_url(cls, v):
        if isinstance(v, str):
            return v.strip()
        return v

    @root_validator
    def validate_and_extract_token(cls, values):
        url = values.get("url")
        if not url:
            raise ValueError("URL is required")

        parsed_url = urlparse(url)
        path = parsed_url.path

        match = re.match(r"^.*/(wiki|drive/folder)/([^/]+)", path)
        if match:
            token = match.group(2)
            values["token"] = token  # Attach the extracted token to the model
        else:
            raise ValueError(
                "URL must be in the format .../wiki/{token} or .../drive/folder/{token}"
            )

        return values


class LarkIntegrationRuleInput(BaseModel):
    name: str = Field(..., min_length=1, strip_whitespace=True)
    app_id: str = Field(..., min_length=1, strip_whitespace=True)
    app_secret: str = Field(..., min_length=1, strip_whitespace=True)
    share_urls: List[LarkShareUrlInput] = Field(..., min_items=1)
    notification_emails: List[EmailStr] = Field(default_factory=list)
    sync_interval: int = Field(
        ...,
        gt=600,
        description="Interval in seconds to sync the Lark integration, 0 means no sync",
    )

    @validator("notification_emails", each_item=True, pre=True)
    def strip_email(cls, v):
        if isinstance(v, str):
            return v.strip()
        return v

    @validator("app_id", pre=True, always=True)
    def strip_app_id(cls, v):
        if isinstance(v, str):
            return v.strip()
        return v

    @validator("app_secret", pre=True, always=True)
    def strip_app_secret(cls, v):
        if isinstance(v, str):
            return v.strip()
        return v

    @root_validator
    def validate_unique_urls(cls, values):
        share_urls = values.get("share_urls", [])
        if not share_urls:
            return values

        # 检查URL重复
        url_set = set()
        token_set = set()

        for share_url in share_urls:
            # 检查URL重复
            url = str(share_url.url).rstrip("/")  # 移除末尾的斜杠以标准化URL
            if url in url_set:
                raise ValueError(f"Duplicate URL found: {url}")
            url_set.add(url)

            # 检查token重复
            token = share_url.token
            if token in token_set:
                raise ValueError(f"Duplicate folder found: {token}")
            token_set.add(token)

        return values
