import asyncio
import traceback
from datetime import datetime
from typing import Any, List
from uuid import UUID

import aiohttp
from loguru import logger as logging

from mygpt import settings
from mygpt.models import Robot, SessionMessage
from mygpt.settings import QUALITY_ASSESSMENT_URL


async def quality_assessment_service(
    api_url: str,
    method: str,
    payload: Any = None,
    json: Any = None,
    params: Any = None,
    type: str = "json",
) -> str:
    async with aiohttp.ClientSession() as session:
        if not QUALITY_ASSESSMENT_URL:
            raise ValueError("QUALITY_ASSESSMENT_URL is not set")
        url = f"{QUALITY_ASSESSMENT_URL}{api_url}"

        if method == "post":
            fun = session.post  # 使用 json 参数传递 JSON 数据
        elif method == "put":
            fun = session.put  # 使用 json 参数传递 JSON 数据
        elif method == "delete":
            fun = session.delete  # 使用 params 参数传递查询字符串参数
        elif method == "patch":
            fun = session.patch  # 使用 json 参数传递 JSON 数据
        else:
            fun = session.get  # 默认使用 GET 请求

        response = fun(url, params=params, json=json, data=payload)

        async with response as r:
            if r.status == 200:
                if type == "json":
                    return await r.json()
                else:
                    return await r.text()
            else:
                text = await r.text()
                detail = (
                    f"quality_assessment_service({url}): {r.status} {r.reason} {text}"
                )
                raise ValueError(detail)


async def create_dataset(name: str, description: str, metadata: dict) -> dict:
    try:
        payload = {"name": name, "description": description, "metadata": metadata}
        return await quality_assessment_service("/dataset", "post", json=payload)
    except Exception as e:
        raise e


async def query_dataset(query: str | None = None, metadata: dict | None = None) -> dict:
    try:
        api_url = "/dataset/query"
        if query:
            api_url = f"{api_url}?query={query}"

        return await quality_assessment_service(api_url, "post", json=metadata)
    except Exception as e:
        raise e


async def get_or_create_daily_dataset_for_robot(
    name: str, description: str, robot_id: str, day: str | None = None
) -> dict:
    try:
        metadata = {
            "robot_id": robot_id,
            "day": day or datetime.now().strftime("%Y%m%d"),
        }

        try:
            result = await query_dataset(metadata=metadata)
            items = result.get("items", [])
            if items:
                return items[0]
        except Exception as e:
            pass
        return await create_dataset(name, description, metadata)
    except Exception as e:
        raise e


async def create_data(
    dataset_id: str,
    question: str,
    answer: str,
    ground_truths: List[str] = [],
    contexts: List = [],
    contexts_with_prompt: List[str] = [],
    metadata: dict = None,
    history: List[str] = [],
    keywords: List[str] = [],
    answer_from_robot_source: str = None,
    model: str = None,
    first_char_receive_time: float = None,
    llm_can_answer: bool = None,
    faq_id: str = None,
):
    try:
        llm_can_answer_value = None
        if llm_can_answer is not None:
            llm_can_answer_value = "true" if llm_can_answer else "false"
        payload = {
            "question": question,
            "answer": answer,
            "ground_truths": ground_truths,
            "contexts": contexts,
            "contexts_with_prompt": contexts_with_prompt,
            "metadata": metadata or {},
            "history": history,
            "keywords": keywords,
            "answer_from_robot_source": answer_from_robot_source,
            "first_char_receive_time": first_char_receive_time,
            "llm_can_answer": llm_can_answer_value,
            "faq_id": faq_id,
            "model": model,
        }
        return await quality_assessment_service(
            f"/dataset/{dataset_id}/data", "post", json=payload
        )
    except Exception as e:
        raise e


async def update_data(
    data_id: str,
    dataset_id: str,
    question: str,
    answer: str,
    ground_truths: List[str] = None,
    contexts: List = None,
    contexts_with_prompt: List[str] = None,
    metadata: dict = None,
    history: List[str] = [],
    keywords: List[str] = None,
    answer_from_robot_source: str = None,
    model: str = None,
    first_char_receive_time: float = None,
    llm_can_answer: bool = None,
    faq_id: str = None,
):
    try:
        llm_can_answer_value = None
        if llm_can_answer is not None:
            llm_can_answer_value = "true" if llm_can_answer else "false"
        payload = {
            "question": question,
            "answer": answer,
            "ground_truths": ground_truths,
            "contexts": contexts,
            "contexts_with_prompt": contexts_with_prompt,
            "metadata": metadata,
            "history": history,
            "keywords": keywords,
            "answer_from_robot_source": answer_from_robot_source,
            "first_char_receive_time": first_char_receive_time,
            "llm_can_answer": llm_can_answer_value,
            "faq_id": faq_id,
            "model": model,
        }
        return await quality_assessment_service(
            f"/dataset/{dataset_id}/data/{data_id}", "put", json=payload
        )
    except Exception as e:
        raise e


message_data_map = {}


async def delete_message_data_map(message_id: str):
    try:
        await asyncio.sleep(300)
        if message_id in message_data_map:
            del message_data_map[message_id]
    except Exception as e:
        logging.error(f"fail to delete_question_data_map in error:{e}")


message_locks = {}


async def save_to_quality_assessment_by_message_id(message_id: str | UUID):
    try:
        message_id = str(message_id)
        if not QUALITY_ASSESSMENT_URL:
            if not settings.IS_USE_LOCAL_VLLM:
                logging.warning(
                    "QUALITY_ASSESSMENT_URL is not configured. Quality assessment data will not be stored, but other functionalities remain unaffected."
                )
            return
        session_message = await SessionMessage.get(id=message_id).prefetch_related(
            "session"
        )
        if not session_message.answer or session_message.is_test:
            return
        if message_id not in message_locks:
            message_locks[message_id] = asyncio.Lock()
        async with message_locks[message_id]:
            robot_id = str(session_message.session.robot_id)
            robot = await Robot.get(id=robot_id)
            robot_name = robot.name
            current_day = datetime.now().strftime("%Y%m%d")
            dataset = await get_or_create_daily_dataset_for_robot(
                f"{robot_name} {current_day}",
                f"Robot {robot_name} chat logs for {current_day}",
                robot_id,
                current_day,
            )

            metadata = session_message.question_metadata

            contexts = []
            for reference in session_message.reference_list or []:
                contexts.append(
                    {
                        "id": reference.get("id"),
                        "score": reference.get("score"),
                        "source": reference.get("source", ""),
                        "page_numbers": reference.get("page_numbers", []),
                        "title": reference.get("title", ""),
                        "content": reference.get("content", ""),
                    }
                )
            first_char_receive_time = (
                session_message.first_response_time / 1000
                if session_message.first_response_time
                and session_message.first_response_time > 0
                else None
            )
            data_id = message_data_map.get(message_id)

            session_id = str(session_message.session_id)
            history_messages = await SessionMessage.filter(
                session_id=session_id
            ).order_by("created_at")
            history = []
            for history_message in history_messages or []:
                if str(history_message.id) == message_id:
                    break
                history.append(history_message.question)
                history.append(history_message.answer)
            if data_id:
                await update_data(
                    data_id,
                    dataset["id"],
                    session_message.question,
                    session_message.answer,
                    [""],
                    contexts,
                    session_message.contexts,
                    metadata,
                    history=history,
                    first_char_receive_time=first_char_receive_time,
                    llm_can_answer=session_message.llm_can_answer,
                    faq_id=(
                        str(session_message.faq_id) if session_message.faq_id else None
                    ),
                )
            else:
                model = (
                    session_message.model.value
                    if session_message.model is not None
                    else None
                )
                data = await create_data(
                    dataset["id"],
                    session_message.question,
                    session_message.answer,
                    [""],
                    contexts,
                    session_message.contexts,
                    metadata,
                    history,
                    [],
                    robot_id,
                    model,
                    first_char_receive_time,
                    faq_id=(
                        str(session_message.faq_id) if session_message.faq_id else None
                    ),
                )
                message_data_map[message_id] = data["id"]
                asyncio.create_task(delete_message_data_map(message_id))

    except Exception as e:
        print(traceback.format_exc())
        logging.error(f"fail to save_to_quality_assessment_by_message_id in error:{e}")
