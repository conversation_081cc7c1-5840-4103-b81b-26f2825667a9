from typing import Union

from loguru import logger as logging
from tortoise.exceptions import DoesNotExist

from mygpt.dao.postgresql_dao.robot_dao import robot_dao
from mygpt.models import User


async def get_verified_robot_by_user_and_name(user_id: str, robot_name: str):
    """
    获取指定用户和名称的机器人实例
    """
    # 获取定制的文章摘要的机器人
    try:
        robots = await robot_dao.find_robot_by_user_id_and_name(user_id=user_id, name=robot_name)
    except Exception as e:
        logging.error(f"[agent_service_util.find_robot_by_user_id_and_name] - error: {e}")
        raise e
    if not robots:
        logging.error(
            f"[agent_service_util.find_robot_by_user_id_and_name] - No robot found for user_id: {user_id} with name: {robot_name}")
        raise DoesNotExist(
            f"[agent_service_util.find_robot_by_user_id_and_name] - No robot found for user_id: {user_id} with name: {robot_name}")
    if len(robots) > 1:
        logging.warning(
            f"[agent_service_util.find_robot_by_user_id_and_name] - Found multiple robots for user_id: {user_id} with name: {robot_name}. Using the first one."
        )
    robot = robots[0]
    return robot
