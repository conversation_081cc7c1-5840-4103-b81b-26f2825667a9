import time
from typing import List, AsyncIterator

from loguru import logger as logging
from asyncio import Event

from langchain_core.messages.human import HumanMessage
from langchain_core.messages.base import BaseMessage
from langchain_core.messages.ai import AIMessage
from langchain_core.outputs.llm_result import LLMResult, Generation
from langchain_core.outputs.chat_generation import ChatGeneration

from mygpt.service.chat.faq_service import _add_child_node, _add_recommended_questions
from mygpt.enums import OpenAIModel, MESSAGE_COMES_FROM, AIConfigType
from mygpt.models import Robot, Faqs
from mygpt.openai_utils import (
    chat_acreate,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>lder,
    ChainResponse<PERSON><PERSON><PERSON>andler,
    language_detect,
)
from mygpt.schemata import QuestionIn
from mygpt.service.rag_control import answer_rag_bot
from mygpt.utils import convert_language_code

translate_prompt = """
You are an intelligent assistant responsible for providing the correct response based on the user's input language and the FAQ answer.
If the language of the user's input matches the language of the FAQ answer, return the FAQ answer directly. Otherwise, translate the FAQ answer into the user's input language and return the translated response.

Here is the input data:
- User's input query: {user_input}
- Matched FAQ answer: {faq_answer}

Please follow the steps below to process:
1. Detect the language of the user's input query.
2. Detect the language of the FAQ answer.
3. If the two languages match: directly output the FAQ answer.
4. If the two languages do not match: translate the FAQ answer into the user's input language and output the translated result.

Please provide the final output based on this logic.
"""


async def answer_to_iterable(answer: str, callbacks: List) -> AsyncIterator:
    for callback in callbacks:
        await callback.on_llm_new_token("")
        if hasattr(callback, "queue"):
            # 弹出队列中的所有元素
            while not callback.queue.empty():
                token = await callback.queue.get()
                yield token
    for char in answer:
        for callback in callbacks:
            await callback.on_llm_new_token(char)
            if hasattr(callback, "queue"):
                while not callback.queue.empty():
                    token = await callback.queue.get()
                    yield token
    generations = [[ChatGeneration(text=answer, message=AIMessage(content=answer))]]
    response = LLMResult(generations=generations)
    for callback in callbacks:
        await callback.on_llm_end(response)
        if hasattr(callback, "queue"):
            while not callback.queue.empty():
                token = await callback.queue.get()
                yield token


from tortoise.queryset import (
    RawSQLQuery,
)


async def fast_faq(
    ai_obj: Robot,
    question_in: QuestionIn,
    event: Event,
    user_id: str,
    question_record_obj,
    chat_history_str: str,
):
    start_time = time.time()
    logging.info(f"[service.chat.fast_faq_service.fast_faq] Start fast_faq service")
    # 根据recommend_faq_id获取faq
    recommend_faq_id = str(question_in.recommend_faq_id)
    logging.info(f"[service.chat.fast_faq_service.fast_faq] ai_id: {ai_obj.id}")
    logging.info(
        f"[service.chat.fast_faq_service.fast_faq] recommend_faq_id: {recommend_faq_id}"
    )
    # recommend_faq = await Faqs.get_or_none(id=recommend_faq_id)
    query_str = """SELECT f.*
        FROM faqs f
        INNER JOIN dataset_robot dr ON f.dataset_id = dr.dataset_id
        WHERE dr.robot_id = $1
            AND (f.similar_questions_ids ? $2 OR f.id = $3)
        LIMIT 1
    """
    conn = Faqs._meta.db
    result = await conn.execute_query(
        query_str, [str(ai_obj.id), recommend_faq_id, recommend_faq_id]
    )
    rows = result[0]
    if not rows:
        raise ValueError(f"recommend_faq_id is invalid - {recommend_faq_id}")
    # 获取第一条记录
    record = result[1][0]
    logging.info(f"[service.chat.fast_faq_service.fast_faq] record: {record}")
    # 答案和类型检查
    answer = record.get("answer")
    type_ = record.get("type")
    if not answer and type_ == "source":
        # 执行原有RAG逻辑
        logging.info(
            f"[service.chat.fast_faq_service.fast_faq] execute original RAG logic - question: {question_in.question}"
        )
        response = await answer_rag_bot(
            user_id=user_id,
            ai_obj=ai_obj,
            question_in=question_in,
            question_record_obj=question_record_obj,
            chat_history_str=chat_history_str,
            event=event,
        )
        return response
    # 语言检测
    ai_lang = ai_obj.get_config(AIConfigType.SOURCE_LANG)
    if not ai_lang:
        ai_lang = "en"
    response_language, total_tokens = await language_detect(
        question_in.question, ai_language=ai_lang
    )
    response_language = convert_language_code(response_language)
    faq_obj = Faqs(**record)
    origin_question = record.get("question")
    faq_answer = record.get("answer")
    user_input = question_in.question
    faq_answer = faq_answer.strip()
    recommended_questions = record.get("recommended_questions")
    openai_model = OpenAIModel.GPT_4_OMNI
    question_record_obj.comes_from = MESSAGE_COMES_FROM.FAQ.value
    question_record_obj.total_tokens = total_tokens
    history_conversation_count = 0
    # 查询是否有子节点_add_child_node
    faq_child_node_list = await _add_child_node(str(faq_obj.id), response_language)
    if faq_child_node_list is None:
        faq_child_node_list = []
    # 查询相关问题
    recommend_list = []
    if faq_obj is not None and faq_obj.recommended_questions:
        # 增补手动录入的推荐
        recommend_list = await _add_recommended_questions(
            faq_obj, recommend_list, response_language
        )

    callback = MyCallbackHanlder(
        format=question_in.format,
        message_id=question_in.message_id,
        is_faq=True,
        reference_list=[],
        recommend_list=recommend_list,
        resources=None,
        tokens=total_tokens,
        event=event,
        origin_question=origin_question,
        user_intent=None,
        faq_child_node_list=faq_child_node_list,
    )

    # 与stream返回无关
    callback_done = ChainResponseAsyncHandler(
        session_id=question_in.session_id,
        message_id=question_in.message_id,
        human_message=HumanMessage(question_in.question),
        final_question_use_gpt4=True,
        messages=[],
        event=event,
        resources=None,
        openai_model=openai_model,
    )
    callbacks = [callback_done]
    if question_in.stream:
        callbacks.append(callback)

    resp = answer_to_iterable(faq_answer, callbacks)
    return resp
