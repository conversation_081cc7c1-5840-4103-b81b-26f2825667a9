import asyncio
import json
import re
import time
from datetime import datetime
from uuid import UUID, uuid4

import aiohttp
import dateparser
import requests
from aiohttp import ClientConnectorError, ClientSession, ClientTimeout
from fastapi import APIRouter, Depends, Request, Security
from langchain.schema import AIMessage, HumanMessage, SystemMessage
from langchain.utilities.openapi import OpenAPISpec
from langchain_community.tools import APIOperation
from loguru import logger as logging
from requests.exceptions import Timeout
from skywalking.decorators import trace
from mygpt.service.chat.faq_service import get_faqs_turbo_16k
from mygpt.enums import OpenAIModel
from mygpt.models import (
    Dataset,
    FunctionCallApi,
)
from mygpt.openai_utils import (
    chat_acreate,
    question_answer_turbo_16k_with_function_call,
)
from mygpt.prompt import (
    function_call_ask_sys_template,
    function_call_ask_user_template,
)
from mygpt.schemata import (
    QuestionIn,
)
from mygpt.utils import num_tokens_from_string


@trace()
async def send_function_call_with_prompt(
    user_question: str, system_message: str, ai_id: UUID, session_id: str
):
    start_time = time.time()
    # 识别用户信息里面有没有关键词
    match_keywords_list = await FunctionCallApi.filter(
        robot_id=ai_id, deleted_at__isnull=True
    ).values_list("matching_keywords", flat=True)
    match_keywords_list = [item for item in match_keywords_list if item]
    if len(match_keywords_list) > 0:
        match_keywords_list = [
            item for sublist in match_keywords_list for item in sublist
        ]
        pattern = r"(?:{})".format("|".join(match_keywords_list))
        contains_keyword = bool(re.search(pattern, user_question))
        if not contains_keyword:
            logging.info("【function_call】 user_question not in keywords_list")
            return False, None, None, None

    dataset_ids = await Dataset.get_dataset_ids_by_robot_id(ai_id)
    if len(dataset_ids) == 0:
        function_call_list = await FunctionCallApi.filter(
            robot_id=ai_id, deleted_at__isnull=True
        )
    else:
        function_call_list = await FunctionCallApi.filter(
            dataset_id__in=dataset_ids, deleted_at__isnull=True
        )

    if function_call_list is None or len(function_call_list) == 0:
        logging.info("【function_call】 function_call_list is None")
        return False, None, None, None

    # 解析function_call_list,提取出标准的function_description和operations类(用于方便调用)
    tasks = [parse_function_call(function_call) for function_call in function_call_list]
    results = await asyncio.gather(*tasks)

    function_descriptions = [result["function_description"] for result in results]
    operations = {
        result["operation"]["function_call_name"]: result["operation"]["operation"]
        for result in results
    }
    function_call_infos = {
        result["operation"]["function_call_name"]: result["operation"][
            "function_call_info"
        ]
        for result in results
    }
    if function_descriptions is None:
        logging.info("【function_call】 function_descriptions is None")
        return False, None, None, None
    logging.info(f"【function_call】 function_descriptions: {function_descriptions}")

    # 日志计时
    end_time = time.time()
    logging.info(f"【function_call】 parse function_call time: {end_time - start_time}")

    # 发送GPT请求
    message = []
    if system_message is not None:
        message.append(
            {
                "role": "system",
                "content": system_message
                + "The current time zone time:"
                + datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }
        )

    # if session_id:
    #     history_list, history_list_count = await get_chat_history_turbo(session_id)
    #     if history_list:
    #         for history in history_list:
    #             if isinstance(history, AIMessage):
    #                 message.append(
    #                     {
    #                         'role': 'assistant',
    #                         'content': history.content
    #                     }
    #                 )
    #             else:
    #                 message.append(
    #                     {
    #                         'role': 'user',
    #                         'content': history.content
    #                     }
    #                 )

    message.append({"role": "user", "content": user_question})
    logging.info(f"【function_call】 message: {message}")
    start_time = time.time()
    gpt_response = await question_answer_turbo_16k_with_function_call(
        messages=message,
        functions=function_descriptions,
        function_call="auto",
        max_tokens=4096,
        stream=False,
    )
    end_time = time.time()
    logging.info(f"【function_call】 response time: {end_time - start_time}")

    logging.info(f"【function_call】response from GPT: {gpt_response}")
    # 解析GPT返回结果
    if gpt_response and gpt_response.additional_kwargs:
        # response = await function_call_api(gpt_response['choices'][0], operations, function_call_infos)
        return True, gpt_response, operations, function_call_infos
    else:
        return False, gpt_response, None, None


async def send_function_call(
    user_question: str,
    ai_id: UUID,
    user_id: str = None,
    query_key_words: str = "",
    session_id: str = "",
):
    prompt_system = """
    Don't make assumptions about what values to insert into the function. Make a function_call call if the user intent is clear, if not return json {"run_sta":false}.
    """
    return await send_function_call_with_prompt(
        f"{user_question}", prompt_system, ai_id, session_id
    )


async def function_call_api(
    gpt_response: AIMessage,
    operations: dict = None,
    function_call_infos: dict = None,
    request: Request = None,
    query_key_words_in_ai_language: str = "",
    response_language: str = "",
    question: QuestionIn = None,
    message_tokens: int = 0,
    user_id: str = None,
    ai_id: str = None,
    event: asyncio.Event = None,
):
    start_time = time.time()
    function_name = gpt_response.additional_kwargs["function_call"]["name"]
    function_arguments = gpt_response.additional_kwargs["function_call"]["arguments"]
    arguments = json.loads(function_arguments)
    logging.info(f"【function_call】 function_name: {function_name}")
    logging.info(f"【function_call】 arguments: {arguments}")

    # 取出function_name中返回的涉及变量的值对象
    operation = operations.get(function_name)
    function_info = function_call_infos.get(function_name)
    logging.info(
        f"【function_call】 operations:{operations} function_info:{function_info} "
    )

    if (
        function_info is not None
        and function_info.body is not None
        and function_info.body != ""
        and function_info.body != "{}"
    ):
        method = function_info.method
        path = function_info.path
    elif operation is not None:
        method = operation.method.value.lower()
        path = operation.path
    else:
        logging.info(f"Operation {function_name} not found")
        return None, 0, None

    # 根据method判断是get还是post
    if method in ["get"]:
        query_param_values = arguments
        body_param_values = None
    else:
        query_param_values = None
        body_param_values = arguments

    # GPT反馈的参数与要发送的请求参数匹配
    data = json.dumps(body_param_values) if method in ["post", "put", "patch"] else None
    params = query_param_values if method == "get" else None
    # 增加header里ip的参数
    # if request and "x-real-ip" in request.headers:
    #     function_info.header_authorization["x-real-ip"] = request.headers["x-real-ip"]

    headers = function_info.header_authorization
    if headers is None:
        headers = {}
    headers["content-type"] = "application/json"

    # if function_info.auth_token_curl is not None:
    #     auth_response = await execute_request(
    #         function_info.auth_token_curl["url"],
    #         function_info.auth_token_curl["method"],
    #         data=function_info.auth_token_curl["data"],
    #         params=None,
    #         headers=function_info.auth_token_curl["headers"],
    #     )
    #     if auth_response is not None:
    #         headers["Authorization"] = f"Bearer {auth_response['access_token']}"
    # elif settings.FUNCTION_CALL_AUTH_URL is not None:
    #     auth_response = await execute_request(
    #         settings.FUNCTION_CALL_AUTH_URL,
    #         "post",
    #         data={
    #             "username": settings.FUNCTION_CALL_AUTH_USERNAME,
    #             "password": settings.FUNCTION_CALL_AUTH_PASSWORD,
    #         },
    #         params=None,
    #         headers={"client_id": "webApp", "client_secret": "webApp"},
    #     )
    #     if auth_response is not None:
    #         headers["Authorization"] = f"Bearer {auth_response['access_token']}"

    # 发送请求
    try:
        logging.info(
            f"【function_call】 send request: {function_info.server_url + path} "
            f"method: {method} data: {data} params: {params} header: {headers}"
        )
        response = await execute_request(
            function_info.server_url + path,
            method,
            data=data,
            params=params,
            headers=headers,
        )
    except Exception as e:
        logging.info(f"【function_call】 ERROR execute_request: {e}")
        response = None

    end_time = time.time()
    logging.info(f"【function_call】 api response time: {end_time - start_time}")

    if query_key_words_in_ai_language != "":
        if not response:
            response = "{}"
        if function_info.llm_sta is False:
            resp, total_tokens = await get_faqs_turbo_16k(
                str(response["answer"]), question, response_language, user_id, ai_id
            )
        else:
            remarks = ""

            if (
                function_info.request_explanation is not None
                and function_info.request_explanation != ""
            ):
                remarks = function_info.request_explanation

            sys_prompt_content = function_call_ask_sys_template.format(
                api_results=str(response),
                language=response_language,
                remarks=remarks,
            )
            messages = [
                SystemMessage(content=sys_prompt_content),
            ]
            messages.append(
                HumanMessage(
                    content=function_call_ask_user_template.format(
                        question=question.question,
                        language=response_language,
                    )
                ),
            )
            resp = await chat_acreate(
                openai_model=OpenAIModel.GPT_4_OMNI,
                origin_question=question.question,
                messages=messages,
                max_tokens=question.max_tokens,
                session_id=question.session_id,
                message_id=question.message_id,
                stream=question.stream,
                format=question.format,
                is_faq=True,
                event=event,
            )
            logging.info(f"【function_call】{messages}")

        question_token = num_tokens_from_string(question.question)
        message_tokens += question_token
        return resp, message_tokens, None
    else:
        return (
            {"answer": response},
            0,
            {
                "method": method,
                "data": json.loads(data) if data is not None else None,
                "params": params,
                "header": headers,
            },
        )


async def generate_event_stream(response_text):
    for event in response_text.split("\n\n"):
        yield event


class MyOpenAPISpec(OpenAPISpec):
    @classmethod
    def from_url(cls, url: str) -> "MyOpenAPISpec":
        try:
            response = requests.get(url, timeout=2)
            return cls.from_text(response.text)
        except Timeout:
            raise ValueError("The request exceeded the timeout duration.")
        except requests.exceptions.RequestException as e:
            raise ValueError(
                f"An error occurred while fetching the OpenAPI specification: {e}"
            )


async def execute_request(
    url: str, method: str, data: str = None, params: dict = None, headers: dict = None
) -> dict:
    timeout = ClientTimeout(total=20)
    try:
        async with ClientSession(
            timeout=timeout, connector=aiohttp.TCPConnector(limit=64, verify_ssl=False)
        ) as session:
            method = method.lower()
            req_func = getattr(session, method, None)
            if req_func is None:
                raise None
            logging.info(f"【function_call】 send url: {url}")
            async with req_func(
                url, data=data, params=params, headers=headers
            ) as response:
                return await response.json()
    except ClientConnectorError as e:
        raise ValueError(f"client connector error:{e}")
    except Exception as e:
        raise ValueError(f"An error occurred during request: {e}")


async def parse_function_call(function_call):
    # function call 的名称需要遵守 ^[a-zA-Z8-9_-](1,64)$ 这个规则，这里需要手动处理成符合规则的名称
    if not re.match(r"^[a-zA-Z8-9_-]{1,64}$", function_call.function_call_name):
        function_call_name = (
            function_call.function_call_name.strip()
            .replace(" ", "_")
            .replace("-", "_")
            .replace(".", "_")
            .replace("/", "_")
            .replace("\\", "_")
        )
        function_call_name = function_call_name[:64]
        function_call.function_call_name = function_call_name

    # parse function_call
    function_description = {
        "name": function_call.function_call_name,
        "description": function_call.function_call_description,
        "parameters": {"type": "object", "properties": {}},
    }
    logging.info(f"function_call.openapi_url: {function_call.openapi_url}")

    # 判断如果写了接口说明，不走下面读取openapi直接返回
    if (
        function_call.body is not None
        and function_call.body != ""
        and function_call.body != "{}"
    ):
        function_description["parameters"]["properties"] = function_call.body
        if (
            function_call.body_required is not None
            and function_call.body_required != "[]u"
        ):
            function_description["parameters"]["required"] = function_call.body_required
        return {
            "function_description": function_description,
            "operation": {
                "function_call_name": function_call.function_call_name,
                "operation": None,
                "function_call_info": function_call,
            },
        }

    # 读取openapi
    try:
        spec = MyOpenAPISpec.from_url(function_call.openapi_url)
        operation = APIOperation.from_openapi_spec(
            spec, function_call.path, function_call.method
        )
    except Exception as e:
        operation = None
        if function_call.function_call_description:
            operation = APIOperation(
                operation_id=str(uuid4()),
                description=function_call.function_call_description,
                base_url=function_call.server_url,
                path=function_call.path,
                method=function_call.method,
                properties=[],
                request_body=None,
            )
            function_description["parameters"]["properties"]["value"] = {
                "type": "object",
                "description": "any value",
            }
        else:
            logging.info(f"【function_call】 ERROR OpenAPISpec.from_url: {e}")
        return {
            "function_description": function_description,
            "operation": {
                "function_call_name": function_call.function_call_name,
                "operation": operation,
                "function_call_info": function_call,
            },
        }
    function_description["description"] = operation.description
    required = []
    for prop in operation.properties:
        function_description["parameters"]["properties"][prop.name] = {
            "type": str(prop.type),
            "description": prop.description,
        }
        if prop.required:
            required.append(prop.name)
    if operation.request_body is not None:
        for prop in operation.request_body.properties:
            function_description["parameters"]["properties"][prop.name] = {
                "type": str(prop.type),
                "description": prop.description,
            }
            if prop.required:
                required.append(prop.name)

    # 拼接是否有必填参数
    if len(required) > 0:
        function_description["parameters"]["required"] = required
    return {
        "function_description": function_description,
        "operation": {
            "function_call_name": function_call.function_call_name,
            "operation": operation,
            "function_call_info": function_call,
        },
    }


# 解析一下里面有没有今日 昨天等关键词，转换一下成时间
async def dateparser_parse(query_key_words_in_ai_language):
    query_key_words_list = query_key_words_in_ai_language.split(" ")
    if len(query_key_words_list) > 0:
        for i in range(len(query_key_words_list)):
            if ":" in query_key_words_list[i]:
                continue
            # 如果带年，月的话，不处理
            if "年" in query_key_words_list[i]:
                continue
            parse_result = dateparser.parse(query_key_words_list[i])
            if parse_result:
                query_key_words_list[i] = parse_result.strftime("%Y-%m-%d")
        query_key_words_in_ai_language = " ".join(query_key_words_list)

    return query_key_words_in_ai_language
