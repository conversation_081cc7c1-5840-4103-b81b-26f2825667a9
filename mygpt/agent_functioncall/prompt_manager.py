from calendar import monthcalendar
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Optional
import json
import lxml.etree as ET

from loguru import logger as logging

from mygpt.agent_functioncall.prompts.builders.components.business_builder import (
    DefaultBusinessBuilder,
)
from mygpt.agent_functioncall.prompts.builders.components.knowledge_builder import (
    RAGKnowledgeBaseBuilder,
)
from mygpt.agent_functioncall.prompts.builders.components.session_builder import (
    JsonSessionContextBuilder,
)
from mygpt.agent_functioncall.prompts.prompt_director import PromptDirector
from mygpt.agent_functioncall.schemas.dataset_schemas import DatasetInfo
from mygpt.agent_functioncall.schemas.prompt_schemas import (
    PromptType,
    BusinessParams,
    SessionParams,
    KnowledgeParams,
    ToolParams,
    GeneralParams,
)
from mygpt.agent_functioncall.types import ToolInfo
from mygpt.models import Robot
from mygpt.schemata import QuestionIn


# enhanced_prompt = """[Instruction]
# Before making any tool calls or information retrievals, you MUST:
# 1. Acknowledge the user's request naturally
# 2. Provide a contextual transition message that:
#    - Matches the conversation style and your persona
#    - Reflects the type of information being retrieved
#    - Feels natural within the ongoing dialogue
# 3. Then proceed with the tool call
#
# [User Question]
# {question}
#
# [Response]"""

enhanced_prompt = """[Instruction]
When responding to the user's question, follow these steps precisely:

1. Provide EXACTLY ONE brief acknowledgment that:
   - Shows you understand the user's request
   - Indicates you will retrieve the requested information
   - Maintains your defined persona and conversation style
   - Keeps the transition natural and contextual

2. After the acknowledgment, proceed directly with the tool call

3. Important: Never repeat or rephrase the acknowledgment message

[User Question]
{question}

[Response]"""

for_tts_meta_prompt = """
<MetaPrompt>
    <Objective>
        Generate natural language output suitable for Text-to-Speech (TTS) model input.
    </Objective>
    <DynamicVariables>
        <CurrentDateTime>
            Date: {current_date}
            Time: {current_time}
            Weekday: {weekday}
        </CurrentDateTime>
    </DynamicVariables>
    <OutputRequirements>
        <Format>
            Use natural language without Markdown formatting or special symbols.
            Avoid using special symbols such as 「 」, @, #, $, %, &, *, or other non-alphanumeric characters unless specifically requested.
        </Format>
        <Language>
            Please refer to the language used in the BusinessPrompt section and use it as the final output language.
        </Language>
        <Style>
            The output should be concise and clear, ensuring ease of understanding.
        </Style>
        <CustomContent>
            Seamlessly integrate the client-provided business-specific prompt content into the response.
        </CustomContent>
    </OutputRequirements>
</MetaPrompt>
<BusinessPrompt>
{robot_prompt}
</BusinessPrompt>
"""

"""
知识库变量组织格式
<KnowledgeBase>
    <Sources>
        <Source>
            <Name>KnowledgeSource1</Name>
            <Description>Description of the first knowledge source.</Description>
        </Source>
        <Source>
            <Name>KnowledgeSource2</Name>
            <Description>Description of the second knowledge source.</Description>
        </Source>
        <!-- Add more sources as needed -->
    </Sources>
    <Integration>
        For queries related to specific knowledge sources, initiate a call to the RAG tool to retrieve accurate and up-to-date information. 
        Ensure that the response prioritizes retrieving verified information from the knowledge sources instead of generating uncertain content.
    </Integration>
</KnowledgeBase>
"""
for_tts_meta_with_rag_prompt = """
<MetaPrompt>
    <Objective>
        Generate voice-friendly responses optimized for Text-to-Speech (TTS) output.
    </Objective>
    <DynamicVariables>
        <CurrentDateTime>
            Date: {current_date}
            Time: {current_time}
            Weekday: {weekday}
        </CurrentDateTime>
        {calendar}
    </DynamicVariables>
    <OutputRequirements>
        <Format>
            - Use pure natural language without any formatting
            - If it is a Cardinal Number, output the number directly. If it is a Digit Number, insert a space between each digit
            - Never use lists, bullet points, or numbered formats
            - Present information in conversational flowing sentences
        </Format>
        <Language>
            Dynamically determine the output language based on the most recent user input:
		    - If the user's input is primarily in one language, respond in that language.
            - If the user's input is a mix of multiple languages, prioritize responding in the dominant language of the input.
            - If the user's intent indicates a specific language preference (e.g., explicitly requesting a specific language), follow the user's indicated preference.
            This ensures that the output matches the user's language expectations and communication needs.
        </Language>
        <Style>
            - Keep responses brief and focused
            - Default to one or two sentences unless more detail is requested
            - Use conversational tone while maintaining professionalism
            - Present information sequentially in natural speech patterns
        </Style>
        <ResponseLength>
            - Prioritize concise, focused answers
            - Elaborate only when specifically requested
            - Break complex information into digestible parts
        </ResponseLength>
        <CustomContent>
            Seamlessly integrate the client-provided business-specific prompt content into the response.
        </CustomContent>
    </OutputRequirements>
    <ToolCallStrategy>
        <ExecutionOrder>
            - Execute tool calls sequentially, one at a time
            - Complete each tool call and process its response before initiating the next tool call
            - Never attempt parallel tool calls
        </ExecutionOrder>
        <PreCallMessage>
            Before making a tool call:
            - Always provide a brief transitional message first
            - Acknowledge the information request
            - Indicate that you are about to retrieve information
            - Keep the message natural and contextual
            - Never skip this transitional message
        </PreCallMessage>
        <PostCallMessage>
            - Convert tool response to natural speech patterns
            - Remove all formatting from tool responses
            - Present information conversationally
            - Maintain brevity and clarity
        </PostCallMessage>
    </ToolCallStrategy>
</MetaPrompt>
{knowledge_base}
<BusinessPrompt>
{robot_prompt}

{dynamic_background}
<Instructions>
- When using tools, always provide a transitional message first
- Treat every response as part of a voice conversation
- Keep transitions natural and brief when using tools
- Present information in a conversational flow without lists or formatting
- Maintain the tone and style from BusinessPrompt while ensuring voice-friendly delivery
</Instructions>
</BusinessPrompt>
"""

for_chat_meta_with_rag_prompt = """
<MetaPrompt>
    <Objective>
        Generate interactive and informative responses strictly within the scope of the client's specified business context. Avoid responding to questions that fall outside the defined business context or knowledge base.
    </Objective>
    <DynamicVariables>
        <CurrentDateTime>
            Date: {current_date}
            Time: {current_time}
            Weekday: {weekday}
        </CurrentDateTime>
        {calendar}
    </DynamicVariables>
    <OutputRequirements>
        <Format>
            Use natural language and Markdown formatting where appropriate to include images and links.
            Utilize special symbols as needed to format images and links correctly.
        </Format>
        <Language>
            Dynamically determine the output language based on the most recent user input:
            - If the user's input is primarily in one language, respond in that language.
            - If the user's input is a mix of multiple languages, prioritize responding in the dominant language of the input.
            - If the user's intent indicates a specific language preference (e.g., explicitly requesting a specific language), follow the user's indicated preference.
            This ensures that the output matches the user's language expectations and communication needs.
        </Language>
        <Style>
            The output should be engaging, clear, and easy to understand, maintaining a conversational tone.
        </Style>
        <CustomContent>
            Integrate client-provided business-specific prompt content naturally into the response.
            Ensure image information is incorporated effectively into the conversation.
        </CustomContent>
        <KnowledgePriority>
            Always prioritize using the knowledge_base content when responding to questions.
            If a response requires information not available in the knowledge_base, explicitly state: "This information is based on general knowledge and may not directly align with the business context."
        </KnowledgePriority>
    </OutputRequirements>
    <ToolCallStrategy>
        <ExecutionOrder>
            - Execute tool calls sequentially, one at a time
            - Complete each tool call and process its response before initiating the next tool call
            - Never attempt parallel tool calls
        </ExecutionOrder>
        <PreCallMessage>
            - Use greeting only once at the beginning of conversation
            - Use brief transitional phrases like "Let me check" or "One moment please"
            - Avoid repeating self-introduction
        </PreCallMessage>
        <PostCallMessage>
            - Directly integrate tool response without additional greetings
            - Seamlessly incorporate tool response into the conversation
            - Ensure response coherence and fluency
            - Remove any redundant content from tool responses
        </PostCallMessage>
        <ResponseStructure>
            - Initial greeting (once only)
            - Transition phrase (if needed)
            - Core information content
            - Interactive prompt (e.g., asking if more information is needed)
        </ResponseStructure>
    </ToolCallStrategy>
</MetaPrompt>
{knowledge_base}
<BusinessPrompt>
{robot_prompt}

{dynamic_background}
<Instructions>
- If the task requires the use of a tool, provide the necessary tool call and include any relevant natural language context.
- For image information:
    - Recognize images by text prefixed with "Image:" and include them using Markdown syntax.
    - Example: Image: ![](https://www.example.com/image.jpg)
    - Ensure images are associated with the relevant parts of the text or sentence.
    - Do not ignore image data; integrate it seamlessly into the response.
    
- If the user query falls outside the client's specified business domain:
    - Politely inform the user that the question is not within the scope of this system's expertise.
    - Redirect the user back to the business-specific domain or encourage them to rephrase their query.
    - Example response: "This system specializes in [business-specific domain]. Could you please provide a query within this area?"
- Do not fabricate answers based on general knowledge when the query is unrelated to the business domain.
</Instructions>
</BusinessPrompt>
"""

from lunar_python import Lunar


class CalendarContextGenerator:
    def __init__(self, current_date: datetime = None, indent_num: int = 0):
        self.current_date = current_date or datetime.now()
        self.indent = "" if indent_num == 0 else " " * 4 * indent_num

    def _format_date(self, date: datetime) -> str:
        return date.strftime("%d")

    def _is_leap_year(self, year: int) -> bool:
        year = year or self.current_date.year
        return year % 4 == 0 and (year % 100 != 0 or year % 400 == 0)

    def _get_year_info(self) -> str:
        year = self.current_date.year
        is_leap = self._is_leap_year(year)
        days_in_year = 366 if is_leap else 365
        current_day = self.current_date.timetuple().tm_yday
        remaining_days = days_in_year - current_day

        return (
            f"{self.indent}    Year Info: {year} "
            f"({'Leap' if is_leap else 'Common'} Year, "
            f"Day {current_day}/{days_in_year}, "
            f"{remaining_days} days remaining)"
        )

    def _get_month_name(self, date: datetime) -> str:
        return date.strftime("%b")

    def _get_week_dates(self, start_date: datetime) -> List[str]:
        dates = []
        for i in range(7):
            day = start_date + timedelta(days=i)
            dates.append(self._format_date(day))
        return dates

    def _get_month_reference(self) -> str:
        last_month = self.current_date - timedelta(days=self.current_date.day)
        next_month = (self.current_date.replace(day=28) + timedelta(days=4)).replace(
            day=1
        )

        months = [last_month, self.current_date, next_month]
        month_ref = []

        for month in months:
            weeks = monthcalendar(month.year, month.month)
            week_str = []
            for i, week in enumerate(weeks, 1):
                start = min(d for d in week if d != 0)
                end = max(d for d in week if d != 0)
                week_str.append(f"W{i}({start}-{end})")

            month_line = f"{self.indent}    {month.strftime('%B')} ({month.strftime('%b')}): {', '.join(week_str)}"
            month_ref.append(month_line)

        return "\n".join(month_ref)

    def _get_weekly_view(self) -> str:
        # 获取前2周和后4周的信息
        weeks = []

        # 添加表头信息
        week_days = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
        week_header = f"{self.indent}    {'Week':<5} | {' '.join(f'{day:>3}' for day in week_days)} |"
        weeks.append(week_header)

        for week_offset in range(-4, 5):
            week_start = self.current_date + timedelta(weeks=week_offset)
            week_monday = week_start - timedelta(days=week_start.weekday())
            dates = self._get_week_dates(week_monday)

            # 标记当前日期
            if week_offset == 0:
                current_day_idx = self.current_date.weekday()
                dates[current_day_idx] = (
                    f"[{self.current_date.strftime('%d')}]"  # 使用实际日期
                )

            # 确定周参考信息
            if week_offset < 0:
                ref = f"{self._get_month_name(week_start)}(W{week_start.isocalendar()[1]})"
            elif week_offset == 0:
                ref = f"{self._get_month_name(week_start)}(W{week_start.isocalendar()[1]})"
            else:
                ref = f"{self._get_month_name(week_start)}(W{week_start.isocalendar()[1]})"

            # 使用固定宽度格式化日期
            formatted_dates = [f"{date:>3}" for date in dates]

            # week_label = f"{'-2w' if week_offset == -2 else '-1w' if week_offset == -1 else 'Now' if week_offset == 0 else f'+{week_offset}w'}"
            week_label = (
                f"{week_offset}w"
                if week_offset < 0
                else f"+{week_offset}w" if week_offset > 0 else "Now"
            )
            week_str = f"{self.indent}    {week_label:<5} | {' '.join(formatted_dates)} | {ref}"
            weeks.append(week_str)

        return "\n".join(weeks)

    def _get_key_dates(self) -> str:
        """获取关键信息"""

        def _calculate_qingming(year: int):
            """计算清明节具体日期（返回4月几号）"""
            # 获取年份后两位
            y = year % 100
            # 确定世纪系数
            c = 4.81 if year >= 2000 else 5.59

            # 计算从2000年开始到目标年份间的闰年数
            def count_leap_years(year):
                if year >= 2000:
                    start = 2000
                    end = year
                else:
                    start = year
                    end = 2000
                count = 0
                for y in range(start, end):
                    if (y % 4 == 0 and y % 100 != 0) or (y % 400 == 0):
                        count += 1
                return count

            l = count_leap_years(year)
            # 计算清明节日期
            date = y * 0.2422 + c - l
            # 结果取整，得到的是4月几号
            return round(date)

        def _get_lunar_holidays(year: int) -> Dict[str, str]:
            current_year = self.current_date.year
            lunar_holidays = {}
            # 农历新年
            spring_festival = Lunar.fromYmd(year, 1, 1).getSolar()
            lunar_holidays[
                f"{year}/{spring_festival.getMonth()}/{spring_festival.getDay()}"
            ] = "CNY"
            # 除夕
            lunar_new_year_eve = spring_festival.nextDay(-1)
            lunar_holidays[
                f"{year}/{lunar_new_year_eve.getMonth()}/{lunar_new_year_eve.getDay()}"
            ] = "CNY-Eve"
            # 元宵节
            lantern_festival = Lunar.fromYmd(year, 1, 15).getSolar()
            lunar_holidays[
                f"{year}/{lantern_festival.getMonth()}/{lantern_festival.getDay()}"
            ] = "Lantern"
            # 清明节
            qingming_day = _calculate_qingming(year)
            # qingming_festival = Lunar.fromYmd(year, 4, 4).getSolar()
            lunar_holidays[f"{year}/4/{qingming_day}"] = "Qingming"
            # 端午节
            dragon_boat_festival = Lunar.fromYmd(year, 5, 5).getSolar()
            lunar_holidays[
                f"{year}/{dragon_boat_festival.getMonth()}/{dragon_boat_festival.getDay()}"
            ] = "Dragon-Boat"
            # 七夕
            qixi_festival = Lunar.fromYmd(year, 7, 7).getSolar()
            lunar_holidays[
                f"{year}/{qixi_festival.getMonth()}/{qixi_festival.getDay()}"
            ] = "Qixi"
            # 中秋节
            mid_autumn_festival = Lunar.fromYmd(year, 8, 15).getSolar()
            lunar_holidays[
                f"{year}/{mid_autumn_festival.getMonth()}/{mid_autumn_festival.getDay()}"
            ] = "Mid-Autumn"
            # 重阳节
            double_ninth_festival = Lunar.fromYmd(year, 9, 9).getSolar()
            lunar_holidays[
                f"{year}/{double_ninth_festival.getMonth()}/{double_ninth_festival.getDay()}"
            ] = "Double-Ninth"
            return lunar_holidays

        def get_sort_key(holiday_str: str) -> int:
            """从节日字符串中提取月和日用于排序
            输入格式: "CNY(1/10)" 或 "NewYear(1/1)"
            """
            date_part = holiday_str.split("(")[1].rstrip(")")
            month, day = map(int, date_part.split("/"))
            return month * 100 + day  # 例如: 1月10日 => 110

        current_year = self.current_date.year
        # 获取农历节日
        lunar_holidays = _get_lunar_holidays(current_year)
        # 如果当前日期在下半年, 还需要获取下一年的农历节日
        if self.current_date.month > 6:
            next_year_lunar = _get_lunar_holidays(current_year + 1)
            lunar_holidays.update(next_year_lunar)
        # 如果当前日期在上半年, 还需要获取上一年的农历节日
        elif self.current_date.month < 6:
            last_year_lunar = _get_lunar_holidays(current_year - 1)
            lunar_holidays.update(last_year_lunar)

        # 固定节日
        solar_holidays = {
            "1/1": "NewYear",
            "2/14": "Valentine",
            "5/1": "Labor-Day",
            "10/1": "National-Day",
            "12/25": "Christmas",
        }
        # 分类节日
        recent = []
        coming = []
        # 处理农历节日 (带年份的)
        for date, name in lunar_holidays.items():
            year, month, day = map(int, date.split("/"))
            holiday_date = datetime(year, month, day)
            # 计算与当前日期的差值
            days_diff = (holiday_date - self.current_date).days
            # 获取180天内的归为recent, 未来60天的归为coming
            if -180 <= days_diff < 0:
                recent.append(f"{name}({month}/{day})")  # 输出时仍只显示月/日
            elif 0 < days_diff <= 185:
                coming.append(f"{name}({month}/{day})")
        # 处理固定节日（当前年份）
        for date, name in solar_holidays.items():
            month, day = map(int, date.split("/"))
            holiday_date = datetime(current_year, month, day)
            days_diff = (holiday_date - self.current_date).days
            if -180 <= days_diff < 0:
                recent.append(f"{name}({date})")
            elif 0 < days_diff <= 185:
                coming.append(f"{name}({date})")
        # 对recent和coming进行排序
        recent.sort(key=get_sort_key, reverse=True)
        coming.sort(key=get_sort_key)
        # 格式化输出
        recent_str = f"{self.indent}    Recent: {', '.join(recent) if recent else 'No recent holidays'}"
        coming_str = f"{self.indent}    Coming: {', '.join(coming) if coming else 'No upcoming holidays'}"
        return f"{recent_str}\n{coming_str}"

    def generate_calendar_context(self, use_key_dates=False) -> str:
        past_30d = self.current_date - timedelta(days=30)
        next_60d = self.current_date + timedelta(days=60)

        context = f"""
{self.indent}<CalendarContext>
{self.indent}    # Year Reference
{self._get_year_info()}

{self.indent}    # Month Reference
{self._get_month_reference()}

{self.indent}    # Weekly View (±4 weeks)
{self._get_weekly_view()}

{self.indent}    # Quick Reference
{self.indent}    Current: [{self.current_date.strftime('%b %d, %a')}] Week {self.current_date.isocalendar()[1]} of {self.current_date.strftime('%B')}
{self.indent}    Past 30d: {past_30d.strftime('%b %d')} - {(self.current_date - timedelta(days=1)).strftime('%b %d')}
{self.indent}    Next 60d: {(self.current_date + timedelta(days=1)).strftime('%b %d')} - {next_60d.strftime('%b %d')}
"""
        if use_key_dates:
            context += f"""
{self.indent}    # Key Dates
{self._get_key_dates()}"""
        context += f"""
{self.indent}</CalendarContext>"""

        return context


def datasets_xml_format_callback(**kwargs):
    meta_prompt = kwargs.get("meta_prompt")
    robot: Robot = kwargs.get("robot")
    datasets = robot.datasets.related_objects
    knowledge_base = ""
    dynamic_background = kwargs.get("dynamic_background")
    logging.info(
        f"[prompt_menager - datasets_xml_format_callback]: dynamic_background: {dynamic_background}"
    )
    if dynamic_background and isinstance(dynamic_background, str):
        try:
            dynamic_background = json.loads(dynamic_background)
        except json.JSONDecodeError:
            raise ValueError(
                f"dyanmic_background is not a valid JSON string: {dynamic_background}"
            )
    if dynamic_background:
        """
        dynamic_background 结构示例
        {
            "type": "user_context",
            "description": "This section provides additional contextual information about the user and session to assist in processing their requests.",
            "context_infomation": [
                {
                    "description": "The correspondence between the user's phone number and the encrypted information is represented by the variables phone and encrypted_phone. phone represents the user's real phone number, while encrypted_phone represents the encrypted string of the user's real phone number.",
                    "phone": "13012341234",
                    "encrypted_phone_num": "esi3j4k5l6m7n8o9p0q"
                }
            ]
        }
        """
        dynamic_background_obj = {
            "type": "user_context",
            "description": "This section provides additional contextual information about the user and session to assist in processing their requests.",
            "context_infomation": dynamic_background,
        }
        dynamic_background_json = json.dumps(
            dynamic_background_obj, ensure_ascii=False, indent=4
        )
        dynamic_background = f"<UserContext>\n{dynamic_background_json}\n</UserContext>"
    else:
        dynamic_background = ""
    if datasets:
        """
        <KnowledgeBase>
            <Sources>
                <Source>
                    <Name>KnowledgeSource1</Name>
                    <Description>Description of the first knowledge source.</Description>
                </Source>
                <Source>
                    <Name>KnowledgeSource2</Name>
                    <Description>Description of the second knowledge source.</Description>
                </Source>
                <!-- Add more sources as needed -->
            </Sources>
            <Integration>
                For queries related to specific knowledge sources, initiate a call to the RAG tool to retrieve accurate and up-to-date information.
                Ensure that the response prioritizes retrieving verified information from the knowledge sources instead of generating uncertain content.
            </Integration>
        </KnowledgeBase>
        """
        knowledge_base_ele = ET.Element("KnowledgeBase")
        sources_element = ET.SubElement(knowledge_base_ele, "Sources")
        # 判断是否有vectorfiles, 如果有dataset, 但是dataset中没有vectorfiles, 就不需要生成knowledge_base
        empty_vectorfiles_flag = True
        for dataset in datasets:
            vectorfiles = dataset.vectorfiles.related_objects
            if not vectorfiles:
                continue
            # 如果有vectorfiles，就不是空的
            empty_vectorfiles_flag = False
            source_element = ET.SubElement(sources_element, "Source")
            name_element = ET.SubElement(source_element, "Name")
            name_element.text = dataset.name
            if dataset.description:
                description_element = ET.SubElement(source_element, "Description")
                description_element.text = dataset.description
        if empty_vectorfiles_flag:
            knowledge_base = ""
        else:
            integration_element = ET.SubElement(knowledge_base_ele, "Integration")
            integration_element.text = "For queries related to specific knowledge sources, initiate a call to the RAG tool to retrieve accurate and up-to-date information. Ensure that the response prioritizes retrieving verified information from the knowledge sources instead of generating uncertain content."
            knowledge_base = ET.tostring(
                knowledge_base_ele, encoding="unicode", pretty_print=True
            )
    meta_prompt = meta_prompt.format(
        knowledge_base=knowledge_base, dynamic_background=dynamic_background
    )
    return meta_prompt


class MetaPromptProcessor:
    def __init__(
        self,
        question_in: QuestionIn,
        robot: Robot,
        tools: List[ToolInfo] = None,
        callback: callable = datasets_xml_format_callback,
    ):
        self.question_in = question_in
        self.robot = robot
        self.tools = tools
        self.callback = callback
        self.for_tts = for_tts_meta_with_rag_prompt
        self.for_chat = for_chat_meta_with_rag_prompt

    def _perpare_user_prompt(self, user_prompt) -> str:
        # 预处理用户自定义的 prompt, 进行一些容错处理
        # 替换处理 meta prompt 中存在`{`或`}`的问题, 将其替换为`{{`或`}}`
        user_prompt = user_prompt.replace("{", "{{").replace("}", "}}")
        return user_prompt

    def get_meta_prompt(self, dynamic_background, mode) -> str:
        # meta prompt 预处理
        logging.info(f"[prompt_manager - get_meta_prompt]: mode: {mode}")
        robot_prompt = self._perpare_user_prompt(self.robot.prompt)
        now = datetime.now()
        date = {
            "current_date": now.strftime("%Y-%m-%d"),
            "current_time": now.strftime("%H:%M:%S"),
            "weekday": now.strftime("%A"),
        }
        # calendar context
        calendar = CalendarContextGenerator(
            current_date=now, indent_num=2
        ).generate_calendar_context()

        if mode == "tts":
            meta_prompt = self.for_tts.format(
                robot_prompt=robot_prompt,
                knowledge_base="{knowledge_base}",
                dynamic_background="{dynamic_background}",
                calendar=calendar,
                **date,
            )
            meta_prompt = self.callback(
                meta_prompt=meta_prompt,
                question_in=self.question_in,
                robot=self.robot,
                dynamic_background=dynamic_background,
            )
        elif mode == "chat":
            meta_prompt = self.for_chat.format(
                robot_prompt=robot_prompt,
                knowledge_base="{knowledge_base}",
                dynamic_background="{dynamic_background}",
                calendar=calendar,
                **date,
            )
            meta_prompt = self.callback(
                meta_prompt=meta_prompt,
                question_in=self.question_in,
                robot=self.robot,
                dynamic_background=dynamic_background,
            )
        else:
            raise ValueError(f"Unknown mode: {mode}")
        return meta_prompt

    async def gen_meta_prompt(
        self,
        prompt_text: str,
        prompt_type: PromptType,
        dynamic_background: Optional[List] = None,
        datasets: Optional[List[DatasetInfo]] = None,
        tools: Optional[List[ToolInfo]] = None,
        **kwargs,
    ) -> str:
        logging.info(f"[prompt_manager - gen_meta_prompt]: prompt_type: {prompt_type}")
        # 构建PromptDirector需要用到的参数 - business_params
        business_params = BusinessParams(
            prompt_text=prompt_text, role_description="", instructions=[]
        )
        # 构建PromptDirector需要用到的参数 - session_params
        session_params = None
        if dynamic_background:
            context_description = "This section provides additional contextual information about the user and session to assist in processing their requests."
            session_params = SessionParams(
                description=context_description,
                context_items=dynamic_background,
                format_type="json",
            )
        # 构建PromptDirector需要用到的参数 - knowledge_params
        knowledge_params = None
        if datasets:
            knowledge_params = KnowledgeParams(
                datasets=datasets,
                tag_name="KnowledgeBase",
            )
        # 构建PromptDirector需要用到的参数 - tool_params
        tool_params = None
        if tools:
            tool_params = ToolParams(tools=tools)
        # 构建PromptDirector需要用到的参数 - general_params
        general_params = None
        if kwargs:
            general_params_dic = {}
            for param_name in GeneralParams.__annotations__.keys():
                if param_name in kwargs:
                    value = kwargs.pop(param_name)
                    general_params_dic[param_name] = value
            general_params = GeneralParams(**general_params_dic)
        # 创建协调器
        director = PromptDirector()
        prompt = await director.create_prompt(
            prompt_type=prompt_type,
            business_params=business_params,
            session_params=session_params,
            knowledge_params=knowledge_params,
            tool_params=tool_params,
            general_params=general_params,
            **kwargs,
        )
        return prompt
