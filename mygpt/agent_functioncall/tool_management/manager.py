"""核心管理器"""
import inspect
import os
import importlib
import pkgutil
import functools
from loguru import logger as logging
from typing import List, Dict, Any, Optional, Callable, Type, Union

from mygpt.agent_functioncall.types import ToolInfo, ToolDefinition


def tool(name=None, description=None, params=None, return_direct: bool = False):
    """工具装饰器，用于标记一个方法为工具
    
    Args:
        name: 工具名称，默认使用方法名称
        description: 工具描述，默认使用方法文档
        params: 参数说明，格式为 {参数名: {"description": 描述, "required": 是否必传, "type": 类型}}
    
    Example:
        @tool(
            name="search", 
            description="搜索信息",
            params={
                "query": {
                    "description": "搜索关键词",
                    "required": True,
                    "type": "string"
                    "enum": ["a", "b", "c"]
                }
            }
        )
        def search_info(self, query: str):
            pass
    """
    def decorator(func):
        func._tool_info = {
            "name": name,
            "description": description,
            "params": params or {},
            "return_direct": return_direct
        }
        return func
    return decorator


class ToolManager:
    """工具管理器"""
    _instance = None
    _initialized = False

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(ToolManager, cls).__new__(cls)
            cls._instance.tool_definitions = {}  # 工具名称 -> ToolDefinition
            cls._instance.tool_map = {}  # 工具名称 -> ToolInfo
            cls._instance.class_map = {}  # 类名 -> 类
            cls._instance.tool_method_map = {}  # 工具名 -> 方法名
            cls._instance.method_tool_map = {}  # 方法名 -> 工具名称
            cls._instance.tool_class_map = {}  # 工具名 -> 类
        return cls._instance

    def __init__(self, auto_scan=True):
        """初始化工具管理器
        
        Args:
            auto_scan: 是否自动扫描工具目录
        """
        # 保证只初始化一次
        if not self._initialized:
            self._initialized = True
            if auto_scan:
                self.scan_tools_directory()

    def _get_param_type(self, param):
        """获取参数类型"""
        type_map = {
            str: "string",
            int: "integer",
            float: "number",
            bool: "boolean",
            list: "array",
            dict: "object",
            type(None): "null",
        }
        try:
            return type_map.get(param.annotation, "string")
        except:
            return "string"

    def parse_tool_definition(self, cls: Type, method_name: str) -> ToolDefinition:
        """解析工具定义

                整合了_create_placeholder_tool_info和parse_method_to_tool的逻辑
                """
        method = getattr(cls, method_name)

        # 获取工具注解信息
        tool_name = None
        tool_description = None
        params_info = None
        return_direct = False

        # 检查是否有@tool装饰器
        if hasattr(method, '_tool_info'):
            tool_info = method._tool_info
            tool_name = tool_info.get('name')
            tool_description = tool_info.get('description')
            params_info = tool_info.get('params')
            return_direct = tool_info.get('return_direct', False)

        # 解析方法签名
        try:
            signature = inspect.signature(method)
        except ValueError:
            signature = inspect.Signature()

        # 解析参数属性
        properties = {}
        for param in signature.parameters.values():
            if param.name == 'self':
                continue

            # 获取参数类型
            param_type = self._get_param_type(param)

            # 使用注解中的参数信息
            param_property = {"type": param_type}
            if params_info and param.name in params_info:
                param_info = params_info[param.name]

                if "type" in param_info:
                    param_property["type"] = param_info["type"]

                if "description" in param_info:
                    param_property["description"] = param_info["description"]

                if "enum" in param_info:
                    param_property["enum"] = param_info["enum"]

                if "items" in param_info:
                    param_property["items"] = param_info["items"]

            properties[param.name] = param_property

        # 获取必传参数
        required = []
        for param in signature.parameters.values():
            if param.name == 'self':
                continue

            is_required = param.default == inspect._empty

            if params_info and param.name in params_info and "required" in params_info[param.name]:
                is_required = params_info[param.name]["required"]

            if is_required:
                required.append(param.name)

        # 确定工具名称和描述
        final_name = tool_name or f"{cls.__name__}.{method_name}"
        description = tool_description or method.__doc__ or f"{cls.__name__}类的{method_name}方法"

        return ToolDefinition(
            name=final_name,
            description=description,
            properties=properties,
            required=required,
            class_type=cls,
            method_name=method_name,
            is_api_source=False,
            return_direct=return_direct,
            class_info={
                "cls": cls,
                "method": method
            }
        )

    def _register_tool_class(self, cls: Type):
        """注册工具类，但不实例化"""
        cls_name = cls.__name__
        self.class_map[cls_name] = cls

        # 获取类中带有@tool注解的方法
        for method_name in dir(cls):
            if method_name.startswith('_'):
                continue

            method = getattr(cls, method_name)
            if inspect.isfunction(method) or inspect.ismethod(method):
                # 只注册带有@tool注解的方法
                if hasattr(method, '_tool_info'):
                    # 解析工具定义
                    tool_def = self.parse_tool_definition(cls, method_name)
                    self.tool_definitions[tool_def.name] = tool_def

                    # 添加方法名到工具名称的映射
                    self.method_tool_map[method_name] = tool_def.name

    def get_tool_definition(self, name: str) -> Optional[ToolDefinition]:
        """获取工具定义"""
        # 先尝试直接获取
        if name in self.tool_definitions:
            return self.tool_definitions[name]
        # 尝试通过方法名找到对应的工具名
        if name in self.method_tool_map:
            tool_name = self.method_tool_map[name]
            return self.tool_definitions.get(tool_name)
        return None

    def get_tool(self, tool_name, context=None, agent=None) -> ToolInfo:
        """获取工具实例
        每次调用都会创建新的ToolInfo实例，避免线程安全问题
        """
        # 获取工具定义
        tool_def = self.get_tool_definition(tool_name)
        if not tool_def:
            logging.error(f"[ToolManager] get_tool 未找到工具定义 {tool_name}")
            raise ValueError(f"未找到工具定义 {tool_name}")

        # 获取类和方法名
        cls = tool_def.class_type
        method_name = tool_def.method_name

        # 实例化类
        try:
            # 根据初始化参数决定如何实例化
            init_sig = inspect.signature(cls.__init__)
            kwargs = {}

            if 'agent' in init_sig.parameters and agent:
                kwargs['agent'] = agent
            if 'context' in init_sig.parameters and context:
                kwargs['context'] = context

            instance = cls(**kwargs)
            method = getattr(instance, method_name)

            # 创建新的工具信息
            tool_info = ToolInfo(
                name=tool_def.name,
                description=tool_def.description,
                properties=tool_def.properties,
                required=tool_def.required,
                tool_instance=instance,
                method=method,
                is_api_source=tool_def.is_api_source,
                class_info=tool_def.class_info
            )

            return tool_info
        except Exception as e:
            logging.error(f"[ToolManager] get_tool 实例化工具类 {cls.__name__} 失败: {str(e)}")
            raise e

    def get_tools(self, tool_names: List[str], context=None, agent=None) -> List[ToolInfo]:
        """获取工具实例列表"""
        return [self.get_tool(tool_name, context, agent) for tool_name in tool_names]

    def scan_tools_directory(self, base_path: str = None):
        """扫描工具目录，识别工具类和方法"""
        if base_path is None:
            # 默认使用当前文件所在目录作为基础路径
            base_path = os.path.dirname(os.path.abspath(__file__))
        
        # 扫描tools目录
        tools_path = os.path.join(base_path, "tools")
        if not os.path.exists(tools_path):
            return
            
        # 获取包路径
        package_path = ".".join(os.path.abspath(base_path).split(os.sep)[-3:])
        tools_package = f"{package_path}.tools"
        
        # 遍历包中的模块
        for _, module_name, is_pkg in pkgutil.iter_modules([tools_path]):
            if is_pkg:  # 如果是包，跳过
                continue
                
            # 动态导入模块
            module = importlib.import_module(f"{tools_package}.{module_name}")
            
            # 查找模块中的类
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if inspect.isclass(attr) and attr.__module__ == module.__name__:
                    # 类注册，但不实例化
                    self._register_tool_class(attr)

    def initialize(self):
        """手动初始化管理器，扫描工具目录
        
        当auto_scan=False时，可以使用此方法手动初始化
        """
        self.scan_tools_directory()


if __name__ == '__main__':
    # 测试工具注解
    class TestTool:
        def __init__(self, context=None, agent=None):
            self.context = context
            self.agent = agent
            
        @tool(
            name="search", 
            description="搜索信息",
            params={
                "query": {
                    "description": "搜索关键词",
                    "required": True,
                    "type": "string",
                    "enum": ["关键字1", "关键字2"]
                },
                "limit": {
                    "description": "返回结果数量",
                    "required": False,
                    "type": "integer"
                }
            }
        )
        def search_info(self, query: str, limit: int = 10):
            """搜索信息"""
            print(self)
            print(self.agent, self.context)
            return f"搜索结果: {query}, 数量: {limit}"
            
        def normal_method(self, param: str):
            """普通方法"""
            return f"普通结果: {param}"
    
    # 测试代码
    manager = ToolManager(auto_scan=True)  # 关闭自动扫描，手动注册
    
    # 手动注册测试类
    manager._register_tool_class(TestTool)
    
    # 获取所有工具名称
    print(f"已注册工具: {list(manager.tool_map.keys())}")

    # 通过方法名获取工具
    tool1 = manager.get_tool("search")
    if tool1:
        print(f"工具名称: {tool1.name}")
        print(f"工具描述: {tool1.description}")
        print(f"工具参数: {tool1.properties}")
        print(f"必传参数: {tool1.required}")

    # 再次实例化，验证单例模式
    manager2 = ToolManager()
    print(f"是否为同一实例: {manager is manager2}")

    # 获取工具实例, 执行方法
    tool = manager.get_tool("search")
    result = tool.method("搜索关键字", 5)

    tools = manager.get_tools(["search", "find_doc_by_ids"], agent="agent", context="context")
    for tool in tools:
        if tool.name == "find_doc_by_ids":
            result = tool.method(["doc123", "doc456"])
            print(result)
