"""MCP适配器"""

import json
from typing import Dict, Any, List

from mygpt.agent_functioncall.tool_management.adapters.base import ToolAdapter
from mygpt.agent_functioncall.tool_management.schemas import (
    ToolDefinition,
    ToolCallData,
    ToolResult,
)


class MCPAdapter(ToolAdapter):
    """MCP协议适配器"""

    def format_tool_definition(self, tool_def: ToolDefinition) -> Dict[str, Any]:
        """将工具定义转换为MCP格式"""
        return {
            "type": "function",
            "function": {
                "name": tool_def.name,
                "description": tool_def.description,
                "parameters": {
                    "type": "object",
                    "properties": tool_def.parameters,
                    "required": tool_def.required,
                },
            },
        }

    def parse_tool_call(self, raw_data: Dict[str, Any]) -> ToolCallData:
        """解析MCP格式的工具调用"""
        function_data = raw_data.get("function", {})
        arguments = function_data.get("arguments", "{}")

        # 处理arguments可能是字符串或字典的情况
        if isinstance(arguments, str):
            try:
                args_dict = json.loads(arguments)
            except:
                args_dict = {}
        else:
            args_dict = arguments

        return ToolCallData(
            tool_call_id=raw_data.get("id", ""),
            name=function_data.get("name", ""),
            arguments=args_dict,
            raw_arguments=(
                arguments if isinstance(arguments, str) else json.dumps(arguments)
            ),
        )

    def format_tool_result(self, result: ToolResult) -> Dict[str, Any]:
        """格式化为MCP工具结果格式"""
        return {
            "role": "tool",
            "tool_call_id": result.tool_call_id,
            "content": result.content,
        }
