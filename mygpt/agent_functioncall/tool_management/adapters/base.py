"""适配器基类"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List

from ..schemas import ToolDefinition, ToolCallData, ToolResult


class ToolAdapter(ABC):
    """工具协议适配器基类"""

    @abstractmethod
    def format_tool_definition(self, tool_def: ToolDefinition) -> Dict[str, Any]:
        """将工具定义转换为特定协议格式"""
        pass

    @abstractmethod
    def parse_tool_call(self, raw_data: Dict[str, Any]) -> ToolCallData:
        """解析工具调用请求"""
        pass

    @abstractmethod
    def format_tool_result(self, result: ToolResult) -> Dict[str, Any]:
        """格式化工具执行结果"""
        pass

    def format_all_tools(self, tools: List[ToolDefinition]) -> List[Dict[str, Any]]:
        """格式化工具列表"""
        return [self.format_tool_definition(tool) for tool in tools]
