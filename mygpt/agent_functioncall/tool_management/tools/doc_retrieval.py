import json
from typing import List
from loguru import logger as logging

from mygpt.agent_functioncall.tool_management.manager import tool
from mygpt.agent_functioncall.types import Result
from mygpt.opensearch_knowledge import OpenSearchKnowledgeClient
from mygpt.dao.opensearch_dao.knowledge_docs_dao import knowledge_docs_dao


class DocRetrievalTool:
    def __init__(self, context, agent=None):
        self.agent = agent
        self.context = context
        self.client = OpenSearchKnowledgeClient.get_instance()
        self.knowledge_docs_dao = knowledge_docs_dao

    @tool(
        name="find_doc_by_ids",
        description="根据文档ID列表获取对应的文档内容。提供一组文档ID，返回这些文档的详细信息，包括标题和正文内容。",
        params={
            "doc_ids": {
                "description": "要检索的文档ID列表，例如 [\"doc123\", \"doc456\"]。每个ID应该是一个唯一标识文档的字符串。",
                "required": True,
                "type": "array",
                "items": {
                    "type": "string"
                }
            }
        },
        return_direct=False
    )
    async def find_doc_by_ids(
        self,
        doc_ids: List[str],
    ) -> Result:
        """根据文档ID列表获取对应的文档内容。提供一组文档ID，返回这些文档的详细信息，包括标题和正文内容。"""
        if not doc_ids:
            return Result(value="doc_ids cannot be empty")
        # 获取文档内容
        logging.info(f"【DocRetrievalTool.doc_retrevial_tool】doc_ids: {doc_ids}")
        source = [
            "doc_id",
            "title",
            "content",
        ]
        rs = await self.client.aget_by_doc_ids(
            doc_ids=doc_ids,
            fields=source,
        )
        logging.info(
            f"【DocRetrievalTool.doc_retrevial_tool】received data from opensearch, hits: {rs.get('hits', {}).get('total', 0)}")
        hits = rs.get("hits", {}).get("hits", [])
        if not hits:
            logging.warning(
                f"【DocRetrievalTool.doc_retrevial_tool】No document found in the Knowledge Base. doc_ids: {doc_ids}")
            return Result(value="No document found in the Knowledge Base.")
        documents = []
        for hit in hits:
            doc_dic = hit.get("_source", {})
            doc = {
                "doc_id": doc_dic.get("doc_id", ""),
                "title": doc_dic.get("title", ""),
                "content": doc_dic.get("content", ""),
            }
            documents.append(doc)
        result = Result(value=json.dumps(documents, ensure_ascii=False, indent=4))
        return result

    @tool(
        name="find_docs_info_by_dir_ids",
        description="根据目录ID列表获取对应的目录信息。提供一组目录ID，返回这些目录下包含的文档summary的summary信息. 注意, 此工具仅会返回当前目录下的文档summary, 不会返回子目录下的文档信息.",
        params={
            "dir_ids": {
                "description": "要检索的目录ID列表，例如 [\"dir123\", \"dir456\"]。每个ID应该是一个唯一标识目录的字符串。",
                "required": True,
                "type": "array",
                "items": {
                    "type": "string"
                }
            }
        },
        return_direct=False
    )
    async def find_docs_info_by_dir_ids(
            self,
            dir_ids: List[str]
    ) -> Result:
        """根据目录ID列表获取对应的目录信息。
        提供一组目录ID，返回这些目录下包含的文档summary信息, 以及子目录的描述信息
        """
        if not dir_ids:
            return Result(value="[ERROR] doc_ids cannot be empty")
        # 获取文档内容
        logging.info(f"【DocRetrievalTool.find_info_by_dir_ids】dir_ids: {dir_ids}")
        rs = await self.knowledge_docs_dao.find_docs_info_by_dir_ids(
            dir_ids=dir_ids,
            fields=None,
        )
        logging.info(
            f"【DocRetrievalTool.find_info_by_dir_ids】received data from opensearch, hits: {rs.get('hits', {}).get('total', 0)}")
        hits = rs.get("hits", {}).get("hits", [])
        if not hits:
            logging.warning(
                f"【DocRetrievalTool.find_info_by_dir_ids】No document found in the Knowledge Base. doc_ids: {dir_ids}")
            return Result(value=f"[INFO] No document and directory found in the Knowledge Base. dir_ids: {dir_ids}")
        documents = []
        for hit in hits:
            doc_dic = hit.get("_source", {})
            doc = {
                "doc_id": doc_dic.get("doc_id", ""),
                "title": doc_dic.get("title", ""),
                "summary": doc_dic.get("summary", ""),
                "path": doc_dic.get("path"),
                "tags": doc_dic.get("tags"),
                "topics": doc_dic.get("topics"),
                "missing_key_aspects": doc_dic.get("missing_key_aspects")
            }
            documents.append(doc)
        result = Result(value=json.dumps(documents, ensure_ascii=False, indent=4))
        return result
