# 目录结构
```textmate
agent_functioncall/
└── tool_management/
    ├── __init__.py               # 导出核心组件
    ├── schemas.py                # 数据模型定义
    ├── enums.py                  # 枚举和常量
    ├── registry.py               # 工具注册中心
    ├── manager.py                # 核心管理器
    ├── executor.py               # 工具执行器
    ├── adapters/                 # 协议适配器
    │   ├── __init__.py
    │   ├── base.py               # 适配器基类/接口
    │   ├── mcp.py                # MCP协议适配器
    │   └── legacy.py             # 旧版协议适配器
    ├── tools/                    # 内置工具实现
    │   ├── __init__.py
    │   ├── core.py               # 核心工具集
    │   ├── rag.py                # RAG工具集
    │   └── research.py           # 研究工具集
    └── utils/                    # 工具函数
        ├── __init__.py
        └── converters.py         # 格式转换工具
```


# 工具创建说明


