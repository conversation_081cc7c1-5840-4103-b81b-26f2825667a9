import re
from datetime import datetime
from typing import List, Async<PERSON>terator, Optional, Callable

from fastapi import APIRouter, Depends, Request, Security
from requests.exceptions import Timeout
from uuid import UUID, uuid4
import requests

import asyncio
import time
from uuid import UUID

from loguru import logger as logging

from mygpt.agent_exp.core.mod.langchain_core_messages_base import BaseMessage
from mygpt.agent_exp.core.storage import save_message
from mygpt.agent_exp.utils.using_util import convert_base_messages_to_openai_format
from mygpt.endpoints.function_call_api import function_call_api
from mygpt.enums import UserIntent, AIConfigType, OpenAIModel
from mygpt.error import sentry_sdk_capture_exception
from mygpt.models import Robot
from mygpt.openai_utils import (
    user_intent_detect,
    question_answer_turbo_16k_with_function_call,
    get_chat_history_turbo,
    get_chat_model,
    question_answer_with_function_call,
)
from mygpt.schemata import QuestionIn

from langchain_community.tools import APIOperation
from skywalking.decorators import trace
from langchain.utilities.openapi import OpenAPISpec

for_tts_meta_prompt = """
<MetaPrompt>
    <Objective>
        Generate natural language output suitable for Text-to-Speech (TTS) model input.
    </Objective>
    <OutputRequirements>
        <Format>
            Use natural language without Markdown formatting or special symbols.
            Avoid using special symbols such as 「 」, @, #, $, %, &, *, or other non-alphanumeric characters unless specifically requested.
        </Format>
        <Language>
            Please refer to the language used in the BusinessPrompt section and use it as the final output language.
        </Language>
        <Style>
            The output should be concise and clear, ensuring ease of understanding.
        </Style>
        <CustomContent>
            Seamlessly integrate the client-provided business-specific prompt content into the response.
        </CustomContent>
    </OutputRequirements>
</MetaPrompt>
<BusinessPrompt>
{robot_prompt}
</BusinessPrompt>
"""


class MyOpenAPISpec(OpenAPISpec):
    @classmethod
    def from_url(cls, url: str) -> "MyOpenAPISpec":
        try:
            response = requests.get(url, timeout=2)
            return cls.from_text(response.text)
        except Timeout:
            raise ValueError("The request exceeded the timeout duration.")
        except requests.exceptions.RequestException as e:
            raise ValueError(
                f"An error occurred while fetching the OpenAPI specification: {e}"
            )


async def parse_function_call(function_call):
    # function call 的名称需要遵守 ^[a-zA-Z8-9_-](1,64)$ 这个规则，这里需要手动处理成符合规则的名称
    if not re.match(r"^[a-zA-Z8-9_-]{1,64}$", function_call.function_call_name):
        function_call_name = (
            function_call.function_call_name.strip()
            .replace(" ", "_")
            .replace("-", "_")
            .replace(".", "_")
            .replace("/", "_")
            .replace("\\", "_")
        )
        function_call_name = function_call_name[:64]
        function_call.function_call_name = function_call_name

    # parse function_call
    function_description = {
        "name": function_call.function_call_name,
        "description": function_call.function_call_description,
        "parameters": {"type": "object", "properties": {}},
    }
    logging.info(f"function_call.openapi_url: {function_call.openapi_url}")

    # 判断如果写了接口说明，不走下面读取openapi直接返回
    if (
        function_call.body is not None
        and function_call.body != ""
        and function_call.body != "{}"
    ):
        function_description["parameters"]["properties"] = function_call.body
        if (
            function_call.body_required is not None
            and function_call.body_required != "[]u"
        ):
            function_description["parameters"]["required"] = function_call.body_required
        return {
            "function_description": function_description,
            "operation": {
                "function_call_name": function_call.function_call_name,
                "operation": None,
                "function_call_info": function_call,
            },
        }

    # 读取openapi
    try:
        spec = MyOpenAPISpec.from_url(function_call.openapi_url)
        operation = APIOperation.from_openapi_spec(
            spec, function_call.path, function_call.method
        )
    except Exception as e:
        operation = None
        if function_call.function_call_description:
            operation = APIOperation(
                operation_id=str(uuid4()),
                description=function_call.function_call_description,
                base_url=function_call.server_url,
                path=function_call.path,
                method=function_call.method,
                properties=[],
                request_body=None,
            )
            function_description["parameters"]["properties"]["value"] = {
                "type": "object",
                "description": "any value",
            }
        else:
            logging.info(f"【function_call】 ERROR OpenAPISpec.from_url: {e}")
        return {
            "function_description": function_description,
            "operation": {
                "function_call_name": function_call.function_call_name,
                "operation": operation,
                "function_call_info": function_call,
            },
        }
    function_description["description"] = operation.description
    required = []
    for prop in operation.properties:
        function_description["parameters"]["properties"][prop.name] = {
            "type": str(prop.type),
            "description": prop.description,
        }
        if prop.required:
            required.append(prop.name)
    if operation.request_body is not None:
        for prop in operation.request_body.properties:
            function_description["parameters"]["properties"][prop.name] = {
                "type": str(prop.type),
                "description": prop.description,
            }
            if prop.required:
                required.append(prop.name)

    # 拼接是否有必填参数
    if len(required) > 0:
        function_description["parameters"]["required"] = required
    return {
        "function_description": function_description,
        "operation": {
            "function_call_name": function_call.function_call_name,
            "operation": operation,
            "function_call_info": function_call,
        },
    }


@trace()
async def send_function_call_with_prompt(
    user_question: str, system_message: str, history, ai_obj: Robot, session_id: str
):
    start_time = time.time()
    # 识别用户信息里面有没有关键词
    # match_keywords_list = await Agent.filter(
    #     robot_id=ai_id, deleted_at__isnull=True
    # ).values_list("matching_keywords", flat=True)
    # match_keywords_list = [item for item in match_keywords_list if item]
    # if len(match_keywords_list) > 0:
    #     match_keywords_list = [
    #         item for sublist in match_keywords_list for item in sublist
    #     ]
    #     pattern = r"(?:{})".format("|".join(match_keywords_list))
    #     contains_keyword = bool(re.search(pattern, user_question))
    #     if not contains_keyword:
    #         logging.info("【function_call】 user_question not in keywords_list")
    #         return False, None, None, None
    function_call_list = ai_obj.apis.related_objects

    # 整理历史信息
    openai_history = convert_base_messages_to_openai_format(history)
    messages = []
    if system_message is not None:
        messages.append(
            {
                "role": "system",
                "content": system_message
                + "The current time zone time:"
                + datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }
        )
    messages.extend(openai_history)
    # if session_id:
    #     history_list, history_list_count = await get_chat_history_turbo(session_id)
    #     if history_list:
    #         for history in history_list:
    #             if isinstance(history, AIMessage):
    #                 message.append(
    #                     {
    #                         'role': 'assistant',
    #                         'content': history.content
    #                     }
    #                 )
    #             else:
    #                 message.append(
    #                     {
    #                         'role': 'user',
    #                         'content': history.content
    #                     }
    #                 )

    messages.append({"role": "user", "content": user_question})
    logging.info(f"【function_call】 message: {messages}")

    if function_call_list is None or len(function_call_list) == 0:
        logging.info("【function_call】 function_call_list is None")
        chat = get_chat_model(
            model=OpenAIModel.default(), streaming=False, max_tokens=4096
        )
        res = await chat.ainvoke(input=messages)
        return False, res, None, None

    # 解析function_call_list,提取出标准的function_description和operations类(用于方便调用)
    tasks = [parse_function_call(function_call) for function_call in function_call_list]
    results = await asyncio.gather(*tasks)

    function_descriptions = [result["function_description"] for result in results]
    operations = {
        result["operation"]["function_call_name"]: result["operation"]["operation"]
        for result in results
    }
    function_call_infos = {
        result["operation"]["function_call_name"]: result["operation"][
            "function_call_info"
        ]
        for result in results
    }
    if function_descriptions is None:
        logging.info("【function_call】 function_descriptions is None")
        return False, None, None, None
    logging.info(f"【function_call】 function_descriptions: {function_descriptions}")

    # 日志计时
    end_time = time.time()
    logging.info(f"【function_call】 parse function_call time: {end_time - start_time}")

    start_time = time.time()
    gpt_response = await question_answer_turbo_16k_with_function_call(
        messages=messages,
        functions=function_descriptions,
        function_call="auto",
        max_tokens=4096,
        stream=False,
    )
    end_time = time.time()
    logging.info(f"【function_call】 response time: {end_time - start_time}")

    logging.info(f"【function_call】response from GPT: {gpt_response}")
    # 解析GPT返回结果
    if gpt_response and gpt_response.additional_kwargs:
        # response = await function_call_api(gpt_response['choices'][0], operations, function_call_infos)
        return True, gpt_response, operations, function_call_infos
    else:
        return False, gpt_response, None, None


@trace()
async def analyze_question(
    question: QuestionIn,
    ai_obj: Robot,
    chat_history_str: str = None,
    user_id: str = None,
):
    ai_lang = ai_obj.get_config(AIConfigType.SOURCE_LANG)
    if not ai_lang:
        ai_lang = "en"
    resp = None
    user_intent = UserIntent.ASK_INFO_ABOUT_COMPANY.value
    query_key_words = question.question
    response_language = ai_lang
    message_tokens = 0

    start_time = time.time()
    try:
        resp, message_tokens = await user_intent_detect(
            session_id=question.session_id,
            question=question.question,
            chat_history_str=chat_history_str,
            dictionaries=ai_obj.dictionaries(question.question),
            ai_language=ai_lang,
            user_id=user_id,
            ai_id=str(ai_obj.id),
        )
    except Exception as e:
        logging.error(f"fail to explain question in error: {e}, response json:{resp}")
        sentry_sdk_capture_exception(e)
        user_intent = UserIntent.ASK_INFO_ABOUT_COMPANY.value
        query_key_words = question.question
        response_language = ai_lang
    finally:
        logging.info(
            f"【{question.message_id}】Handle question cost time: {time.time() - start_time} seconds"
        )

    if not resp:
        logging.warning(
            f"【{question.message_id}】error to detect user intent, ai:{ai_obj.id} question:{question}"
        )
    else:
        response_language = resp.get("question_language", None)
        user_intent = resp.get("user_intent", None)
        if not user_intent:
            logging.warning(
                f"【{question.message_id}】user_intent is None, ai:{ai_obj.id} question:{question}"
            )
        elif user_intent == UserIntent.ASK_INFO_ABOUT_BOT.value:
            pass
        else:
            if user_intent != UserIntent.ASK_INFO_ABOUT_COMPANY.value:
                logging.warning(f"unknown user intent, response:{resp}")
                user_intent = UserIntent.ASK_INFO_ABOUT_COMPANY.value

            query_key_words = resp.get("query_key_words", None)
            if not query_key_words:
                query_key_words = question.question

    logging.info(
        f"【{question.message_id}】ai:{ai_obj.id} detect question:{question.question}, ai_lang:{ai_lang}, response_language:{response_language}, query_key_words:{query_key_words}, resp: {resp}"
    )
    return (
        user_intent,
        response_language,
        query_key_words,
        message_tokens,
    )


main_prompt = """# Role
你是Sparticle公司的一名客服人员, 你的工作职责是与员工进行沟通.
旨在为员工进行每日酒精检查。需要按照特定顺序询问一系列问题并记录回答。
请以清晰、专业、友好的方式交流。
在整个对话中使用礼貌的中文。

# General
对话流程:
- 开始问候
“感谢您的来电。我是酒精检查中心的YUMI。请告诉我您的姓名和法人号码。”
- 问题列表:
用户回答法人号码和姓名
例：“法人号码〇的<姓名/>。” 聊天机器人复述并确认。
例：“〇号码的〇〇先生/女士，对吧？” 聊天机器人询问当前酒精残留情况。 “现在是否没有酒精残留？”
用户回答“没有残留”。
- 信息写入完成后, 请礼貌表示感谢, 并结束当前对话任务.
"""


# 2. 当所有信息收集完成后, 请调用  方法完成信息的写入.


async def send_function_call(
    user_question: str,
    ai_obj: Robot,
    history,
    user_id: str = None,
    query_key_words: str = "",
    session_id: str = "",
):
    if not ai_obj.prompt:
        raise ValueError(f"【ai_id: {str(ai_obj.id)}】ai_obj.prompt is Empty")

    res = await send_function_call_with_prompt(
        f"{user_question}", ai_obj.prompt, history, ai_obj, session_id
    )
    return res


async def run_function_call_agent(
    request: Request,
    question_in: QuestionIn,
    ai_obj: Robot,
    chat_history_str: str,
    user_id: str,
    event: asyncio.Event,
    start_time=time.time(),
):
    (
        user_intent,  # 用户输入的分类识别, 相当于意图检测, 是闲聊还是任务. (ABOUT_BOT, ABOUT_COMPANY)
        response_language,  # 检测用户使用的语言, 回复时按照用户使用的语言进行回复
        query_key_words,  # 当前问句中的关键信息 (相当于实体抽取)
        message_tokens,  # 执行当前任务消耗的token数量
    ) = await analyze_question(  # 解析用户意图, 使用的语言, 用于向量检索的关键词s
        question_in, ai_obj, chat_history_str, user_id
    )
    # 获取历史记录
    history, count = await get_chat_history_turbo(question_in.session_id, 12)
    # todo - 临时测试代码块: --------------------------------------------
    fc_handler = FunctionCallHandler(question_in, history, start_time=start_time)
    function_call_sta, gpt_response, operations, function_call_infos = (
        await fc_handler.send_function_call_with_prompt(
            question_in.question,
            history,
            ai_obj,
        )
    )
    # todo ------------------------------------------------------------
    # function_call_sta, gpt_response, operations, function_call_infos = (
    #     await send_function_call(
    #         f"{question_in.question}",
    #         ai_obj,
    #         history,
    #         user_id,
    #         query_key_words,
    #         question_in.session_id,
    #     )
    # )
    if function_call_sta:
        # 判断是否使用了function_call
        resp, max_tokens, send_info = await function_call_api(
            gpt_response,
            operations,
            function_call_infos,
            request,
            query_key_words,
            response_language,
            question_in,
            event=event,
        )
    else:
        if isinstance(gpt_response, AsyncIterator):
            # 如果 gpt_response 是一个迭代器, 那么存储处理的逻辑在迭代器中进行, 这里不需要处理.
            resp = gpt_response
        else:
            # 没有调用function_call, 需要对返回的回答进行存储处理.
            resp = gpt_response.content
            asyncio.create_task(save_message(question_in, resp, history))
    return function_call_sta, resp, operations, function_call_infos


import time


class FunctionCallHandler:
    def __init__(self, question_in: QuestionIn, history, start_time=time.time()):
        # user_question: str, system_message: str, history, ai_obj: Robot, session_id: str
        self.question_in = question_in
        self.history = history
        self.function_call_sta = None
        self.gpt_response = None
        self.operations = None
        self.function_call_infos = None
        # 对chunk进行处理的回调函数
        self.chunk_process_callback: Optional[Callable] = None
        self.content = []
        self.start_time = start_time
        self.first_token_time = None

    async def retry_anext(self, gpt_response_iter, retries=3, delay=1):
        """获取迭代器的第一个元素, 如果获取失败, 则重试, 防止迭代器为空"""
        for attempt in range(retries):
            try:
                return await asyncio.wait_for(anext(gpt_response_iter), timeout=10)
            except (StopAsyncIteration, asyncio.TimeoutError) as e:
                if attempt < retries - 1:
                    logging.warning(
                        f"Retrying anext due to: {e}, attempt {attempt + 1}"
                    )
                    await asyncio.sleep(delay)
                else:
                    logging.error(
                        f"Failed to get first element after {retries} attempts"
                    )
                    raise StopAsyncIteration

    async def message_token_handle(self, gpt_response_iter) -> AsyncIterator:
        """如果没有调用function_call, 需要对返回的回答进行存储处理, 处理token形式, 同时保存历史记录"""
        async for item in gpt_response_iter:
            self.content.append(item.content)
            if self.chunk_process_callback:
                chunk = self.chunk_process_callback(item)
            else:
                chunk = item
            yield chunk
        gpt_response_content = "".join(self.content)
        logging.info(
            f"【FunctionCallHandler】message_id: {self.question_in.message_id}, response from GPT: {gpt_response_content}"
        )
        # 保存历史记录
        asyncio.create_task(
            save_message(self.question_in, gpt_response_content, self.history)
        )

    async def function_call_handle(
        self, gpt_response_iter: AsyncIterator
    ) -> AsyncIterator:
        """大模型调用的处理函数, 通过这个函数判断是否调用了function_call
        如果调用FunctionCall, 则需要接收所有流式返回的信息, 然后调用api函数
        如果没有调用FunctionCall, 则直接返回迭代器
        """
        try:
            first_element = await self.retry_anext(gpt_response_iter)
            received_time = time.time()
            logging.info(
                f"【FunctionCallHandler】received first element, time: {received_time - self.start_time}"
            )
        except StopAsyncIteration:
            # 如果迭代器为空, 直接返回
            logging.warning(
                f"【FunctionCallHandler】function_call_handle stop async iteration, unable to get first element; message_id: {self.question_in.message_id} - {self.question_in.question}"
            )
            return
        # 判断第一个元素
        if first_element and first_element.additional_kwargs:
            gpt_response = first_element
            self.function_call_sta = True
            arguments_chunks = []
            additional_kwargs = {
                "function_call": {
                    "arguments": "",
                    "name": first_element.additional_kwargs["function_call"]["name"],
                }
            }
            # 迭代出全部结果
            async for item in gpt_response_iter:
                if item.additional_kwargs:
                    arguments_chunks.append(
                        item.additional_kwargs["function_call"]["arguments"]
                    )
                else:
                    logging.info(f"【FunctionCallHandler】item: {item}")
            additional_kwargs["function_call"]["arguments"] = "".join(arguments_chunks)
            gpt_response.additional_kwargs = additional_kwargs
            return gpt_response
        else:
            # 没有调用function_call, 直接返回迭代器
            self.function_call_sta = False
            return gpt_response_iter

    async def send_function_call_with_prompt(
        self, user_question: str, history, ai_obj: Robot, session_id: str = None
    ):
        start_time = time.time()
        # 对prompt进行模板填充
        # system_message = ai_obj.prompt
        system_message = for_tts_meta_prompt.format(robot_prompt=ai_obj.prompt)
        if not system_message:
            logging.error(
                f"【Agent Function Call】 ai_id: {ai_obj.id} - prompt is None"
            )
            raise ValueError(
                f"【Agent Function Call】 ai_id: {ai_obj.id} - prompt is None"
            )
        function_call_list = ai_obj.apis.related_objects
        # 整理历史信息
        openai_history = convert_base_messages_to_openai_format(history)
        messages = []
        if system_message is not None:
            messages.append(
                {
                    "role": "system",
                    "content": system_message
                    + "The current time zone time:"
                    + datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                }
            )
        messages.extend(openai_history)
        messages.append({"role": "user", "content": user_question})
        logging.info(f"【Agent Function Call】 message: {messages}")
        if function_call_list is None or len(function_call_list) == 0:
            logging.info("【Agent Function Call】 function_call_list is None")
            chat = get_chat_model(
                model=OpenAIModel.default(), streaming=False, max_tokens=4096
            )
            res = await chat.ainvoke(input=messages)
            return False, res, None, None
        # 解析function_call_list,提取出标准的function_description和operations类(用于方便调用)
        tasks = [
            parse_function_call(function_call) for function_call in function_call_list
        ]
        results = await asyncio.gather(*tasks)

        function_descriptions = [result["function_description"] for result in results]
        operations = {
            result["operation"]["function_call_name"]: result["operation"]["operation"]
            for result in results
        }
        function_call_infos = {
            result["operation"]["function_call_name"]: result["operation"][
                "function_call_info"
            ]
            for result in results
        }
        if function_descriptions is None:
            logging.info("【Agent Function Call】 function_descriptions is None")
            return False, None, None, None
        logging.info(
            f"【Agent Function Call】 function_descriptions: {function_descriptions}"
        )

        # 日志计时
        end_time = time.time()
        logging.info(
            f"【Agent Function Call】 parse function_call time: {end_time - start_time}"
        )

        # 请求调用function_call, 返回一个异步可迭代对象
        try:
            gpt_response_iter = question_answer_with_function_call(
                messages=messages,
                functions=function_descriptions,
                function_call="auto",
                max_tokens=4096,
                stream=True,
            )
        except Exception as e:
            logging.error(
                f"【Agent Function Call】question_answer_with_function_call error: {e}"
            )
            raise e
        # 交给辅助处理器进行处理
        gpt_response = await self.function_call_handle(gpt_response_iter)
        if self.function_call_sta:
            return self.function_call_sta, gpt_response, operations, function_call_infos
        else:
            # 没有调用function_call, 需要对返回的流式信息进行一些回调处理, 比如调整一下流式返回的格式, 最终完成输出后的数据保存操作.
            gpt_response = self.message_token_handle(gpt_response)
            return False, gpt_response, None, None
