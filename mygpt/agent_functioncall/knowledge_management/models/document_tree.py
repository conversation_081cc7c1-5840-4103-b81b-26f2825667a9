import uuid
from enum import Enum
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any, Union


class NodeType(str, Enum):
    """节点类型枚举"""
    ROOT = "root"
    CATEGORY = "category"
    DOCUMENT = "document"


@dataclass
class NodeChildren:
    """节点子集合"""
    _items: List["DocTreeNode"] = field(default_factory=list)  # 保持顺序的列表
    _lookup: Dict[str, "DocTreeNode"] = field(default_factory=dict)  # 用于快速查找的字典
    _id_lookup: Dict[str, "DocTreeNode"] = field(default_factory=dict)  # 用于ID查找的字典

    def add(self, node: "DocTreeNode") -> None:
        """添加子节点"""
        self._items.append(node)
        self._lookup[node.name] = node
        self._id_lookup[node.node_id] = node

    def get_by_name(self, name: str):
        """根据名称获取节点"""
        return self._lookup.get(name)

    def get_by_id(self, node_id: str):
        """根据ID获取节点"""
        return self._id_lookup.get(node_id)

    def __iter__(self):
        return iter(self._items)

    def __len__(self):
        return len(self._items)


@dataclass
class DocTreeNode:
    """目录树节点

    表示知识目录树中的一个节点，可以是分类或文档
    """
    node_id: str  # 节点ID
    name: str  # 节点名称
    node_type: NodeType  # 节点类型：'root', 'category', 'document'
    content: Optional[str] = None  # 文档内容
    summary: Optional[str] = None  # 文档摘要
    topics: List[str] = field(default_factory=list)  # 主题
    tags: List[str] = field(default_factory=list)  # 标签
    summary_token_count: int = 0  # 摘要的token数量
    content_token_count: int = 0  # 内容的token数量
    information_coverage: float = 0.0  # 信息覆盖率
    missing_key_aspects: List[str] = field(default_factory=list)  # 缺失的关键要素
    doc_path: List[str] = field(default_factory=list)  # 文档路径
    doc_path_str: str = ""  # 文档路径字符串

    directory_description: Optional[str] = None  # 目录描述
    direct_document_count: int = 0  # 文档数量 (当前目录下的文档数量)

    parent_id: Optional[str] = None  # 父节点ID
    parent: Optional["DocTreeNode"] = None  # 父节点
    children: NodeChildren = field(default_factory=NodeChildren)  # 子节点
    metadata: Dict[str, Any] = field(default_factory=dict)  # 节点元数据


@dataclass
class TreeConfig:
    """目录树配置"""
    enabled: bool = False  # 是否启用目录树
    max_depth: int = 3  # 最大深度
    auto_categorize: bool = False  # 是否自动分类
    root_name: str = "知识库"  # 根节点名称


@dataclass
class DocumentTree:
    """文档树

    用于构建、管理和格式化文档的树形结构
    """

    config: TreeConfig = field(default_factory=TreeConfig)  # 树配置
    root: Optional[DocTreeNode] = None  # 根节点
    # 设置一个自增的category_id
    category_id: int = 0
    root_node_id: str = ""  # 根目录ID

    def __post_init__(self):
        """初始化后的处理"""
        if self.root is None:
            # 创建根节点
            self.root = DocTreeNode(
                name=self.config.root_name,
                node_type=NodeType.ROOT,
                node_id=f"rt_{str(uuid.uuid4())[:8]}",
                parent_id=None,
                children=NodeChildren(),
                metadata={}
            )

    def set_root_dir_id(self, root_node_id: str):
        """设置根目录ID"""
        self.root_node_id = root_node_id
        if self.root:
            self.root.node_id = root_node_id

    def _normalize_path(self, path: Union[str, List[str]]) -> List[str]:
        """规范化路径

        将字符串路径转换为列表路径

        Args:
            path: 文档路径（字符串或列表形式）

        Returns:
            列表形式的路径
        """
        if isinstance(path, str):
            # 将字符串路径拆分为列表
            return path.split("/")
        return path

    async def add_document(
        self,
        doc_id: str,  # 文档ID
        title: str,  # 文档标题
        path: Union[str, List[str]] = None,  # 文档路径
        directory_ids: List[str] = field(default_factory=list),  # 目录ID列表
        content: str = None,  # 文档内容
        summary: Optional[str] = None,  # 文档摘要
        topics: Union[List[str]] = None,  # 主题
        tags: Union[List[str]] = None,  # 标签
        content_token_count: int = 0,  # 内容的token数量
        summary_token_count: int = 0,  # 摘要的token数量
        information_coverage: float = 0.0,  # 信息覆盖率
        missing_key_aspects: Union[List[str]] = None,  # 缺失的关键要素
        metadata: Optional[Dict[str, Any]] = None,
    ) -> DocTreeNode:
        """添加文档到树中"""
        def build_doc_node(
            node_id: str,
            name: str,
            parent_id: str,
            parent: Optional[DocTreeNode] = None,
            content: Optional[str] = None,
            summary: Optional[str] = None,
            topics: List[str] = field(default_factory=list),
            tags: List[str] = field(default_factory=list),
            content_token_count: int = 0,
            summary_token_count: int = 0,
            information_coverage: float = 0.0,
            missing_key_aspects: List[str] = field(default_factory=list),
            doc_path: List[str] = field(default_factory=list),
            doc_path_str: str = "",
            metadata: Optional[Dict[str, Any]] = None,
        ):
            doc_node = DocTreeNode(
                node_id=node_id,
                name=name,
                node_type=NodeType.DOCUMENT,
                parent=parent,
                parent_id=parent_id,
                content=content,
                summary=summary,
                topics=topics,
                tags=tags,
                content_token_count=content_token_count,
                summary_token_count=summary_token_count,
                information_coverage=information_coverage,
                missing_key_aspects=missing_key_aspects,
                doc_path=doc_path,
                doc_path_str=doc_path_str,
                metadata=metadata or {},
            )
            return doc_node
        """
        id: str  # 节点ID
        name: str  # 节点名称
        node_type: NodeType  # 节点类型：'root', 'category', 'document'
        content: Optional[str] = None  # 文档内容
        summary: Optional[str] = None  # 文档摘要
        topics: List[str] = field(default_factory=list)  # 主题
        tags: List[str] = field(default_factory=list)  # 标签
        summary_token_count: int = 0  # 摘要的token数量
        content_token_count: int = 0  # 内容的token数量
        information_coverage: float = 0.0  # 信息覆盖率
        missing_key_aspects: List[str] = field(default_factory=list)  # 缺失的关键要素
        doc_path: List[str] = field(default_factory=list)  # 文档路径
        doc_path_str: str = ""  # 文档路径字符串
        """
        # 路径规范化处理
        path_list = self._normalize_path(path)
        # 路径(dir) id列表
        directory_ids = directory_ids or []
        # 如果路径为空，直接添加到根节点下
        if not path_list or (len(path_list) == 1 and not path_list[0]):
            # 创建文档节点
            doc_node = build_doc_node(
                node_id=doc_id,
                name=title,
                parent_id=self.root.node_id,
                parent=self.root,
                content=content,
                summary=summary,
                topics=topics,
                tags=tags,
                content_token_count=content_token_count,
                summary_token_count=summary_token_count,
                information_coverage=information_coverage,
                missing_key_aspects=missing_key_aspects,
                doc_path=path_list,
                doc_path_str="/".join(path_list),
                metadata=metadata
            )
            # 添加到根节点下
            self.root.children.add(doc_node)
            # 更新文档计数
            self.root.direct_document_count += 1
            return doc_node
        # 添加到文档树当中
        current_node = self.root
        # 遍历路径, 创建或获取中间节点
        for idx, segment in enumerate(path_list[:-1]):
            # 当前目录id
            cur_dir_id = directory_ids[idx] if idx < len(directory_ids) else ""
            # 检查节点是否存在
            child = current_node.children.get_by_id(cur_dir_id) if cur_dir_id else None
            # 如果通过ID没找到，尝试通过名称查找
            if child is None:
                child = current_node.children.get_by_name(segment)
            if child is None:
                # 创建分类节点
                self.category_id += 1
                new_node = DocTreeNode(
                    node_id=cur_dir_id or f"cat_{self.category_id}",
                    name=segment,
                    node_type=NodeType.CATEGORY,
                    parent=current_node,
                    parent_id=current_node.node_id,
                    children=NodeChildren(),
                    metadata={},
                )
                current_node.children.add(new_node)
                current_node = new_node
            else:
                current_node = child
        # 处理最后一个路径段, 创建文档节点
        last_segment = path_list[-1] if path_list else ""
        doc_node = build_doc_node(
            node_id=doc_id,
            name=title or last_segment,
            parent=current_node,
            parent_id=current_node.node_id,
            content=content,
            summary=summary,
            topics=topics,
            tags=tags,
            content_token_count=content_token_count,
            summary_token_count=summary_token_count,
            information_coverage=information_coverage,
            missing_key_aspects=missing_key_aspects,
            doc_path=path_list,
            doc_path_str="/".join(path_list) if path_list else "",
            metadata=metadata
        )
        # 添加到当前节点
        current_node.children.add(doc_node)
        # 更新文档计数
        current_node.direct_document_count += 1

    async def add_directory(
        self,
        dir_id: str,  # 目录ID
        title: str,  # 目录标题
        path: Union[str, List[str]] = None,  # 目录路径
        directory_ids: List[str] = field(default_factory=list),  # 目录ID列表
        directory_description: Optional[str] = None,  # 目录描述
        metadata: Optional[Dict[str, Any]] = None
    ):
        # 路径规范化处理
        path_list = self._normalize_path(path)

        # 如果路径为空，直接添加到根节点下
        if not path_list or (len(path_list) == 1 and not path_list[0]):
            # 创建目录节点
            dir_node = DocTreeNode(
                node_id=dir_id,
                name=title,
                node_type=NodeType.CATEGORY,
                parent_id=self.root.node_id,
                parent=self.root,
                directory_description=directory_description,
                children=NodeChildren(),
                metadata=metadata or {},
            )
            # 添加到根节点下
            self.root.children.add(dir_node)
            return dir_node

        # 在树中导航并创建缺失的目录
        current_node = self.root
        for idx, segment in enumerate(path_list):
            # 判断是否是最后一个节点
            is_last_node = idx == len(path_list) - 1

            # 确定当前节点的ID
            if is_last_node:
                # 最后一个节点使用dir_id参数
                curr_dir_id = dir_id
            else:
                # 中间节点使用directory_ids列表
                curr_dir_id = directory_ids[idx] if idx < len(directory_ids) else ""

            # 通过ID查找子节点
            child = current_node.children.get_by_id(curr_dir_id) if curr_dir_id else None

            # 如果通过ID没找到，尝试通过名称查找
            if child is None:
                child = current_node.children.get_by_name(segment)

            if child is None:
                # 创建新目录节点
                self.category_id += 1
                new_node = DocTreeNode(
                    node_id=curr_dir_id or f"cat_{self.category_id}",
                    name=segment,
                    node_type=NodeType.CATEGORY,
                    parent=current_node,
                    parent_id=current_node.node_id,
                    directory_description=directory_description if is_last_node else None,
                    children=NodeChildren(),
                    metadata=metadata or {},
                )
                current_node.children.add(new_node)
                current_node = new_node
            else:
                # 如果节点已存在且是最后一个节点，更新其description
                if is_last_node and directory_description:
                    child.directory_description = directory_description
                current_node = child

        # 返回最后创建或找到的节点
        return current_node

    async def build_from_documents(
        self,
        documents: List[Dict[str, Any]],
        path_key: str = "doc_tree_path",
        alt_path_key: str = "category_path",
    ) -> None:
        """从文档列表构建树

        Args:
            documents: 文档列表
            path_key: 路径字段名
            alt_path_key: 备用路径字段名
        """
        for doc in documents:
            # 提取必要字段
            doc_id = doc.get("id") or doc.get("id_") or str(uuid.uuid4())
            title = doc.get("title", f"未命名文档_{doc_id}")
            content = doc.get("content")
            summary = doc.get("summary")

            # 确定文档路径
            path = None
            if path_key in doc and doc[path_key]:
                path = doc[path_key]
            elif alt_path_key in doc and doc[alt_path_key]:
                path = doc[alt_path_key]
            else:
                path = ["未分类文档"]
            # 构建文档元数据
            metadata = {k: v for k, v in doc.items()
                        if k not in ["id", "id_", "title", "content", "summary", path_key, alt_path_key]}
            # 添加到树中
            await self.add_document(
                doc_id=doc_id,
                path=path,
                title=title,
                content=content,
                summary=summary,
                metadata=metadata
            )


