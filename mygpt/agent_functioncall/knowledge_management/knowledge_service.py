from typing import List, Dict, Optional, Any
from mygpt.agent_functioncall.schemas.dataset_schemas import (
    DatasetInfo,
    DocumentInfo,
    DocumentType,
    SourceType,
)


class KnowledgeService:
    """知识管理服务

    主要功能：
    1. 接收并处理文章数据
    2. 转换为DatasetInfo格式
    3. 预留目录树构建接口
    """

    @staticmethod
    async def process_articles(
        articles: List[Dict[str, Any]],
        dataset_name: str,
        dataset_description: Optional[str] = None,
        source_type: SourceType = SourceType.GENERAL,
        **kwargs,
    ) -> DatasetInfo:
        """处理文章列表，生成DatasetInfo

        Args:
            articles: 文章列表，每篇文章是一个字典
            dataset_name: 数据集名称
            dataset_description: 数据集描述
            source_type: 数据来源类型

        Returns:
            构建好的DatasetInfo对象
        """
        documents = []
        for idx, article in enumerate(articles):
            title = article.get("title", f"未命名文档{idx+1}")
            content = article.get("content", "")
            article_id = article.get("article_id", f"article_{idx+1}")
            if not content:
                continue
            doc = DocumentInfo(
                doc_id=article_id,
                title=title,
                content=content,
                doc_type=DocumentType.ARTICLE,
            )
            documents.append(doc)

        # 创建数据集
        dataset_info = DatasetInfo(
            name=dataset_name,
            description=dataset_description or f"{dataset_name}的知识库",
            source_type=source_type,
            documents=documents,
            metadata=kwargs.get("metadata", {}),
        )

        # 如果指定了构建目录树
        if kwargs.get("build_tree", False):
            tree_data = await KnowledgeService.build_tree_structure(
                dataset_info, **kwargs
            )
            # 将目录树信息保存到数据集的元数据中
            dataset_info.metadata["tree_structure"] = tree_data

        return dataset_info

    @staticmethod
    async def process_documents(
        documents: List[DocumentInfo], dataset_name: str, dataset_description: str
    ) -> DatasetInfo:
        """处理文档列表，生成DatasetInfo

        Args:
            documents: 文档列表
            dataset_name: 数据集名称
            dataset_description: 数据集描述

        Returns:
            构建好的DatasetInfo对象
        """
        # 创建数据集
        dataset_info = DatasetInfo(
            name=dataset_name,
            description=dataset_description,
            source_type=SourceType.GENERAL,
            documents=documents,
        )

        return dataset_info

    @staticmethod
    async def build_tree_structure(dataset: DatasetInfo, **kwargs) -> Dict:
        """构建目录树结构（预留接口）

        Args:
            dataset: 数据集对象
            **kwargs: 其他配置参数

        Returns:
            目录树结构
        """
        # 预留接口，返回一个基础结构
        return {
            "name": dataset.name,
            "type": "root",
            "children": [
                {
                    "name": "全部文档",
                    "type": "category",
                    "document_count": len(dataset.documents),
                }
            ],
        }
