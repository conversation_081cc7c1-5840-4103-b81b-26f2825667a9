import asyncio
from typing import Any, List

from langchain.callbacks import Async<PERSON>teratorCallbackHandler
from langchain.callbacks.base import As<PERSON><PERSON><PERSON>backHandler
from langchain.schema import AIMessage, HumanMessage
from loguru import logger as logging

from mygpt.agent_functioncall.schemas.tool_schemas import ToolCallInfomation
from mygpt.agent_functioncall.types import Result
from mygpt.enums import StreamingMessageDataType, StreamingMessageDataFormat
from mygpt.models import Robot
from mygpt.openai_utils import (
    RedisChatMessageHistory,
    task_done_exception_logging_callback,
    update_message_to_finished,
    check_llm_can_answer,
    check_llm_is_clarification,
    release_message_lock,
)
from mygpt.schemata import QuestionIn, StreamOut, OpenAIStyleChunkOut
from mygpt.service.quality_assessment import save_to_quality_assessment_by_message_id
from mygpt.utils import (
    num_tokens_from_string,
    num_tokens_for_langchain_messages,
    replace_md_uuid_images,
    ends_with_markdown_image_syntax_index,
    ends_with_markdown_link_syntax_index,
)


class FCAgentBaseCallbackHandler(AsyncCallbackHandler):
    """
    langchain回调处理器 - 处理基础回调, 主要用于数据库写入等操作
    """

    def __init__(
        self,
        question_in: QuestionIn,
        robot: Robot,
        history,
        question_record_obj,
        event: asyncio.Event = None,
        store_message: bool = True,
    ) -> None:
        super().__init__()
        self.question_in = question_in
        self.robot = robot
        self.history = history
        self.question_record_obj = question_record_obj
        self.event = event
        self.is_tool_calls: bool = False
        self.store_message: bool = store_message
        # 对于agnet来说可能多次调用大模型, 所以需要记录多次的回答, 例如第一次回答: `请稍等, 正在为您查询`, 第二次回答: `查询结果为...`
        self.answers = []

    async def on_llm_new_token(self, token: str, **kwargs: Any) -> None:
        # 流式输出，统计第1个字符接收到的时长
        if self.event and not self.event.is_set():
            self.event.set()

    async def on_llm_end(self, response=None, **kwargs: Any) -> None:
        answer = ""
        if response and hasattr(response, "generations") and response.generations:
            answer = response.generations[0][0].message.content
        logging.info(f"[FCAgentBaseCallbackHandler] response answer: {answer}")
        # 如果没有message_id, 直接返回
        if not self.question_in.message_id:
            return
        if self.is_tool_calls:
            # 如果是工具调用, 需要判断是否存在正常的answer信息, 如果有, 则放入answers中, 作为最终answer的一部分
            if answer:
                logging.info("[FCAgentBaseCallbackHandler] received tool call answer: {answer}")
                self.answers.append(answer)
            return

        self.session_id = self.question_in.session_id
        self.message_id = self.question_in.message_id
        total_tokens = 0

        try:
            # 从response中获取answer，如果没有response，则跳过
            if answer:
                self.answers.append(answer)
            if not answer.strip():
                logging.warning(f"received empty answer: {answer} | user question: {self.question_in.question}")
            logging.info(f"response answer: {answer}")

            if not self.store_message:
                return
            # 聊天记录缓存
            chat_memory = RedisChatMessageHistory(ttl=3600)
            await chat_memory.add_message(
                self.session_id, HumanMessage(content=self.question_in.question)
            )
            final_answer = "".join(self.answers)
            logging.info(f"final answer: {final_answer}, original answers: {self.answers}")
            await chat_memory.add_message(self.session_id, AIMessage(content=final_answer))

            # 计算tokens
            total_tokens += num_tokens_for_langchain_messages(self.history)
            tokens = num_tokens_from_string(final_answer)
            total_tokens += tokens

            # 保存质量评估
            asyncio.create_task(
                save_to_quality_assessment_by_message_id(self.message_id)
            ).add_done_callback(task_done_exception_logging_callback)
            self.question_record_obj.answer = final_answer
            self.question_record_obj.total_tokens = total_tokens
            # 更新消息状态
            asyncio.create_task(
                update_message_to_finished(
                    message_id=str(self.message_id),
                    answer=final_answer,
                    total_tokens=total_tokens,
                )
            ).add_done_callback(task_done_exception_logging_callback)
        except Exception as e:
            logging.error(f"on_llm_end error: {e}")
            raise e
        finally:
            if not self.is_tool_calls:
                # 操作完成后释放锁
                logging.debug(f"message unlocked: {str(self.question_in.message_id)}")
                await release_message_lock(str(self.question_in.message_id))

    async def on_error(
        self, error: Exception | KeyboardInterrupt, **kwargs: Any
    ) -> None:
        # 大模型接口非流式返回异常
        if self.event and not self.event.is_set():
            self.event.set()


class FCAgentStreamCallbackHandler(AsyncIteratorCallbackHandler):
    """
    langchain回调处理器 - 处理流式输出, 主要用于流式输出的格式处理
    """

    def __init__(
        self,
        question_in: QuestionIn,
        robot: Robot,
        tokens: int = 0,
        question_record_obj=None,
        format=None,
        event: asyncio.Event = None,
        context_vars=None,
    ):
        super().__init__()
        self.question_in = question_in
        self.robot = robot
        self.tokens = tokens
        self.format = format
        self.question_record_obj = question_record_obj
        self.context_vars = context_vars
        self.event = event
        self.is_tool_calls = False
        self.run_id = None
        self.pending_texts = []
        self.cache_links = []
        self.answer_buffer = []
        self.turn_answer_buffer = [[]]
        self.message_id = question_in.message_id if question_in else None
        self.session_id = question_in.session_id if question_in else None

    def handle_image_content(self, content: str, resources: dict[str, str]):
        if self.pending_texts:
            text = "".join(self.pending_texts)
            content = text + content
            self.pending_texts = []

        content = replace_md_uuid_images(content, resources, self.cache_links)

        image_index = ends_with_markdown_image_syntax_index(content)
        link_index = ends_with_markdown_link_syntax_index(content)
        if image_index > -1:
            ends_with_index = image_index
        else:
            ends_with_index = link_index

        if ends_with_index > -1:
            ends_text = content[ends_with_index:]
            content = content[:ends_with_index]
            # cache pending text
            self.pending_texts.append(ends_text)
        return content

    def handle_content(self, content: str):
        if not self.context_vars:
            return content
        resources = self.context_vars.get("resources", {})
        image_resources = resources.get("image", {})
        content = self.handle_image_content(content, image_resources)
        return content

    def create_stream_out(
        self, message_type: StreamingMessageDataType, content: str | List | dict
    ):
        if message_type == StreamingMessageDataType.PLAIN_TEXT:
            content = self.handle_content(content)

        return StreamOut(
            message_id=self.question_in.message_id,
            message_type=message_type,
            content=content,
            use_faq=False,
            tokens=self.tokens,
        )

    def token_format(self, token: str, **kwargs) -> str:
        if self.format == StreamingMessageDataFormat.JSON_AST:
            logging.debug(f"Formatting token with JSON_AST: {token}")
            rs = (
                self.create_stream_out(
                    message_type=StreamingMessageDataType.PLAIN_TEXT,
                    content=token,
                ).json(ensure_ascii=False)
                + "\n"
            )
            return rs
        else:
            return token

    async def on_llm_new_token(self, token: str, **kwargs: Any) -> None:
        chunk = kwargs.get("chunk")

        # 如果是工具调用，返回原始chunk
        if chunk and hasattr(chunk, "message") and chunk.message.additional_kwargs:
            logging.debug(f"Received tool call chunk: {chunk}")
            await super().on_llm_new_token(chunk, **kwargs)  # 返回原始chunk
            return

        # 处理普通token
        if token == "":
            print("received empty token")

        # 收集token用于最终的answer分析
        self.answer_buffer.append(token)
        self.turn_answer_buffer[-1].append(token)

        # 格式化并发送token
        format_token = self.token_format(token, **kwargs)
        try:
            logging.debug(f"Sending formatted token: {format_token}")
            await super().on_llm_new_token(format_token, **kwargs)
        except Exception as e:
            logging.error(f"on_llm_new_token error: {e}")

    async def on_error(
        self, error: Exception | KeyboardInterrupt, **kwargs: Any
    ) -> None:
        try:
            # 大模型接口流式返回异常
            if self.format == StreamingMessageDataFormat.JSON_AST:
                rs = self.create_stream_out(
                    message_type=StreamingMessageDataType.ERROR,
                    content=str(error),
                ).json(ensure_ascii=False)
                await super().on_llm_new_token(rs + "\n", **kwargs)
                return

            await super().on_llm_new_token(str(error) + "\n", **kwargs)
        finally:
            if self.event and not self.event.is_set():
                self.event.set()

    # async def send_agent_tool_task_state(
    #     self,
    #     tool_call_infomation_list: List[ToolCallInfomation],
    #     **kwargs: Any
    # ):
    #     """
    #     发送Agent工具调用的状态更新
    #     :param tool_name: 工具名称
    #     :param tool_args: 工具参数
    #     :param tool_result: 工具结果
    #     """
    #     for tool_call_infomation in tool_call_infomation_list:
    #         # 处理工具调用状态
    #         rs = self.create_stream_out(
    #             message_type=StreamingMessageDataType.TOOL_CALL,
    #             # content={
    #             #     "tool_call_name": tool_call_infomation.tool_call_name,
    #             #     "tool_call_args": tool_call_infomation.tool_call_args,
    #             #     "tool_call_id": tool_call_infomation.tool_call_id,
    #             #     "tool_call_status": tool_call_infomation.tool_call_status,
    #             #     "tool_call_result": tool_call_infomation.tool_call_result
    #             # },
    #             content="asdfasdfasdf"
    #         ).json(ensure_ascii=False)
    #         await super().on_llm_new_token(rs + "\n")
    #         logging.info(f"[send_agent_tool_task_state] tool_call_name: {tool_call_infomation.tool_call_name}, tool_call_status: {tool_call_infomation.tool_call_status}, tool_call_args: {tool_call_infomation.tool_call_args}")

    async def on_llm_end(self, response=None, **kwargs: Any) -> None:
        try:
            # 如果是工具调用过程中，不执行结束处理
            if self.is_tool_calls:
                return

            if self.format == StreamingMessageDataFormat.OPENAI_AST:
                try:
                    logging.info(f"Sending finish token")
                    await super().on_llm_new_token("data: [DONE]\n\n", **kwargs)
                except Exception as e:
                    logging.error(f"on_llm_end error: {e}")

            # 大模型接口流式返回结束
            if self.format == StreamingMessageDataFormat.JSON_AST:
                # 获取answer内容
                question = self.question_in.question
                answer = "".join(self.answer_buffer)
                logging.info(f"question: {question}, answer: {answer}")

                # 处理llm_can_answer和clarification
                clarification_type = ["phone", "email"]
                check_llm_can_answer_coroutine = check_llm_can_answer(question, answer)
                check_llm_is_clarification_coroutine = check_llm_is_clarification(
                    question, answer, clarification_type=clarification_type
                )
                (llm_can_answer, show_reference_button), clarification_info = (
                    await asyncio.gather(
                        check_llm_can_answer_coroutine,
                        check_llm_is_clarification_coroutine,
                    )
                )
                # 只有在非tool_calls的情况下才发送这些结束标记
                if not self.is_tool_calls:
                    # 基础信息
                    clraification_rs = self.create_stream_out(
                        message_type=StreamingMessageDataType.CLARIFICATION,
                        content=str(clarification_info).lower(),
                    ).json(ensure_ascii=False)
                    await super().on_llm_new_token(clraification_rs + "\n", **kwargs)

                    # rs = self.create_stream_out(
                    #     message_type=StreamingMessageDataType.REFERENCE_LIST,
                    #     content=[],
                    # ).json(ensure_ascii=False)
                    # await super().on_llm_new_token(rs + "\n", **kwargs)

                    rs = self.create_stream_out(
                        message_type=StreamingMessageDataType.RECOMMEND_LIST,
                        content=[],
                    ).json(ensure_ascii=False)
                    await super().on_llm_new_token(rs + "\n", **kwargs)

                    rs = self.create_stream_out(
                        message_type=StreamingMessageDataType.FAQ_CHILD_NODE,
                        content=[],
                    ).json(ensure_ascii=False)
                    await super().on_llm_new_token(rs + "\n", **kwargs)

                    # 处理reference_list
                    reference_list = []
                    if (
                        self.question_record_obj
                        and self.question_record_obj.reference_list
                    ):
                        show_reference_button = True
                        reference_list = self.question_record_obj.reference_list

                    # 发送各种状态信息
                    show_reference_button_rs = self.create_stream_out(
                        message_type=StreamingMessageDataType.SHOW_REFERENCE_BUTTON,
                        content=str(show_reference_button).lower(),
                    ).json(ensure_ascii=False)
                    await super().on_llm_new_token(
                        show_reference_button_rs + "\n", **kwargs
                    )

                    # logging.info(f"sending REFERENCE_LIST {reference_list}")
                    rs = self.create_stream_out(
                        message_type=StreamingMessageDataType.REFERENCE_LIST,
                        content=reference_list,
                    ).json(ensure_ascii=False)
                    await super().on_llm_new_token(rs + "\n", **kwargs)

                    llm_can_answer_rs = self.create_stream_out(
                        message_type=StreamingMessageDataType.LLM_CAN_ANSWER,
                        content=str(llm_can_answer).lower(),
                    ).json(ensure_ascii=False)
                    await super().on_llm_new_token(llm_can_answer_rs + "\n", **kwargs)
                    llm_can_answer = True
                    self.question_record_obj.llm_can_answer = llm_can_answer
                # 发送结束标记
                over_rs = self.create_stream_out(
                    message_type=StreamingMessageDataType.FINISH,
                    content="",
                ).json(ensure_ascii=False)
                await super().on_llm_new_token(over_rs + "\n", **kwargs)
                # try:
                #     logging.debug(f"Waiting for stream queue join, size : {self.queue.qsize()}")
                #     await asyncio.wait_for(self.queue.join(), timeout=10.0)
                # except asyncio.TimeoutError:
                #     logging.debug(f"[FCAgentStreamCallbackHandler] stream queue join timeout, size : {self.queue.qsize()}")
                #     logging.warning(
                #         f"[FCAgentStreamCallbackHandler] stream queue join timeout"
                #     )
                # 短暂延迟，确保消息有时间被传递和处理
                await asyncio.sleep(0.5)
        finally:
            if self.event and not self.event.is_set():
                self.event.set()


class DigitalHumanFCAgentStreamCallbackHandler(FCAgentStreamCallbackHandler):
    """
    langchain回调处理器 - 处理数字人流式输出, 主要用于数字人流式输出的格式处理
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.pending_asterisk = False  # 是否有未处理的星号
        self.last_token = ""  # 上一个token

    def _clean_token_for_tts(self, token: str) -> str:
        """
        清理token中的连续星号（**）
        """
        cleaned_token = token
        # 处理跨token的情况
        if self.pending_asterisk and token.startswith("*"):
            # 如果上一个token结尾是*，当前token开头是*，说明是**的情况
            cleaned_token = token[1:]
            self.pending_asterisk = False
        # 处理当前token内的**
        while "**" in cleaned_token:
            cleaned_token = cleaned_token.replace("**", "")
        # 更新pending状态
        self.pending_asterisk = cleaned_token.endswith("*")
        self.last_token = cleaned_token
        return cleaned_token

    def format_open_ai_chunk_response(
        self,
        chunk: str,
        message_id,
        finish_reason: str = None,
        to_json_str: bool = True,
    ):
        """
        使用OpenAI Style的response的chunk格式对消息进行封装
        暂时先简单实现, 如果后续有需要可以继续强化扩展.
        """
        if finish_reason:
            delta = dict()
        else:
            delta = OpenAIStyleChunkOut.Choice.Delta(content=chunk)
            if chunk == "":
                delta.role = "assistant"
            else:
                delta.role = None
        choice = OpenAIStyleChunkOut.Choice(delta=delta)
        choice.finish_reason = finish_reason
        resp = OpenAIStyleChunkOut(id=str(message_id))
        resp.choices.append(choice)
        return "data: " + resp.json(ensure_ascii=False) + "\n\n"

    def token_format(self, token: str, **kwargs) -> str:
        rs = ""
        if self.format == StreamingMessageDataFormat.JSON_AST:
            rs = (
                self.create_stream_out(
                    message_type=StreamingMessageDataType.PLAIN_TEXT,
                    content=token,
                ).json(ensure_ascii=False)
                + "\n"
            )
        elif self.format == StreamingMessageDataFormat.OPENAI_AST:
            chunk = kwargs.get("chunk")
            cleaned_token = self._clean_token_for_tts(token)
            if token == "" and chunk.generation_info:
                rs = self.format_open_ai_chunk_response(
                    chunk=token,
                    message_id=self.question_in.message_id,
                    finish_reason=chunk.generation_info.get("finish_reason", "stop"),
                    to_json_str=True,
                )
            else:
                if cleaned_token:
                    token = cleaned_token
                else:
                    token = ""
                rs = self.format_open_ai_chunk_response(
                    chunk=token,
                    message_id=self.question_in.message_id,
                    to_json_str=True,
                )
        return rs
