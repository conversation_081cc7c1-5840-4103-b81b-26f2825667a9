import requests
from requests.exceptions import Timeout
from langchain.utilities.openapi import OpenAPISpec


class MyOpenAPISpec(OpenAPISpec):
    @classmethod
    def from_url(cls, url: str) -> "MyOpenAPISpec":
        try:
            response = requests.get(url, timeout=2)
            return cls.from_text(response.text)
        except Timeout:
            raise ValueError("The request exceeded the timeout duration.")
        except requests.exceptions.RequestException as e:
            raise ValueError(
                f"An error occurred while fetching the OpenAPI specification: {e}"
            )
