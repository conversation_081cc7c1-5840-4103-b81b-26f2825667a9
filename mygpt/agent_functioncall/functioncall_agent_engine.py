import copy
import inspect
import json
from enum import Enum

import asyncio
import re
import time
import traceback
from typing import List, AsyncIterator, Optional
from uuid import uuid4

from loguru import logger as logging

from mygpt.agent_exp.utils.using_util import convert_base_messages_to_openai_format
from mygpt.agent_functioncall.NetRequestHandler import MyOpenAPISpec
from mygpt.agent_functioncall.callback_handler import (
    FCAgentStreamCallbackHandler,
    FCAgentBaseCallbackHandler,
)
from mygpt.agent_functioncall.context_management.context import BaseContext
from mygpt.agent_functioncall.llm_client import OpenAILLMClient, ClaudeLLMClient, GeminiLLMClient
from mygpt.agent_functioncall.schemas.dataset_schemas import DatasetInfo
from mygpt.agent_functioncall.schemas.prompt_schemas import PromptType
from mygpt.agent_functioncall.schemas.tool_schemas import ToolExecutionStatus, ToolCallInfomation
from mygpt.agent_functioncall.types import ToolInfo, Response, Result
from mygpt.enums import StreamingMessageDataFormat, OpenAIModel, FunctionCallStyle
from mygpt.models import Robot
from mygpt.schemata import QuestionIn

from langchain_community.tools import APIOperation
from mygpt.agent_functioncall.prompt_manager import MetaPromptProcessor, enhanced_prompt
from mygpt.agent_functioncall.using_util import (
    execute_request,
    format_info_2_tool,
    parse_tool_call_stream_result,
    collect_and_process_tool_calls,
)
from mygpt.agent_functioncall.tools_manager import ToolFactory

from deprecated import deprecated


class AgentFunctionCallEngine:
    def __init__(
        self,
        question_in: QuestionIn,
        robot: Robot,
        chat_history=None,
        stream=True,
        base_model=OpenAIModel.GPT_4_OMNI,
        start_event: asyncio.Event = asyncio.Event(),
        finish_event: asyncio.Event = asyncio.Event(),
        stream_callback: FCAgentStreamCallbackHandler = None,
        storage_callback: FCAgentBaseCallbackHandler = None,
        prompt_type=PromptType.CHAT,
        datasets: List[DatasetInfo] = None,
        context: Optional[BaseContext] = None,
        **kwargs,
    ):
        # 最新的上下文对象, 未完全完成, 暂时先用于一些特定的工具的获取 todo - 需要继续改进Agent模块的上下文管理, 工具管理, 然后引擎的一些代码也要整合到context中
        self.context = context
        # 上下文信息, 用于存储运行时的一些信息, 比如工具调用中的额外信息, callback中如果需要使用可以到这里获取
        self.context_vars = {}
        self.datasets = datasets

        # prompt_type是新属性
        self.prompt_type = prompt_type
        self.mode = (
            prompt_type.value
        )  # [chat, tts] - 使用的meta prompt的类型不同 ||| 旧属性暂时保留

        self.system_prompt = None
        self.question_in: QuestionIn = question_in
        self.robot: Robot = robot
        self.function_call_list = self.robot.apis.related_objects
        # 长时记忆
        self.chat_history = chat_history if chat_history else []
        # 短时记忆
        self.history = copy.deepcopy(self.chat_history)
        self.function_call_sta = None
        self.operations = dict()
        self.function_call_infos = None
        self.function_descriptions = None
        self.base_model = base_model
        self.start_time = time.time()
        self.stream = stream
        self.start_event = start_event
        self.finish_event = finish_event
        self._gpt_response = None
        self.tool_call_id = None
        self.stream_callback = stream_callback
        self.storage_callback = storage_callback
        self.send_info = None
        self.tool_resp = None
        self.chat_history_str = kwargs.get("chat_history_str", "")
        self.question_record_obj = kwargs.get("question_record_obj", None)
        # 初始化回调函数
        self.callbacks = []
        self._init_callbacks()
        self.stream_callback.context_vars = self.context_vars
        self.max_tokens = kwargs.get("max_tokens", 4096)
        if (
            self.base_model == OpenAIModel.CLAUDE_35_SONNET
            or base_model == OpenAIModel.CLAUDE_37_SONNET
        ):
            self.llm_client = ClaudeLLMClient(
                self.robot.prompt, self.history, self.callbacks, model=self.base_model
            )
        elif self.base_model.value.startswith("gemini"):
            self.llm_client = GeminiLLMClient(
                self.robot.prompt, self.history, self.callbacks, model=self.base_model
            )
        else:
            self.llm_client = OpenAILLMClient(
                self.robot.prompt, self.history, self.callbacks, model=self.base_model
            )

        # 注册tools到Agent中
        self.tools = []
        self.tool_map = {}
        self._register_base_tools()

    def _register_base_tools(self):
        """注册基础工具 目前只有一个RAG工具"""
        tools = ToolFactory.get_tools(self)
        for tool in tools:
            self.tools.append(format_info_2_tool(tool, self.llm_client.call_style))
            self.tool_map[tool.name] = tool
        if self.context:
            for tool in self.context.tools:
                formated_tool = format_info_2_tool(tool, self.llm_client.call_style)
                self.tools.append(formated_tool)
                self.tool_map[tool.name] = tool

    def _init_callbacks(self):
        self.callbacks = []
        if self.stream and self.stream_callback is None:
            self.stream_callback = FCAgentStreamCallbackHandler(
                question_in=self.question_in,
                robot=self.robot,
                question_record_obj=self.question_record_obj,
                format=StreamingMessageDataFormat.JSON_AST,
                event=self.start_event,
            )
        if not self.storage_callback:
            self.storage_callback = FCAgentBaseCallbackHandler(
                question_in=self.question_in,
                robot=self.robot,
                history=self.chat_history,
                event=self.start_event,
                question_record_obj=self.question_record_obj,
            )
        if self.stream:
            self.callbacks.append(self.stream_callback)
        self.callbacks.append(self.storage_callback)
        logging.info(
            f"【AgentFunctionCallEngine】init callbacks success, stream_callback: {self.stream_callback}, storage_callback: {self.storage_callback}"
        )

    def handle_function_result(self, tool_call_id, tool_name, raw_result) -> Result:
        """处理function_call的结果, 将结果转换为ToolCallsResult对象
        todo - 还有一个Agent的类型没有定义, 这个需要在多Agent的改造时完成这部分的内容.
        """
        if isinstance(raw_result, AsyncIterator):
            return Result(
                name=tool_name,
                tool_call_id=tool_call_id,
                gpt_response_iter=raw_result,
            )
        match raw_result:
            case Result() as result:
                result.tool_call_id = tool_call_id
                result.name = tool_name
                return result
            case _:
                try:
                    return Result(
                        name=tool_name, tool_call_id=tool_call_id, value=str(raw_result)
                    )
                except Exception as e:
                    error_message = f"Failed to cast response to string: {raw_result}. Make sure agent functions return a string or Result object. Error: {str(e)}"
                    raise TypeError(error_message)

    async def _call_tool_or_function(self, response: Response) -> List[Result]:
        """大模型调用function_call的处理函数, 需要self._gpt_response对象中包含additional_kwargs"""
        if not response.tool_calls:
            logging.warning(
                f"【AgentFunctionCallEngine】 gpt_response or gpt_response.additional_kwargs is None"
            )
            raise ValueError("gpt_response or gpt_response.additional_kwargs is None")
        start_time = time.time()
        """
        agent: Optional[object] = Field(None, description="The agent.")
        tool_calls: Optional[List[Dict]] = Field([], description="The tool calls.")
        tool_calls_results: Optional[List[ToolCallsResult]] = Field([], description="The tool calls result.")
        context: str = Field("", description="The context for llm response.")
        gpt_response_iter: Optional[Callable] = Field(None, description="The generator for the response.")
        role: Literal["assistant", "tool"] = Field("assistant", description="The role of llm response. emum: [assistant, tool]")
        """
        tool_calls_results = []
        tasks = []
        tasks_info = []
        for tool_call in response.tool_calls:
            index = tool_call.get("index")
            id_ = tool_call.get("id")
            function = tool_call.get("function")
            function_name = function.get("name")
            function_arguments = (
                function.get("arguments") if function.get("arguments").strip() else "{}"
            )
            try:
                arguments = json.loads(function_arguments)
            except Exception as e:
                logging.error(
                    f"【AgentFunctionCallEngine】 ERROR json.loads: {e}, function_arguments: {function_arguments}"
                )
                raise e
            logging.info(f"【AgentFunctionCallEngine】function_name: {function_name}")
            logging.info(f"【AgentFunctionCallEngine】arguments: {arguments}")
            # 判断function_name 是否为空
            if not function_name or not id_:
                logging.warning(
                    f"【AgentFunctionCallEngine】function_name or function_id is None or empty, function_arguments: {function_arguments}"
                )
                tool_result = Result(
                    name="",
                    tool_call_id="",
                    value=f"异步调用工具时出错: the name or id of the tool call is None or empty - please ensure the function name, id and arguments are correct",
                )
                tool_calls_results.append(tool_result)
                continue

            # 判断调用的工具名称是否在默认工具中
            func = self.tool_map.get(function_name)
            if func is not None:
                if inspect.iscoroutinefunction(func.method):
                    # 异步优化 - 如果是异步函数, 则将其加入到异步任务中, 等待异步调用处理
                    try:
                        tasks.append(func.method(**arguments))
                        tasks_info.append({"function_name": function_name, "id_": id_})
                    except Exception as e:
                        logging.error(
                            f"【AgentFunctionCallEngine】异步调用工具 {function_name} 时出错: {e}"
                        )
                        tool_result = Result(
                            name=function_name,
                            tool_call_id=id_,
                            value=f"异步调用工具 {function_name} 时出错: {str(e)}",
                        )
                        tool_calls_results.append(tool_result)
                    continue
                else:
                    try:
                        raw_result = func.method(**arguments)
                    except Exception as e:
                        logging.error(
                            f"【AgentFunctionCallEngine】同步调用工具 {function_name} 时出错: {e}"
                        )
                        tool_result = Result(
                            name=function_name,
                            tool_call_id=id_,
                            value=f"同步调用工具 {function_name} 时出错: {str(e)}",
                        )
                        tool_calls_results.append(tool_result)
                        continue

                if isinstance(raw_result, dict):
                    raw_result = json.dumps(raw_result, ensure_ascii=False, indent=2)
                logging.info(
                    f"【AgentFunctionCallEngine】call tool, name: {function_name}, result: {raw_result}"
                )
                tool_result: Result = self.handle_function_result(
                    tool_call_id=id_, tool_name=function_name, raw_result=raw_result
                )
                tool_calls_results.append(tool_result)
                continue

            # 取出function_name中返回的涉及变量的值对象
            operation = self.operations.get(function_name)
            function_info = self.function_call_infos.get(function_name)
            logging.info(
                f"【AgentFunctionCallEngine】operations:{self.operations} function_info:{function_info} "
            )
            if not operation and not function_info:
                logging.warning(
                    f"【AgentFunctionCallEngine】Operation {function_name} not found"
                )
                if id_ is None:
                    id_ = ""
                tool_result = Result(
                    name=str(function_name),
                    tool_call_id=id_,
                    value=f"Exception: tool name is not found: {str(function_name)}",
                )
                tool_calls_results.append(tool_result)
                continue


            if (
                function_info is not None
                and function_info.body is not None
                and function_info.body != ""
                and function_info.body != "{}"
            ):
                method = function_info.method
                path = function_info.path
            elif operation is not None:
                method = operation.method.value.lower()
                path = operation.path
            else:
                logging.warning(
                    f"【AgentFunctionCallEngine】Operation {function_name} not found"
                )
                tool_result = Result(
                    name=function_name,
                    tool_call_id=id_,
                    value=f"Operation {function_name} not found",
                )
                tool_calls_results.append(tool_result)
                continue

            # 根据method判断是get还是post
            if method in ["get"]:
                query_param_values = arguments
                body_param_values = None
            else:
                query_param_values = None
                body_param_values = arguments
            # GPT反馈的参数与要发送的请求参数匹配
            data = (
                json.dumps(body_param_values)
                if method in ["post", "put", "patch"]
                else None
            )
            params = query_param_values if method == "get" else None
            headers = function_info.header_authorization
            if headers is None:
                headers = {}
            headers["content-type"] = "application/json"

            # 发送请求
            try:
                logging.info(
                    f"【AgentFunctionCallEngine】 send request: {function_info.server_url + path} "
                    f"method: {method} data: {data} params: {params} header: {headers}"
                )
                tool_response = await execute_request(
                    function_info.server_url + path,
                    method,
                    data=data,
                    params=params,
                    headers=headers,
                )
                end_time = time.time()
                logging.info(
                    f"【AgentFunctionCallEngine】 api response time: {end_time - start_time}"
                )
                if isinstance(tool_response, dict):
                    tool_response = json.dumps(
                        tool_response, ensure_ascii=False, indent=2
                    )
                else:
                    tool_response = str(tool_response)
                tool_result = Result(
                    name=function_name, tool_call_id=id_, value=tool_response
                )
                logging.info(
                    f"【AgentFunctionCallEngine】call outer API Source - name: {function_name} tool_result: {tool_response}"
                )
                tool_calls_results.append(tool_result)
            except Exception as e:
                logging.warning(
                    f"【AgentFunctionCallEngine】 ERROR execute_request: {e}"
                )
                tool_response = str(e)
                tool_result = Result(
                    name=function_name, tool_call_id=id_, value=tool_response
                )
                tool_calls_results.append(tool_result)
        if tasks:
            # 异步优化
            try:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                for result, info_dict in zip(results, tasks_info):
                    id_ = info_dict.get("id_")
                    function_name = info_dict.get("function_name")
                    # 检查结果是否为异常
                    if isinstance(result, Exception):
                        error_msg = f"异步执行工具 {function_name} 时出错: {str(result)}"
                        logging.error(f"【AgentFunctionCallEngine】异步执行错误: {result}")
                        tool_result = Result(
                            name=function_name,
                            tool_call_id=id_,
                            value=error_msg
                        )
                    else:
                        tool_result: Result = self.handle_function_result(
                            tool_call_id=id_,
                            tool_name=function_name,
                            raw_result=result.value,
                        )
                    tool_calls_results.append(tool_result)
            except Exception as e:
                # 处理gather整体失败的情况
                logging.error(f"【AgentFunctionCallEngine】 异步任务gather出错: {e}")
                logging.error(traceback)
                for info_dict in tasks_info:
                    id_ = info_dict.get("id_")
                    function_name = info_dict.get("function_name")
                    error_msg = f"异步执行工具时出现严重错误: {str(e)}"
                    tool_result = Result(
                        name=function_name,
                        tool_call_id=id_,
                        value=error_msg
                    )
                    tool_calls_results.append(tool_result)
        return tool_calls_results

    async def _parse_function_call(self, function_call):
        # function call 的名称需要遵守 ^[a-zA-Z8-9_-](1,64)$ 这个规则，这里需要手动处理成符合规则的名称
        if not re.match(r"^[a-zA-Z8-9_-]{1,64}$", function_call.function_call_name):
            function_call_name = (
                function_call.function_call_name.strip()
                .replace(" ", "_")
                .replace("-", "_")
                .replace(".", "_")
                .replace("/", "_")
                .replace("\\", "_")
            )
            function_call_name = function_call_name[:64]
            function_call.function_call_name = function_call_name

        # parse function_call
        function_description = {
            "name": function_call.function_call_name,
            "description": function_call.function_call_description,
            "parameters": {"type": "object", "properties": {}},
        }
        logging.info(f"function_call.openapi_url: {function_call.openapi_url}")

        # 判断如果写了接口说明，不走下面读取openapi直接返回
        if (
            function_call.body is not None
            and function_call.body != ""
            and function_call.body != "{}"
        ):
            function_description["parameters"]["properties"] = function_call.body
            if (
                function_call.body_required is not None
                and function_call.body_required != "[]u"
            ):
                function_description["parameters"][
                    "required"
                ] = function_call.body_required
            return {
                "function_description": function_description,
                "operation": {
                    "function_call_name": function_call.function_call_name,
                    "operation": None,
                    "function_call_info": function_call,
                },
            }

        # 读取openapi
        try:
            spec = MyOpenAPISpec.from_url(function_call.openapi_url)
            operation = APIOperation.from_openapi_spec(
                spec, function_call.path, function_call.method
            )
        except Exception as e:
            operation = None
            if function_call.function_call_description:
                operation = APIOperation(
                    operation_id=str(uuid4()),
                    description=function_call.function_call_description,
                    base_url=function_call.server_url,
                    path=function_call.path,
                    method=function_call.method,
                    properties=[],
                    request_body=None,
                )
                function_description["parameters"]["properties"]["value"] = {
                    "type": "object",
                    "description": "any value",
                }
            else:
                logging.info(f"【function_call】 ERROR OpenAPISpec.from_url: {e}")
            return {
                "function_description": function_description,
                "operation": {
                    "function_call_name": function_call.function_call_name,
                    "operation": operation,
                    "function_call_info": function_call,
                },
            }
        function_description["description"] = operation.description
        required = []
        for prop in operation.properties:
            function_description["parameters"]["properties"][prop.name] = {
                "type": str(prop.type),
                "description": prop.description,
            }
            if prop.required:
                required.append(prop.name)
        if operation.request_body is not None:
            for prop in operation.request_body.properties:
                function_description["parameters"]["properties"][prop.name] = {
                    "type": str(prop.type),
                    "description": prop.description,
                }
                if prop.required:
                    required.append(prop.name)

        # 拼接是否有必填参数
        if len(required) > 0:
            function_description["parameters"]["required"] = required
        return {
            "function_description": function_description,
            "operation": {
                "function_call_name": function_call.function_call_name,
                "operation": operation,
                "function_call_info": function_call,
            },
        }

    async def _retry_anext(self, gpt_response_iter, retries=3, delay=1):
        """获取迭代器的第一个元素, 如果获取失败, 则重试, 防止迭代器为空"""
        for attempt in range(retries):
            try:
                # 添加超时控制
                return await asyncio.wait_for(anext(gpt_response_iter), timeout=100)
            except asyncio.TimeoutError as e:
                if attempt < retries - 1:
                    logging.warning(
                        f"Retrying anext due to: {e}, attempt {attempt + 1}, retrying..."
                    )
                    await asyncio.sleep(delay)
                else:
                    raise
            except (StopAsyncIteration, asyncio.TimeoutError) as e:
                raise

    async def function_call_handle(self, gpt_response_iter: AsyncIterator) -> Response:
        """大模型调用的处理函数, 通过这个函数判断是否调用了function_call
        如果调用FunctionCall, 则需要接收所有流式返回的信息, 然后调用api函数
        如果没有调用FunctionCall, 则直接返回迭代器
        """
        try:
            first_element = await self._retry_anext(gpt_response_iter)
            received_time = time.time()
            logging.info(
                f"【FunctionCallHandler】First element type: {type(first_element)}, "
                f"has additional_kwargs: {hasattr(first_element, 'additional_kwargs')}, "
                f"time: {received_time - self.start_time}"
            )
            if hasattr(first_element, "additional_kwargs"):
                logging.debug(
                    f"【FunctionCallHandler】additional_kwargs content: "
                    f"{first_element.additional_kwargs}"
                )
        except StopAsyncIteration:
            # 如果迭代器为空, 直接返回
            logging.warning(
                f"【FunctionCallHandler】function_call_handle stop async iteration, unable to get first element; message_id: {self.question_in.message_id} - {self.question_in.question}"
            )
            raise StopAsyncIteration
        # 判断第一个元素
        if (
            first_element
            and not isinstance(first_element, str)
            and first_element.message.additional_kwargs
        ):
            self.function_call_sta = True
            additional_kwargs = await parse_tool_call_stream_result(
                first_element, gpt_response_iter, self.call_style
            )
            gpt_response = first_element.message
            gpt_response.additional_kwargs = additional_kwargs
            # 整理成为LLMResponse, 方便统一操作.
            response = Response(
                tool_calls=additional_kwargs.get("tool_calls"),
            )
        else:
            # 没有调用function_call, 直接返回迭代器
            self.function_call_sta = False
            self.stream_callback.queue.put_nowait(first_element)
            response = Response(
                gpt_response_iter=gpt_response_iter,
            )
        return response

    async def _perpare_tools_and_apis(self, style=FunctionCallStyle.OPENAI_STYLE_OLD):
        """准备工具和APIs, 最终返回一个tools列表, 不管是自定义工具还是外部的API Source都会被注册到tools列表中"""
        function_call_list = []
        for function_call_api in self.robot.apis.related_objects:
            # 过滤掉已经删除的API (容错处理)
            if function_call_api.deleted_at is not None:
                continue
            function_call_list.append(function_call_api)
        self.function_call_list = function_call_list
        if not self.function_call_list:
            # todo - 无function_call_list的情况
            self.function_descriptions = []
            pass
        else:
            # 解析function_call_list,提取出标准的function_description和operations类(用于方便调用)
            tasks = [
                self._parse_function_call(function_call)
                for function_call in function_call_list
            ]
            results = await asyncio.gather(*tasks)
            self.function_descriptions = [
                result["function_description"] for result in results
            ]
            self.operations = {
                result["operation"]["function_call_name"]: result["operation"][
                    "operation"
                ]
                for result in results
            }
            self.function_call_infos = {
                result["operation"]["function_call_name"]: result["operation"][
                    "function_call_info"
                ]
                for result in results
            }
            if self.function_descriptions is None:
                logging.warning(
                    f"【AgentFunctionCallEngine】 function_descriptions is None. ai_di: {self.robot.id}"
                )
                self.function_call_sta = False
                raise ValueError(
                    "function_descriptions is None. please check the robot apis"
                )
            logging.info(
                f"【AgentFunctionCallEngine】 function_descriptions: {self.function_descriptions}"
            )
            # 日志计时
            end_time = time.time()
            logging.info(
                f"【AgentFunctionCallEngine】 parse function_call time: {end_time - self.start_time}"
            )
        # 将API Source注册到tools列表中
        api_source_tools = []
        for item in self.function_descriptions:
            name = item.get("name")
            description = item.get("description")
            parameters = item.get("parameters")
            properties = parameters.get("properties")
            required = parameters.get("required")
            tool_info = ToolInfo(
                name=name,
                description=description,
                properties=properties,
                required=required,
                is_api_source=True,
            )
            tool = format_info_2_tool(tool_info, style=style)
            api_source_tools.append(tool)
        return api_source_tools

    async def get_debug_prompt(self):
        if not self.robot.prompt:
            logging.warning(
                f"【AgentFunctionCallEngine】robot prompt is empty, robot_id: {str(self.robot.id)}"
            )
            raise ValueError("robot prompt is empty")
        meta_prompt = MetaPromptProcessor(self.question_in, self.robot).get_meta_prompt(
            dynamic_background=self.question_in.dynamic_background, mode=self.mode
        )
        # 整理历史信息
        openai_history = convert_base_messages_to_openai_format(self.chat_history)
        if self.mode == "tts":
            question = enhanced_prompt.format(question=self.question_in.question)
            openai_history.append({"role": "user", "content": question})
        else:
            openai_history.append(
                {"role": "user", "content": self.question_in.question}
            )
        # 构建tools列表
        api_source_tools = await self._perpare_tools_and_apis(
            self.llm_client.call_style
        )
        self.tools.extend(api_source_tools)
        return meta_prompt, openai_history, self.tools

    async def run(self, **kwargs):
        # 校验robot的prompt是否为空
        if not self.robot.prompt:
            logging.warning(
                f"【AgentFunctionCallEngine】robot prompt is empty, robot_id: {str(self.robot.id)}"
            )
            raise ValueError("robot prompt is empty")

        # 构建System的prompt
        # meta_prompt = MetaPromptProcessor(self.question_in, self.robot).get_meta_prompt(
        #     dynamic_background=self.question_in.dynamic_background, mode=self.mode
        # )
        # 构建datasets
        if not self.datasets:
            """
            关于self.datasets的说明:
            最开始的时候是用于让Agent兼容RAG, 更好的调用RAG工具, 所以需要整理GBase中数据集的名称和描述, 给到Agent中
            之后又出现了一个新的想法, 就是把背景知识全部给到大模型或者给大模型一个文档目录树, 让大模型进行召回的工作, 所以这个datasets的作用就有点变了
            - 目前的设计是让知识上下文的构建在AgentEngine外部进行, 如果datasets不为空, 则agent不会使用原有的dataset的构建逻辑
            - 如果datasets为空, 则agent会使用原有的dataset的构建逻辑, 保证之前代码的兼容性
            注意: 这个设计是为了让AgentEngine更加灵活, 以后可以根据需求进行调整 - 另外, 目前使用背景知识和使用RAG工具设计为不兼容的, 也就是只能选择一种方式
            """
            robot_datasets = self.robot.datasets.related_objects
            self.datasets = []
            if robot_datasets:
                for dataset in robot_datasets:
                    DatasetInfo(
                        name=dataset.name,
                        description=dataset.description,
                    )
                    self.datasets.append(dataset)

        # 构建tools列表
        api_source_tools = await self._perpare_tools_and_apis(
            self.llm_client.call_style
        )
        self.tools.extend(api_source_tools)

        meta_prompt_processor = MetaPromptProcessor(self.question_in, self.robot)
        meta_prompt = await meta_prompt_processor.gen_meta_prompt(
            prompt_text=self.robot.prompt,
            prompt_type=self.prompt_type,
            dynamic_background=self.question_in.dynamic_background,
            datasets=self.datasets,
            tools=self.tools,
            **kwargs,
        )
        logging.info(f"【AgentFunctionCallEngine】- run - meta_prompt: {meta_prompt}")

        # 整理历史信息
        openai_history = convert_base_messages_to_openai_format(self.chat_history)
        # 处理运行时上下文信息
        if self.context and self.context.runtime_context:
            openai_history.append({
                "role": "assistant",
                "content": f"AGENT RUNTIME: {json.dumps(self.context.runtime_context, ensure_ascii=False)}",
            })
        if self.prompt_type == PromptType.TTS:
            question = enhanced_prompt.format(question=self.question_in.question)
            openai_history.append({"role": "user", "content": question})
        else:
            openai_history.append(
                {"role": "user", "content": self.question_in.question}
            )

        self.llm_client.meta_prompt = meta_prompt
        self.llm_client.history = openai_history
        self.llm_client.construct_request_messages()

        try:
            # 构建初始请求
            gpt_response_iter = await self.llm_client.request_model(
                self.tools, max_tokens=self.max_tokens
            )

            # 创建内部迭代器
            internal_iter = InternalStreamIterator(self, context=self.context)
            internal_iter.current_stream = gpt_response_iter
            response = Response(
                gpt_response_iter=internal_iter,
                question_record_obj=self.question_record_obj,
            )
            # 返回内部迭代器
            return response

        except Exception as e:
            logging.error(
                f"【Agent Function Call】question_answer_with_function_call error: {e}"
            )
            raise e

    def reset_callback_state(self):
        """重置callback的状态"""
        if self.stream_callback:
            # 重置队列
            self.stream_callback.queue = asyncio.Queue()
            # 重置事件
            if self.stream_callback.event:
                self.stream_callback.event.clear()
            # 重置done状态
            self.stream_callback.done.clear()
            # 重置工具调用状态
            self.stream_callback.is_tool_calls = False
            self.stream_callback.turn_answer_buffer.append([])
        self.storage_callback.is_tool_calls = False
        if self.storage_callback.event:
            self.storage_callback.event.clear()
        # self.storage_callback.done.clear()


class InternalStreamIterator:
    """内部迭代器,用于处理混合流(普通回答和工具调用)"""

    def __init__(self, engine, context=None):
        self.engine = engine
        self.current_stream = None
        self.buffer = []
        self.tool_calls_count = 0  # 添加计数器防止无限循环
        self.MAX_TOOL_CALLS = 5  # 最大工具调用次数
        self.context = context  # 全局上下文对象
        if self.context and self.context.question_in:
            self.message_id = self.context.question_in.message_id
        else:
            self.message_id = self.engine.question_in.message_id
        self.tool_thinking_timeout = 60.0  # 工具思考超时时间
        self.tool_call_timeout = 60.0  # 工具调用超时时间
        self.answer_buffer = []  # 用于存储回答的buffer

        # 状态机相关
        self.status = ToolExecutionStatus.NO_CALL  # 当前状态
        self.pending_tool_info = {}  # 待处理的工具调用信息
        self.tool_results = []  # 工具调用结果

        # 工具调用相关
        self.is_processing_tool = False
        self._collection_task = None  # 用于收集工具调用信息的任务
        self._tool_chunk_buffer = []  # 工具调用的chunk缓存, 理论上应该只有一个元素, 但是为了安全起见, 还是使用列表
        self._tool_execution_task = None

        # 工具调用信息反馈开关 (是否返回工具调用状态)
        self.enable_tool_call_messages = False
        if self.context and self.context.enable_tool_call_messages:
            self.enable_tool_call_messages = self.context.enable_tool_call_messages

        # 状态处理器映射
        self._state_handlers = {
            ToolExecutionStatus.NO_CALL: self._handle_normal_state,
            ToolExecutionStatus.TOOL_START: self._handle_tool_start_state,
            ToolExecutionStatus.TOOL_PROCESSING: self._handle_tool_processing_state,
            ToolExecutionStatus.TOOL_END: self._handle_tool_end_state,
        }

    def __aiter__(self):
        return self

    def _is_tool_status_message(self, chunk):
        pass

    async def __anext__(self):
        """处理流式输出的核心方法"""
        # 检查工具调用次数限制
        if self.tool_calls_count >= self.MAX_TOOL_CALLS:
            raise StopAsyncIteration

        try:
            # 优先处理buffer中的数据
            if self.buffer:
                chunk = self.buffer.pop(0)
                if self.enable_tool_call_messages and isinstance(chunk, ToolCallInfomation):
                    # 开启了工具调用状态返回开关, 并且chunk是工具调用状态类型, 转成字符串返回
                    return chunk.format_output_chunk(message_id=self.message_id)
                else:
                    # 这个chunk不要了, 递归下一个chunk的输出
                    return await self.__anext__()

            # 获取当前状态对应的处理器
            handler = self._state_handlers.get(self.status)
            if not handler:
                logging.error(f"Invalid status: {self.status}")
                raise ValueError(f"Invalid status: {self.status}")

            # 执行状态处理器
            result_chunk = await handler()
            # 根据result_chunk的类型进行处理
            if isinstance(result_chunk, ToolCallInfomation):
                if self.enable_tool_call_messages:
                    return result_chunk.format_output_chunk(message_id=self.message_id)
                # 启用状态消息返回开关, 转换格式并返回
                return await self.__anext__()
            else:
                return result_chunk
        except StopAsyncIteration as e:
            raise e
        except Exception as e:
            logging.error(f"[InternalStreamIterator] Error : {e}")

        # 根据当前状态执行不同逻辑
        # if self.status == ToolExecutionStatus.NO_CALL:
        #     return await self._handle_normal_state()
        # elif self.status == ToolExecutionStatus.TOOL_START:
        #     return await self._handle_tool_start_state()
        # elif self.status == ToolExecutionStatus.TOOL_PROCESSING:
        #     return await self._handle_tool_processing_state()
        # elif self.status == ToolExecutionStatus.TOOL_END:
        #     return await self._handle_tool_end_state()

    async def _handle_normal_state(self):
        """处理普通状态 (大模型正常的answer输出)"""
        if not self.current_stream:
            raise StopAsyncIteration

        try:
            # 获取下一个chunk
            chunk = await anext(self.current_stream)
            logging.debug(f"[_handle_normal_state] Received chunk: {chunk}")
            # 检查是否是工具调用
            if hasattr(chunk, "message") and chunk.message.additional_kwargs:
                logging.info(f"[_handle_normal_state] Detected tool call chunk")
                logging.debug(f"本次请求调用工具了")
                # 标记为正在处理工具调用
                self.is_processing_tool = True
                # 保存工具调用的初始chunk
                self._tool_chunk_buffer.append(chunk)
                # 启动异步任务收集工具调用信息
                self._collection_task = asyncio.create_task(
                    self._collect_tool_call_info(chunk)
                )
                # 递归调用以返回工具思考状态
                tool_call_infomation = ToolCallInfomation(
                    tool_status=ToolExecutionStatus.TOOL_THINKING,
                )
                self.status = ToolExecutionStatus.TOOL_START
                return tool_call_infomation
            if not self.is_processing_tool:
                # 返回字符串chunk
                return chunk
            else:
                logging.error(f"[_handle_normal_state] Already processing tool call - chunk: {chunk}")
                raise Exception(f"[InternalStreamIterator] Already processing tool call - chunk: {chunk}")
        except StopAsyncIteration:
            # 当前流结束
            if not self.is_processing_tool:
                logging.info(f"Stream ended - not processing tool calls")
            self.engine.finish_event.set()
            logging.info(f"Stream ended - current tool calls count: {self.tool_calls_count}")
            raise StopAsyncIteration

    @deprecated(version="0.1.0", reason="这个方法已经不再使用了, 暂时放在这里, 如果验证后没问题, 则删除")
    async def _handle_tool_thinking_state(self):
        """处理工具思考状态"""
        tool_call_infomation = ToolCallInfomation(
            tool_status=self.status,
        )
        self.status = ToolExecutionStatus.TOOL_START
        return tool_call_infomation

    async def _handle_tool_start_state(self):
        """处理工具调用开始状态
            等待收集任务完成，然后开始工具调用
            """
        # 等待收集任务完成
        if self._collection_task and not self._collection_task.done():
            # 等待收集完成，但设置超时避免永久阻塞
            try:
                additional_kwargs, buffer_messages, answer_buffer, turn_answer_buffer = await asyncio.wait_for(
                    self._collection_task,
                    timeout=self.tool_thinking_timeout  # 设置合理的超时时间
                )
                if not self.pending_tool_info:
                    # 更新工具信息
                    self.pending_tool_info.update({
                        "additional_kwargs": additional_kwargs,
                        "buffer_messages": buffer_messages,
                        "answer_buffer": answer_buffer,
                        "turn_answer_buffer": turn_answer_buffer,
                        "call_style": self.engine.llm_client.call_style
                    })
                else:
                    new_tool_calls = additional_kwargs.get("tool_calls", [])
                    cur_rent_tool_calls = self.pending_tool_info["additional_kwargs"].get("tool_calls", [])
                    for ix, tool_call in enumerate(cur_rent_tool_calls):
                        tool_call["id"] = new_tool_calls[ix].get("id") if new_tool_calls[ix].get("id") else tool_call["id"]
                        tool_call["function"]["arguments"] = new_tool_calls[ix].get("function").get("arguments") if new_tool_calls[ix].get("function").get("arguments") else tool_call["function"]["arguments"]
                        tool_call["function"]["name"] = new_tool_calls[ix].get("function").get("name") if new_tool_calls[ix].get("function").get("name") else tool_call["function"]["name"]
            except asyncio.TimeoutError:
                logging.error(f"[_handle_tool_start_state] 收集工具调用信息超时")
                self.status = ToolExecutionStatus.NO_CALL
                # 超时处理：回到普通状态
                self.is_processing_tool = False
                tool_call_infomation = ToolCallInfomation(
                    tool_status=ToolExecutionStatus.TOOL_TIMEOUT,
                    tool_result=f"[ERROR] 收集工具调用信息超时 - 超时设定时间: {self.tool_thinking_timeout}秒"
                )
                return tool_call_infomation
            except Exception as e:
                logging.error(f"[_handle_tool_start_state] 收集工具调用信息失败: {e}")
                # 出错回到普通状态
                self.state = ToolExecutionStatus.NO_CALL
                self.is_processing_tool = False
                tool_call_infomation = ToolCallInfomation(
                    tool_status=ToolExecutionStatus.TOOL_ERROR,
                )
                return tool_call_infomation
        # 到这里收集任务已完成
        try:
            # 获取所有工具调用
            tool_calls = self._get_all_tool_calls(
                self.pending_tool_info["additional_kwargs"],
                self.pending_tool_info.get("call_style", self.engine.llm_client.call_style)  # 使用get避免键不存在
            )
            # 如果没有工具调用，返回错误
            if not tool_calls:
                logging.error(f"[_handle_tool_start_state] No tool calls found")
                self.status = ToolExecutionStatus.NO_CALL
                tool_call_infomation = ToolCallInfomation(
                    tool_status=ToolExecutionStatus.TOOL_ERROR,
                    tool_result="[ERROR] No tool calls found"
                )
                return tool_call_infomation
            for tool_call in tool_calls:
                logging.info(f"[_handle_tool_start_state] Tool call: {tool_call}")
                tool_call_infomation = ToolCallInfomation(
                    tool_status=ToolExecutionStatus.TOOL_START,
                    tool_call=tool_call
                )
                self.buffer.append(tool_call_infomation)
            # 转到处理中状态
            self.status = ToolExecutionStatus.TOOL_PROCESSING
            logging.info(f"[_handle_tool_start_state] 转换到处理状态，buffer中有 {len(self.buffer)} 个工具开始状态")
            # 返回第一个工具开始状态
            if self.buffer:
                return self.buffer.pop(0)
            else:
                # 防御性编程：这种情况理论上不应该发生，但为了安全
                logging.warning("[_handle_tool_start_state] Buffer为空，但我们期望至少有一个工具开始状态")
                tool_call_infomation = ToolCallInfomation(
                    tool_status=ToolExecutionStatus.TOOL_START,
                    tool_call={"name": "unknown", "id": str(uuid4()), "arguments": "{}"}
                )
                return tool_call_infomation
        except Exception as e:
            # 全局异常处理
            logging.error(f"[_handle_tool_start_state] 处理工具调用信息失败: {e}")
            self.status = ToolExecutionStatus.NO_CALL
            self._collection_task = None  # 清空收集任务
            self.is_processing_tool = False
            tool_call_infomation = ToolCallInfomation(
                tool_status=ToolExecutionStatus.TOOL_ERROR,
                tool_result=f"[ERROR] {str(e)}"
            )
            return tool_call_infomation


    async def _handle_tool_processing_state(self):
        """处理工具调用处理中状态, 启动异步任务执行工具调用，返回处理中状态
        """
        # 如果尚未创建执行任务，则创建
        if not self._tool_execution_task:
            try:
                # 创建Response对象
                if self.pending_tool_info.get("call_style") == FunctionCallStyle.OPENAI_STYLE_OLD:
                    response = Response(
                        tool_calls=[
                            {
                                "id": str(uuid4()),
                                "function": self.pending_tool_info["additional_kwargs"]["function_call"],
                                "type": "function",
                            }
                        ]
                    )
                else:
                    content = "".join(self.pending_tool_info["answer_buffer"]) if self.pending_tool_info.get(
                        "answer_buffer") else ""
                    turn_answer = "".join(self.pending_tool_info["turn_answer_buffer"][-1])
                    response = Response(
                        tool_calls=self.pending_tool_info["additional_kwargs"].get("tool_calls", []),
                        content=content,
                        turn_answer=turn_answer,
                    )
                # 启动异步任务执行工具调用
                logging.info(f"[_handle_tool_processing_state] 启动工具调用异步任务")
                self._tool_execution_task = asyncio.create_task(
                    self.engine._call_tool_or_function(response)
                )
                # 将工具调用信息添加到llm_client
                self.engine.llm_client.add_llm_tool_calls_response(response)
                # 返回处理中状态
                for tool_call in response.tool_calls:
                    tool_call_infomation = ToolCallInfomation(
                        tool_status=ToolExecutionStatus.TOOL_PROCESSING,
                        tool_call=tool_call
                    )
                    self.buffer.append(tool_call_infomation)
                # 直接转到结束状态
                self.status = ToolExecutionStatus.TOOL_END
                if not self.buffer:
                    logging.error(f"[_handle_tool_processing_state] Buffer为空，但我们期望至少有一个工具调用信息")
                    tool_call_infomation = ToolCallInfomation(
                        tool_status=ToolExecutionStatus.TOOL_ERROR,
                        tool_result="[ERROR] Buffer为空，但我们��望至少有一个工具调用信息"
                    )
                    return tool_call_infomation
                return self.buffer.pop(0)
            except Exception as e:
                logging.error(f"[_handle_tool_processing_state] 创建工具调用任务失败: {e}")
                self.status = ToolExecutionStatus.NO_CALL
                self.is_processing_tool = False
                tool_call_infomation = ToolCallInfomation(
                    tool_status=ToolExecutionStatus.TOOL_ERROR,
                    tool_result=f"[ERROR] {str(e)}"
                )
                return tool_call_infomation
        else:
            # 如果已经创建了任务但代码仍到达这里（理论上不应该发生）
            logging.error(f"[_handle_tool_processing_state] 工具调用任务已经创建，但仍然到达这里")
            # 作为防御性编程，返回处理中状态
            tool_call_infomation = ToolCallInfomation(
                tool_status=ToolExecutionStatus.TOOL_ERROR,
                tool_result=f"[ERROR] 工具调用任务已经创建，但仍然到达工具调用处理中状态"
            )
            return tool_call_infomation

    async def _handle_tool_end_state(self):
        """处理工具调用结束状态, 等待工具调用完成，重置状态，请求新的模型响应，然后处理新流"""
        try:
            # 等待工具调用任务完成
            if self._tool_execution_task:
                if not self._tool_execution_task.done():
                    try:
                        # 等待完成，设置超时避免永久阻塞
                        tool_results = await asyncio.wait_for(self._tool_execution_task, timeout=self.tool_call_timeout)
                        # 保存结果
                        self.tool_results = tool_results
                    except asyncio.TimeoutError:
                        logging.error(f"[_handle_tool_end_state] 等待工具调用结果超时")
                        tool_call_infomation = ToolCallInfomation(
                            tool_status=ToolExecutionStatus.TOOL_TIMEOUT,
                            tool_result=f"[ERROR] 等待工具调用结果超时 - 超时设定时间: {self.tool_call_timeout}秒"
                        )
                        return tool_call_infomation
                    except Exception as e:
                        logging.error(f"[_handle_tool_end_state] 等待工具调用结果失败: {e}")
                        tool_call_infomation = ToolCallInfomation(
                            tool_status=ToolExecutionStatus.TOOL_ERROR,
                            tool_result=f"[ERROR] {str(e)}"
                        )
                        self.status = ToolExecutionStatus.NO_CALL
                        return tool_call_infomation
                else:
                    # 任务已完成，检查结果或异常
                    try:
                        # 尝试获取任务结果，这会重新引发任务中的异常
                        tool_results = self._tool_execution_task.result()
                        self.tool_results = tool_results
                    except Exception as e:
                        logging.error(f"[_handle_tool_end_state] 工具调用执行失败: {e}")
                        tool_call_infomation = ToolCallInfomation(
                            tool_status=ToolExecutionStatus.TOOL_ERROR,
                            tool_result=f"[ERROR] 工具调用执行失败: {str(e)}"
                        )
                        self.status = ToolExecutionStatus.NO_CALL
                        return tool_call_infomation
            else:
                # 任务未创建，直接返回结束状态
                logging.error(f"[_handle_tool_end_state] 工具调用任务未创建")
                tool_call_infomation = ToolCallInfomation(
                    tool_status=ToolExecutionStatus.TOOL_ERROR,
                    tool_result="[ERROR] 工具调用任务未创建"
                )
                return tool_call_infomation
            # 获取工具调用信息
            # tool_calls = self._get_all_tool_calls(
            #     self.pending_tool_info["additional_kwargs"],
            #     self.pending_tool_info.get("call_style", self.engine.llm_client.call_style)
            # )
            for tool_result in tool_results:
                # 创建工具调用结束状态信息
                tool_call_infomation = ToolCallInfomation(
                    tool_status=ToolExecutionStatus.TOOL_END,
                    tool_result=tool_result.value
                )
                tool_call_infomation.tool_id=tool_result.tool_call_id
                tool_call_infomation.tool_name=tool_result.name
                # 生成结束状态信息（将此信息发送回前端）
                self.buffer.append(tool_call_infomation)  # 将结束状态添加到缓冲区
            # 增加工具调用计数
            self.tool_calls_count += 1
            # 重置callback状态 (这个会在reset_callback_state中处理)
            self.engine.reset_callback_state()
            # 将工具调用结果添加到llm_client
            self.engine.llm_client.add_tool_calls_result(tool_results)
            logging.debug(f"工具调用结果: {tool_results}")
            self.current_stream = await self.engine.llm_client.request_model(
                self.engine.tools, self.engine.max_tokens
            )
            # 无论执行路径如何，都确保状态被重置
            self.status = ToolExecutionStatus.NO_CALL
            self.is_processing_tool = False
            # self.pending_tool_info = {}
            self._collection_task = None
            self._tool_execution_task = None
            self._tool_chunk_buffer = []
            logging.info(f"[_handle_tool_end_state] 状态已重置")
            return await self.__anext__()  # 递归调用处理新流
        except Exception as e:
            logging.error(f"[_handle_tool_end_state] 处理工具调用结束状态失败: {e}")
            tool_call_infomation = ToolCallInfomation(
                tool_status=ToolExecutionStatus.TOOL_ERROR,
                tool_result=f"[ERROR] {str(e)}"
            )
            self.status = ToolExecutionStatus.NO_CALL
            self.is_processing_tool = False
            # self.pending_tool_info = {}
            self._collection_task = None
            self._tool_execution_task = None
            self._tool_chunk_buffer = []
            logging.info(f"[_handle_tool_end_state] 状态已重置")
            return tool_call_infomation

    async def _collect_tool_call_info(self, initial_chunk):
        """收集完整的工具调用信息，复用现有的collect_and_process_tool_calls方法"""
        # 直接复用现有的collect_and_process_tool_calls方法
        additional_kwargs, buffer_messages = await collect_and_process_tool_calls(
            initial_chunk, self.current_stream, self.engine.llm_client.call_style
        )

        # 获取和处理answer_buffer，保持与原代码一致
        answer_buffer = []
        turn_answer_buffer = [[]]
        for callback in self.engine.callbacks:
            if hasattr(callback, "is_tool_calls"):
                callback.is_tool_calls = True
            if hasattr(callback, "answer_buffer"):
                answer_buffer = callback.answer_buffer
                turn_answer_buffer = callback.turn_answer_buffer

        return additional_kwargs, buffer_messages, answer_buffer, turn_answer_buffer

    def _get_all_tool_calls(self, additional_kwargs, call_style):
        """从kwargs中获取所有工具调用"""
        tool_calls = []

        if call_style == FunctionCallStyle.OPENAI_STYLE_OLD:
            # 旧版本格式只支持单个工具调用
            if "function_call" in additional_kwargs:
                tool_calls.append({
                    "name": additional_kwargs["function_call"].get("name", "未知工具"),
                    "arguments": additional_kwargs["function_call"].get("arguments", "{}"),
                    "id": str(uuid4())
                })
        else:
            # 新版本格式支持多个工具调用
            for tool_call in additional_kwargs.get("tool_calls", []):
                tool_calls.append({
                    "name": tool_call.get("function", {}).get("name", "未知工具"),
                    "arguments": tool_call.get("function", {}).get("arguments", "{}"),
                    "id": tool_call.get("id", str(uuid4()))
                })

        return tool_calls
