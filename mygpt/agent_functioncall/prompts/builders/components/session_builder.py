# mygpt/agent_functioncall/prompts/builders/session_builder.py

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import json


class SessionContextBuilder(ABC):
    """会话上下文构建器的抽象基类

    用于构建会话上下文信息，包括用户状态、背景信息等。
    子类可以根据具体需求实现自己的构建逻辑。
    """

    def __init__(
        self,
        context_items: List[Dict[str, Any]],
        description: Optional[str] = None,
        custom_params: Optional[Dict[str, Any]] = None,
        **kwargs,
    ):
        """
        Args:
            context_items: 上下文信息列表，每个item是一个字典，包含具体的上下文信息
            description: 整个上下文块的描述信息（可选）
        """
        self.context_items = context_items
        self.description = (
            description
            or "This section provides additional contextual information about the user and session."
        )
        self.custom_params = custom_params
        self._kwargs = kwargs

    @abstractmethod
    def build(self) -> str:
        """构建会话上下文内容"""
        pass


class JsonSessionContextBuilder(SessionContextBuilder):
    """JSON格式的会话上下文构建器"""

    def build(self) -> str:
        context = {
            "type": "user_context",
            "description": self.description,
            "context_information": self.context_items,
        }
        return json.dumps(context, indent=2, ensure_ascii=False)


class MarkdownSessionContextBuilder(SessionContextBuilder):
    """Markdown格式的会话上下文构建器"""

    def build(self) -> str:
        lines = [
            "# User Context",
            f"\n{self.description}\n",
            "## Context Information\n",
        ]

        for item in self.context_items:
            lines.append("### " + item.get("description", "Context Item"))
            for key, value in item.items():
                if key != "description":
                    lines.append(f"- {key}: {value}")
            lines.append("")

        return "\n".join(lines)


if __name__ == "__main__":
    context_items = [
        {"phone": "13012341234", "encrypted_phone_num": "esi3j4k5l6m7n8o9p0q"},
        {"language": "Chinese", "timezone": "Asia/Shanghai"},
    ]

    # 使用JSON格式构建器
    json_builder = JsonSessionContextBuilder(
        context_items=context_items, description="Custom session context description"
    )

    # 使用Markdown格式构建器
    md_builder = MarkdownSessionContextBuilder(context_items=context_items)

    # 输出结果
    print(json_builder.build())
    print(md_builder.build())
    print()
