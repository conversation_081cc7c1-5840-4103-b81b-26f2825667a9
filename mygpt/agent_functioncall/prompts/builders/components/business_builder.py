# mygpt/agent_functioncall/prompts/builders/business_builder.py

from abc import ABC, abstractmethod
from typing import List, Optional, Union, Any, Dict
from mygpt.agent_functioncall.prompts.base.manager import PromptManager
from mygpt.agent_functioncall.schemas.prompt_schemas import GeneralParams


class BusinessPromptBuilder(ABC):
    """业务prompt构建器的抽象基类"""

    def __init__(
        self,
        prompt_text: str,
        instructions: Optional[Union[List[str], str]] = None,
        role_description: Optional[str] = None,
        custom_params: Optional[Dict[str, Any]] = None,
        **kwargs,
    ):
        self.prompt_text = prompt_text
        self.instructions = instructions or []
        self.role_description = role_description
        self.custom_params = custom_params
        self._kwargs = kwargs
        self._manager: Optional["PromptManager"] = None

    def set_manager(self, manager: "PromptManager"):
        """设置PromptManager"""
        self._manager = manager

    @abstractmethod
    def build(self, **kwargs) -> str:
        """构建业务prompt内容"""
        pass


class DefaultBusinessBuilder(BusinessPromptBuilder):
    """默认的业务prompt构建器实现"""

    def build(self, **kwargs):
        """使用BusinessPrompt组件构建内容"""
        if not self._manager:
            raise ValueError("PromptManager not set")

        # 3. 处理instructions
        if self.instructions:
            # 将instructions列表转换为格式化文本
            if isinstance(self.instructions, str):
                instructions_text = self.instructions
            else:
                instructions_text = "\n".join(
                    f"- {instruction}" for instruction in self.instructions
                )
            self._manager.add_instructions(instructions_text)
        return self.prompt_text
