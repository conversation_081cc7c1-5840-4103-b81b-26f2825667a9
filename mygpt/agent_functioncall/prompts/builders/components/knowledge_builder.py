from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Optional, Dict, Any
from loguru import logger as logging

from mygpt.agent_functioncall.knowledge_management.dataset_builder import DatasetBaseBuilder
from mygpt.agent_functioncall.knowledge_management.models.document_tree import DocTreeNode, DocumentTree, NodeType
from mygpt.agent_functioncall.schemas.dataset_schemas import (
    DatasetInfo,
    DocumentInfo,
    DocumentType,
    ImageInfo, BuildDocumentIn, BuildDatasetIn, DirectoryInfo,
)


class KnowledgeBaseBuilder(ABC):
    """知识库构建器基类"""

    def __init__(
        self,
        datasets: Optional[List[DatasetInfo]] = None,
        tag_name: str = "KnowledgeBase",
        custom_params: Optional[Dict[str, Any]] = None,
        **kwargs,
    ):
        self.datasets = datasets or []
        self.tag_name = tag_name
        self.custom_params = custom_params
        self._kwargs = kwargs

    @abstractmethod
    def build(self) -> str:
        """构建知识库内容
        Returns:
            str: 构建好的知识库内容
        """
        pass


class RAGKnowledgeBaseBuilder(KnowledgeBaseBuilder):
    """RAG工具调用的知识库构建器
    构建XML格式的知识库内容，用于RAG工具调用
    """

    def build(self) -> str:
        if not self.datasets:
            return ""
        sources = []
        for dataset in self.datasets:
            source = f"""    <Source>
        <Name>{dataset.name}</Name>
        <Description>{dataset.description}</Description>
    </Source>"""
            sources.append(source)
        sources_content = "\n".join(sources)

        return f"""    <Sources>
{sources_content}
</Sources>
<Integration>
    For queries related to specific knowledge sources, initiate a call to the RAG tool to retrieve accurate and up-to-date information.
    Ensure that the response prioritizes retrieving verified information from the knowledge sources instead of generating uncertain content.
</Integration>"""


class ContextKnowledgeBaseBuilder(KnowledgeBaseBuilder):
    """上下文知识库构建器
        直接将知识内容构建为文本格式，用于大模型的上下文
        返回文本示例:
        ```
    <AvailableKnowledge>
    Here are the relevant documents that you should use to inform your responses:

    Document 1: "数字经济发展趋势"
    Type: Article
    Metadata:
    - Author: 张三
    - Published: 2024-02-15
    - Source: 经济日报
    - Tags: 数字经济, 发展趋势, 产业变革

    Content:
    数字经济已经成为推动全球经济发展的重要引擎...（文档具体内容）

    Related Images:
    - Caption: 2023年数字经济占GDP比重
      URL: https://example.com/digital-economy-2023.jpg

    ---

    Document 2: "人工智能在医疗领域的应用"
    Type: Research Report
    Metadata:
    - Authors: <AUTHORS>
    - Institution: XX研究院
    - Published: 2024-01-20
    - Tags: AI, 医疗, 技术应用

    Content:
    近年来，人工智能技术在医疗领域的应用取得了显著进展...（文档具体内容）

    Related Images:
    - Caption: AI辅助诊断系统架构图
      URL: https://example.com/ai-medical-system.jpg

    ---

    Instructions:
    - Use these documents as your primary source of information
    - When citing information, refer to the specific document
    - Integrate image information when relevant to the response
    - Maintain the factual accuracy of the provided content
    </AvailableKnowledge>
        ```
    """

    def _build_document_content(self, doc: DocumentInfo, index: int) -> str:
        """构建单个文档的内容"""
        parts = [f'\nDocument {index}: "{doc.title}"', f"Type: {doc.doc_type.value}"]

        # 构建元数据部分
        metadata_parts = []
        if doc.author:
            metadata_parts.append(f"- Author: {doc.author}")
        if doc.source:
            metadata_parts.append(f"- Source: {doc.source}")
        if doc.published_date:
            metadata_parts.append(
                f"- Published: {doc.published_date.strftime('%Y-%m-%d')}"
            )
        if doc.tags:
            metadata_parts.append(f"- Tags: {', '.join(doc.tags)}")

        if metadata_parts:
            parts.append("Metadata:")
            parts.extend(metadata_parts)

        # 添加文档内容
        parts.extend(["\nContent:", doc.content])

        # 添加图片信息
        if doc.images:
            parts.append("\nRelated Images:")
            for img in doc.images:
                parts.append(f"- Caption: {img.caption or 'No caption'}")
                parts.append(f"  URL: {img.url}")

        parts.append("\n---")
        return "\n".join(parts)

    def build(self) -> str:
        if not self.datasets:
            return ""
        documents = self.datasets[0].documents
        if not documents:
            return ""
        # 构建知识库内容
        parts = [
            "Here are the relevant documents that you should use to inform your responses:\n"
        ]
        # 添加每个文档的内容
        for i, doc in enumerate(documents, 1):
            parts.append(self._build_document_content(doc, i))

        # 添加使用说明
        parts.extend(
            [
                "Instructions:",
                "- Use these documents as your primary source of information",
                "- When citing information, refer to the specific document",
                "- Integrate image information when relevant to the response",
                "- Maintain the factual accuracy of the provided content",
            ]
        )

        return "\n".join(parts)


class ContextWithDocTreeKnowledgeBuilder(KnowledgeBaseBuilder):
    """上下文知识库构建器（包含文档树）
    上下文知识库构建器，包含文档树结构, 上下文内容使用Summary而非完整的文档内容
    返回文本示例:
    ```
<AvailableKnowledge>
# Knowledge Libraries

## Library 1: AI Technology Knowledge Base
This knowledge base contains information about artificial intelligence technologies.

### Document Structure
===
Knowledge Base: AI Technology Knowledge Base
|-- + Fundamental Theories
|   |-- - Supervised Learning Principles [doc_101]
|   |-- - Unsupervised Learning Methods [doc_102]
|-- + Applied Technologies
|   |-- + Natural Language Processing
|       |-- - Transformer Architecture [doc_201]
|       |-- - Large Language Models [doc_202]
|-- + Industry Applications
    |-- - AI in Healthcare [doc_301]
===

### Document Information

[doc_101] Supervised Learning Principles
Summary: Supervised learning is a primary machine learning paradigm that uses labeled data to train predictive models.
Topics: Machine Learning, Algorithm Fundamentals
Tags: #MachineLearning #Classification #Regression #Algorithms
Information Coverage: 85%
Key Missing Aspects: Latest algorithm developments, Complex scenario applications

[doc_202] Large Language Models
Summary: Introduction to principles, architecture and applications of large language models, analyzing features of mainstream models.
Topics: NLP, LLM, Pre-trained Models
Tags: #GPT #Transformer #NLP
Information Coverage: 92%
Key Missing Aspects: Training cost analysis

## Library 2: Programming Languages Knowledge Base
This knowledge base contains programming language learning materials.

### Document Structure
===
Knowledge Base: Programming Languages Knowledge Base
|-- + Python
|   |-- - Python Basic Syntax [doc_501]
|   |-- - Python Advanced Features [doc_502]
|-- + JavaScript
    |-- - JavaScript Introduction [doc_601]
    |-- - React Framework Tutorial [doc_602]
===

### Document Information

[doc_501] Python Basic Syntax
Summary: Introduction to Python's basic syntax, data types and control structures.
Topics: Python, Programming Fundamentals
Tags: #Python #ProgrammingBasics
Information Coverage: 90%
Key Missing Aspects: Object-oriented programming examples

[doc_602] React Framework Tutorial
Summary: Detailed explanation of React framework core concepts, component development and state management.
Topics: Frontend Development, React
Tags: #JavaScript #React #Hooks #Frontend
Information Coverage: 88%
Key Missing Aspects: Performance optimization techniques, Large project architecture

# Usage Guidelines
- Answer questions based on the knowledge provided in these documents
- If the summary information is sufficient, use it to answer directly
- Consider information coverage and key missing aspects when providing answers
- You may integrate information from multiple documents when appropriate
- For questions outside the scope of these documents, indicate the limitations of the available information
</AvailableKnowledge>
    ```
    """

    def _format_node(self, node: DocTreeNode, is_last=False, prefix="", use_emoji=True) -> str:
        """格式化树节点
        Args:
            node: 树节点
            is_last: 是否是父节点的最后一个子节点
            prefix: 当前行的前缀字符串
        Returns:
            格式化后的文本
        """
        result = []
        # 根节点特殊处理
        if node.node_type == NodeType.ROOT:
            line = node.name
        else:
            # 非根节点添加连接符和类型标识
            if node.node_type == NodeType.DOCUMENT:
                doc_id = node.node_id
                id_str = f" [{doc_id}]" if doc_id else ""
                if use_emoji:
                    line = f"{prefix}|-- 📄 {node.name}{id_str}"
                else:
                    # 文档节点: 添加"-"符号和ID
                    line = f"{prefix}|-- - {node.name}{id_str}"
            else:
                dir_id = node.node_id
                id_str = f" [{dir_id}]" if dir_id else ""
                if use_emoji:
                    line = f"{prefix}|-- 📁 {node.name}{id_str}"
                else:
                    # 目录节点: 添加"+"符号
                    line = f"{prefix}|-- + {node.name}{id_str}"
        result.append(line)
        # 递归处理子节点
        if hasattr(node, "children") and node.children:
            children = list(node.children)
            # 处理每个子节点
            for i, child in enumerate(children):
                # 判断是否是最后一个子节点
                is_child_last = (i == len(children) - 1)
                # 计算子节点的前缀
                # 如果当前节点是根节点，子节点前缀为空
                if node.node_type == NodeType.ROOT:
                    child_prefix = ""
                else:
                    # 否则，添加竖线或空格作为延伸
                    child_prefix = prefix + ("|   " if not is_last else "    ")

                # 递归处理子节点
                child_result = self._format_node(child, is_child_last, child_prefix)
                result.append(child_result)
        return "\n".join(result)

    def _build_tree_structure(self, dataset: DatasetInfo) -> str:
        """构建文档树结构字符串"""
        # 首先检查数据集是否包含文档树对象
        if "document_tree" in dataset.metadata and dataset.metadata["document_tree"]:
            doc_tree: DocumentTree = dataset.metadata["document_tree"]
            if hasattr(doc_tree, "root"):
                header = f"Knowledge Base: {dataset.name}"
                tree_text = self._format_node(doc_tree.root)
                return f"{header}\n{tree_text}"
            raise ValueError("Document tree root not found in the dataset metadata")
        # 没有文档树对象时，返回一个描述字符串信息, 告诉大模型没有文档树
        return "Document tree structure not available for this dataset."

    def _build_document_summary(self, dataset: DatasetInfo) -> str:
        """构建文档摘要信息"""
        parts = []
        for doc in dataset.documents:
            doc_id = doc.doc_id or ""
            if not doc_id:
                logging.warning(f"Document ID not found for document: {doc.title}")
            parts.append(f"[{doc_id}] {doc.title}")
            # 获取摘要信息
            summary_info = doc.summary_info
            # 添加摘要
            if summary_info.summary:
                parts.append(f"Summary: {summary_info.summary}")
            else:
                logging.warning(f"Summary not found for document: {doc.title}")
                if not doc.content:
                    logging.warning(f"Content not found for document: {doc.title}")
                    logging.warning("Skipping document without content")
                    continue
                # 使用内容的前100个字符作为预览
                # preview = doc.content[:100] + "..." if len(doc.content) > 100 else doc.content
                content = doc.content
                parts.append(f"Content: \n```\n{content}\n```")
            # 添加主题
            if summary_info.topics:
                parts.append(f"Topics: {', '.join(summary_info.topics)}")
            # 添加标签
            if summary_info.tags:
                parts.append(f"Tags: {', '.join(summary_info.tags)}")
            # 添加信息覆盖率
            if summary_info.information_coverage:
                parts.append(f"Information Coverage: {summary_info.information_coverage}%")
            # 添加关键缺失点
            if summary_info.missing_key_aspects:
                parts.append(f"Key Missing Aspects: {', '.join(summary_info.missing_key_aspects)}")
            parts.append("")
        return "\n".join(parts)

    def build(self) -> str:
        """构建包含文档树结构的上下文知识库内容"""
        if not self.datasets:
            return ""
        parts = ["", "# Knowledge Libraries", ""]
        # 处理每个知识库
        for i, dataset in enumerate(self.datasets, 1):
            if not dataset.documents:
                continue
            # 添加知识库标题和描述
            parts.append(f"## Library {i}: {dataset.name}")
            if dataset.description:
                parts.append(dataset.description)
            parts.append("")
            # 添加文档树结构
            parts.append("### Document Structure")
            parts.append("===")
            parts.append(self._build_tree_structure(dataset))
            parts.append("===")
            parts.append("")
            # 添加文档摘要信息
            parts.append("### Document Information")
            parts.append("")
            summarires_text = self._build_document_summary(dataset)
            parts.append(summarires_text)
            parts.append("")

        # 添加使用指南
        parts.append("# Usage Guidelines")
        parts.extend(
            [
                "- Answer questions based on the knowledge provided in these documents",
                "- If the summary information is sufficient, use it to answer directly",
                "- Consider information coverage and key missing aspects when providing answers",
                "- You may integrate information from multiple documents when appropriate",
                "- For questions outside the scope of these documents, indicate the limitations of the available information",
                ""
            ]
        )
        return "\n".join(parts)


class DirWithDocTreeKnowledgeBuilder(ContextWithDocTreeKnowledgeBuilder):
    def _build_directory_infomation(self, dataset: DatasetInfo) -> str:
        """构建目录摘要信息"""
        parts = []
        for dir in dataset.directories:
            dir: DirectoryInfo
            dir_id = dir.dir_id
            if not dir_id:
                logging.warning(f"Document ID not found for document: {dir.title}")
            parts.append(f"[{dir_id}] {dir.title}")
            if dir.doc_path:
                if isinstance(dir.doc_path, list):
                    path = " > ".join(dir.doc_path)
                else:
                    path = dir.doc_path
                parts.append(f"Directory Path: {path}")
            if dir.dir_tags:
                parts.append(f"Directory Tags: {', '.join(dir.dir_tags)}")
            if dir.dir_topics:
                parts.append(f"Directory Topics: {', '.join(dir.dir_topics)}")
            # 添加目录概述信息
            if dir.directory_description:
                parts.append(f"Directory description: {dir.directory_description}")
            else:
                logging.warning(f"Directory description not found for directory: {dir.title}")
            parts.append("")
        return "\n".join(parts)

    def build(self) -> str:
        """构建包含文档树结构的上下文知识库内容"""
        if not self.datasets:
            return ""
        parts = ["", "# Knowledge Libraries", ""]
        # 处理每个知识库
        for i, dataset in enumerate(self.datasets, 1):
            if not dataset.directories:
                continue
            # 添加知识库标题和描述
            parts.append(f"## Library {i}: {dataset.name}")
            if dataset.description:
                parts.append(dataset.description)
            parts.append("")
            # 添加文档树结构
            parts.append("### Directory Structure")
            parts.append("===")
            parts.append(self._build_tree_structure(dataset))
            parts.append("===")
            parts.append("")
            # 添加文档摘要信息
            parts.append("### Directory Information")
            parts.append("")
            summarires_text = self._build_directory_infomation(dataset)
            parts.append(summarires_text)
            parts.append("")

        # 添加使用指南
        parts.append("# Usage Guidelines")
        parts.extend(
            [
                "- Analyze user queries against the directory structure and descriptions to identify relevant knowledge domains",
                "- When directory information is insufficient, use tool functions to retrieve document summaries from relevant directories",
                "- After analyzing document summaries, retrieve full document content only when necessary for detailed information",
                "- Follow a progressive retrieval strategy: browse directories -> get relevant document summaries -> retrieve full documents as needed",
                "- Avoid requesting too many documents at once; prioritize the most relevant directories first",
                "- When answering user questions, clearly cite information sources and explain the retrieval process",
                "- When available information is insufficient, clearly state the limitations and suggest more specific query criteria",
                "- For questions completely outside the knowledge base scope, honestly acknowledge when no relevant information can be found",
                ""
            ]
        )
        return "\n".join(parts)


async def test_doc_tree_builder():
    # 创建测试文档
    ai_docs = [
        BuildDocumentIn(
            title="Supervised Learning Principles",
            doc_id="doc_101",
            content="Supervised learning is a primary machine learning paradigm...",
            summary="Supervised learning is a primary machine learning paradigm that uses labeled data to train predictive models.",
            doc_path=["Fundamental Theories", "Supervised Learning Principles"],
            topics=["Machine Learning", "Algorithm Fundamentals"],
            tags=["MachineLearning", "Classification", "Regression", "Algorithms"],
            information_coverage=85.0,
            missing_key_aspects=["Latest algorithm developments", "Complex scenario applications"],
        ),
        BuildDocumentIn(
            title="Large Language Models",
            doc_id="doc_202",
            content="Introduction to principles, architecture and applications of large language models...",
            summary="Introduction to principles, architecture and applications of large language models, analyzing features of mainstream models.",
            doc_path=["Applied Technologies", "Natural Language Processing", "Large Language Models"],
            topics=["NLP", "LLM", "Pre-trained Models"],
            tags=["GPT", "Transformer", "NLP"],
            information_coverage=92.0,
            missing_key_aspects=["Training cost analysis"],
        ),
    ]

    programming_docs = [
        BuildDocumentIn(
            title="Python Basic Syntax",
            doc_id="doc_501",
            content="Introduction to Python's basic syntax, data types and control structures...",
            summary="Introduction to Python's basic syntax, data types and control structures.",
            doc_path=["Python", "Python Basic Syntax"],
            topics=["Python", "Programming Fundamentals"],
            tags=["Python", "ProgrammingBasics"],
            information_coverage=90.0,
            missing_key_aspects=["Object-oriented programming examples"],
        ),
    ]

    # 构建数据集
    datasets_in = [
        BuildDatasetIn(
            name="AI Technology Knowledge Base",
            description="This knowledge base contains information about artificial intelligence technologies.",
            documents=ai_docs,
        ),
        BuildDatasetIn(
            name="Programming Languages Knowledge Base",
            description="This knowledge base contains programming language learning materials.",
            documents=programming_docs,
        ),
    ]

    # 构建知识库
    dataset_info_list = await DatasetBaseBuilder.build_document_context(build_dataset_in_list=datasets_in)

    # 测试文档树构建器
    doc_tree_builder = ContextWithDocTreeKnowledgeBuilder(dataset_info_list)
    knowledge_text = doc_tree_builder.build()

    print("\n=== 文档树知识库测试 ===")
    print(knowledge_text)


if __name__ == "__main__":
    import asyncio

    asyncio.run(test_doc_tree_builder())
