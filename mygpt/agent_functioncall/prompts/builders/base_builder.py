# mygpt/agent_functioncall/prompts/builders/base_builder.py
from datetime import datetime, timedelta
from calendar import monthcalendar
from typing import Dict, Optional
from mygpt.agent_functioncall.prompts.base.manager import PromptManager
from mygpt.agent_functioncall.prompts.builders.components.business_builder import (
    BusinessPromptBuilder,
    DefaultBusinessBuilder,
)
from mygpt.agent_functioncall.prompts.builders.components.knowledge_builder import (
    KnowledgeBaseBuilder,
    RAGKnowledgeBaseBuilder,
)
from mygpt.agent_functioncall.prompts.builders.components.session_builder import (
    SessionContextBuilder,
    JsonSessionContextBuilder,
)
from mygpt.agent_functioncall.prompts.utils.calendar_generator import (
    CalendarContextGenerator,
)
from mygpt.agent_functioncall.schemas.prompt_schemas import (
    BusinessParams,
    SessionParams,
    KnowledgeParams,
    ToolParams,
    GeneralParams,
)


class BasePromptBuilder:
    """基础prompt构建器，包含共用的构建逻辑"""

    def __init__(
        self,
        business_params: BusinessParams,
        session_params: Optional[SessionParams] = None,
        knowledge_params: Optional[KnowledgeParams] = None,
        tool_params: Optional[ToolParams] = None,
        general_params: Optional[GeneralParams] = None,
        **kwargs,
    ):
        self._manager = PromptManager()
        self._calendar_generator = CalendarContextGenerator()

        # 保存参数引用
        self._business_params = business_params
        self._session_params = session_params
        self._knowledge_params = knowledge_params
        self._tool_params = tool_params
        self._general_params = general_params
        self._kwargs = kwargs

        # 子类将在_init_builders中设置这些属性
        self._business_builder = None
        self._session_builder = None
        self._knowledge_builder = None

    async def _build_time_context(self, indent_num: int = 0) -> str:
        """构建时间相关的上下文"""
        now = datetime.now()
        indent = " " * 4 * indent_num
        return f"""{indent}Date: {now.strftime("%Y-%m-%d")}
{indent}Time: {now.strftime("%H:%M:%S")}
{indent}Weekday: {now.strftime("%A")}"""

    async def _add_time_info(self):
        """添加时间信息到动态变量"""
        time_context = await self._build_time_context()
        # 不再单独添加时间变量，而是添加整个时间上下文结构
        self._manager.add_dynamic_variable("CurrentDateTime", time_context)

    async def _add_calendar_info(self):
        """添加日历信息
        使用CalendarContextGenerator生成日历信息
        """
        calendar_context = self._calendar_generator.generate_calendar_context()
        self._manager.add_dynamic_variable("CalendarContext", calendar_context)

    async def _init_builders(self):
        """初始化各种构建器"""
        raise NotImplementedError("Subclass must implement abstract method")

    async def _create_business_builder(self) -> BusinessPromptBuilder:
        """创建业务构建器"""
        # 默认实现，子类可以覆盖
        if not self._business_params:
            raise ValueError("Missing business_params")

        builder = DefaultBusinessBuilder(
            prompt_text=self._business_params.prompt_text,
            instructions=self._business_params.instructions,
            role_description=self._business_params.role_description,
            general_params=self._general_params,
            **self._kwargs,
        )
        builder.set_manager(self._manager)
        return builder

    async def _create_session_builder(self) -> Optional[SessionContextBuilder]:
        """创建会话上下文构建器"""
        if not self._session_params:
            return None

        if self._session_params.format_type.lower() == "json":
            return JsonSessionContextBuilder(
                context_items=self._session_params.context_items,
                description=self._session_params.description,
                custom_params=self._session_params.custom_params,
                **self._kwargs,
            )
        # 可以根据format_type添加其他类型的构建器 - todo 目前只支持json

    async def _create_knowledge_builder(self) -> Optional[KnowledgeBaseBuilder]:
        """创建知识库构建器"""
        if not self._knowledge_params:
            return None
        # 默认创建RAG类型的知识库构建器
        return RAGKnowledgeBaseBuilder(
            datasets=self._knowledge_params.datasets,
            tag_name=self._knowledge_params.tag_name,
            custom_params=self._knowledge_params.custom_params,
            **self._kwargs,
        )

    async def _get_knowledge_content(self) -> str:
        """获取知识库内容"""
        # 将来可能从数据库或其他异步源获取
        if hasattr(self._knowledge_builder, "build_async"):
            return await self._knowledge_builder.build_async()
        return self._knowledge_builder.build()

    async def _get_session_content(self) -> str:
        """获取会话上下文内容"""
        if hasattr(self._session_builder, "build_async"):
            return await self._session_builder.build_async()
        return self._session_builder.build()

    async def _get_business_content(self) -> str:
        """获取业务内容"""
        if hasattr(self._business_builder, "build_async"):
            return await self._business_builder.build_async()
        return self._business_builder.build()

    async def _build_specific(self):
        """特定类型的构建逻辑
        子类应该重写此方法来实现特定类型的构建逻辑
        """
        pass

    async def build(self) -> str:
        """构建prompt

        添加基础配置并构建最终的prompt
        子类可以通过重写此方法来添加特定的构建逻辑
        """
        # 初始化构建器
        await self._init_builders()

        # 添加基础配置
        await self._add_time_info()
        await self._add_calendar_info()

        # 添加知识库信息 (如果有)
        if self._knowledge_builder:
            knowledge_content = self._knowledge_builder.build()
            if knowledge_content:
                self._manager.add_knowledge_base(
                    knowledge_content, tag_name=self._knowledge_builder.tag_name
                )

        # 添加会话上下文信息 (如果有)
        if self._session_builder:
            session_content = self._session_builder.build()
            if session_content:
                self._manager.add_session_context("SessionContext", session_content)

        # 添加业务prompt信息
        business_content = self._business_builder.build()
        self._manager.add_business(business_content)

        # 子类可以通过重写此方法来添加额外的构建步骤
        await self._build_specific()

        # 构建最终的prompt
        return self._manager.build()

    @property
    def manager(self) -> PromptManager:
        """获取prompt管理器
        Returns:
            PromptManager实例
        """
        return self._manager
