from typing import Optional

from mygpt.agent_functioncall.prompts.base.manager import PromptManager
from mygpt.agent_functioncall.prompts.builders.base_builder import BasePromptBuilder
from mygpt.agent_functioncall.prompts.builders.components.business_builder import (
    BusinessPromptBuilder,
)
from mygpt.agent_functioncall.prompts.builders.components.knowledge_builder import (
    KnowledgeBaseBuilder,
)
from mygpt.agent_functioncall.prompts.builders.components.session_builder import (
    SessionContextBuilder,
)

output_requirements_format = """- Use pure natural language without any formatting
- If it is a Cardinal Number, output the number directly. If it is a Digit Number, insert a space between each digit
- Never use lists, bullet points, or numbered formats
- Present information in conversational flowing sentences"""

output_requirements_language = """Dynamically determine the output language based on the most recent user input:
- If the user's input is primarily in one language, respond in that language.
- If the user's input is a mix of multiple languages, prioritize responding in the dominant language of the input.
- If the user's intent indicates a specific language preference (e.g., explicitly requesting a specific language), follow the user's indicated preference.
This ensures that the output matches the user's language expectations and communication needs."""

output_requirements_style = """- Keep responses brief and focused
- Default to one or two sentences unless more detail is requested
- Use conversational tone while maintaining professionalism
- Present information sequentially in natural speech patterns"""

output_requirements_response_length = """- Prioritize concise, focused answers
- Elaborate only when specifically requested
- Break complex information into digestible parts"""

output_requirements_custom_content = """Seamlessly integrate the client-provided business-specific prompt content into the response."""

tool_call_strategy_execution_order = """- Execute tool calls sequentially, one at a time
- Complete each tool call and process its response before initiating the next tool call
- Never attempt parallel tool calls"""

tool_call_strategy_pre_call_message = """Before making a tool call:
- Always provide a brief transitional message first
- Acknowledge the information request
- Indicate that you are about to retrieve information
- Keep the message natural and contextual
- Never skip this transitional message"""

tool_call_strategy_post_call_message = """- Convert tool response to natural speech patterns
- Remove all formatting from tool responses
- Present information conversationally
- Maintain brevity and clarity"""

business_instructions_prompt = """- If the task requires the use of a tool, provide only the necessary tool call information and do not output any natural language response.
- If no tool usage is required, proceed with providing a concise and clear natural language answer."""


class TTSPromptBuilder(BasePromptBuilder):
    """TTS场景的Prompt管理器"""

    # def __init__(
    #     self,
    #     business_builder: BusinessPromptBuilder,
    #     session_builder: Optional[SessionContextBuilder] = None,
    #     knowledge_builder: Optional[KnowledgeBaseBuilder] = None,
    #     **kwargs,
    # ):
    #     super().__init__(
    #         business_builder=business_builder,
    #         session_builder=session_builder,
    #         knowledge_builder=knowledge_builder,
    #     )
    #     # 设置TTS场景的默认目标
    #     self.manager.objective(
    #         "Generate natural language output suitable for Text-to-Speech (TTS) model input."
    #     )
    #     # 设置TTS场景的默认输出要求
    #     self.manager.add_output_requirements("Format", output_requirements_format)
    #     self.manager.add_output_requirements("Language", output_requirements_language)
    #     self.manager.add_output_requirements("Style", output_requirements_style)
    #     self.manager.add_output_requirements(
    #         "ResponseLength", output_requirements_response_length
    #     )
    #     self.manager.add_output_requirements(
    #         "CustomContent", output_requirements_custom_content
    #     )
    #     # 设置TTS场景的默认工具调用策略
    #     self.manager.add_tool_call_strategy(
    #         "ExecutionOrder", tool_call_strategy_execution_order
    #     ).add_tool_call_strategy(
    #         "PreCallMessage", tool_call_strategy_pre_call_message
    #     ).add_tool_call_strategy(
    #         "PostCallMessage", tool_call_strategy_post_call_message
    #     )
    #     # 添加业务prompt的补充规则
    #     self.manager.add_instructions(business_instructions_prompt)

    async def _init_builders(self):
        """初始化各种构建器"""
        self._business_builder = await self._create_business_builder()
        self._session_builder = await self._create_session_builder()
        self._knowledge_builder = await self._create_knowledge_builder()

    async def _add_tool_call_strategies(self):
        """添加工具调用策略"""
        # 这里可能将来会有一些异步操作，比如从配置服务获取策略
        self.manager.add_tool_call_strategy(
            "ExecutionOrder", tool_call_strategy_execution_order
        )
        self.manager.add_tool_call_strategy(
            "PreCallMessage", tool_call_strategy_pre_call_message
        )
        self.manager.add_tool_call_strategy(
            "PostCallMessage", tool_call_strategy_post_call_message
        )

    async def _build_specific(self):
        """添加TTS类型特有的配置"""
        # 设置目标
        self.manager.objective(
            "Generate natural language output suitable for Text-to-Speech (TTS) model input."
        )

        # 添加TTS场景的默认输出要求
        self.manager.add_output_requirements("Format", output_requirements_format)
        self.manager.add_output_requirements("Language", output_requirements_language)
        self.manager.add_output_requirements("Style", output_requirements_style)
        self.manager.add_output_requirements(
            "ResponseLength", output_requirements_response_length
        )
        self.manager.add_output_requirements(
            "CustomContent", output_requirements_custom_content
        )

        # 根据工具参数添加工具调用策略
        if self._tool_params and self._tool_params.tools:
            await self._add_tool_call_strategies()

        # 添加业务prompt的补充规则
        self.manager.add_instructions(business_instructions_prompt)
