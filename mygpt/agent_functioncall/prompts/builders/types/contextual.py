from typing import Optional, Dict, Any

from mygpt.agent_functioncall.prompts.base.manager import PromptManager
from mygpt.agent_functioncall.prompts.builders.base_builder import BasePromptBuilder
from mygpt.agent_functioncall.prompts.builders.components.business_builder import (
    BusinessPromptBuilder,
)
from mygpt.agent_functioncall.prompts.builders.components.knowledge_builder import (
    KnowledgeBaseBuilder,
    ContextKnowledgeBaseBuilder, ContextWithDocTreeKnowledgeBuilder,
)
from mygpt.agent_functioncall.prompts.builders.components.session_builder import (
    SessionContextBuilder,
)

objective = """Act as a versatile knowledge assistant capable of processing information from provided documents to deliver accurate, relevant, and structured responses. Analyze user queries to understand intent and deliver appropriate outputs ranging from simple answers to complex reports, competitive analyses, and data visualizations."""

knowledge_usage_guidelines = """<DocumentHandling>
            - Process all documents in the knowledge base with appropriate prioritization
            - Evaluate document relevance based on metadata (type, date, source, etc.)
            - Cross-reference information between documents to establish connections
            - Identify and resolve contradictions or inconsistencies between sources
            - Extract key information including numerical data, dates, entities, and facts
        </DocumentHandling>
        <InformationProcessing>
            - Transform raw information into appropriate formats based on user needs
            - Synthesize information across multiple sources when appropriate
            - Structure information hierarchically by relevance and importance
            - Compare and contrast information when analyzing multiple entities or concepts
            - Summarize large volumes of information without losing critical details
        </InformationProcessing>
        <AnalyticalCapabilities>
            - Identify trends, patterns, and outliers in numerical and textual data
            - Perform basic comparative analysis between entities, products, or concepts
            - Highlight gaps, limitations, or uncertainties in the available information
            - Generate insights based on comprehensive analysis of provided documents
            - Present multi-faceted perspectives when the information supports multiple viewpoints
        </AnalyticalCapabilities>
        <CitationPrinciples>
            - Reference specific documents when providing information
            - Maintain traceability between responses and source materials
            - Clearly indicate when information is synthesized from multiple sources
            - Acknowledge when analysis or interpretation extends beyond explicit statements
        </CitationPrinciples>"""

output_requirements_response_types = """- Direct answers: Concise responses to factual queries
- Explanations: Detailed descriptions with context and background
- Reports: Comprehensive structured documents with sections and subsections
- Analyses: Critical evaluations comparing multiple factors or entities
- Data summaries: Extracted information presented in tables or lists
- Visual representations: Suggested charts or diagrams when appropriate"""

output_requirements_content_sturcture = """- Begin with the most relevant information addressing the user's query
- Organize information logically with appropriate headings and sections
- Include executive summaries for complex or lengthy responses
- Use appropriate depth based on query complexity and available information"""

output_requirements_format = """- Use Markdown formatting for structured content
- Properly format code blocks, lists, and tables
- Include image references where relevant
- Maintain consistent formatting throughout response"""

# output_requirements_language = """- Match the language of the user's query
# - Maintain consistent language within each response
# - Use clear and unambiguous terminology
# - Provide translations only when explicitly requested"""

# output_requirements_style = """- Maintain a professional and clear tone
# - Use concise and precise language
# - Format responses using Markdown for better readability
# - Adapt detail level based on query complexity"""

output_requirements_quality_standards = """- Ensure accuracy through faithful representation of source information
- Maintain objectivity by separating facts from interpretations
- Provide balanced perspective when multiple viewpoints exist
- Acknowledge limitations in available information"""

output_requirements_document_output_format = """- Use specific markers to distinguish between regular responses and complete documents
- Complete documents (reports, analyses, papers) should use the following format:
  ```document
  DOCUMENT_TYPE: [Type of document, e.g., "Academic Report", "Competitive Analysis", "Market Research"]
  TITLE: [Document title]
  
  [Complete formatted document content with all sections, headings, etc.]
  ```
- Brief interaction text may precede the document, but the main content should be within document markers
- Maintain consistent formatting within the document using Markdown syntax"""

business_instructions_prompt = """- For document generation:
    - When users request complete documents (reports, analyses, papers), use this specific format:
      ```document
      DOCUMENT_TYPE: [Type of document]
      TITLE: [Document title]
      
      [Complete formatted document content]
      ```
    - The system will recognize this format and display it in a dedicated canvas for editing

- For image information:
    - Recognize images prefixed with "Image:" and include using Markdown syntax
    - Example: Image: ![](https://www.example.com/image.jpg)
    - Integrate image data seamlessly into relevant parts of the response

- When information is unavailable:
    - Clearly state limitations of the knowledge base
    - Avoid fabricating answers based on general knowledge
    - Suggest how users might rephrase queries to match available information

- Always prioritize information from the knowledge base over general knowledge
- When synthesizing from multiple documents, indicate the connections between sources
- For complex topics, provide appropriate context before detailed explanations"""


class ContextualBuilder(BasePromptBuilder):
    """TTS场景的Prompt管理器"""

    # def __init__(
    #     self,
    #     business_builder: BusinessPromptBuilder,
    #     session_builder: Optional[SessionContextBuilder] = None,
    #     knowledge_builder: Optional[KnowledgeBaseBuilder] = None,
    #     knowledge_params: Optional[Dict[str, Any]] = None,
    #     **kwargs,
    # ):
    #     if knowledge_params:
    #         knowledge_builder = ContextKnowledgeBaseBuilder(**knowledge_params)
    #
    #     super().__init__(
    #         business_builder=business_builder,
    #         session_builder=session_builder,
    #         knowledge_builder=knowledge_builder,
    #     )
    #     # 设置TTS场景的默认目标
    #     self.manager.objective(objective)
    #     # 设置自定义的知识库使用规则
    #     self.manager.add_custom_component(
    #         name="KnowledgeUsageGuidelines", content=knowledge_usage_guidelines
    #     )
    #     # 设置Contextual场景的默认输出要求
    #     self.manager.add_output_requirements(
    #         "ContentStructure", output_requirements_content_sturcture
    #     ).add_output_requirements(
    #         "Format", output_requirements_format
    #     ).add_output_requirements(
    #         "Language", output_requirements_language
    #     ).add_output_requirements(
    #         "Style", output_requirements_style
    #     ).add_output_requirements(
    #         "QualityStandards", output_requirements_quality_standards
    #     ).add_output_requirements(
    #         "Limitations", output_requirements_limitations
    #     )
    #     # 添加业务prompt的补充规则
    #     self.manager.add_instructions(business_instructions_prompt)

    async def _init_builders(self):
        """初始化各种构建器"""
        self._business_builder = await self._create_business_builder()
        self._session_builder = await self._create_session_builder()
        # 特殊处理: 如果有knowledge_params, 使用ContextKnowledgeBaseBuilder
        if self._knowledge_params:
            self._knowledge_builder = ContextWithDocTreeKnowledgeBuilder(
                datasets=self._knowledge_params.datasets,
                tag_name=self._knowledge_params.tag_name,
                custom_params=self._knowledge_params.custom_params,
                **self._kwargs,
            )
        else:
            self._knowledge_builder = await self._create_knowledge_builder()

    async def _add_tool_call_strategies(self):
        pass

    async def _build_specific(self):
        """添加Contextual类型特有的配置"""
        # 设置目标
        self.manager.objective(objective)

        # 设置自定义的知识库使用规则
        self.manager.add_custom_component(
            name="KnowledgeUsageGuidelines", content=knowledge_usage_guidelines
        )

        # 设置Contextual场景的默认输出要求
        self.manager.add_output_requirements(
            "ResponseTypes",
            output_requirements_response_types,
        ).add_output_requirements(
            "ContentStructure", output_requirements_content_sturcture
        ).add_output_requirements(
            "Format", output_requirements_format
        ).add_output_requirements(
            "QualityStandards", output_requirements_quality_standards
        ).add_output_requirements(
            "DocumentOutputFormat", output_requirements_document_output_format
        )

        # 根据工具参数添加工具调用策略
        # if self.tool_params and self.tool_params.tools:
        #     await self._add_tool_call_strategies()

        # 添加业务prompt的补充规则
        self.manager.add_instructions(business_instructions_prompt)
