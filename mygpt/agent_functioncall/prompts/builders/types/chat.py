from typing import Optional

from mygpt.agent_functioncall.prompts.base.manager import PromptManager
from mygpt.agent_functioncall.prompts.builders.base_builder import BasePromptBuilder
from mygpt.agent_functioncall.prompts.builders.components.business_builder import (
    BusinessPromptBuilder,
)
from mygpt.agent_functioncall.prompts.builders.components.knowledge_builder import (
    KnowledgeBaseBuilder,
)
from mygpt.agent_functioncall.prompts.builders.components.session_builder import (
    SessionContextBuilder,
)

objective = """Generate interactive and informative responses strictly within the scope of the client's specified business context. Avoid responding to questions that fall outside the defined business context or knowledge base."""

output_requirements_format = """Use natural language and Markdown formatting where appropriate to include images and links.
Utilize special symbols as needed to format images and links correctly."""

output_requirements_language = """Dynamically determine the output language based on the most recent user input:
- If the user's input is primarily in one language, respond in that language.
- If the user's input is a mix of multiple languages, prioritize responding in the dominant language of the input.
- If the user's intent indicates a specific language preference (e.g., explicitly requesting a specific language), follow the user's indicated preference.
- Never mix languages in a single response unless explicitly requested by the user.
- For transitional phrases during tool calls, always use the same language as the main response."""

output_requirements_style = """The output should be engaging, clear, and easy to understand, maintaining a conversational tone."""

output_requirements_custom_content = """Integrate client-provided business-specific prompt content naturally into the response.
Ensure image information is incorporated effectively into the conversation."""

output_requirements_knowledge_priority = """Always prioritize using the knowledge_base content when responding to questions.
If a response requires information not available in the knowledge_base, explicitly state: "This information is based on general knowledge and may not directly align with the business context.\""""

tool_call_strategy_execution_order = """- Execute tool calls sequentially, one at a time
- Complete each tool call and process its response before initiating the next tool call
- Never attempt parallel tool calls"""

tool_call_strategy_pre_call_message = """- Use greeting only once at the beginning of conversation
- Use brief conversational transitional phrases (such as "Let me check" or "One moment") in the EXACT SAME language as the user's query
- Never mention technical terms like "RAG tool", "tool call", or any system components
- Avoid repeating self-introduction"""

tool_call_strategy_post_call_message = """- Directly integrate tool response without mentioning the tool or how information was retrieved
- Never say phrases like "Based on the information retrieved" or "According to the tool"
- Seamlessly incorporate information into the conversation as if you naturally know it
- Ensure response coherence and fluency
- Remove any redundant content from tool responses
- Maintain the same language throughout the entire response"""

tool_call_strategy_response_structure = """- Initial greeting (once only)
- Transition phrase (if needed)
- Core information content
- Interactive prompt (e.g., asking if more information is needed)"""

business_instructions_prompt = """- If the task requires the use of a tool, provide the necessary tool call and include any relevant natural language context.
- Never expose technical terms like "RAG", "tool", "API", or any system components in responses to users.
- Always maintain language consistency: if the user asks in Japanese, respond entirely in Japanese; if in English, respond entirely in English; if in Chinese, respond entirely in Chinese.
- Transitional phrases like "Let me check" should be in the same language as the rest of the response.

- For image information:
    - Recognize images by text prefixed with "Image:" and include them using Markdown syntax.
    - Example: Image: ![](https://www.example.com/image.jpg)
    - Ensure images are associated with the relevant parts of the text or sentence.
    - Do not ignore image data; integrate it seamlessly into the response.
    
- If the user query falls outside the client's specified business domain:
    - Politely inform the user that the question is not within the scope of this system's expertise.
    - Redirect the user back to the business-specific domain or encourage them to rephrase their query.
    - Example response: "This system specializes in [business-specific domain]. Could you please provide a query within this area?"
- Do not fabricate answers based on general knowledge when the query is unrelated to the business domain."""


class ChatPromptBuilder(BasePromptBuilder):
    """Chat场景的Prompt管理器"""

    # def __init__(
    #     self,
    #     business_builder: BusinessPromptBuilder,
    #     session_builder: Optional[SessionContextBuilder] = None,
    #     knowledge_builder: Optional[KnowledgeBaseBuilder] = None,
    #     **kwargs,
    # ):
    #     super().__init__(
    #         business_builder=business_builder,
    #         session_builder=session_builder,
    #         knowledge_builder=knowledge_builder,
    #     )
    #     # 设置TTS场景的默认目标
    #     self.manager.objective(objective)
    #     # 设置TTS场景的默认输出要求
    #     self.manager.add_output_requirements("Format", output_requirements_format)
    #     self.manager.add_output_requirements("Language", output_requirements_language)
    #     self.manager.add_output_requirements("Style", output_requirements_style)
    #     self.manager.add_output_requirements(
    #         "CustomContent", output_requirements_custom_content
    #     )
    #     self.manager.add_output_requirements(
    #         "KnowledgePriority", output_requirements_knowledge_priority
    #     )
    #     self.manager.add_tool_call_strategy(
    #         "ExecutionOrder", tool_call_strategy_execution_order
    #     ).add_tool_call_strategy(
    #         "PreCallMessage", tool_call_strategy_pre_call_message
    #     ).add_tool_call_strategy(
    #         "PostCallMessage", tool_call_strategy_post_call_message
    #     ).add_tool_call_strategy(
    #         "ResponseStructure", tool_call_strategy_response_structure
    #     )
    #     # 添加业务prompt的补充规则
    #     self.manager.add_instructions(business_instructions_prompt)
    async def _init_builders(self):
        """初始化各种构建器"""
        self._business_builder = await self._create_business_builder()
        self._session_builder = await self._create_session_builder()
        self._knowledge_builder = await self._create_knowledge_builder()

    async def _add_tool_call_strategies(self):
        """添加工具调用策略"""
        # 这里可能将来会有一些异步操作，比如从配置服务获取策略
        self.manager.add_tool_call_strategy(
            "ExecutionOrder", tool_call_strategy_execution_order
        )
        self.manager.add_tool_call_strategy(
            "PreCallMessage", tool_call_strategy_pre_call_message
        )
        self.manager.add_tool_call_strategy(
            "PostCallMessage", tool_call_strategy_post_call_message
        )
        self.manager.add_tool_call_strategy(
            "ResponseStructure", tool_call_strategy_response_structure
        )

    async def _build_specific(self):
        """添加Chat类型特有的配置"""
        # 设置目标
        self.manager.objective(objective)

        # 添加输出要求
        self.manager.add_output_requirements("Format", output_requirements_format)
        self.manager.add_output_requirements("Language", output_requirements_language)
        self.manager.add_output_requirements("Style", output_requirements_style)
        self.manager.add_output_requirements(
            "CustomContent", output_requirements_custom_content
        )
        self.manager.add_output_requirements(
            "KnowledgePriority", output_requirements_knowledge_priority
        )

        # 根据工具参数添加工具调用策略
        if self._tool_params and self._tool_params.tools:
            await self._add_tool_call_strategies()

        # 添加业务补充规则
        self.manager.add_instructions(business_instructions_prompt)
