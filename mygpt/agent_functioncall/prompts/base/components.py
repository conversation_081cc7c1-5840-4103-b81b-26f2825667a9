import json
from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, Union
from abc import ABC, abstractmethod


class PromptComponent(ABC):
    """Prompt组件基类"""

    @abstractmethod
    def to_xml(self) -> str:
        """将组件转换为XML格式"""
        pass


@dataclass
class Objective(PromptComponent):
    """目标描述组件"""

    content: str

    def to_xml(self) -> str:
        return f"""    <Objective>
        {self.content}
    </Objective>"""


@dataclass
class DynamicVariable(PromptComponent):
    """单个动态变量"""

    name: str
    value: str

    def to_xml(self) -> str:
        # 如果value中包含多行，需要保持缩进
        if "\n" in self.value:
            # 对多行内容进行缩进处理
            indented_value = "\n".join(
                f"            {line}" if line.strip() else line
                for line in self.value.split("\n")
            )
            return f"""        <{self.name}>
{indented_value}
        </{self.name}>"""
        else:
            # 单行内容直接处理
            return f"        <{self.name}>{self.value}</{self.name}>"


@dataclass
class DynamicVariables(PromptComponent):
    """动态变量组件"""

    variables: Dict[str, str]

    def to_xml(self) -> str:
        # 处理每个变量，确保正确的缩进
        vars_xml = []
        for name, value in self.variables.items():
            var = DynamicVariable(name, value)
            vars_xml.append(var.to_xml())
        content = "\n".join(vars_xml)
        return f"""    <DynamicVariables>
{content}
    </DynamicVariables>"""


@dataclass
class CustomComponent(PromptComponent):
    """自定义组件"""

    name: str
    content: str

    def to_xml(self) -> str:
        self.content = "\n".join(self.content.split("\n"))
        return f"""    <{self.name}>
        {self.content}
    </{self.name}>"""


@dataclass
class OutputRequirements(PromptComponent):
    """输出要求组件"""

    requirements: Dict[str, str]

    def to_xml(self) -> str:
        # 直接在这里处理每个要求的格式化
        reqs_parts = []
        for name, content in self.requirements.items():
            # 处理多行内容的缩进
            content_lines = content.split("\n")
            indented_content = "\n            ".join(line for line in content_lines)
            req_xml = (
                f"        <{name}>\n"
                f"            {indented_content}\n"
                f"        </{name}>"
            )
            reqs_parts.append(req_xml)

        return (
            f"    <OutputRequirements>\n"
            f"{chr(10).join(reqs_parts)}\n"
            f"    </OutputRequirements>"
        )


@dataclass
class ToolCallStrategy(PromptComponent):
    """工具调用策略组件"""

    strategies: Dict[str, str]

    def to_xml(self) -> str:
        # 类似OutputRequirements的处理方式
        strategy_parts = []
        for name, content in self.strategies.items():
            # 处理多行内容的缩进
            content_lines = content.split("\n")
            indented_content = "\n            ".join(line for line in content_lines)
            strategy_xml = (
                f"        <{name}>\n"
                f"            {indented_content}\n"
                f"        </{name}>"
            )
            strategy_parts.append(strategy_xml)

        return (
            f"    <ToolCallStrategy>\n"
            f"{chr(10).join(strategy_parts)}\n"
            f"    </ToolCallStrategy>"
        )


@dataclass
class KnowledgePrompt(PromptComponent):
    """知识prompt组件"""

    content: str
    tag_name: str = "KnowledgeBase"
    add_indent: bool = True

    def to_xml(self) -> str:
        # 处理content的缩进
        Knowledge_content = "\n    ".join(self.content.split("\n"))
        final_content = (
            f"<{self.tag_name}>\n" f"{Knowledge_content}\n" f"</{self.tag_name}>"
        )
        return final_content


@dataclass
class BusinessPrompt(PromptComponent):
    """业务prompt组件
    如果不给定的话，session_context默认为None
    如果给定, session_context在这里直接处理
    """

    content: str = field(default="")
    # elements 用于存储额外的元素，如session_context, instructions等, 可以自定义
    elements: Dict[str, str] = field(default_factory=dict)

    def to_xml(self) -> str:
        # 处理dynamic_background的缩进
        elements_content = ""
        if self.elements:
            ele_parts = []
            for name, ele_content in self.elements.items():
                # 处理多行内容的缩进
                content_lines = ele_content.split("\n")
                indented_content = "\n    ".join(line for line in content_lines)
                ele_xml = f"<{name}>\n" f"    {indented_content}\n" f"</{name}>\n"
                ele_parts.append(ele_xml)
            elements_content = "\n".join(ele_parts)
        return f"""<BusinessPrompt>
{self.content}\n{elements_content}
</BusinessPrompt>"""
