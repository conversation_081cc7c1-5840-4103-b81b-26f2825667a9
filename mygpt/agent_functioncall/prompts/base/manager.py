from typing import Dict, Optional
from .components import *


class PromptManager:
    """Prompt管理器"""

    def __init__(self):
        self._objective: Optional[Objective] = None
        self._dynamic_vars: Optional[DynamicVariables] = None
        self._output_reqs: Optional[OutputRequirements] = None
        self._tool_call_strategy: Optional[ToolCallStrategy] = None
        self._knowledge_base: Optional[KnowledgePrompt] = None
        self._business_prompt: Optional[BusinessPrompt] = None
        self._custom_components: List[CustomComponent] = []

    def objective(self, content: str) -> "PromptManager":
        """设置目标"""
        self._objective = Objective(content)
        return self

    def add_dynamic_variable(self, name: str, value: str) -> "PromptManager":
        """添加动态变量"""
        if not self._dynamic_vars:
            self._dynamic_vars = DynamicVariables({})
        self._dynamic_vars.variables[name] = value
        return self

    def add_output_requirements(self, name: str, *contents: str) -> "PromptManager":
        """添加输出要求
        Args:
            name: 要求的名称
            *contents: 一个或多个内容行

        Example:
            manager.add_output_requirement(
                "Format",
                "Use natural language without Markdown formatting",
                "Avoid using special symbols",
                "Keep the text clean and readable"
            )

        Returns:
            self for method chaining
        """
        if not self._output_reqs:
            self._output_reqs = OutputRequirements({})
        # 如果已存在该名称的要求，将新内容添加到现有内容后面
        if name in self._output_reqs.requirements:
            existing_content = self._output_reqs.requirements[name]
            formatted_content = "\n".join(contents)
            self._output_reqs.requirements[name] = (
                f"{existing_content}\n            {formatted_content}"
            )
        else:
            # 如果是新的要求，直接添加
            formatted_content = "\n".join(contents)
            self._output_reqs.requirements[name] = formatted_content
        return self

    def add_knowledge_base(
        self, content: str, tag_name="KnowledgeBase"
    ) -> "PromptManager":
        """添加知识信息"""
        self._knowledge_base = KnowledgePrompt(content=content, tag_name=tag_name)
        return self

    def add_session_context(self, name, content) -> "PromptManager":
        """在Business块中, 添加会话上下文信息"""
        if not self._business_prompt:
            self._business_prompt = BusinessPrompt("")
        self._business_prompt.elements[name] = content
        return self

    def add_instructions(self, content: str) -> "PromptManager":
        """在Business块中, 添加指令引导信息"""
        if not self._business_prompt:
            self._business_prompt = BusinessPrompt("")
        self._business_prompt.elements["Instructions"] = content
        return self

    def add_business(self, content: str) -> "PromptManager":
        """设置业务prompt
        Args:
            content: 业务prompt的主要内容
        """
        if not self._business_prompt:
            self._business_prompt = BusinessPrompt(content)
        else:
            self._business_prompt.content = content
        return self

    def add_tool_call_strategy(self, name: str, *contents: str) -> "PromptManager":
        """添加工具调用策略

        Args:
            name: 策略名称
            *contents: 一个或多个内容行

        Example:
            manager.add_tool_call_strategy(
                "ExecutionOrder",
                "Execute tool calls sequentially, one at a time",
                "Complete each tool call and process its response before initiating the next"
            )
        """
        if not self._tool_call_strategy:
            self._tool_call_strategy = ToolCallStrategy({})

        # 将多行内容合并
        self._tool_call_strategy.strategies[name] = "\n".join(contents)
        return self

    def add_custom_component(self, name: str, content: str) -> "PromptManager":
        """添加自定义组件"""
        self._custom_components.append(CustomComponent(name, content))
        return self

    def build(self) -> str:
        """构建最终的prompt"""
        components = []

        # 构建MetaPrompt部分
        meta_components = []
        if self._objective:
            meta_components.append(self._objective.to_xml())
        if self._dynamic_vars:
            meta_components.append(self._dynamic_vars.to_xml())
        if self._custom_components:
            meta_components.extend(comp.to_xml() for comp in self._custom_components)
        if self._output_reqs:
            meta_components.append(self._output_reqs.to_xml())
        if self._tool_call_strategy:
            meta_components.append(self._tool_call_strategy.to_xml())

        meta_prompt = "<MetaPrompt>\n{}\n</MetaPrompt>".format(
            "\n".join(meta_components)
        )
        components.append(meta_prompt)
        # 添加KnowledgePrompt部分
        if self._knowledge_base:
            components.append(self._knowledge_base.to_xml())
        # 添加BusinessPrompt部分
        if self._business_prompt:
            components.append(self._business_prompt.to_xml())

        return "\n".join(components)
