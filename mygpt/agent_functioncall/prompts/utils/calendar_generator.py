from datetime import datetime, timedelta
from calendar import monthcalendar
from typing import List, Dict
from lunar_python import Lunar, Solar


class CalendarContextGenerator:
    def __init__(self, current_date: datetime = None, indent_num: int = 0):
        self.current_date = current_date or datetime.now()
        self.indent = "" if indent_num == 0 else " " * 4 * indent_num

    def _format_date(self, date: datetime) -> str:
        return date.strftime("%d")

    def _is_leap_year(self, year: int) -> bool:
        year = year or self.current_date.year
        return year % 4 == 0 and (year % 100 != 0 or year % 400 == 0)

    def _get_year_info(self) -> str:
        year = self.current_date.year
        is_leap = self._is_leap_year(year)
        days_in_year = 366 if is_leap else 365
        current_day = self.current_date.timetuple().tm_yday
        remaining_days = days_in_year - current_day

        return (
            f"{self.indent}Year Info: {year} "
            f"({'Leap' if is_leap else 'Common'} Year, "
            f"Day {current_day}/{days_in_year}, "
            f"{remaining_days} days remaining)"
        )

    def _get_month_name(self, date: datetime) -> str:
        return date.strftime("%b")

    def _get_week_dates(self, start_date: datetime) -> List[str]:
        dates = []
        for i in range(7):
            day = start_date + timedelta(days=i)
            dates.append(self._format_date(day))
        return dates

    def _get_month_reference(self) -> str:
        last_month = self.current_date - timedelta(days=self.current_date.day)
        next_month = (self.current_date.replace(day=28) + timedelta(days=4)).replace(
            day=1
        )

        months = [last_month, self.current_date, next_month]
        month_ref = []

        for month in months:
            weeks = monthcalendar(month.year, month.month)
            week_str = []
            for i, week in enumerate(weeks, 1):
                start = min(d for d in week if d != 0)
                end = max(d for d in week if d != 0)
                week_str.append(f"W{i}({start}-{end})")

            month_line = f"{self.indent}{month.strftime('%B')} ({month.strftime('%b')}): {', '.join(week_str)}"
            month_ref.append(month_line)

        return "\n".join(month_ref)

    def _get_weekly_view(self) -> str:
        # 获取前2周和后4周的信息
        weeks = []

        # 添加表头信息
        week_days = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
        week_header = (
            f"{self.indent}{'Week':<5} | {' '.join(f'{day:>3}' for day in week_days)} |"
        )
        weeks.append(week_header)

        for week_offset in range(-4, 5):
            week_start = self.current_date + timedelta(weeks=week_offset)
            week_monday = week_start - timedelta(days=week_start.weekday())
            dates = self._get_week_dates(week_monday)

            # 标记当前日期
            if week_offset == 0:
                current_day_idx = self.current_date.weekday()
                dates[current_day_idx] = (
                    f"[{self.current_date.strftime('%d')}]"  # 使用实际日期
                )

            # 确定周参考信息
            if week_offset < 0:
                ref = f"{self._get_month_name(week_start)}(W{week_start.isocalendar()[1]})"
            elif week_offset == 0:
                ref = f"{self._get_month_name(week_start)}(W{week_start.isocalendar()[1]})"
            else:
                ref = f"{self._get_month_name(week_start)}(W{week_start.isocalendar()[1]})"

            # 使用固定宽度格式化日期
            formatted_dates = [f"{date:>3}" for date in dates]

            # week_label = f"{'-2w' if week_offset == -2 else '-1w' if week_offset == -1 else 'Now' if week_offset == 0 else f'+{week_offset}w'}"
            week_label = (
                f"{week_offset}w"
                if week_offset < 0
                else f"+{week_offset}w" if week_offset > 0 else "Now"
            )
            week_str = (
                f"{self.indent}{week_label:<5} | {' '.join(formatted_dates)} | {ref}"
            )
            weeks.append(week_str)

        return "\n".join(weeks)

    def _get_key_dates(self) -> str:
        """获取关键信息"""

        def _calculate_qingming(year: int):
            """计算清明节具体日期（返回4月几号）"""
            # 获取年份后两位
            y = year % 100
            # 确定世纪系数
            c = 4.81 if year >= 2000 else 5.59

            # 计算从2000年开始到目标年份间的闰年数
            def count_leap_years(year):
                if year >= 2000:
                    start = 2000
                    end = year
                else:
                    start = year
                    end = 2000
                count = 0
                for y in range(start, end):
                    if (y % 4 == 0 and y % 100 != 0) or (y % 400 == 0):
                        count += 1
                return count

            l = count_leap_years(year)
            # 计算清明节日期
            date = y * 0.2422 + c - l
            # 结果取整，得到的是4月几号
            return round(date)

        def _get_lunar_holidays(year: int) -> Dict[str, str]:
            current_year = self.current_date.year
            lunar_holidays = {}
            # 农历新年
            spring_festival = Lunar.fromYmd(year, 1, 1).getSolar()
            lunar_holidays[
                f"{year}/{spring_festival.getMonth()}/{spring_festival.getDay()}"
            ] = "CNY"
            # 除夕
            lunar_new_year_eve = spring_festival.nextDay(-1)
            lunar_holidays[
                f"{year}/{lunar_new_year_eve.getMonth()}/{lunar_new_year_eve.getDay()}"
            ] = "CNY-Eve"
            # 元宵节
            lantern_festival = Lunar.fromYmd(year, 1, 15).getSolar()
            lunar_holidays[
                f"{year}/{lantern_festival.getMonth()}/{lantern_festival.getDay()}"
            ] = "Lantern"
            # 清明节
            qingming_day = _calculate_qingming(year)
            # qingming_festival = Lunar.fromYmd(year, 4, 4).getSolar()
            lunar_holidays[f"{year}/4/{qingming_day}"] = "Qingming"
            # 端午节
            dragon_boat_festival = Lunar.fromYmd(year, 5, 5).getSolar()
            lunar_holidays[
                f"{year}/{dragon_boat_festival.getMonth()}/{dragon_boat_festival.getDay()}"
            ] = "Dragon-Boat"
            # 七夕
            qixi_festival = Lunar.fromYmd(year, 7, 7).getSolar()
            lunar_holidays[
                f"{year}/{qixi_festival.getMonth()}/{qixi_festival.getDay()}"
            ] = "Qixi"
            # 中秋节
            mid_autumn_festival = Lunar.fromYmd(year, 8, 15).getSolar()
            lunar_holidays[
                f"{year}/{mid_autumn_festival.getMonth()}/{mid_autumn_festival.getDay()}"
            ] = "Mid-Autumn"
            # 重阳节
            double_ninth_festival = Lunar.fromYmd(year, 9, 9).getSolar()
            lunar_holidays[
                f"{year}/{double_ninth_festival.getMonth()}/{double_ninth_festival.getDay()}"
            ] = "Double-Ninth"
            return lunar_holidays

        def get_sort_key(holiday_str: str) -> int:
            """从节日字符串中提取月和日用于排序
            输入格式: "CNY(1/10)" 或 "NewYear(1/1)"
            """
            date_part = holiday_str.split("(")[1].rstrip(")")
            month, day = map(int, date_part.split("/"))
            return month * 100 + day  # 例如: 1月10日 => 110

        current_year = self.current_date.year
        # 获取农历节日
        lunar_holidays = _get_lunar_holidays(current_year)
        # 如果当前日期在下半年, 还需要获取下一年的农历节日
        if self.current_date.month > 6:
            next_year_lunar = _get_lunar_holidays(current_year + 1)
            lunar_holidays.update(next_year_lunar)
        # 如果当前日期在上半年, 还需要获取上一年的农历节日
        elif self.current_date.month < 6:
            last_year_lunar = _get_lunar_holidays(current_year - 1)
            lunar_holidays.update(last_year_lunar)

        # 固定节日
        solar_holidays = {
            "1/1": "NewYear",
            "2/14": "Valentine",
            "5/1": "Labor-Day",
            "10/1": "National-Day",
            "12/25": "Christmas",
        }
        # 分类节日
        recent = []
        coming = []
        # 处理农历节日 (带年份的)
        for date, name in lunar_holidays.items():
            year, month, day = map(int, date.split("/"))
            holiday_date = datetime(year, month, day)
            # 计算与当前日期的差值
            days_diff = (holiday_date - self.current_date).days
            # 获取180天内的归为recent, 未来60天的归为coming
            if -180 <= days_diff < 0:
                recent.append(f"{name}({month}/{day})")  # 输出时仍只显示月/日
            elif 0 < days_diff <= 185:
                coming.append(f"{name}({month}/{day})")
        # 处理固定节日（当前年份）
        for date, name in solar_holidays.items():
            month, day = map(int, date.split("/"))
            holiday_date = datetime(current_year, month, day)
            days_diff = (holiday_date - self.current_date).days
            if -180 <= days_diff < 0:
                recent.append(f"{name}({date})")
            elif 0 < days_diff <= 185:
                coming.append(f"{name}({date})")
        # 对recent和coming进行排序
        recent.sort(key=get_sort_key, reverse=True)
        coming.sort(key=get_sort_key)
        # 格式化输出
        recent_str = f"{self.indent}Recent: {', '.join(recent) if recent else 'No recent holidays'}"
        coming_str = f"{self.indent}Coming: {', '.join(coming) if coming else 'No upcoming holidays'}"
        return f"{recent_str}\n{coming_str}"

    def generate_calendar_context(self, use_key_dates=False) -> str:
        past_30d = self.current_date - timedelta(days=30)
        next_60d = self.current_date + timedelta(days=60)

        context = f"""{self.indent}# Year Reference
{self._get_year_info()}

{self.indent}# Month Reference
{self._get_month_reference()}

{self.indent}# Weekly View (±4 weeks)
{self._get_weekly_view()}

{self.indent}# Quick Reference
{self.indent}Current: [{self.current_date.strftime('%b %d, %a')}] Week {self.current_date.isocalendar()[1]} of {self.current_date.strftime('%B')}
{self.indent}Past 30d: {past_30d.strftime('%b %d')} - {(self.current_date - timedelta(days=1)).strftime('%b %d')}
{self.indent}Next 60d: {(self.current_date + timedelta(days=1)).strftime('%b %d')} - {next_60d.strftime('%b %d')}"""
        if use_key_dates:
            context += f"""
{self.indent}# Key Dates
{self._get_key_dates()}"""
        context += f"""
"""

        return context
