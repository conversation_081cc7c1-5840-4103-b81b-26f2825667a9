from dataclasses import dataclass, field
from enum import Enum
from typing import List, Optional, Dict, Union
from datetime import datetime


class DocumentType(str, Enum):
    """文档类型枚举"""

    ARTICLE = "Article"
    LEARNING = "Learning"
    REPORT = "Report"
    RESEARCH = "Research"
    GUIDE = "Guide"
    NEWS = "News"
    # 可以根据需要添加更多类型

    # 将参数传入模型转换
    @classmethod
    def to_type(cls, type_: str):
        if type_ == "Article":
            return cls.ARTICLE
        elif type_ == "Learning":
            return cls.LEARNING
        elif type_ == "Report":
            return cls.REPORT
        elif type_ == "Research":
            return cls.RESEARCH
        elif type_ == "Guide":
            return cls.GUIDE
        elif type_ == "News":
            return cls.NEWS
        else:
            return cls.ARTICLE


class SourceType(str, Enum):
    """数据来源类型"""
    GENERAL = "general"  # 通用
    DEEPRESEARCH = "deepresearch"  # 深度研究
    PROJECT = "project"  # 项目
    GBASE = "gbase"  # GBase
    ARTICLE = "article"  # 文章


@dataclass
class ImageInfo:
    """图片信息"""

    url: str  # 图片URL或路径
    caption: Optional[str]  # 图片描述


@dataclass
class SummaryInfo:
    """摘要信息"""
    summary: str = ""
    topics: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    missing_key_aspects: List[str] = field(default_factory=list)
    original_content_token_count: int = 0
    summary_token_count: int = 0
    information_coverage: float = 0.0


@dataclass
class DocumentInfo:
    """文档信息"""

    title: str  # 文档标题
    doc_type: DocumentType  # 文档类型
    doc_id: Optional[str] = None  # 文档ID
    content: Optional[str] = None  # 文档内容
    author: Optional[str] = None  # 作者
    published_date: Optional[datetime] = None  # 发布日期
    source: Optional[str] = None  # 来源
    images: List[ImageInfo] = None  # 相关图片
    summary_info: Optional[SummaryInfo] = field(default_factory=SummaryInfo)  # 摘要信息
    metadata: Dict = None  # 其他元数据

    # 新增路径相关字段
    category_path: List[str] = None  # 分类路径，如["技术文档", "AI", "LLM"]
    doc_tree_path: Optional[Union[str, List[str]]] = None  # 文档在系统中的路径
    parent_id: Optional[str] = None  # 父文档ID（用于嵌套文档）

    def __post_init__(self):
        """初始化后的处理"""
        if self.images is None:
            self.images = []
        if self.metadata is None:
            self.metadata = {}
        if self.category_path is None:
            self.category_path = []


@dataclass
class DirectoryInfo:
    """文档信息"""

    title: str  # 目录名称
    dir_id: Optional[str] = ""  # 目录ID
    directory_description: Optional[str] = ""  # 目录描述

    dir_tags: List[str] = None  # 目录标签
    dir_topics: List[str] = None  # 目录主题
    dir_path_str: str = None  # 目录路径 如: /技术文档/AI/LLM
    directory_ids: List[str] = None  # 目录ID列表
    doc_path: Optional[Union[str, List[str]]] = None  # 文档在系统中的路径

    metadata: Dict = None  # 其他元数据

    def __post_init__(self):
        """初始化后的处理"""
        if self.metadata is None:
            self.metadata = {}


@dataclass
class DatasetInfo:
    """数据集信息

    这个类包含了数据集的基本信息和元数据，可用于RAG和直接上下文两种场景。
    不同的知识库构建器可以选择性地使用其中的字段。
    """

    name: str  # 数据集名称
    description: Optional[str] = None  # 数据集描述
    source_type: SourceType = SourceType.PROJECT  # 数据来源
    documents: List[DocumentInfo] = None  # 文档列表
    directories: List[DirectoryInfo] = None  # 目录列表
    created_at: datetime = None  # 创建时间
    updated_at: datetime = None  # 更新时间
    tags: List[str] = None  # 标签
    version: Optional[str] = None  # 版本
    owner: Optional[str] = None  # 所有者
    is_public: bool = False  # 是否公开
    metadata: Dict = None  # 其他元数据

    def __post_init__(self):
        """初始化后的处理"""
        if self.documents is None:
            self.documents = []
        if self.tags is None:
            self.tags = []
        if self.metadata is None:
            self.metadata = {}
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = self.created_at


@dataclass
class BuildDocumentIn:
    """构建文档输入参数"""
    title: str
    doc_id: str = ""
    doc_path: List = field(default_factory=list)  # 文档路径 ["技术文档", "AI", "LLM"]
    is_directory: bool = False
    doc_path_str: str = ""  # 文档路径字符串 "技术文档/AI/LLM"
    directory_ids: List[str] = field(default_factory=list)  # 目录ID列表
    summary: Optional[str] = ""
    content: str = ""
    language: str = ""
    topics: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    information_coverage: float = 0.0
    missing_key_aspects: List[str] = field(default_factory=list)
    content_token_count: int = 0
    summary_token_count: int = 0
    metadata: Dict = field(default_factory=dict)


@dataclass
class BuildDocTreeIn:
    """构建文档树的输入参数"""
    title: str
    doc_id: str = ""
    path_parts: List = field(default_factory=list)  # 文档路径 ["技术文档", "AI", "LLM"]
    is_directory: bool = False
    path_str: str = ""  # 文档路径字符串 "技术文档/AI/LLM"
    directory_ids: List[str] = field(default_factory=list)  # 目录ID列表
    dir_id: str = ""
    current_directory_id: str = ""
    directory_description: Optional[str] = ""
    summary: Optional[str] = ""
    content: str = ""
    language: str = ""
    depth_level: int = 0
    topics: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    information_coverage: float = 0.0
    missing_key_aspects: List[str] = field(default_factory=list)
    content_token_count: int = 0
    summary_token_count: int = 0
    metadata: Dict = field(default_factory=dict)


@dataclass
class BuildDatasetIn:
    """构建数据集输入参数"""
    name: str  # 数据集名称
    source_type: SourceType = SourceType.PROJECT  # 数据来源
    dataset_id: Optional[str] = ""  # 数据集ID
    description: Optional[str] = ""  # 数据集描述
    documents: List[Union[BuildDocumentIn]] = field(default_factory=list)  # 文档列表
    doc_tree_in_list: List[Union[BuildDocTreeIn]] = field(default_factory=list)  # 文档树列表





