import uuid
import aiohttp
from aiohttp import ClientSession, ClientTimeout, ClientConnectorError
from typing import Union, List, Dict, AsyncIterable, Optional, Tuple
from loguru import logger as logging

from langchain.adapters.openai import convert_openai_messages
from langchain.schema import BaseMessage

from mygpt.agent_functioncall.types import ToolInfo
from mygpt.enums import FunctionCallStyle


async def execute_request(
    url: str, method: str, data: str = None, params: dict = None, headers: dict = None
) -> dict:
    """网络请求工具"""
    timeout = ClientTimeout(total=20)
    try:
        async with ClientSession(
            timeout=timeout, connector=aiohttp.TCPConnector(limit=64, verify_ssl=False)
        ) as session:
            method = method.lower()
            req_func = getattr(session, method, None)
            if req_func is None:
                raise None
            logging.info(f"【function_call】 send url: {url}")
            async with req_func(
                url, data=data, params=params, headers=headers
            ) as response:
                return await response.json()
    except ClientConnectorError as e:
        raise ValueError(f"client connector error:{e}")
    except Exception as e:
        raise ValueError(f"An error occurred during request: {e}")


def format_info_2_tool(tool_info: ToolInfo, style: FunctionCallStyle) -> Dict:
    """将工具标准类转换为dict格式, 方面大模型的调用
    说明: 这里的tool_info是一个类, 里面包含了工具的信息, 包括name, description, parameters等
    Args:
        tool_info: ToolInfo, 工具的信息
        style: FunctionCallStyle, 调用风格
    Returns:
        根据不同的风格返回不同的格式, 因为不同的大模型调用tool call的格式不同
    """
    name = tool_info.name
    description = tool_info.description
    properties = tool_info.properties
    required = tool_info.required
    """将infos中的信息转换为tools的格式"""
    if style == FunctionCallStyle.OPENAI_STYLE:
        tool = {
            "type": "function",
            "function": {
                "name": name,
                "description": description,
                # "parameters": {
                #     "type": "object",
                #     "properties": properties,
                #     "required": required,
                # },
            },
        }
        if properties:
            tool["function"]["parameters"] = {
                "type": "object",
                "properties": properties,
                "required": required,
            }
        else:
            tool["function"]["parameters"] = {
                "type": "object",
                "properties": {},
                "required": [],
            }
    elif style == FunctionCallStyle.OPENAI_STYLE_OLD:
        tool = {
            "name": name,
            "description": description,
            "parameters": {
                "type": "object",
                "properties": properties,
                "required": required,
            },
        }
    elif style == FunctionCallStyle.CLAUDE_STYLE:
        tool = {
            "name": name,
            "description": description,
            "input_schema": {
                "type": "object",
                "properties": properties,
                "required": required,
            },
        }
    else:
        raise ValueError(f"[format_info_4_tools] unsupported style: {style}")
    return tool


async def parse_tool_call_stream_result(
    first_element, async_iter: AsyncIterable, FunctionCallStyle
) -> Dict:
    """解析工具调用的异步流结果, 将异步流的结果转换为工具调用的格式"""
    if not first_element or not hasattr(first_element, "message"):
        raise ValueError("Invalid first_element")

    if not hasattr(first_element.message, "additional_kwargs"):
        raise ValueError("Missing additional_kwargs in message")

    additional_kwargs = first_element.message.additional_kwargs
    if not additional_kwargs:
        raise ValueError("Empty additional_kwargs")

    tool_calls_final = []
    if FunctionCallStyle == FunctionCallStyle.OPENAI_STYLE:
        tool_calls = additional_kwargs.get("tool_calls")
        if not tool_calls:
            raise ValueError(
                "[agent functionall - using_util] tool_calls is None, please check the additional_kwargs"
            )
        tool_calls_final.append(tool_calls[0])
        cur_index = 0
        # function_call = additional_kwargs.get("function_call")
        arguments_chunk_perpare_list = []
        async for chunk in async_iter:
            message = chunk.message
            if not message.additional_kwargs:
                logging.info(
                    f"【parse_tool_call_stream_result】generation_info: {chunk.generation_info}"
                )
            else:
                for ix, tool_call_item in enumerate(
                    message.additional_kwargs["tool_calls"]
                ):
                    try:
                        index = message.additional_kwargs["tool_calls"][ix]["index"]
                        if index != cur_index:
                            tool_calls_final[cur_index]["function"]["arguments"] = (
                                "".join(arguments_chunk_perpare_list)
                            )
                            arguments_chunk_perpare_list = []
                            cur_index = index
                            tool_calls_final.append(tool_call_item)
                            continue
                        arguments_chunk = message.additional_kwargs["tool_calls"][ix][
                            "function"
                        ]["arguments"]
                        arguments_chunk_perpare_list.append(arguments_chunk)
                    except Exception as e:
                        logging.error(f"【parse_tool_call_stream_result】error: {e}")
        tool_calls_final[-1]["function"]["arguments"] = "".join(
            arguments_chunk_perpare_list
        )
        # 容错操作, 如果tool_calls中的元素中有index属性, 则移除. 如果function的arguments为空, 则给定'{}' 作为默认值
        for item in tool_calls_final:
            if "index" in item:
                del item["index"]
            if not item["function"]["arguments"]:
                item["function"]["arguments"] = "{}"
        additional_kwargs["tool_calls"] = tool_calls_final
    elif FunctionCallStyle == FunctionCallStyle.CLAUDE_STYLE:
        pass
    elif FunctionCallStyle == FunctionCallStyle.OPENAI_STYLE_OLD:
        arguments_chunks = []
        additional_kwargs = {
            "function_call": {
                "arguments": "",
                "name": first_element.additional_kwargs["function_call"]["name"],
            }
        }
        # 迭代出全部结果
        async for item in async_iter:
            if hasattr(item, "generation_info"):
                print(item.generation_info)
            elif item.additional_kwargs:
                arguments_chunks.append(
                    item.additional_kwargs["function_call"]["arguments"]
                )
            else:
                logging.info(f"【parse_tool_call_stream_result】item: {item}")
        additional_kwargs["function_call"]["arguments"] = "".join(arguments_chunks)
    return additional_kwargs


# def format_meta_functions_info_2_tools(function_descriptions: List[Dict], style=FunctionCallStyle.OPENAI_STYLE):
#     """将meta_functions中的信息转换为tools的格式"""
#     tools = []
#     for item in function_descriptions:
#         name = item.get("name")
#         description = item.get("description")
#         parameters = item.get("parameters")
#         properties = parameters.get("properties")
#         required = parameters.get("required")
#         format_info_2_tool(name, description, properties, required, style=style)
#     return tools


async def prepare_messages_call_llm_with_tool_result(
    messages: Union[List[Dict]],
    additional_kwargs: List[Dict],
    tool_results: Union[List[str], List[Dict]],
):
    """function call 的中间过程调用, 需要提供tool的返回结果
    Args:
        messages: List[Dict] or List[BaseMessage], 传入的消息(历史消息)
        additional_kwargs: dict, 调用tool call的参数信息, 格式示例:
            [
                {
                    "function_call": {
                        "arguments": "这里是大模型返回的参数字符串",
                        "name": "function_name"  # 这里的名字也是大模型返回的名字
                    }
                }
            ]
        tool_result: [str或dict], tool的返回结果

    Returns:
        参考格式:
            {
                "role": "system",
                "content": "你是一个数学家"
            }
            {
                "role": "user",
                "content": "Tell me the sum of 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."
            }
            {
                "content": null,
                "refusal": null,
                "role": "assistant",
                "function_call": null,
                "tool_calls": [
                    {
                        "id": "call_aAIfOBKZiohdqEK1T1S5utvg",
                        "function": {
                            "arguments": "{\"numbers\":[1,2,3,4,5,6,7,8,9,10]}",
                            "name": "sum"
                        },
                        "type": "function"
                    }
                ]
            }
            {
                "tool_call_id": "call_aAIfOBKZiohdqEK1T1S5utvg",
                "role": "tool",
                "name": "sum",
                "content": "55"
            }
            {
                "content": "The sum of the numbers 1 through 10 is 55.",
                "refusal": null,
                "role": "assistant",
                "function_call": null,
                "tool_calls": null
            }
    """
    # todo - 这里需要debug来确认代码具体的实现细节.
    tool_calls = []
    tool_res_list = []
    for item, tool_result in zip(additional_kwargs, tool_results):
        call_id = str(uuid.uuid4())
        tool_call = {
            "id": call_id,
            "function": item.get("function_call"),
            "type": "function",
        }
        tool_calls.append(tool_call)
        tool_res_list.append(
            {
                "tool_call_id": call_id,
                "role": "tool",
                "name": item.get("function_call").get("name"),
                "content": str(tool_result),
            }
        )
    tool_message = {
        "content": None,
        "refusal": None,
        "role": "assistant",
        "function_call": None,
        "tool_calls": tool_calls,
    }
    messages.append(tool_message)
    messages.extend(tool_res_list)
    # langchain_messages = convert_openai_messages(messages)
    return messages


async def collect_and_process_tool_calls(
    chunk, current_stream, call_style=FunctionCallStyle.OPENAI_STYLE_OLD
) -> Tuple[Dict, List[BaseMessage]]:
    """收集并处理工具调用信息
    Args:
        chunk: 当前chunk
        current_stream: 当前流
        call_style: 调用风格
    Returns:
        Tuple[Dict, List[BaseMessage]]: (处理后的additional_kwargs, 缓存的消息)
    """
    buffer_messages = []
    additional_kwargs = chunk.message.additional_kwargs

    try:
        if call_style == FunctionCallStyle.OPENAI_STYLE_OLD:
            # 旧版本格式
            arguments_chunks = [additional_kwargs["function_call"]["arguments"]]
            try:
                while True:
                    next_chunk = await anext(current_stream)
                    if (
                        hasattr(next_chunk, "message")
                        and next_chunk.message.additional_kwargs
                    ):
                        arguments_chunks.append(
                            next_chunk.message.additional_kwargs["function_call"][
                                "arguments"
                            ]
                        )
                    else:
                        if next_chunk:
                            buffer_messages.append(next_chunk)
                        break
            except StopAsyncIteration:
                pass
            additional_kwargs["function_call"]["arguments"] = "".join(arguments_chunks)
        elif call_style == FunctionCallStyle.CLAUDE_STYLE:
            arguments_chunk = additional_kwargs["tool_calls"][0]["function"][
                "arguments"
            ]
            if arguments_chunk is None:
                arguments_chunk = ""
            arguments_chunks = [arguments_chunk]
            try:
                while True:
                    next_chunk = await anext(current_stream)
                    if (
                        hasattr(next_chunk, "message")
                        and next_chunk.message.additional_kwargs
                    ):
                        arguments_chunk = next_chunk.message.additional_kwargs[
                            "tool_calls"
                        ][0]["function"]["arguments"]
                        if arguments_chunk is None:
                            arguments_chunk = ""
                        arguments_chunks.append(arguments_chunk)
                    else:
                        if next_chunk:
                            buffer_messages.append(next_chunk)
                        break
            except StopAsyncIteration:
                pass
        else:
            # 新版本格式
            arguments = additional_kwargs["tool_calls"][0]["function"]["arguments"]
            if arguments is None:
                arguments = ""
                additional_kwargs["tool_calls"][0]["function"]["arguments"] = arguments
            arguments_chunks = [
                additional_kwargs["tool_calls"][0]["function"]["arguments"]
            ]
            try:
                while True:
                    next_chunk = await anext(current_stream)
                    if (
                        hasattr(next_chunk, "message")
                        and next_chunk.message.additional_kwargs
                    ):
                        arguments_chunk = next_chunk.message.additional_kwargs[
                            "tool_calls"
                        ][0]["function"]["arguments"]
                        if arguments_chunk is not None:
                            arguments_chunks.append(arguments_chunk)
                    else:
                        if next_chunk:
                            buffer_messages.append(next_chunk)
                        break
            except StopAsyncIteration:
                pass
            try:
                additional_kwargs["tool_calls"][0]["function"]["arguments"] = "".join(
                    arguments_chunks
                )
            except Exception as e:
                print(e)

        return additional_kwargs, buffer_messages

    except Exception as e:
        logging.error(f"Error collecting tool calls: {e}")
        raise
