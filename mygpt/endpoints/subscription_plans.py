import logging
from datetime import datetime, timedelta, timezone
from typing import Optional
from uuid import UUID

from dateutil.relativedelta import relativedelta
from fastapi import APIRouter, HTTPException
from fastapi.params import Depends
from fastapi import Query, Path, Body
from starlette.status import HTTP_204_NO_CONTENT

from mygpt.auth0.init import auth
from mygpt.authorization import verify_super_admin_access
from mygpt.models import (
    Plan,
    SubscriptionStatus,
    PlanSubscription,
    DurationUnit,
    PlanType,
    User,
)
from mygpt.parameters import ListAPIParams, tortoise_paginate, Page
from mygpt.schemata import (
    PlanIn,
    PlanOutBase,
    PlanSubscriptionIn,
    PlanSubscriptionDetailOut,
)

router = APIRouter(prefix="/plans", tags=["Plans"])


# Helper function to calculate end time based on duration
def calculate_end_time(start_time: datetime, plan: Plan) -> datetime:
    """This function is kept for backward compatibility with existing code.
    It will be deprecated once all code is updated to use the new duration fields."""
    if start_time.tzinfo is None:
        start_time = start_time.replace(tzinfo=timezone.utc)

    # Default to 1 month if no duration is specified
    return start_time + relativedelta(months=1)


@router.get(
    "",
    response_model=Page[PlanOutBase],
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_super_admin_access),
    ],
)
async def get_plans(
    params: ListAPIParams = Depends(),
):
    """
    Get a paginated list of active subscription plans.

    - **limit**: number of items per page
    - **offset**: pagination offset

    Returns a Page object containing PlanOutBase items.
    """
    plans_qs = Plan.filter(deleted_at__isnull=True)
    pages = await tortoise_paginate(plans_qs, params)
    return pages


@router.post(
    "",
    response_model=PlanOutBase,
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_super_admin_access),
    ],
)
async def create_plan(
    plan_in: PlanIn = Body(..., description="Subscription plan creation payload"),
):
    """
    Create a new subscription plan.

    - **plan_in**: PlanIn object containing plan details.

    Returns PlanOutBase of the created plan.
    Throws 400 if plan type is TRIAL or creation fails.
    """
    if plan_in.plan_type == PlanType.TRIAL:
        raise HTTPException(
            status_code=400, detail="Cannot create TRIAL plans via API."
        )

    try:
        plan = await Plan.create(**plan_in.dict())
        return await PlanOutBase.from_tortoise_orm(plan)
    except Exception as e:
        logging.error(f"Error creating plan: {e}")
        raise HTTPException(status_code=400, detail=f"Failed to create plan: {e}")


@router.post(
    "/subscription",
    response_model=PlanSubscriptionDetailOut,
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_super_admin_access)],
)
async def create_plan_subscription(
    subscription_in: PlanSubscriptionIn = Body(
        ..., description="User subscription creation payload"
    ),
):
    """
    Create a subscription for a user on a specified plan.

    - **subscription_in**: PlanSubscriptionIn, includes user_id and plan_id.

    Cancels any existing active subscriptions for the user before creating a new one.

    Returns PlanSubscriptionDetailOut of the new subscription.
    Throws 404 for missing user/plan, 500 for internal errors, 400 for creation failures.
    """
    # First, verify the user exists
    user = await User.get_or_none(id=subscription_in.user_id, deleted_at__isnull=True)
    if not user:
        logging.error(f"User with ID {subscription_in.user_id} not found")
        raise HTTPException(
            status_code=404, detail=f"User with ID {subscription_in.user_id} not found."
        )

    # Then verify the plan exists
    plan = await Plan.get_or_none(id=subscription_in.plan_id, deleted_at__isnull=True)
    if not plan:
        logging.error(f"Plan with ID {subscription_in.plan_id} not found")
        raise HTTPException(
            status_code=404, detail=f"Plan with ID {subscription_in.plan_id} not found."
        )

    # Use provided start_at or default to now
    start_at = subscription_in.start_at or datetime.now(timezone.utc)

    # Calculate expiration date based on provided duration or plan's duration
    try:
        if hasattr(subscription_in, "duration_unit") and hasattr(
            subscription_in, "duration_length"
        ):
            # Use provided duration values
            if subscription_in.duration_unit == DurationUnit.DAY:
                expires_at = start_at + timedelta(days=subscription_in.duration_length)
            elif subscription_in.duration_unit == DurationUnit.MONTH:
                expires_at = start_at + relativedelta(
                    months=subscription_in.duration_length
                )
            else:
                raise ValueError(
                    f"Unsupported duration unit: {subscription_in.duration_unit}"
                )
        else:
            # Fallback to plan's duration (for backward compatibility)
            expires_at = calculate_end_time(start_at, plan)
    except ValueError as e:
        logging.error(f"Error calculating end time: {e}")
        raise HTTPException(
            status_code=500, detail="Internal error calculating subscription end time."
        )

    # 检查是否有重叠的订阅
    overlapping_subscription = await PlanSubscription.check_overlapping_subscriptions(
        user_id=subscription_in.user_id, start_at=start_at, expires_at=expires_at
    )

    if overlapping_subscription:
        # 如果有重叠的订阅，返回错误信息
        overlap_plan = await overlapping_subscription.plan
        logging.warning(
            f"User {subscription_in.user_id} has an overlapping subscription {overlapping_subscription.id} "
            f"for plan {overlap_plan.name} from {overlapping_subscription.start_at} to {overlapping_subscription.expires_at}"
        )
        raise HTTPException(
            status_code=400,
            detail=f"plan exists for current period ({overlap_plan.name}), starts at {overlapping_subscription.start_at.strftime('%Y-%m-%d %H:%M:%S')} and expires at {overlapping_subscription.expires_at.strftime('%Y-%m-%d %H:%M:%S')}.",
        )

    try:
        # 使用事务包装所有数据库操作
        from tortoise.transactions import in_transaction  # type: ignore

        async with in_transaction() as connection:
            # 创建新的订阅
            logging.info(
                f"Creating new subscription for user {subscription_in.user_id} with plan {subscription_in.plan_id}"
            )
            subscription_data = subscription_in.dict()
            subscription_data["start_at"] = start_at
            subscription_data["expires_at"] = expires_at

            new_subscription = await PlanSubscription.create(
                **subscription_data, using_db=connection
            )

        # 事务外获取关联数据
        logging.info(
            f"Fetching related data for new subscription {new_subscription.id}"
        )
        await new_subscription.fetch_related("user", "plan")

        plan_sub_out = await PlanSubscriptionDetailOut.from_tortoise_orm(
            new_subscription
        )
        logging.info(f"Successfully created subscription {new_subscription.id}")
        return plan_sub_out

    except Exception as e:
        logging.error(
            f"Error creating plan subscription for user {subscription_in.user_id}: {e}"
        )
        # Add more detailed error information
        import traceback

        logging.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=400, detail=f"Failed to create plan subscription: {e}"
        )


@router.post(
    "/subscription/{subscription_id}/cancel",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_super_admin_access)],
)
async def cancel_plan_subscription(
    subscription_id: UUID = Path(..., description="Subscription ID to cancel"),
):
    """
    Cancel an existing plan subscription.

    - **subscription_id**: UUID of the subscription to cancel.

    Returns no content on success (204), 404 if not found.
    """
    subscription = await PlanSubscription.get_or_none(
        id=subscription_id, deleted_at__isnull=True
    )
    if not subscription:
        raise HTTPException(
            status_code=404,
            detail=f"Plan subscription with ID {subscription_id} not found.",
        )

    await subscription.cancel()

    return None


@router.get(
    "/subscriptions",
    response_model=Page[PlanSubscriptionDetailOut],
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_super_admin_access)],
)
async def get_plan_subscriptions(
    params: ListAPIParams = Depends(),  # type: ignore
    user_id: Optional[UUID] = Query(None, description="Filter by user UUID"),
    user_email: Optional[str] = Query(None, description="Filter by user email"),
    status: Optional[SubscriptionStatus] = Query(
        None, description="Filter by subscription status"
    ),
):
    """
    Get a paginated list of plan subscriptions.

    - **limit**: items per page
    - **offset**: pagination offset
    - **user_id**: optional filter by user
    - **status**: optional filter by subscription status

    Returns a Page of PlanSubscriptionDetailOut.
    """
    query = PlanSubscription.filter(deleted_at__isnull=True, cancelled_at__isnull=True).prefetch_related(
        "plan", "user__userconfigs"
    )

    if user_id:
        query = query.filter(user_id=user_id)
    if status:
        query = query.filter(status=status)
    if user_email:
        query = query.filter(user__email__icontains=user_email)

    pages = await tortoise_paginate(query, params)
    return pages
