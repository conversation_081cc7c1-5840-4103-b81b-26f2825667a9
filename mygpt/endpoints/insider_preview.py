from uuid import UUID

from fastapi import API<PERSON><PERSON><PERSON>, Body, Depends
from fastapi_pagination import Page
from starlette.status import HTTP_204_NO_CONTENT

from mygpt.auth0.init import auth
from mygpt.authorization import current_user
from mygpt.error import NotFoundException, OperationFailedException
from mygpt.models import InsiderPreviewUser, Robot, User
from mygpt.parameters import ListAPIParams, tortoise_paginate
from mygpt.schemata import InsiderPreviewUserOut

router = APIRouter(prefix="/previews", tags=["Insider Preview"])


@router.get(
    "",
    summary="Get Insider Preview",
    response_model=Page[InsiderPreviewUserOut],
    dependencies=[Depends(auth.implicit_scheme)],
)
async def get_insider_preview(
    user: User = Depends(current_user),
    params: ListAPIParams = Depends(),
):
    """
    Get Insider Preview
    """
    queryset = InsiderPreviewUser.filter(deleted_at__isnull=True)
    return await tortoise_paginate(queryset, params, True)


@router.post(
    "",
    summary="Create Insider Preview",
    response_model=InsiderPreviewUserOut,
    dependencies=[Depends(auth.implicit_scheme)],
)
async def create_insider_preview(
    email: str = Body(None, embed=True),
    ai_id: UUID = Body(None, embed=True),
    user: User = Depends(current_user),
):
    """
    Create Insider Preview
    """
    if email:
        users = await User.filter(email=email, deleted_at__isnull=True)
        if not users:
            raise OperationFailedException("User not found")
        insider_preview = None
        for user in users:
            insider_preview, _ = await InsiderPreviewUser.get_or_create(
                user_id=user.user_id
            )
        return await InsiderPreviewUserOut.from_tortoise_orm(insider_preview)
    elif ai_id:
        ai_obj = await Robot.get_or_none(id=ai_id, deleted_at__isnull=True)
        if not ai_obj:
            raise OperationFailedException("AI not found")
        insider_preview, _ = await InsiderPreviewUser.get_or_create(robot=ai_obj)
        return await InsiderPreviewUserOut.from_tortoise_orm(insider_preview)
    else:
        raise OperationFailedException("Invalid email or ai_id")


@router.delete(
    "/{id}",
    summary="Delete Insider Preview",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[Depends(auth.implicit_scheme)],
)
async def delete_insider_preview(
    id: UUID,
    user: User = Depends(current_user),
):
    """
    Delete Insider Preview
    """
    insider_preview = await InsiderPreviewUser.get_or_none(id=id)
    if not insider_preview:
        raise NotFoundException("Insider Preview not found")
    await insider_preview.delete()
