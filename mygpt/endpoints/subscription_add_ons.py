import logging
from datetime import datetime, timezone, timedelta
from typing import Union, List, Optional
from uuid import UUID

from dateutil.relativedelta import relativedelta
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body
from pydantic import root_validator
from starlette.status import HTTP_204_NO_CONTENT
from tortoise.contrib.pydantic import pydantic_model_creator

from mygpt.auth0.init import auth
from mygpt.authorization import verify_super_admin_access
from mygpt.models import (
    AddOn,
    SubscriptionStatus,
    AddOnSubscription,
    PLAN_FIELD_METADATA,
    User,
)
from mygpt.parameters import ListAPIParams, tortoise_paginate, Page
from mygpt.schemata import AddOnOutBase, AddOnSubscriptionIn, AddOnSubscriptionOut
from mygpt.services.addon_service import (
    get_addon,
    get_addons,
    create_addon,
    subscribe_addon,
    cancel_addon_subscription,
    get_addon_subscriptions,
)

router = APIRouter(prefix="/add-ons", tags=["Add-Ons"])


class AddOnIn(
    pydantic_model_creator(
        AddOn,
        name="AddOnIn",
        exclude_readonly=True,
        exclude=("id", "created_at", "updated_at", "deleted_at"),
    )
):
    value: Union[int, bool, List[str]]

    @root_validator
    def check_fields(cls, values):
        target = values.get("target_field")
        value = values.get("value")
        if not target:
            # 422
            raise HTTPException(
                status_code=422,
                detail="target_field is a required field.",
            )
        if value is None:
            raise HTTPException(
                status_code=422,
                detail="value is a required field.",
            )

        metadata = PLAN_FIELD_METADATA.get(target)
        if not metadata:
            valid_options = [key.value for key in PLAN_FIELD_METADATA.keys()]
            # 422
            raise HTTPException(
                status_code=422,
                detail=f"{target} is not a valid target_field. Valid options are: {valid_options}",
            )
        expected_py_type = metadata["expected_py_type"]
        if not isinstance(value, expected_py_type):
            raise HTTPException(
                status_code=422,
                detail=f"value must be {expected_py_type.__name__}, "
                f"got {type(value).__name__}",
            )

        if metadata["type"] == "numeric":
            if not isinstance(value, int) or value <= 0:
                raise HTTPException(
                    status_code=422,
                    detail="Numeric value must be a positive integer.",
                )
        return values


@router.get(
    "",
    summary="Get paginated list of add-ons",
    description="Retrieve a paginated list of all active add-ons. Super admin access required.",
    response_model=Page[AddOnOutBase],
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_super_admin_access)],
)
async def get_add_ons(
    params: ListAPIParams = Depends(),  # type: ignore
):
    """
    Get a paginated list of add-ons.

    - **size**: number of items per page
    - **order_by**: comma-separated sort fields, prefix with '-' for descending.

    Returns a paginated list of AddOnOutBase.
    """
    # Use the query from the service layer, but still apply pagination through tortoise_paginate
    addons_qs = AddOn.filter(deleted_at__isnull=True)
    pages = await tortoise_paginate(addons_qs, params)
    return pages


@router.post(
    "",
    summary="Create a new add-on",
    description="Create a new add-on. Super admin access required.",
    response_model=AddOnOutBase,
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_super_admin_access)],
)
async def create_add_on(
    addon_in: AddOnIn = Body(..., description="Add-on creation payload"),
):
    """
    Create a new add-on.

    - **addon_in**: AddOnIn model with target_field and value.

    Returns the created AddOnOutBase.
    """
    # Use the service layer to create the add-on
    addon = await create_addon(addon_in.dict())
    return await AddOnOutBase.from_tortoise_orm(addon)


@router.post(
    "/subscription",
    summary="Create add-on subscription",
    description="Subscribe a user to an add-on. Super admin access required.",
    response_model=AddOnSubscriptionOut,
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_super_admin_access)],
)
async def create_addon_subscription(
    addon_subscription_in: AddOnSubscriptionIn = Body(
        ..., description="Add-on subscription payload"
    ),
):
    """
    Create a new add-on subscription for a user.

    - **addon_subscription_in**: AddOnSubscriptionIn model with user_id and add_on_id.

    Automatically sets start_at and calculates expires_at.
    Returns the created AddOnSubscriptionOut.
    """
    # Use the service layer to create the subscription
    addon_subscription = await subscribe_addon(
        user_id=addon_subscription_in.user_id,
        addon_id=addon_subscription_in.add_on_id,
        start_at=addon_subscription_in.start_at,
        duration_unit=addon_subscription_in.duration_unit,
        duration_length=addon_subscription_in.duration_length,
    )

    # Prepare the response
    addon_subscription_out = await AddOnSubscriptionOut.from_tortoise_orm(
        addon_subscription
    )
    return addon_subscription_out


@router.post(
    "/subscription/{subscription_id}/cancel",
    summary="Cancel an add-on subscription",
    description="Cancel an existing add-on subscription by its ID. Super admin access required.",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_super_admin_access)],
)
async def cancel_subscription(
    subscription_id: UUID = Path(..., description="ID of the subscription to cancel"),
):
    """
    Manually cancel an existing add-on subscription.

    - **subscription_id**: UUID of the subscription to cancel.

    Returns no content (204) on success.
    """
    # Use the service layer to cancel the subscription
    await cancel_addon_subscription(subscription_id)
    return None  # Return None for 204


@router.get(
    "/subscriptions",
    summary="List add-on subscriptions",
    description="Retrieve a paginated list of add-on subscriptions. Super admin access required.",
    response_model=Page[AddOnSubscriptionOut],  # Use AddOnSubscriptionOut from schemata
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_super_admin_access)],
)
async def get_add_on_subscriptions(
    params: ListAPIParams = Depends(),  # type: ignore
    user_id: Optional[UUID] = Query(None, description="Filter by user UUID"),
    user_email: Optional[str] = Query(None, description="Filter by user email"),
    status: Optional[SubscriptionStatus] = Query(
        None, description="Filter by subscription status"
    ),
):
    """
    Get a paginated list of add-on subscriptions.

    - **size**: number of items per page
    - **order_by**: comma-separated sort fields, prefix with '-' for descending.
    - **user_id**: optional filter by user UUID
    - **status**: optional filter by subscription status

    Returns paginated AddOnSubscriptionOut list.
    """
    # Build the query with filters
    query = AddOnSubscription.filter(deleted_at__isnull=True).prefetch_related(
        "add_on", "user__userconfigs"
    )  # Prefetch add_on

    if user_id:
        query = query.filter(user_id=user_id)
    if status:
        query = query.filter(status=status)
    if user_email:
        query = query.filter(user__email__icontains=user_email)

    # Apply pagination
    pages = await tortoise_paginate(query, params)
    return pages
