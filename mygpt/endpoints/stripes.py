import asyncio
import json
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Callable, Optional

import stripe
from fastapi import APIRouter, Depends, Request, Security
from fastapi_auth0 import Auth0User
from loguru import logger as logging
from starlette.responses import RedirectResponse

from mygpt.auth0.init import auth
from mygpt.authorization import verify_admin_access
from mygpt.enums import StripeModel
from mygpt.error import NotFoundException, ServerErrorException
from mygpt.models import StripePayments, StripeProducts, StripeWebhookLog, User
from mygpt.schemata import MyPlanOut, StripeProductApiIdOut
from mygpt.settings import DOMAIN, LOCAL_PROXY, STRIPE_API_KEY
from mygpt.utils_stripe import clean_question_cache, get_plan

router = APIRouter(prefix="/stripes", tags=["Stripe"])


# 查询StripeProducts
@router.get("/products", response_model=list[StripeProductApiIdOut])
async def get_products():
    result = []
    hobby = {"order": 1}
    growth = {"order": 2}
    standard = {"order": 3}
    obj_list = await StripeProducts.filter(deleted_at__isnull=True).all()
    if len(obj_list) > 0:
        for obj in obj_list:
            if obj.order == 1:
                if obj.month == 1:
                    hobby["api_id_monthly"] = obj.api_id
                else:
                    hobby["api_id_yearly"] = obj.api_id
            elif obj.order == 2:
                if obj.month == 1:
                    growth["api_id_monthly"] = obj.api_id
                else:
                    growth["api_id_yearly"] = obj.api_id
            elif obj.order == 3:
                if obj.month == 1:
                    standard["api_id_monthly"] = obj.api_id
                else:
                    standard["api_id_yearly"] = obj.api_id
        result.append(hobby)
        result.append(growth)
        result.append(standard)
    return result


@router.get("/create-checkout", dependencies=[Depends(auth.implicit_scheme)])
async def create_checkout(api_id: str, origin: str, user_id: str):
    user_obj = await User.get_or_none(user_id=user_id)
    try:
        if origin == "":
            origin = DOMAIN
        stripe.api_key = STRIPE_API_KEY
        stripe.proxy = LOCAL_PROXY
        # 查询付费计划
        product = await StripeProducts.filter(
            api_id=api_id, deleted_at__isnull=True
        ).first()
        if product is None:
            raise NotFoundException("Product not found")
        checkout_session = stripe.checkout.Session.create(
            line_items=[
                {
                    "price": api_id,
                    "quantity": 1,
                },
            ],
            mode=product.mode.value,
            success_url=origin,
            cancel_url=origin,
            metadata={
                "origin": origin,
                "api_id": api_id,
                "mode": product.mode.value,
                "user_id": user_obj.id,
            },
        )
        return RedirectResponse(url=checkout_session.url, status_code=303)
    except Exception as e:
        raise ServerErrorException(e)


@router.post(
    "/cancel",
    dependencies=[Depends(auth.implicit_scheme)],
)
async def cancel_pay(user: Auth0User = Security(auth.get_user)):
    user_obj = await User.get_or_none(user_id=user.id)
    obj = (
        await StripePayments.filter(
            user_id=user_obj.id,
            deleted_at__isnull=True,
            type__in=["payment_intent.succeeded", "checkout.session.completed"],
        )
        .prefetch_related("stripe_products")
        .order_by("-created_at")
        .first()
    )
    if obj is None:
        raise NotFoundException("Payment not found")
    stripe.api_key = STRIPE_API_KEY
    stripe.proxy = LOCAL_PROXY
    if obj.mode == StripeModel.SUBSCRIPTION:
        sub_info = stripe.Subscription.delete(
            obj.sub_id,
        )
        await StripePayments.filter(id=obj.id).update(canceled_at=datetime.now())
        return sub_info
    if obj.mode == StripeModel.PAYMENT:
        await StripePayments.filter(id=obj.id).update(
            type="canceled", canceled_at=datetime.now()
        )
        return None


@router.get(
    "/my-plan",
    dependencies=[Depends(auth.implicit_scheme)],
    response_model=MyPlanOut,
)
async def get_my_plan(user: Optional[Auth0User] = Security(auth.get_user)):
    async def _retry(
        func: Callable, max_retries: int = 5, retry_delay: int = 1, *args, **kwargs
    ):
        for i in range(max_retries):
            result = await func(*args, **kwargs)
            if result:
                return result
            await asyncio.sleep(retry_delay)
        return None

    user_obj = await _retry(User.get_or_none, user_id=user.id)
    if not user_obj:
        logging.error(f"User not found: {user.id}")
        raise NotFoundException("User not found")
    user_obj = await User.get_or_none(user_id=user.id).prefetch_related("userconfigs")
    return await get_plan(user_obj)


@router.get(
    "/robot-plan/{ai_id}",
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_admin_access),
    ],
    response_model=MyPlanOut,
)
async def get_robot_plan(
    ai_id: str,
    gt_date: Optional[bool] = False,
):
    user = await User.get_or_none(robots__id=ai_id).prefetch_related("userconfigs")
    return await get_plan(user, ai_id, gt_date)


@router.post("/webhook")
async def webhook(request: Request):
    # 获取请求体
    payload = await request.body()
    try:
        event = stripe.Event.construct_from(json.loads(payload), stripe.api_key)
        await StripeWebhookLog.create(
            type=event["type"],
            data=json.dumps(event["data"]),
        )

        event_type = event.type

        # 获取事件的元数据
        metadata = event.data.object.get("metadata")

        if event_type == "payment_intent.succeeded":
            # 支付成功
            payment_id = event.data.object.id
            invoice = event.data.object.get("invoice")
            print(f"支付成功：{payment_id} invoice:{invoice}")

        elif event_type == "payment_intent.payment_failed":
            # 支付失败
            payment_id = event.data.object.id
            invoice = event.data.object.get("invoice")
            print(f"支付失败：{payment_id} invoice:{invoice}")
        elif event_type == "charge.succeeded":
            # 付款成功
            payment_id = event.data.object.id
            invoice = event.data.object.get("invoice")
            print(f"已付款：{payment_id} invoice:{invoice}")
        elif event_type == "charge.refunded":
            # 退款成功
            payment_id = event.data.object.id
            invoice = event.data.object.get("invoice")
            print(f"已退款：{payment_id} invoice:{invoice}")
        elif event_type == "customer.subscription.created":
            # 订阅已创建
            subscription_id = event.data.object.id
            invoice = event.data.object.get("invoice")
            print(f"订阅已创建：{subscription_id} invoice:{invoice}")
        elif event_type == "customer.subscription.updated":
            # 订阅已更新
            subscription_id = event.data.object.id
            invoice = event.data.object.get("invoice")
            print(f"订阅已更新：{subscription_id} invoice:{invoice}")
        elif event_type == "customer.subscription.deleted":
            # 订阅已取消
            subscription_id = event.data.object.id
            sub_id = event.data.object.get("id")
            print(f"订阅已取消：{subscription_id} sub_id:{sub_id}")
            await StripePayments.filter(
                sub_id=sub_id,
            ).update(type="customer.subscription.deleted")

        elif event_type == "invoice.payment_succeeded":
            # 发票已支付
            invoice_id = event.data.object.id
            invoice = event.data.object.get("id")
            lines = event.data.object.get("lines")
            print(f"发票已支付：{invoice_id} invoice:{invoice}")
            product_obj = await StripeProducts.filter(
                api_id=lines.data[0]["plan"]["id"]
            ).first()
            await StripePayments.filter(
                invoice=invoice,
            ).update(
                stripe_products_id=product_obj.id,
                type="payment_intent.succeeded",
                sub_id=lines.data[0]["subscription"],
            )
        elif event_type == "checkout.session.completed":
            # Checkout 支付已完成
            checkout_id = event.data.object.id
            organization_id = metadata.get("organization_id")
            api_id = metadata.get("api_id")
            mode = metadata.get("mode")
            invoice = event.data.object.get("invoice")
            payment_id = event.data.object.get("payment_intent")
            stripe_products_obj = await StripeProducts.filter(api_id=api_id).first()
            today = datetime.now().date()
            if stripe_products_obj.month == 1:
                end_day = today + timedelta(days=30)
            else:
                end_day = today + timedelta(days=365)
            datetime_obj = datetime(
                year=end_day.year, month=end_day.month, day=end_day.day
            )
            await StripePayments.create(
                type="checkout.session.completed",
                stripe_products_id=stripe_products_obj.id,
                invoice=invoice,
                payment_id=payment_id,
                api_id=api_id,
                mode=mode,
                end_at=datetime_obj,
                user_id=metadata.get("user_id"),
                customer_details=json.dumps(event.data.object.get("customer_details")),
            )
            await clean_question_cache(metadata.get("user_id"))
            print(
                f"Checkout 支付已完成：{checkout_id} [组织 ID：{organization_id}] invoice:{invoice}"
            )
        elif event_type == "account.updated":
            # Stripe 账户已更新
            account_id = event.data.object.id
            invoice = event.data.object.get("invoice")
            print(f"Stripe 账户已更新：{account_id} invoice:{invoice}")
        else:
            # 其他事件
            print(f"其他事件类型：{event_type}")
        return {"message": "success"}
    except Exception as e:
        # 其他异常错误
        print(f"其他异常错误：{e}")
        raise ServerErrorException()
