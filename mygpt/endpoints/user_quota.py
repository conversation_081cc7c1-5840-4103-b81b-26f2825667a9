import logging
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException
from starlette.status import HTTP_404_NOT_FOUND

from mygpt.authorization import current_user
from mygpt.models import User, UsageType, QuotaGrantType
from mygpt.schemata import (
    UserQuotaInfo,
    QuotaDetails,
    UserOutBase,
    PlanOutBase,
    UserEffectiveQuotas,
    PlanQuotaDetails,
    PlanSubscriptionWithUsage,
    AddOnSubscriptionWithUsage,
    AddOnOutBase,
    PlanSubscriptionBaseOut,
    AddOnSubscriptionBaseOut,
)
from mygpt.services.quota_service import QuotaService, QuotaSource

router = APIRouter(prefix="/user/quota", tags=["User Quota"])


async def _get_user_quota_info_internal(user: User) -> UserQuotaInfo:
    """
    Internal helper function to get quota information for a given user.
    """
    user_id = str(user.id)

    # Get bot quota details
    bot_count, bot_limit = await QuotaService.get_bot_quota_details(user_id)

    # Get effective total quota details (used for the top-level 'effective_quotas' display)
    message_available, message_total_limit, message_used_effective = (
        await QuotaService.get_effective_quota(user_id, UsageType.MESSAGE_SENT)
    )
    if message_total_limit is None:  # Unlimited effective quota
        message_used_effective = (
            await QuotaService.get_total_consumed_count_current_period(
                user_id, UsageType.MESSAGE_SENT
            )
        )

    web_page_available, web_page_total_limit, web_page_used_effective = (
        await QuotaService.get_effective_quota(user_id, UsageType.WEB_PAGE_LEARNED)
    )
    if web_page_total_limit is None:
        web_page_used_effective = (
            await QuotaService.get_total_consumed_count_current_period(
                user_id, UsageType.WEB_PAGE_LEARNED
            )
        )

    multi_modal_available, multi_modal_total_limit, multi_modal_used_effective = (
        await QuotaService.get_effective_quota(user_id, UsageType.MULTI_MODAL_PARSING)
    )
    if multi_modal_total_limit is None:
        multi_modal_used_effective = (
            await QuotaService.get_total_consumed_count_current_period(
                user_id, UsageType.MULTI_MODAL_PARSING
            )
        )

    plan_subscription_db = await user.get_active_plan_subscription()
    user_schema_out = await UserOutBase.from_tortoise_orm(user)
    effective_is_corporate = await user.effective_is_corporate()

    # --- Populate UserEffectiveQuotas (uses overall effective values) ---
    effective_bot_quota = QuotaDetails(
        used=bot_count,
        limit=bot_limit,
        available=(bot_limit - bot_count) if bot_limit is not None else None,
    )
    effective_message_quota = QuotaDetails(
        used=message_used_effective,
        limit=message_total_limit,
        available=message_available,
    )
    effective_web_page_quota = QuotaDetails(
        used=web_page_used_effective,
        limit=web_page_total_limit,
        available=web_page_available,
    )
    effective_multi_modal_parsing_quota = QuotaDetails(
        used=multi_modal_used_effective,
        limit=multi_modal_total_limit,
        available=multi_modal_available,
    )

    user_effective_quotas_obj = UserEffectiveQuotas(
        bot_quota=effective_bot_quota,
        message_quota=effective_message_quota,
        web_page_quota=effective_web_page_quota,
        multi_modal_parsing_quota=effective_multi_modal_parsing_quota,
        is_corporate=effective_is_corporate,
    )

    bots_to_attribute = effective_bot_quota.used or 0
    plan_provides_unlimited_bots = False
    plan_provides_unlimited_messages = False
    plan_provides_unlimited_web_pages = False
    plan_provides_unlimited_multi_modal = False

    plan_active_subscription_display = None
    if plan_subscription_db:
        actual_plan_from_db = await plan_subscription_db.plan
        plan_out = await PlanOutBase.from_tortoise_orm(actual_plan_from_db)
        base_plan_subscription_pydantic = (
            await PlanSubscriptionBaseOut.from_tortoise_orm(plan_subscription_db)
        )
        display_plan_usage_details = PlanQuotaDetails()

        # Bot Quota (Plan)
        limit_bots_plan = actual_plan_from_db.bot_quota
        used_bots_plan = 0
        if limit_bots_plan is None:
            used_bots_plan = bots_to_attribute
            bots_to_attribute = 0
            plan_provides_unlimited_bots = True
        elif bots_to_attribute > 0:
            limit_bots_plan_val = (
                int(limit_bots_plan) if limit_bots_plan is not None else 0
            )
            can_use_bots = min(bots_to_attribute, limit_bots_plan_val)
            used_bots_plan = can_use_bots
            bots_to_attribute -= can_use_bots
        available_bots_plan = (
            (limit_bots_plan - used_bots_plan) if limit_bots_plan is not None else None
        )
        display_plan_usage_details.bot_quota = QuotaDetails(
            used=used_bots_plan, limit=limit_bots_plan, available=available_bots_plan
        )

        async def get_plan_consumable_usage(
            usage_type: UsageType, plan_quota_val, grant_type_val
        ) -> QuotaDetails:
            nonlocal plan_provides_unlimited_messages, plan_provides_unlimited_web_pages, plan_provides_unlimited_multi_modal
            limit = plan_quota_val
            used_from_log = 0
            if grant_type_val:
                plan_source = QuotaSource(
                    subscription_id=plan_subscription_db.id,
                    source_type="plan",
                    granted_amount=limit,
                    grant_type=grant_type_val,
                    start_time=plan_subscription_db.start_at,
                    expiry_time=plan_subscription_db.expires_at,
                )
                used_from_log = await QuotaService._calculate_consumed_for_source(
                    user_id, usage_type, plan_source
                )

            if limit is None:
                if usage_type == UsageType.MESSAGE_SENT:
                    plan_provides_unlimited_messages = True
                elif usage_type == UsageType.WEB_PAGE_LEARNED:
                    plan_provides_unlimited_web_pages = True
                elif usage_type == UsageType.MULTI_MODAL_PARSING:
                    plan_provides_unlimited_multi_modal = True

            available = (
                (limit - used_from_log)
                if limit is not None and isinstance(used_from_log, (int, float))
                else None
            )
            return QuotaDetails(used=used_from_log, limit=limit, available=available)

        display_plan_usage_details.message_quota = await get_plan_consumable_usage(
            UsageType.MESSAGE_SENT,
            actual_plan_from_db.message_quota,
            actual_plan_from_db.message_quota_grant_type,
        )
        display_plan_usage_details.web_page_quota = await get_plan_consumable_usage(
            UsageType.WEB_PAGE_LEARNED,
            actual_plan_from_db.web_page_quota,
            actual_plan_from_db.web_page_quota_grant_type,
        )
        display_plan_usage_details.multi_modal_parsing_quota = (
            await get_plan_consumable_usage(
                UsageType.MULTI_MODAL_PARSING,
                actual_plan_from_db.multi_modal_parsing_quota,
                actual_plan_from_db.multi_modal_parsing_quota_grant_type,
            )
        )

        plan_active_subscription_display = PlanSubscriptionWithUsage(
            **base_plan_subscription_pydantic.dict(
                exclude_unset=True, exclude={"plan"}
            ),
            plan=plan_out,
            usage=display_plan_usage_details,
        )

    active_addon_subscriptions_db = await user.get_active_add_on_subscription()
    sorted_addon_subscriptions = sorted(
        active_addon_subscriptions_db, key=lambda sub: sub.start_at
    )
    addons_with_usage_list_display = []
    addon_provides_unlimited_bots_agg = False

    def get_usage_type_from_target_field(target_field: str) -> Optional[UsageType]:
        mapping = {
            "message_quota": UsageType.MESSAGE_SENT,
            "web_page_quota": UsageType.WEB_PAGE_LEARNED,
            "multi_modal_parsing_quota": UsageType.MULTI_MODAL_PARSING,
        }
        return mapping.get(target_field)

    for addon_sub in sorted_addon_subscriptions:
        addon = await addon_sub.add_on
        if not addon:
            continue

        addon_out = await AddOnOutBase.from_tortoise_orm(addon)
        base_addon_subscription_pydantic = (
            await AddOnSubscriptionBaseOut.from_tortoise_orm(addon_sub)
        )

        target_field = addon.target_field
        addon_limit_value = addon.value
        addon_item_used = 0

        usage_type_for_consumable = get_usage_type_from_target_field(target_field)

        if usage_type_for_consumable:
            # Ensure addon_limit_value is int or None for QuotaSource
            consumable_limit_val = None
            if isinstance(addon_limit_value, (int, float)):
                consumable_limit_val = int(addon_limit_value)
            elif isinstance(addon_limit_value, str) and addon_limit_value.isdigit():
                consumable_limit_val = int(addon_limit_value)
            # else: consumable_limit_val remains None if addon_limit_value is None or non-numeric string

            addon_source = QuotaSource(
                subscription_id=addon_sub.id,
                source_type="addon",
                granted_amount=consumable_limit_val,
                grant_type=QuotaGrantType.TOTAL,
                start_time=addon_sub.start_at,
                expiry_time=addon_sub.expires_at,
            )
            addon_item_used = await QuotaService._calculate_consumed_for_source(
                user_id, usage_type_for_consumable, addon_source
            )
        elif target_field == "bot_quota":
            current_bot_limit_val = None
            if isinstance(addon_limit_value, (int, float)):
                current_bot_limit_val = int(addon_limit_value)
            elif isinstance(addon_limit_value, str) and addon_limit_value.isdigit():
                current_bot_limit_val = int(addon_limit_value)

            if (
                plan_provides_unlimited_bots
                or addon_provides_unlimited_bots_agg
                or bots_to_attribute == 0
            ):
                addon_item_used = 0
            elif current_bot_limit_val is None:
                addon_item_used = bots_to_attribute
                bots_to_attribute = 0
                addon_provides_unlimited_bots_agg = True
            elif current_bot_limit_val is not None:
                can_use_bots = min(bots_to_attribute, current_bot_limit_val)
                addon_item_used = can_use_bots
                bots_to_attribute -= can_use_bots
        else:
            addon_item_used = 0

        # Ensure addon_limit_value is comparable for available calculation
        final_addon_limit_for_calc = None
        if isinstance(addon_limit_value, (int, float)):
            final_addon_limit_for_calc = int(addon_limit_value)
        elif isinstance(addon_limit_value, str) and addon_limit_value.isdigit():
            final_addon_limit_for_calc = int(addon_limit_value)

        addon_item_available = (
            (final_addon_limit_for_calc - addon_item_used)
            if final_addon_limit_for_calc is not None
            and isinstance(addon_item_used, (int, float))
            else None
        )

        display_addon_usage = QuotaDetails(
            used=addon_item_used,
            limit=addon_limit_value,
            available=addon_item_available,
        )

        addons_with_usage_list_display.append(
            AddOnSubscriptionWithUsage(
                **base_addon_subscription_pydantic.dict(
                    exclude_unset=True, exclude={"add_on"}
                ),
                add_on=addon_out,
                usage=display_addon_usage,
            )
        )

    quota_info = UserQuotaInfo(
        user=user_schema_out,
        active_plan_subscription=plan_active_subscription_display,
        active_add_on_subscriptions=addons_with_usage_list_display,
        effective_quotas=user_effective_quotas_obj,
    )
    return quota_info


@router.get("", response_model=UserQuotaInfo)
async def get_user_quota(user: User = Depends(current_user)):
    """
    Get the quota information for the current user.
    """
    try:
        user_quota_info = await _get_user_quota_info_internal(user)
        return user_quota_info
    except Exception as e:
        logging.error(f"Error getting user quota for current user: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error getting user quota: {str(e)}"
        )


@router.get("/{user_id}", response_model=UserQuotaInfo)
async def get_user_quota_by_id(
    user_id: str, current_user: User = Depends(current_user)
):
    """
    Get the quota information for a specific user.
    Only available to super admins.
    """
    is_super_admin = await User.is_super_admin(current_user.user_id)
    if not is_super_admin:
        raise HTTPException(
            status_code=403,
            detail="Only super admins can access other users' quota information.",
        )

    target_user = await User.get_or_none(id=user_id)
    if not target_user:
        raise HTTPException(
            status_code=HTTP_404_NOT_FOUND, detail=f"User with ID {user_id} not found."
        )

    try:
        user_quota_info = await _get_user_quota_info_internal(target_user)
        return user_quota_info
    except Exception as e:
        logging.error(
            f"Error getting user quota for user_id {user_id}: {e}", exc_info=True
        )
        raise HTTPException(
            status_code=500, detail=f"Error getting user quota: {str(e)}"
        )
