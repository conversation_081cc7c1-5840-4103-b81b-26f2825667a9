import asyncio
import json
import re
import time
from datetime import datetime
from typing import Optional, List
from uuid import UUID, uuid4

from tortoise.transactions import in_transaction

from fastapi import APIRouter, Depends, Request, Security
from fastapi_auth0 import Auth0User
from fastapi_pagination import Page
from loguru import logger as logging
from starlette.status import HTTP_204_NO_CONTENT
from mygpt.auth0.init import auth
from mygpt.auth0.utils import get_current_user
from mygpt.authorization import verify_dataset_access
from mygpt.error import NotFoundException
from mygpt.models import (
    FunctionCallApi,
    AgentFunctionCallApi,
    Robot,
    AgentFunctionCallApiRobot,
)
from mygpt.openai_utils import (
    chat_acreate,
    question_answer_turbo_16k_with_function_call,
)
from mygpt.parameters import ListAPIParams, tortoise_paginate
from mygpt.prompt import (
    function_call_system_content,
    function_call_ask_sys_template,
    function_call_ask_user_template,
)
from mygpt.schemata import (
    FunctionCallApiIn,
    FunctionCallApiOut,
    QuestionIn,
    AgentFunctionCallApiOut,
    AgentFunctionCallApiIn,
)
from mygpt.service.chat.function_call_service import (
    parse_function_call,
    function_call_api,
)
from mygpt.utils import num_tokens_from_string
from mygpt.utils_stripe import verify_questions

function_router = APIRouter(prefix="/datasets", tags=["Functions"])
agent_function_router = APIRouter(prefix="/apis", tags=["Agent Functions"])


@function_router.get(
    "/{dataset_id}/functions",
    response_model=Page[FunctionCallApiOut],
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_dataset_access),
    ],
)
async def read_root(
    dataset_id: str,
    user: Optional[Auth0User] = Security(get_current_user),
    params: ListAPIParams = Depends(),
    search: Optional[str] = None,
):
    if search:
        queryset = FunctionCallApi.filter(
            dataset_id=dataset_id,
            deleted_at__isnull=True,
            function_call_name__icontains=search,
        )
    else:
        queryset = FunctionCallApi.filter(
            dataset_id=dataset_id, deleted_at__isnull=True
        )
    pages = await tortoise_paginate(queryset, params, ["robot"])

    if pages.total > 0:
        for function_info in pages.items:
            if function_info.body == {}:
                function_info.body = None
            if not function_info.body_required:
                function_info.body_required = None

    return pages


@function_router.delete(
    "/{dataset_id}/functions/{function_call_id}",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_dataset_access),
    ],
)
async def read_root(
    function_call_id: UUID,
    dataset_id: str,
    user: Optional[Auth0User] = Security(get_current_user),
):
    obj = await FunctionCallApi.get_or_none(
        dataset_id=dataset_id, id=function_call_id, deleted_at__isnull=True
    )
    if obj is None:
        raise NotFoundException("Function Call API not found")
    else:
        await FunctionCallApi.filter(id=obj.id).update(deleted_at=datetime.now())
        return None


@function_router.put(
    "/{dataset_id}/functions/{function_call_id}",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_dataset_access),
    ],
)
async def read_root(
    function_call_id: UUID,
    function_call_api_input: FunctionCallApiIn,
    dataset_id: str,
    user: Optional[Auth0User] = Security(get_current_user),
):
    obj = await function_call_api_info(dataset_id, function_call_id)
    if obj is None:
        raise NotFoundException("FaqProperty not found")
    else:
        await FunctionCallApi.filter(id=obj.id).update(
            dataset_id=dataset_id, **function_call_api_input.dict(exclude_unset=True)
        )


@function_router.post(
    "/{dataset_id}/functions",
    response_model=FunctionCallApiOut,
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_dataset_access),
    ],
)
async def read_root(
    function_call_api_input: FunctionCallApiIn,
    dataset_id: str,
    user: Optional[Auth0User] = Security(get_current_user),
):
    if function_call_api_input.body == {}:
        function_call_api_input.body = None

    obj = await FunctionCallApi.create(
        dataset_id=dataset_id, **function_call_api_input.dict(exclude_unset=True)
    )
    return obj


async def function_call_api_info(dataset_id, function_call_id):
    obj = await FunctionCallApi.get_or_none(
        dataset_id=dataset_id, id=function_call_id, deleted_at__isnull=True
    )
    return obj


@function_router.post(
    "/{dataset_id}/functions/run",
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_dataset_access),
    ],
)
async def run_function_call(
    question: str,
    function_call_api_input: FunctionCallApiIn,
    dataset_id: str,
    user: Optional[Auth0User] = Security(get_current_user),
):
    send_info = None
    result = await parse_function_call(function_call_api_input)
    function_description = result["function_description"]
    operation = result["operation"]
    if function_description is None:
        logging.info("【function_call】 function_descriptions is None")
        return {
            "function_call_sta": False,
            "ask_content": {"messages": ["Not match function_call_api"]},
        }
    logging.info(f"【function_call】 function_descriptions: {function_description}")
    # 发送GPT请求
    message = []
    system_message = function_call_system_content
    if system_message is not None:
        message.append(
            {
                "role": "system",
                "content": system_message
                + "The current time zone time:"
                + datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }
        )
    message.append({"role": "user", "content": question})
    logging.info(f"【function_call】 message: {message}")
    start_time = time.time()
    gpt_response = await question_answer_turbo_16k_with_function_call(
        messages=message,
        functions=[function_description],
        function_call="auto",
        max_tokens=4096,
        stream=False,
    )
    end_time = time.time()
    logging.info(f"【function_call】 response time: {end_time - start_time}")
    function_call_sta = False
    # 解析GPT返回结果
    if gpt_response and gpt_response.additional_kwargs:
        content, message_tokens, send_info = await function_call_api(
            gpt_response,
            {operation["function_call_name"]: operation["operation"]},
            {operation["function_call_name"]: operation["function_call_info"]},
        )
        function_call_sta = True
    else:
        content = {"answer": "", "messages": ["Not match function_call_api"]}
    (
        logging.info(f"user_question:{question} content: {content}")
        if not function_call_sta
        else None
    )
    return {
        "function_call_sta": function_call_sta,
        "send_info": send_info,
        "keywords": question,
        "ask_content": content,
    }


@agent_function_router.get(
    "/{ai_id}/functions_by_robot",
    response_model=Page[AgentFunctionCallApiOut],
    dependencies=[Depends(auth.implicit_scheme)],
)
async def list_apis_by_robot(
    ai_id: UUID,
    # user: Optional[Auth0User] = Security(get_current_user),
    # ai_obj: Robot = Depends(verify_questions),
    params: ListAPIParams = Depends(),
    search: Optional[str] = None,
):
    agentfunctioncall_ids = await AgentFunctionCallApiRobot.filter(
        robot_id=ai_id
    ).values_list("agentfunctioncall_id", flat=True)
    queryset = AgentFunctionCallApi.filter(
        id__in=list(agentfunctioncall_ids),
        deleted_at__isnull=True,
    )
    pages = await tortoise_paginate(queryset, params)
    if pages.total > 0:
        for agent_function_info in pages.items:
            if agent_function_info.body == {}:
                agent_function_info.body = None
            if not agent_function_info.body_required:
                agent_function_info.body_required = None
    return pages


@agent_function_router.delete(
    "/{ai_id}/functions_by_robot/{agent_function_call_id}",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[
        Depends(auth.implicit_scheme),
    ],
)
async def delete_apis_by_robot(
    ai_id: UUID,
    agent_function_call_id: UUID,
    user: Optional[Auth0User] = Security(get_current_user),
):
    agent_api_obj = await AgentFunctionCallApi.get_or_none(
        id__in=await AgentFunctionCallApiRobot.filter(
            robot_id=ai_id,
            agentfunctioncall_id=agent_function_call_id,
        ).values_list("agentfunctioncall_id", flat=True),
        deleted_at__isnull=True,
    )
    logging.info(f"【update_apis_by_robot】 agent_api_obj: {agent_api_obj}")
    if agent_api_obj is None:
        raise NotFoundException(
            f"AgentFunctionCallApi not found, maybe agent_function_call_id: {agent_function_call_id} not in robot: {ai_id} or agent_function_call_id is deleted"
        )
    else:
        await AgentFunctionCallApi.filter(id=agent_api_obj.id).update(
            deleted_at=datetime.now()
        )
        return None


@agent_function_router.put(
    "/{ai_id}/functions_by_robot/{agent_function_call_id}",
    response_model=AgentFunctionCallApiOut,
    dependencies=[
        Depends(auth.implicit_scheme),
    ],
)
async def update_apis_by_robot(
    ai_id: UUID,
    agent_function_call_id: UUID,
    agent_function_call_api_in: AgentFunctionCallApiIn,
    user: Optional[Auth0User] = Security(get_current_user),
):
    """更新agent function call api"""
    logging.info(
        f"update agent function call api, user: {user.id} bot: {ai_id}, agent_function_call_id: {agent_function_call_id}"
    )
    if not agent_function_call_api_in.body:
        agent_function_call_api_in.body = "{}"
    if not agent_function_call_api_in.body_required:
        agent_function_call_api_in.body_required = "[]"
    # 先查询AgentFunctionCallApi是否存在
    agent_api_obj = await AgentFunctionCallApi.get_or_none(
        id__in=await AgentFunctionCallApiRobot.filter(
            robot_id=ai_id,
            agentfunctioncall_id=agent_function_call_id,
        ).values_list("agentfunctioncall_id", flat=True),
        deleted_at__isnull=True,
    )
    logging.info(f"【update_apis_by_robot】 agent_api_obj: {agent_api_obj}")
    if agent_api_obj is None:
        raise NotFoundException(
            f"AgentFunctionCallApi not found, maybe agent_function_call_id: {agent_function_call_id} not in robot: {ai_id} or agent_function_call_id is deleted"
        )
    # 进行数据更新操作
    update_flag = await AgentFunctionCallApi.filter(id=agent_api_obj.id).update(
        **agent_function_call_api_in.dict(exclude_unset=True)
    )
    if update_flag:
        # 更新成功
        updated_obj = await AgentFunctionCallApi.get(id=agent_api_obj.id)
        return updated_obj
    else:
        # 更新失败
        return None


@agent_function_router.post(
    "/{ai_id}/functions_by_robot",
    response_model=AgentFunctionCallApiOut,
    dependencies=[Depends(auth.implicit_scheme)],
)
async def create_apis_by_robot(
    ai_id: UUID,
    agent_function_call_api_in: AgentFunctionCallApiIn,
    user: Optional[Auth0User] = Security(get_current_user),
    ai_obj: Robot = Depends(verify_questions),
):
    """添加agent function call api"""
    logging.info(
        f"create agent function call api, user: {user.id} bot: {ai_id}, bot created at: {ai_obj.created_at}"
    )
    # 获取user_id, 将user_id信息写入到agent function call api中, 让新建的工具与user进行一个关联
    agent_function_call_api_in.user_id = user.id
    # 判断, 给定默认值
    if not agent_function_call_api_in.body:
        agent_function_call_api_in.body = "{}"
    if not agent_function_call_api_in.body_required:
        agent_function_call_api_in.body_required = "[]"
    # 数据库写入
    async with in_transaction() as conn:
        agent_function_call_api = await AgentFunctionCallApi.create(
            robot_id=ai_id,
            **agent_function_call_api_in.dict(exclude_unset=True),
            using_db=conn,
        )
        # 写入关联信息
        res = await AgentFunctionCallApiRobot.create(
            agentfunctioncall_id=agent_function_call_api.id,
            robot_id=ai_id,
            using_db=conn,
        )
        return agent_function_call_api


@agent_function_router.post(
    "/{ai_id}/functions_by_robot/run",
    dependencies=[
        Depends(auth.implicit_scheme),
    ],
)
async def run_test_apis_by_robot(
    question: str,
    agent_function_call_api_in: AgentFunctionCallApiIn,
    ai_id: UUID,
    user: Optional[Auth0User] = Security(get_current_user),
):
    send_info = None
    result = await parse_function_call(agent_function_call_api_in)
    function_description = result["function_description"]
    operation = result["operation"]
    if function_description is None:
        logging.info("【run_test_apis_by_robot】 function_descriptions is None")
        return {
            "function_call_sta": False,
            "ask_content": {"messages": ["Not match function_call_api"]},
        }
    logging.info(
        f"【run_test_apis_by_robot】 function_descriptions: {function_description}"
    )
    # 发送GPT请求
    message = []
    system_message = function_call_system_content
    if system_message is not None:
        message.append(
            {
                "role": "system",
                "content": system_message
                + "The current time zone time:"
                + datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }
        )
    message.append({"role": "user", "content": question})
    logging.info(f"【run_test_apis_by_robot】 message: {message}")
    start_time = time.time()
    gpt_response = await question_answer_turbo_16k_with_function_call(
        messages=message,
        functions=[function_description],
        function_call="auto",
        max_tokens=4096,
        stream=False,
    )
    end_time = time.time()
    logging.info(f"【run_test_apis_by_robot】 response time: {end_time - start_time}")
    function_call_sta = False
    # 解析GPT返回结果
    if gpt_response and gpt_response.additional_kwargs:
        content, message_tokens, send_info = await function_call_api(
            gpt_response,
            {operation["function_call_name"]: operation["operation"]},
            {operation["function_call_name"]: operation["function_call_info"]},
        )
        function_call_sta = True
    else:
        content = {"answer": "", "messages": ["Not match function_call_api"]}
    (
        logging.info(f"user_question:{question} content: {content}")
        if not function_call_sta
        else None
    )
    return {
        "function_call_sta": function_call_sta,
        "send_info": send_info,
        "keywords": question,
        "ask_content": content,
    }


@agent_function_router.get(
    "",
    response_model=Page[AgentFunctionCallApiOut],
    dependencies=[
        Depends(auth.implicit_scheme),
    ],
)
async def list_agent_function_call_by_user(
    user: Optional[Auth0User] = Security(get_current_user),
    params: ListAPIParams = Depends(),
):
    """根据用户查询用户的agent function call api"""
    queryset = AgentFunctionCallApi.filter(user_id=user.id, deleted_at__isnull=True)
    pages = await tortoise_paginate(queryset, params, False)
    if pages.total > 0:
        for function_info in pages.items:
            if function_info.body == {}:
                function_info.body = None
            if not function_info.body_required:
                function_info.body_required = None
    return pages


from mygpt.schemata import HisTrafficInformationIn, HisTrafficInformationOut


@agent_function_router.post(
    "/his_traffic_information", response_model=HisTrafficInformationOut
)
async def his_traffic_information(his_traffic_information_in: HisTrafficInformationIn):
    question = his_traffic_information_in.query
    encrpyted_phone = his_traffic_information_in.encrypted_phone

    answer1 = """\
You are currently using the "Enjoy All-in-One Plan," which includes 10GB of domestic data per month, 300 minutes of domestic call time, and a number of free text messages.

As of now, your data usage for this month is as follows:

Data used: 7GB
Data remaining: 3GB
Your call time usage is as follows:

Call time used: 180 minutes
Call time remaining: 120 minutes"""
    answer2 = """\
You are currently using the "Super Enjoy Plan," which includes 15GB of domestic data per month, 500 minutes of domestic call time, and a number of free text messages.

As of now, your data usage for this month is as follows:

Data used: 10GB
Data remaining: 5GB
Your call time usage is as follows:

Call time used: 350 minutes
Call time remaining: 150 minutes"""
    answer3 = """\
You are currently using the "Unlimited Data Plan," which includes 20GB of domestic data per month, 200 minutes of domestic call time, and a number of free text messages.

As of now, your data usage for this month is as follows:

Data used: 18GB
Data remaining: 2GB
Your call time usage is as follows:

Call time used: 150 minutes
Call time remaining: 50 minutes"""
    answer4 = """\
You are currently using the "Data King Plan," which includes 30GB of domestic data per month, 1000 minutes of domestic call time, and a number of free text messages.

As of now, your data usage for this month is as follows:

Data used: 25GB
Data remaining: 5GB
Your call time usage is as follows:

Call time used: 800 minutes
Call time remaining: 200 minutes
    """
    print()
    answers = [answer1, answer2, answer3, answer4]
    index = hash(encrpyted_phone) % len(answers)
    final_answer = answers[index]
    resp = HisTrafficInformationOut(
        response=final_answer, phone_number=encrpyted_phone, timestamp=int(time.time())
    )
    return resp
