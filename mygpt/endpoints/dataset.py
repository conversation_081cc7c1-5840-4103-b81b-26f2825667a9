import json
import logging
import os
import random
import re
import shutil
import threading
import time
import traceback
import uuid
from datetime import datetime
from enum import Enum
from typing import Optional, List, Tuple
from urllib.parse import urlparse
from uuid import UUID
from uuid import uuid4

import aiohttp
import starlette
from fastapi import (
    APIRouter,
    Body,
    Depends,
    File,
    Request,
    Security,
    UploadFile,
)
from fastapi import BackgroundTasks, Query
from fastapi_auth0 import Auth0User
from fastapi_pagination import Page
from langchain.schema import Document
from lark_oapi.api.drive.v1 import DownloadFileRequest
from qdrant_client.http import models
from qdrant_client.http.models import Filter, FieldCondition, MatchValue, PointStruct
from sse_starlette import EventSourceResponse, ServerSentEvent
from starlette.status import HTTP_204_NO_CONTENT
from tortoise import Tortoise
from tortoise.contrib.pydantic import pydantic_model_creator
from tortoise.transactions import in_transaction

import mygpt
from mygpt import settings
from mygpt.auth0.init import auth
from mygpt.authorization import (
    current_user,
    verify_admin_access,
    verify_dataset_admin,
    verify_dataset_owner,
    verify_robots_owner,
    get_current_oauth_user_with_gbase_user,
)

# from mygpt.schemata import VectorFileWithLarkFileOut
from mygpt.authorization import verify_dataset_access
from mygpt.constant import lark_export_file_extension_dict
from mygpt.core.embedding import file_embeddings, EmbeddingFactory
from mygpt.core.utils import get_collection_name
from mygpt.core.vector_storage import VectorStorageFactory
from mygpt.enums import (
    DATASET_STATUS,
    AIConfigType,
    DatasourceType,
    IntegrationSyncOperation,
    IntegrationSyncTriggerType,
    OpenAIModel,
    VectorFileType,
    FileStorage,
    ResourceType,
    IntegrationRuleType,
    IntegrationRuleFileListSyncingStatus,
    VectorFileSourceType,
)
from mygpt.enums import (
    VectorStorageType,
    AIStatus,
)
from mygpt.error import (
    InvalidParameterException,
)
from mygpt.error import (
    NotFoundException,
    OperationFailedException,
)
from mygpt.error import sentry_sdk_capture_exception
from mygpt.file_embeddings import (
    delete_dataset_by_obj,
    task_delete_attachments,
    task_delete_files,
)
from mygpt.lark_utils import (
    get_tenant_access_token,
    check_rules_lark_file_exists,
    fetch_lark_file_list,
    check_any_lark_folder_exists,
    download_single_file,
    re_export_and_download_single_file,
    get_file_metadata,
    FileInfo,
)
from mygpt.loader.document import DocumentLoader
from mygpt.loader.enums import UnstructuredType, filename_to_unstructured_type
from mygpt.loader.service import text_to_pdf
from mygpt.models import (
    Dataset,
    FaqProperty,
    Faqs,
    FunctionCallApi,
    IntegrationSyncLog,
    IntegrationSyncStatus,
    RobotConfig,
    User,
    AccountMember,
    LarkFile,
)
from mygpt.models import (
    Robot,
    IntegrationRuleVectorFile,
)
from mygpt.models import VectorFile, VectorFileStatus
from mygpt.openai_utils import language_detect, user_intent_detect
from mygpt.opensearch import get_open_search_client
from mygpt.parameters import ListAPIParams
from mygpt.pydantic_rules import LarkIntegrationRuleInput, LarkShareUrlInput
from mygpt.schemata import (
    DatasetIn,
    DatasetOut,
    Embeddings,
    VectorFileIn,
    VectorFileOut,
    CloneProgressOut,
    DatasetCloneProgressOut,
    DatasetMemberOut,
    UserMemberOut,
    DatasetSimpleOut,
)
from mygpt.search_utils import async_qdrant_client
from mygpt.settings import (
    CHANNEL_STREAM_EVENT,
    RedisClient,
    IS_LOCAL,
    FILE_LEARNING_TASKS_PER_SERVER,
    FILE_LEARNING_TASKS_PER_SERVER_ADV,
    MAX_FILE_SIZE,
)
from mygpt.site_crawler import SiteCrawler
from mygpt.tasks.lark_integration import (
    process_lark_integration_rule,
    get_vector_file_name_for_lark_file,
    process_lark_file,
    celery_app,
    store_task_id,
)
from mygpt.utils import (
    delete_files_from_s3,
    download_tmp_file_from_s3,
    get_analyzer,
    num_tokens_from_string,
    upload_file_handler,
    semaphore_old,
    semaphore_adv,
)
from mygpt.utils import get_file_tmp_location, upload_binary

NOT_FOUND_OR_NO_ACCESS_TO_DATASET = "Dataset not found, or you do not have access to it"

router = APIRouter(prefix="/datasets", tags=["Datasets"])
robot_router = APIRouter(prefix="/robots", tags=["Robots Datasets"])
datasets_api_router = APIRouter(prefix="/v1/datasets", tags=["Datasets"])


class Stream:
    stream_id: str
    stream_name: str

    def __init__(self, stream_id: str, stream_name: str) -> None:
        self.stream_id = stream_id
        self.stream_name = stream_name
        self._queue = asyncio.Queue[ServerSentEvent]()

    def __aiter__(self) -> "Stream":
        return self

    async def __anext__(self) -> ServerSentEvent:
        try:
            return await self._queue.get()
        except asyncio.CancelledError as e:
            for stream in _streams:
                if stream == self:
                    _streams.remove(stream)
            raise e

    async def asend(self, value: ServerSentEvent) -> None:
        await self._queue.put(value)

    async def ping(self):
        await self._queue.put(
            ServerSentEvent(comment=f"ping - {datetime.utcnow()}").encode()
        )

    @classmethod
    async def asend_by_name(
        cls, stream_name: str, value: ServerSentEvent, publish: bool = True
    ) -> None:
        published = False
        for stream in _streams:
            if stream.stream_name == stream_name:
                published = True
                logging.info(f"asend_by_name: {stream_name}")
                await stream.asend(value)
        if not published and publish:
            # 如果本机没有对应的流，那么就发布到redis
            await RedisClient.get_client().publish(
                CHANNEL_STREAM_EVENT,
                json.dumps({"stream_name": stream_name, "data": value.data}),
            )

    @classmethod
    async def receive_redis_message(cls, message):
        msg = json.loads(message)
        stream_name, data = msg["stream_name"], msg["data"]
        await Stream.asend_by_name(
            stream_name,
            ServerSentEvent(
                # event='new_message',
                id=str(uuid.uuid4()),
                retry=15000,
                data=data,
            ),
            False,
        )


_streams: list[Stream] = []


async def callback(stream_name, data):
    data_str = json.dumps(data)
    await Stream.asend_by_name(
        stream_name,
        ServerSentEvent(
            # event='new_message',
            id=str(uuid.uuid4()),
            retry=15000,
            data=data_str,
        ),
    )


async def _hander_vectorfile(
    vectorfile: VectorFile,
    client: aiohttp.ClientSession = None,
):
    document_loader = DocumentLoader(vectorfile.filename)
    unstructured_type = (
        UnstructuredType.PDF
        if vectorfile.file_type == VectorFileType.HTML_PDF
        else None
    )
    spa = vectorfile.metadata.get("spa", False)
    max_images = vectorfile.metadata.get("max_images", 0)
    documents = await document_loader.wait_for_load(
        type=unstructured_type, spa=spa, max_images=max_images, client=client
    )
    resources = []
    for document in documents:
        document.metadata["file_id"] = str(vectorfile.id)
        document_resources = document.metadata.get("resources", [])
        resources.extend(document_resources)
        if document_resources:
            del document.metadata["resources"]

    vectorfile = await VectorFile.filter(
        id=vectorfile.id, deleted_at__isnull=True
    ).first()
    if not vectorfile:
        return None
    vectorfile.resources = resources
    vectorfile.updated_at = datetime.now()
    await vectorfile.save(update_fields=["resources", "updated_at"])

    if not documents:
        vectorfile.file_status = VectorFileStatus.COMPLETE
        await vectorfile.save(update_fields=["file_status"])
        logging.warning(f"vectorfile is empty: {vectorfile.filename}")
        return vectorfile
    # content_hash = get_hash(documents[0].page_content)
    # # important 注意：这里假设content_hash一致时，生成的qdrant embedding和es index也一致，如果调整了储存机制，需要注释掉下面if分支
    # if vectorfile.content_hash == content_hash:
    #     await VectorFile.filter(id=vectorfile.id).update(
    #         file_status=VectorFileStatus.COMPLETE,
    #     )
    #     return vectorfile
    title = documents[0].metadata.get("title", None)
    if title:
        vectorfile.title = title
        await vectorfile.save(update_fields=["title"])

    vectorfile.metadata.update(documents[0].metadata)
    dataset_obj = await Dataset.get(id=vectorfile.dataset_id)
    vector_storage_type = VectorStorageType(
        dataset_obj.metadata.get("vector_storage", VectorStorageType.PINECONE)
    )
    embedding_params = dataset_obj.get_embedding_params()
    return await file_embeddings(
        vector_storage_type,
        str(vectorfile.dataset_id),
        dataset_obj.collection_name,
        str(vectorfile.id),
        documents=documents,
        metadata=vectorfile.metadata,
        embedding_model=embedding_params.provider,
        embedding_model_name=embedding_params.model_name,
        embedding_dimensions=embedding_params.dimensions,
    )


async def handler_vectorfiles(
    vectorfiles: List[VectorFile],
    callback,
):
    total = len(vectorfiles)
    finished = 0
    robot_id = None
    dataset_id = None
    async with aiohttp.ClientSession() as session:
        for vectorfile in vectorfiles:
            try:
                if vectorfile.file_status == VectorFileStatus.COMPLETE:
                    continue
                bind_id = (
                    str(vectorfile.robot_id)
                    if vectorfile.robot_id
                    else str(vectorfile.dataset_id)
                )
                if vectorfile.robot_id and await Robot.check_deleted(bind_id):
                    continue
                robot_id = vectorfile.robot_id
                dataset_id = vectorfile.dataset_id
                logging.info(f"handler_vectorfiles: {vectorfile.id}")
                try:
                    vectorfile = await VectorFile.filter(
                        id=vectorfile.id, deleted_at__isnull=True
                    ).first()
                    if not vectorfile:
                        continue
                    vectorfile.file_status = VectorFileStatus.PROCESS
                    vectorfile.updated_at = datetime.now()
                    await vectorfile.save(update_fields=["file_status", "updated_at"])
                    vectorfile = await _hander_vectorfile(vectorfile, client=session)
                    if not vectorfile:
                        continue
                    logging.info(f"handler_vectorfiles: {vectorfile.id} finished")
                except Exception as e:
                    logging.error(f"_hander_vectorfile error: {e}")
                    vectorfile = await VectorFile.filter(
                        id=vectorfile.id, deleted_at__isnull=True
                    ).first()
                    if not vectorfile:
                        continue
                    vectorfile.failed_reason = str(e)
                    vectorfile.file_status = VectorFileStatus.FAIL
                    await vectorfile.save(
                        update_fields=["failed_reason", "file_status"]
                    )

                finished += 1
                out = (await VectorFileOut.from_tortoise_orm(vectorfile)).json()
                out_obj = json.loads(out)
                result = dict(
                    finished=finished,
                    total=total,
                    url=out_obj,
                )
                try:
                    await callback(bind_id, result)
                except Exception as e:
                    logging.error(f"callback error: {e}")
            except Exception as e:
                logging.error(
                    f"handler_vectorfiles error: {e}, {traceback.format_exc()}"
                )
    if robot_id:
        await Robot.update_ai_status(robot_id, AIStatus.READY)
    if dataset_id:
        await Dataset.filter(id=dataset_id).update(data_status=DATASET_STATUS.READY)
        await Robot.update_ai_status_from_dataset(dataset_id, AIStatus.READY)


@router.get(
    "/members",
    response_model=Page[DatasetMemberOut],
    dependencies=[
        Depends(auth.implicit_scheme),
    ],
)
async def get_dataset_members(
    params: ListAPIParams = Depends(),
    user: User = Depends(current_user),
):
    queryset = (
        Dataset.filter(
            user_id=user.user_id,
            deleted_at__isnull=True,
        )
        .prefetch_related("user")
        .order_by("-created_at")
    )
    pages = await tortoise_paginate(queryset, params, [])
    datasets = pages.items
    dataset_ids = [dataset.id for dataset in datasets]
    if not dataset_ids:
        return pages
    members = (
        await AccountMember.filter(
            resource_type=ResourceType.DATASET,
            resource_id__in=dataset_ids,
            deleted_at__isnull=True,
        )
        .prefetch_related("member")
        .order_by("-created_at")
    )
    members_dict = {}
    for member in members:
        if member.resource_id not in members_dict:
            members_dict[member.resource_id] = [member]
        else:
            members_dict[member.resource_id].append(member)
    dataset_members = []
    for dataset in datasets:
        members = []
        if str(dataset.id) in members_dict:
            for member in members_dict[str(dataset.id)]:
                members.append(UserMemberOut.from_orm(member))
        dataset_members.append(
            DatasetMemberOut(
                dataset=DatasetSimpleOut.from_orm(dataset), members=members
            )
        )
    pages.items = dataset_members
    return pages


@datasets_api_router.get(
    "",
    summary="Get datasets",
    response_model=Page[DatasetCloneProgressOut],
    dependencies=[Depends(auth.implicit_scheme)],
)
@router.get(
    "",
    summary="Get datasets",
    response_model=Page[DatasetCloneProgressOut],
    dependencies=[Depends(auth.implicit_scheme)],
)
async def get_datasets(
    params: ListAPIParams = Depends(), user: User = Depends(current_user)
):
    """Get user's datasets"""
    queryset = Dataset.filter(
        user_id=user.user_id,
        deleted_at__isnull=True,
    )
    # dataset_info = await tortoise_paginate(queryset, params, ["robots", "robots_apis"])
    dataset_info = await tortoise_paginate(queryset, params, ["robots", "robots__apis"])
    for dataset in dataset_info.items:
        dataset.clone_progress = await dataset_clone_progress(dataset.id)
    return dataset_info


@datasets_api_router.get(
    "/{dataset_id}",
    summary="Get dataset",
    response_model=DatasetOut,
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_dataset_access),
    ],
)
@router.get(
    "/{dataset_id}",
    summary="Get dataset",
    response_model=DatasetOut,
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_dataset_access),
    ],
)
async def get_dataset(
    dataset_id: UUID,
):
    """Get dataset"""
    dataset_obj = await Dataset.get_or_none(
        id=dataset_id,
        deleted_at__isnull=True,
    ).prefetch_related("robots")
    if dataset_obj is None:
        raise NotFoundException("Dataset not found")
    return await DatasetOut.from_tortoise_orm(dataset_obj)


@datasets_api_router.post(
    "",
    summary="Create dataset",
    response_model=DatasetOut,
    dependencies=[Depends(auth.implicit_scheme)],
)
@router.post(
    "",
    summary="Create dataset",
    response_model=DatasetOut,
    dependencies=[Depends(auth.implicit_scheme)],
)
async def create_dataset(dataset: DatasetIn, user: User = Depends(current_user)):
    """Create dataset"""
    metadata = {
        "vector_storage": VectorStorageType.QDRANT_ONE_COLLECTION,
        "faq_vector_storage": VectorStorageType.QDRANT_ONE_COLLECTION,
    }
    if dataset.embedding_model_name:
        try:
            OpenAIModel(dataset.embedding_model_name)
            metadata["embedding_model_name"] = dataset.embedding_model_name
        except Exception as e:
            raise OperationFailedException(
                f"Invalid embedding_model_name {dataset.embedding_model_name}."
            )
    else:
        dataset.embedding_model_name = ""

    if dataset.embedding_dimensions:
        metadata["embedding_dimensions"] = dataset.embedding_dimensions

    # 使用请求体中的ID，如果没有则生成新的
    dataset_id = None
    if dataset.id:
        try:
            dataset_id = UUID(dataset.id)
        except ValueError:
            raise OperationFailedException(f"Invalid UUID format for id: {dataset.id}")
    else:
        dataset_id = uuid.uuid4()

    # 使用事务确保原子性
    async with in_transaction() as _:
        # 检查数据集是否已存在
        existing_dataset = await Dataset.filter(id=dataset_id).first()

        if existing_dataset:
            # 验证当前用户是否是数据集所有者
            if existing_dataset.user_id != user.user_id:
                raise OperationFailedException(
                    "You don't have permission to update this dataset."
                )
            # 数据集已存在，更新可更新的字段
            # 注意：collection_name 不会被更新，因为它等于 str(dataset_id)，而 dataset_id 是固定的

            # 定义可更新的字段列表
            updatable_fields = ["name", "description", "data_status", "url_rules"]

            # 准备更新数据
            update_data = {}
            for field in updatable_fields:
                if hasattr(dataset, field) and getattr(dataset, field) is not None:
                    update_data[field] = getattr(dataset, field)

            # 更新元数据
            if metadata:
                current_metadata = existing_dataset.metadata or {}
                merged_metadata = {**current_metadata, **metadata}
                update_data["metadata"] = merged_metadata

            # 执行更新
            if update_data:
                await Dataset.filter(id=dataset_id).update(**update_data)

            # 获取更新后的数据集
            updated_dataset = await Dataset.get(id=dataset_id)
            return await DatasetOut.from_tortoise_orm(updated_dataset)

        # 创建新数据集
        dataset_dict = dataset.dict()
        if "id" in dataset_dict:
            del dataset_dict["id"]  # 从字典中移除id，因为我们会单独传递
        dataset_obj = await Dataset.create(
            id=dataset_id,
            user_id=user.user_id,
            metadata=metadata,
            # 用于向量数据库collection_name，以及opensearch的index id
            collection_name=str(dataset_id),
            **dataset_dict,
        )

        return await DatasetOut.from_tortoise_orm(dataset_obj)


@datasets_api_router.put(
    "/{dataset_id}",
    summary="Update dataset",
    response_model=DatasetOut,
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_dataset_access),
    ],
)
@router.put(
    "/{dataset_id}",
    summary="Update dataset",
    response_model=DatasetOut,
    dependencies=[
        Depends(auth.implicit_scheme),
    ],
)
async def update_dataset(
    dataset_id: UUID,
    dataset: DatasetIn,
    dataset_obj=Depends(verify_dataset_access),
):
    """Update dataset"""
    if not dataset.id:
        dataset.id = dataset_id
    await dataset_obj.update_from_dict(dataset.dict()).save()
    return await DatasetOut.from_tortoise_orm(dataset_obj)


@datasets_api_router.delete(
    "/{dataset_id}",
    summary="Delete dataset",
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_dataset_owner)],
    status_code=HTTP_204_NO_CONTENT,
)
@router.delete(
    "/{dataset_id}",
    summary="Delete dataset",
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_dataset_owner)],
    status_code=HTTP_204_NO_CONTENT,
)
async def delete_dataset(
    dataset_id: UUID,
):
    """Delete dataset"""
    dataset_obj = await Dataset.get_or_none(
        id=dataset_id,
        deleted_at__isnull=True,
    ).prefetch_related("robots")
    if dataset_obj is None:
        raise NotFoundException("Dataset not found")
    linked_robot_num = len(dataset_obj.robots)
    for robot in dataset_obj.robots:
        if robot.deleted_at:
            linked_robot_num -= 1
    if linked_robot_num > 0:
        raise OperationFailedException("Dataset is connected to ai")
    asyncio.create_task(delete_dataset_by_obj(dataset_obj)).add_done_callback(
        lambda x: logging.info(f"delete_dataset: {x}")
    )
    asyncio.create_task(task_delete_attachments(dataset_obj)).add_done_callback(
        lambda x: logging.info(f"delete_dataset_attachments: {x}")
    )
    await dataset_obj.soft_delete()


@datasets_api_router.get(
    "/{dataset_id}/sources/counts",
    summary="Get dataset files counts",
    response_model=dict,
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_dataset_admin)],
)
@router.get(
    "/{dataset_id}/sources/counts",
    summary="Get dataset files counts",
    response_model=dict,
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_dataset_admin)],
)
async def get_dataset_files_counts(
    dataset_id: UUID,
):
    """Get dataset files counts"""
    queryset = VectorFile.filter(
        dataset_id=dataset_id,
        deleted_at__isnull=True,
    )
    counts = {}
    for item in DatasourceType:
        if item == DatasourceType.FIFE:
            counts[item.value] = await queryset.filter(
                file_type=VectorFileType.UPLOAD
            ).count()
        elif item == DatasourceType.WEBSITE:
            counts[item.value] = await queryset.filter(
                file_type__in=[
                    VectorFileType.HTML.value,
                    VectorFileType.HTML_PDF.value,
                ]
            ).count()
        elif item == DatasourceType.SITEMAP:
            counts[item.value] = await queryset.filter(
                file_type=VectorFileType.SITEMAP
            ).count()
        elif item == DatasourceType.GITBOOK:
            counts[item.value] = await queryset.filter(
                file_type=VectorFileType.GITBOOK
            ).count()
        elif item == DatasourceType.FAQ:
            counts[item.value] = await Faqs.filter(
                dataset_id=dataset_id, deleted_at__isnull=True, is_search=True
            ).count()
        elif item == DatasourceType.API:
            counts[item.value] = await FunctionCallApi.filter(
                dataset_id=dataset_id, deleted_at__isnull=True
            ).count()
        elif item == DatasourceType.INTEGRATION:
            counts[item.value] = await VectorFile.filter(
                dataset_id=dataset_id,
                deleted_at__isnull=True,
                integrationrule_vectorfiles__vectorfile_id__isnull=False,  # 通过关联表进行join
            ).count()
        else:
            logging.warning(f"unknown datasource type:{item}")
            counts[item.value] = 0
    property_count = await FaqProperty.filter(
        dataset_id=dataset_id, deleted_at__isnull=True
    ).count()
    counts["property"] = property_count
    return counts


@datasets_api_router.get(
    "/{dataset_id}/files",
    summary="Get dataset files",
    response_model=Page[VectorFileOut],
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_dataset_admin)],
)
@router.get(
    "/{dataset_id}/files",
    summary="Get dataset files",
    response_model=Page[VectorFileOut],
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_dataset_admin)],
)
async def get_dataset_files(
    dataset_id: UUID,
    file_type: Optional[DatasourceType | str] = DatasourceType.WEBSITE,
    source_type: Optional[str] = None,
    file_status: Optional[VectorFileStatus] = None,
    params: ListAPIParams = Depends(),
    query: Optional[str] = None,
    learn_type_filter: Optional[int] = 0,
    integration_rule_id: Optional[UUID] = None,
    file_ids: List[str] = Query(None, description="list of file IDs to retrieve"),
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
):
    # 刷新数据集文件列表的时候轮询调用
    param_type = Q()
    if file_type == "document":
        param_type = Q(
            file_type__in=[
                VectorFileType.UPLOAD.value,
                VectorFileType.HTML.value,
                VectorFileType.HTML_PDF.value,
                VectorFileType.SITEMAP.value,
                VectorFileType.GITBOOK.value,
            ]
        )
    elif file_type == DatasourceType.WEBSITE:
        param_type = Q(
            file_type__in=[
                VectorFileType.HTML.value,
                VectorFileType.HTML_PDF.value,
            ]
        )
    elif file_type == DatasourceType.FIFE:
        param_type = Q(file_type=VectorFileType.UPLOAD.value)
    elif file_type == DatasourceType.SITEMAP:
        param_type = Q(file_type=VectorFileType.SITEMAP.value)
    elif file_type == DatasourceType.GITBOOK:
        param_type = Q(file_type=VectorFileType.GITBOOK.value)
    elif file_type == VectorFileType.INTEGRATION:
        if source_type:
            param_type = Q(
                file_type=file_type,
                source_type=source_type,
                integrationrule_vectorfiles__vectorfile_id__isnull=False,
            )
        else:
            param_type = Q(
                file_type=file_type,
                integrationrule_vectorfiles__vectorfile_id__isnull=False,
            )
    param = Q(dataset_id=dataset_id, deleted_at__isnull=True) & param_type

    if query:
        query = query.strip()
        param_query = Q(title__icontains=query)
        param_query |= Q(filename__icontains=query)
        param &= param_query

    if file_ids:
        param &= Q(id__in=file_ids)

    if file_status:
        param &= Q(file_status=file_status.value)
    if learn_type_filter == 1:
        param &= Q(learn_type=0)
    elif learn_type_filter == 2:
        param &= Q(learn_type__gt=0)
    if start_time:
        param &= Q(created_at__gte=start_time)
    if end_time:
        param &= Q(created_at__lte=end_time)
    if integration_rule_id:
        integration_rule = await IntegrationRule.get_or_none(
            id=integration_rule_id, dataset_id=dataset_id, deleted_at__isnull=True
        )
        if not integration_rule:
            raise NotFoundException("Integration rule not found")
        vector_file_ids = await IntegrationRuleVectorFile.filter(
            integration_rule_id=integration_rule_id, deleted_at__isnull=True
        ).values_list("vectorfile_id", flat=True)
        param &= Q(id__in=vector_file_ids)
    queryset = VectorFile.filter(param).prefetch_related("lark_file")

    paginated_result = await tortoise_paginate(queryset, params)
    return paginated_result


@datasets_api_router.delete(
    "/{dataset_id}/files",
    summary="Delete dataset file",
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_dataset_access)],
    status_code=HTTP_204_NO_CONTENT,
)
@router.delete(
    "/{dataset_id}/files",
    summary="Delete dataset file",
    dependencies=[Depends(auth.implicit_scheme)],
    status_code=HTTP_204_NO_CONTENT,
)
async def delete_dataset_file(
    dataset_id: UUID,
    file_ids: list[UUID] = Body(..., embed=True),
    dataset_obj=Depends(verify_dataset_access),
):
    """Delete dataset file"""
    if settings.NEW_TRAIN_ENABLED:
        from mygpt.train import train

        return await train.delete_list(
            str(dataset_id), [str(file_id) for file_id in file_ids]
        )

    vectorfiles = await VectorFile.filter(
        id__in=file_ids, dataset_id=dataset_id, deleted_at__isnull=True
    )
    # 更新为已删除
    await VectorFile.filter(
        id__in=file_ids,
        dataset_id=dataset_id,
        deleted_at__isnull=True,
    ).update(deleted_at=datetime.now())
    asyncio.create_task(task_delete_files(vectorfiles, dataset_obj)).add_done_callback(
        lambda x: logging.info(f"delete_dataset_file: {x}")
    )


@datasets_api_router.put(
    "/{dataset_id}/files",
    summary="Update dataset file",
    response_model=VectorFileOut,
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_dataset_admin)],
)
@router.put(
    "/{dataset_id}/files",
    summary="Update dataset file",
    response_model=VectorFileOut,
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_dataset_admin)],
)
async def update_dataset_file(dataset_id: UUID, vector_file: VectorFileIn = Body(...)):
    """Update dataset file"""
    dataset_obj = await Dataset.get_or_none(
        id=dataset_id,
        deleted_at__isnull=True,
    )
    if dataset_obj is None:
        raise NotFoundException("Dataset not found")
    if dataset_obj.data_status != DATASET_STATUS.READY:
        raise OperationFailedException("Dataset is not ready")
    _vector_file = await VectorFile.get_or_none(
        id=vector_file.id, deleted_at__isnull=True
    )
    if not _vector_file:
        raise NotFoundException("File not found")
    if vector_file.title == "":
        vector_file.title = None

    await _vector_file.update_from_dict(
        {
            "title": vector_file.title,
        }
    ).save(update_fields=["title"])
    return await VectorFileOut.from_tortoise_orm(_vector_file)


@datasets_api_router.put(
    "/{dataset_id}/files/refresh_list",
    summary="Refresh dataset file list",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_dataset_admin)],
)
@router.put(
    "/{dataset_id}/files/refresh_list",
    summary="Refresh dataset file list",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_dataset_admin)],
)
async def refresh_dataset_file_list(
    dataset_id: UUID,
    file_id_list: list[UUID] = Body(embed=True),
    learn_type: int = Body(default=None, embed=True),
):
    # 检查是否为多模态解析（learn_type > 1 && < 100）
    is_multimodal_parsing = learn_type is not None and 1 < learn_type < 100
    # 获取数据集所有者
    dataset = await Dataset.get_or_none(id=dataset_id, deleted_at__isnull=True)
    if not dataset:
        raise NotFoundException("Dataset not found")
    dataset_owner_id = (await dataset.user).id

    # 检查多模态解析配额
    if (
        is_multimodal_parsing
        and settings.QUOTA_CHECK
        and not settings.IS_USE_LOCAL_VLLM
    ):
        from mygpt.models import UsageType
        from mygpt.services.quota_service import QuotaService
        from mygpt.exceptions import QuotaException

        try:
            # 检查用户是否有足够的多模态解析配额
            # 每个文件消耗一个配额
            has_quota = await QuotaService.check_quota(
                user_id=str(dataset_owner_id),
                resource_type=UsageType.MULTI_MODAL_PARSING,
                amount_to_consume=len(file_id_list),
            )

            if not has_quota:
                # 获取配额详情以显示更有用的错误消息
                available, total, used = await QuotaService.get_effective_quota(
                    user_id=str(dataset_owner_id),
                    resource_type=UsageType.MULTI_MODAL_PARSING,
                )

                # 根据配额类型格式化错误消息
                if total is None:  # 无限配额的情况
                    # 这种情况不应该发生，因为如果是无限配额，has_quota应该为True
                    quota_message = "Multi-modal parsing quota exceeded."
                else:
                    quota_message = f"Multi-modal parsing quota exceeded. You have used {used} out of {total} multi-modal parsings in your current plan."

                logging.warning(
                    f"User {dataset_owner_id} has exceeded their multi-modal parsing quota. Available: {available}, Total: {total}, Used: {used}"
                )
                raise QuotaException(
                    status_code=402,  # Payment Required
                    detail=f"{quota_message} Please upgrade your plan to continue.",
                )

            logging.info(
                f"User {dataset_owner_id} multi-modal parsing quota check passed. Proceeding with parsing {len(file_id_list)} files."
            )

        except QuotaException as e:
            # 重新抛出配额异常
            raise e
        except Exception as e:
            # 记录其他异常但不阻止请求
            logging.error(f"Error checking multi-modal parsing quota: {e}")
            sentry_sdk_capture_exception(e)

    if settings.NEW_TRAIN_ENABLED:
        from mygpt.train import train

        result = await train.refresh_list(
            dataset_id, file_id_list, DatasourceType.WEBSITE, learn_type=learn_type
        )

        # 消费多模态解析配额
        if (
            is_multimodal_parsing
            and settings.QUOTA_CHECK
            and not settings.IS_USE_LOCAL_VLLM
        ):
            try:
                from mygpt.models import UsageType
                from mygpt.services.quota_service import QuotaService

                # 消费多模态解析配额，每个文件消耗一个配额
                asyncio.create_task(
                    QuotaService.consume_quota(
                        user_id=str(dataset_owner_id),
                        resource_type=UsageType.MULTI_MODAL_PARSING,
                        amount_to_consume=len(file_id_list),
                    )
                )
                logging.info(
                    f"Consumed {len(file_id_list)} multi-modal parsing quota for user {dataset_owner_id}"
                )
            except Exception as e:
                # 记录错误但不阻止操作
                logging.error(f"Error consuming multi-modal parsing quota: {e}")
                sentry_sdk_capture_exception(e)

        return result

    # 选择文件或者url，重新学习的时候调用
    for file_id in file_id_list:
        await refresh_dataset_file(dataset_id, file_id)


# vincent.li 废掉的接口
# @datasets_api_router.put(
#    "/{dataset_id}/files/{file_id}/refresh",
#    summary="Refresh dataset file",
#    status_code=HTTP_204_NO_CONTENT,
#    dependencies=[Depends(auth.implicit_scheme), Depends(verify_dataset_admin)],
# )
# @router.put(
#    "/{dataset_id}/files/{file_id}/refresh",
#    summary="Refresh dataset file",
#    status_code=HTTP_204_NO_CONTENT,
#    dependencies=[Depends(auth.implicit_scheme), Depends(verify_dataset_admin)],
# )
async def refresh_dataset_file(dataset_id: UUID, file_id: UUID):
    dataset = await Dataset.get_or_none(id=dataset_id, deleted_at__isnull=True)
    if not dataset:
        raise NotFoundException("Dataset not found")
    vector_file = await VectorFile.get_or_none(id=file_id, deleted_at__isnull=True)
    if not vector_file:
        raise NotFoundException("Vector File not found")

    metadata = vector_file.metadata
    index_ids = vector_file.index_ids
    resources = vector_file.resources
    file_type = vector_file.file_type
    key = vector_file.key
    filename = vector_file.filename
    vector_storage_type = VectorStorageType(
        dataset.metadata.get("vector_storage", VectorStorageType.PINECONE)
    )
    if vector_storage_type == VectorStorageType.QDRANT:
        raise OperationFailedException(
            "Upgrade vector storage type to qdrant_one_collection"
        )
    embedding_params = dataset.get_embedding_params()
    vector_storage = VectorStorageFactory.get_instance(storage_type=vector_storage_type)
    collection_name = get_collection_name(
        embedding_params.model_name, embedding_params.dimensions
    )
    try:
        # 删除索引
        tasks = []
        tasks.append(vector_file.delete_search_index())

        # 删除资源
        if resources:
            tasks.append(delete_files_from_s3(resources))

        is_link = file_type in [
            VectorFileType.HTML,
            VectorFileType.HTML_PDF,
            VectorFileType.GITBOOK,
            VectorFileType.SITEMAP,
        ]

        new_metadata = {}
        if is_link:
            new_metadata["spa"] = metadata.get("spa", False)
            new_metadata["max_images"] = metadata.get("max_images", 10)

        await VectorFile.reset_status(
            str(vector_file.id),
            {
                "metadata": new_metadata,
            },
        )
        vector_file.file_status = VectorFileStatus.READY
        await Dataset.filter(id=dataset.id).update(
            updated_at=datetime.now(),
        )

        async def handle(path: str = None):
            try:
                if is_link:
                    await handler_vectorfiles(
                        [vector_file],
                        callback,
                    )
                else:
                    await upload_file_handler(dataset, [(vector_file, path)])

                await asyncio.gather(*tasks)
            except Exception as e:
                logging.error(f"refresh_dataset_file error: {e}")
                # 还原为 vector_file 的数据
                await VectorFile.filter(id=vector_file.id).update(
                    metadata=metadata,
                    index_ids=index_ids,
                    resources=resources,
                    file_type=file_type,
                    key=key,
                    file_status=vector_file.file_status,
                    failed_reason=vector_file.failed_reason,
                    token_count=vector_file.token_count,
                    characters_count=vector_file.characters_count,
                )

        if is_link:
            asyncio.create_task(handle())
        else:
            if not key:
                raise NotFoundException("File not found")
            # 下载文件
            path = ""
            try:
                path = await download_tmp_file_from_s3(key)
                # 重命名文件
                new_path = get_file_tmp_location(filename)
                shutil.move(path, new_path)
                path = new_path
            except Exception as e:
                logging.error(f"fail to download file in error:{e}")
                raise OperationFailedException("Fail to download file")

            asyncio.create_task(handle(path))
    except Exception as e:
        logging.error(f"refresh_dataset_file error: {e}")
        await VectorFile.filter(id=vector_file.id).update(
            file_status=VectorFileStatus.FAIL,
            failed_reason=str(e),
        )


@datasets_api_router.put(
    "/{dataset_id}/upload_files/refresh_list",
    summary="Refresh dataset upload file list",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_dataset_admin)],
)
@router.put(
    "/{dataset_id}/upload_files/refresh_list",
    summary="Refresh dataset upload file list",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_dataset_admin)],
)
async def refresh_dataset_upload_file_list(
    dataset_id: UUID,
    file_id_list: list[UUID] = Body(embed=True),
    learn_type: int = Body(default=0, embed=True),
):
    # 检查是否为多模态解析（learn_type > 1 && < 100）
    is_multimodal_parsing = learn_type is not None and 1 < learn_type < 100
    dataset = await Dataset.get_or_none(
        id=dataset_id, deleted_at__isnull=True
    ).prefetch_related("user")
    if not dataset:
        raise NotFoundException("Dataset not found")
    # 获取数据集所有者
    dataset_owner_id = dataset.user.id

    # 检查多模态解析配额
    if (
        is_multimodal_parsing
        and settings.QUOTA_CHECK
        and not settings.IS_USE_LOCAL_VLLM
    ):
        from mygpt.models import UsageType
        from mygpt.services.quota_service import QuotaService, QuotaException

        try:
            # 检查用户是否有足够的多模态解析配额
            # 每个文件消耗一个配额
            has_quota = await QuotaService.check_quota(
                user_id=str(dataset_owner_id),
                resource_type=UsageType.MULTI_MODAL_PARSING,
                amount_to_consume=len(file_id_list),
            )

            if not has_quota:
                # 获取配额详情以显示更有用的错误消息
                available, total, used = await QuotaService.get_effective_quota(
                    user_id=str(dataset_owner_id),
                    resource_type=UsageType.MULTI_MODAL_PARSING,
                )

                # 根据配额类型格式化错误消息
                if total is None:  # 无限配额的情况
                    # 这种情况不应该发生，因为如果是无限配额，has_quota应该为True
                    quota_message = "Multi-modal parsing quota exceeded."
                else:
                    quota_message = f"Multi-modal parsing quota exceeded. You have used {used} out of {total} multi-modal parsings in your current plan. Cannot parse an additional {len(file_id_list)} files.."

                logging.warning(
                    f"User {dataset_owner_id} has exceeded their multi-modal parsing quota. Available: {available}, Total: {total}, Used: {used}"
                )
                raise QuotaException(
                    detail=f"{quota_message} Please upgrade your plan to continue.",
                )

            logging.info(
                f"User {dataset_owner_id} multi-modal parsing quota check passed. Proceeding with parsing {len(file_id_list)} files."
            )

        except QuotaException as e:
            # 重新抛出配额异常
            raise e
        except Exception as e:
            # 记录其他异常但不阻止请求
            logging.error(f"Error checking multi-modal parsing quota: {e}")
            sentry_sdk_capture_exception(e)

    # upload的文件重学
    if settings.NEW_TRAIN_ENABLED:
        from mygpt.train import train

        result = await train.refresh_list(
            dataset_id, file_id_list, DatasourceType.FIFE, learn_type=learn_type
        )
        return result

    dataset = await Dataset.get_or_none(id=dataset_id, deleted_at__isnull=True)
    if not dataset:
        raise NotFoundException("Dataset not found")
    vector_storage_type = VectorStorageType(
        dataset.metadata.get("vector_storage", VectorStorageType.PINECONE)
    )
    if vector_storage_type == VectorStorageType.QDRANT:
        raise OperationFailedException(
            "Upgrade vector storage type to qdrant_one_collection"
        )
    embedding_params = dataset.get_embedding_params()
    vector_storage = VectorStorageFactory.get_instance(storage_type=vector_storage_type)
    collection_name = get_collection_name(
        embedding_params.model_name, embedding_params.dimensions
    )

    file_results: List[dict] = []

    file_objs = await VectorFile.filter(id__in=file_id_list)
    if len(file_objs) != len(file_id_list):
        raise OperationFailedException(f"Number of files does not match")

    for file in file_objs:
        if file.file_size > MAX_FILE_SIZE:
            raise OperationFailedException(
                f"File {file.filename} size {file.file_size} exceeds the limit of {MAX_FILE_SIZE} bytes"
            )

        if file.file_size == 0:
            raise OperationFailedException(f"Empty file not allowed: {file.filename}")
        unstructured_type = filename_to_unstructured_type(file.filename)
        if unstructured_type == UnstructuredType.UNKNOWN:
            raise OperationFailedException(
                f"File extension not supported for file {file.filename}"
            )
        filename = file.filename
        file_key = file.key
        if not file_key:
            raise NotFoundException(f"File key not found, filename:{filename}")

        file_results.append(
            {
                "filename": filename,
                "index": file.index_ids,
            }
        )

    try:
        await VectorFile.filter(id__in=file_id_list).update(
            file_status=VectorFileStatus.READY,
            failed_reason="",
            token_count=0,
            characters_count=0,
            metadata={},
            index_ids={},
            content_hash="",
            resources={},  # 图片资源
            learn_type=learn_type,
            updated_at=datetime.now(),
        )

        # 注意这里开始内存对象与数据库不同步，注意后续使用内存对象
        for file_obj in file_objs:
            file_obj.learn_type = learn_type

        await Dataset.filter(id=dataset_id).update(
            updated_at=datetime.now(),
        )

        os_client = get_open_search_client()

        async def handle(file_objs):
            for index, file_obj in enumerate(file_objs):
                # 修复docx生成预览pdf后覆盖原文件key的问题
                file_ext = os.path.splitext(file_obj.filename)[1].lower()
                if (
                    file_ext in [".doc", ".docx"]
                    and file_obj.key
                    and file_obj.key.lower().endswith(file_ext) == False
                ):
                    key_before = file_obj.key
                    key_recovered = (
                        os.path.splitext(file_obj.key)[0]
                        + os.path.splitext(file_obj.filename)[1]
                    )
                    file_obj.key = key_recovered
                    await VectorFile.filter(id__in=[str(file.id)]).update(
                        key=key_recovered
                    )
                    logging.warning(
                        f"vector file {file_obj.id} key recoverd: {key_before} -> {key_recovered}"
                    )

                task_files = []
                file_key = file_obj.key
                filename = file_obj.filename
                index_ids = file_obj.index_ids
                file_location = get_file_tmp_location(file_key)
                try:
                    # 删除索引
                    tasks = []
                    if file_key:
                        tasks.append(download_tmp_file_from_s3(file_key))

                    tasks.append(file_obj.delete_search_index())
                    if tasks:
                        task_results = await asyncio.gather(*tasks)
                        if len(task_results) > 0 and os.path.exists(task_results[0]):
                            download_tmpfile = task_results[0]
                            if settings.FILE_STORAGE == FileStorage.LOCATION:
                                # 重命名文件
                                shutil.copy(download_tmpfile, file_location)
                        else:
                            raise NotFoundException(
                                f"File not exist, filename: {filename}"
                            )

                    task_files.append((file_obj, file_location))
                    asyncio.create_task(upload_file_handler(dataset, task_files))
                except Exception as e:
                    logging.error(f"refresh_dataset_file error: {e}, file:{filename}")
                    logging.error(traceback.format_exc())

                    await VectorFile.filter(id=file_obj.id).update(
                        file_status=VectorFileStatus.FAIL,
                        failed_reason=str(e),
                        updated_at=datetime.now(),
                    )

        asyncio.create_task(handle(file_objs))
    except Exception as e:
        logging.error(f"refresh_dataset_file error: {e}")
        logging.error(traceback.format_exc())


@datasets_api_router.put(
    "/{dataset_id}/lark_files/refresh_list",
    summary="Refresh dataset upload file list",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_dataset_admin)],
)
@router.put(
    "/{dataset_id}/lark_files/refresh_list",
    summary="Refresh dataset upload file list",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_dataset_admin)],
)
async def refresh_dataset_lark_file_list(
    dataset_id: UUID,
    file_id_list: list[UUID] = Body(embed=True),
    learn_type: int = Body(default=0, embed=True),
    integration_rule_id: str = Body(default=None, embed=True),
):
    # 检查是否为多模态解析（learn_type > 1 && < 100）
    is_multimodal_parsing = learn_type is not None and 1 < learn_type < 100

    # 检查多模态解析配额
    if (
        is_multimodal_parsing
        and settings.QUOTA_CHECK
        and not settings.IS_USE_LOCAL_VLLM
    ):
        from mygpt.models import UsageType
        from mygpt.services.quota_service import QuotaService, QuotaException

        # 获取数据集所有者
        dataset = await Dataset.get_or_none(
            id=dataset_id, deleted_at__isnull=True
        ).prefetch_related("user")
        if not dataset:
            raise NotFoundException("Dataset not found")
        dataset_owner_id = dataset.user.id

        try:
            # 检查用户是否有足够的多模态解析配额
            # 每个文件消耗一个配额
            has_quota = await QuotaService.check_quota(
                user_id=str(dataset_owner_id),
                resource_type=UsageType.MULTI_MODAL_PARSING,
                amount_to_consume=len(file_id_list),
            )

            if not has_quota:
                # 获取配额详情以显示更有用的错误消息
                available, total, used = await QuotaService.get_effective_quota(
                    user_id=str(dataset_owner_id),
                    resource_type=UsageType.MULTI_MODAL_PARSING,
                )

                # 根据配额类型格式化错误消息
                if total is None:  # 无限配额的情况
                    # 这种情况不应该发生，因为如果是无限配额，has_quota应该为True
                    quota_message = "Multi-modal parsing quota exceeded."
                else:
                    quota_message = f"Multi-modal parsing quota exceeded. You have used {used} out of {total} multi-modal parsings in your current plan."

                logging.warning(
                    f"User {dataset_owner_id} has exceeded their multi-modal parsing quota. Available: {available}, Total: {total}, Used: {used}"
                )
                raise QuotaException(
                    status_code=402,  # Payment Required
                    detail=f"{quota_message} Please upgrade your plan to continue.",
                )

            logging.info(
                f"User {dataset_owner_id} multi-modal parsing quota check passed. Proceeding with parsing {len(file_id_list)} files."
            )

        except QuotaException as e:
            # 重新抛出配额异常
            raise e
        except Exception as e:
            # 记录其他异常但不阻止请求
            logging.error(f"Error checking multi-modal parsing quota: {e}")
            sentry_sdk_capture_exception(e)
    dataset = await Dataset.get_or_none(id=dataset_id, deleted_at__isnull=True)
    if not dataset:
        raise NotFoundException("Dataset not found")
    vector_storage_type = VectorStorageType(
        dataset.metadata.get("vector_storage", VectorStorageType.PINECONE)
    )
    if vector_storage_type == VectorStorageType.QDRANT:
        raise OperationFailedException(
            "Upgrade vector storage type to qdrant_one_collection"
        )
    embedding_params = dataset.get_embedding_params()
    vector_storage = VectorStorageFactory.get_instance(storage_type=vector_storage_type)
    collection_name = get_collection_name(
        embedding_params.model_name, embedding_params.dimensions
    )

    integration_rule = await IntegrationRule.get(id=integration_rule_id)
    if not integration_rule:
        raise ValueError(f"IntegrationRule not found")
    lark_integration_rule = await LarkIntegrationRule.get(
        integration_rule_id=integration_rule.id, deleted_at__isnull=True
    )
    if not lark_integration_rule:
        raise ValueError(
            f"LarkIntegrationRule not found for IntegrationRule {integration_rule.id}"
        )

    try:
        await VectorFile.filter(id__in=file_id_list).update(
            file_status=VectorFileStatus.READY,
            failed_reason="",
            token_count=0,
            characters_count=0,
            metadata={},
            index_ids={},
            content_hash="",
            resources={},  # 图片资源
            learn_type=learn_type,
            updated_at=datetime.now(),
        )

        file_objs = await VectorFile.filter(id__in=file_id_list).prefetch_related(
            "lark_file"
        )

        await Dataset.filter(id=dataset_id).update(
            updated_at=datetime.now(),
        )
        os_client = get_open_search_client()

        # 消费多模态解析配额
        if (
            is_multimodal_parsing
            and settings.QUOTA_CHECK
            and not settings.IS_USE_LOCAL_VLLM
        ):
            try:
                from mygpt.models import UsageType
                from mygpt.services.quota_service import QuotaService

                # 消费多模态解析配额，每个文件消耗一个配额
                asyncio.create_task(
                    QuotaService.consume_quota(
                        user_id=str(dataset.user.id),
                        resource_type=UsageType.MULTI_MODAL_PARSING,
                        amount_to_consume=len(file_id_list),
                    )
                )
                logging.info(
                    f"Consumed {len(file_id_list)} multi-modal parsing quota for user {dataset.user.id}"
                )
            except Exception as e:
                # 记录错误但不阻止操作
                logging.error(f"Error consuming multi-modal parsing quota: {e}")
                sentry_sdk_capture_exception(e)

        async def handle(file_objs: list[VectorFile]):
            for index, file_obj in enumerate(file_objs):
                task_files = []
                file_key = file_obj.key
                filename = file_obj.filename
                index_ids = file_obj.index_ids
                lark_file = file_obj.lark_file

                try:
                    # 删除索引
                    tasks = []
                    if lark_file.type == "file":
                        tasks.append(
                            download_single_file(
                                lark_integration_rule.app_id,
                                lark_integration_rule.app_secret,
                                lark_file.token,
                            )
                        )
                        tasks.append(
                            get_file_metadata(
                                lark_integration_rule.app_id,
                                lark_integration_rule.app_secret,
                                lark_file.token,
                                lark_file.type,
                            )
                        )

                    elif lark_file.type in lark_export_file_extension_dict:
                        logging.info(
                            f"non file type: {lark_file.type}, {lark_file.name}, export_and_download"
                        )
                        tasks.append(
                            re_export_and_download_single_file(
                                lark_integration_rule.app_id,
                                lark_integration_rule.app_secret,
                                lark_file,
                            )
                        )
                        tasks.append(
                            get_file_metadata(
                                lark_integration_rule.app_id,
                                lark_integration_rule.app_secret,
                                lark_file.token,
                                lark_file.type,
                            )
                        )
                    else:
                        failed_reason = (
                            f"Cannot export '{lark_file.type}' file '{lark_file.name}' as Lark only "
                            f"supports doc, sheet, bitable, and docx formats."
                        )
                        raise Exception(failed_reason)

                    tasks.append(file_obj.delete_search_index())

                    if file_key:
                        tasks.append(delete_files_from_s3(file_key))

                    if tasks:
                        task_results = await asyncio.gather(
                            *tasks, return_exceptions=True
                        )

                        file_bytes = task_results[0]
                        file_metadata = task_results[1]

                        if isinstance(file_bytes, Exception):
                            if str(file_bytes) == "None":
                                raise Exception("Failed to download the file")
                            else:
                                raise Exception(
                                    f"Failed to download the file: {str(file_bytes)}"
                                )

                        if isinstance(file_metadata, Exception):
                            raise Exception(
                                f"Failed to obtain file metadata: {str(file_metadata)}"
                            )

                        if len(file_bytes) > MAX_FILE_SIZE:
                            raise ValueError(
                                f"File {lark_file.name} size {len(file_bytes)} exceeds the limit of {MAX_FILE_SIZE} bytes"
                            )

                        """
                        if len(file_bytes) == 0:
                            raise ValueError(f"Empty file not allowed: {filename}")
                        """

                        file_obj.file_size = len(file_bytes)
                        await file_obj.save()

                        filename = get_vector_file_name_for_lark_file(file_metadata)
                        file_location = get_file_tmp_location(
                            f"{file_obj.id}__{filename}"
                        )

                        with open(file_location, "wb") as f:
                            f.write(file_bytes)

                        await LarkFile.filter(vector_file_id=file_obj.id).update(
                            name=file_metadata.name,
                            type=file_metadata.type,
                            token=file_metadata.token,
                            parent_token=file_metadata.parent_token,
                            created_time=file_metadata.created_time,
                            modified_time=file_metadata.modified_time,
                            owner_id=file_metadata.owner_id,
                            url=file_metadata.url,
                            updated_at=datetime.now(),
                        )

                        unstructured_type = filename_to_unstructured_type(filename)
                        if unstructured_type == UnstructuredType.UNKNOWN:
                            raise ValueError(
                                f"File extension not supported for file {filename}"
                            )

                        name, suffix = os.path.splitext(filename)
                        unstructured_type = filename_to_unstructured_type(filename)
                        if unstructured_type == UnstructuredType.TXT:
                            pdf_content = await text_to_pdf(file_location)
                            file_location = get_file_tmp_location(
                                f"{file_obj.id}__{name}.pdf"
                            )
                            suffix = ".pdf"
                            with open(file_location, "wb") as f:
                                f.write(pdf_content)

                        upload_key = f"{file_obj.id.hex}{suffix}"

                        await upload_binary(upload_key, file_location)
                        await VectorFile.filter(id=file_obj.id).update(
                            key=upload_key,
                            filename=get_vector_file_name_for_lark_file(file_metadata),
                            title=file_metadata.name,
                        )

                    task_files.append((file_obj, file_location))
                    asyncio.create_task(upload_file_handler(dataset, task_files))

                except (ValueError, Exception) as e:
                    logging.warning(f"refresh_dataset_file error: {e}, file:{filename}")
                    logging.warning(traceback.format_exc())

                    await VectorFile.filter(id=file_obj.id).update(
                        file_status=VectorFileStatus.FAIL,
                        failed_reason=str(e),
                        updated_at=datetime.now(),
                    )

        asyncio.create_task(handle(file_objs))
    except Exception as e:
        logging.error(f"refresh_dataset_file error: {e}")
        logging.error(traceback.format_exc())


@datasets_api_router.get(
    "/{dataset_id}/files/{file_id}/chunks",
    summary="Get dataset file chunks",
    response_model=Page[Embeddings],
    dependencies=[Depends(auth.implicit_scheme)],
)
@router.get(
    "/{dataset_id}/files/{file_id}/chunks",
    summary="Get dataset file chunks",
    response_model=Page[Embeddings],
    dependencies=[Depends(auth.implicit_scheme)],
)
async def get_dataset_file_chunks(
    dataset_id: UUID,
    file_id: UUID,
    query: str = "",
    user: User = Depends(current_user),
    params: ListAPIParams = Depends(),
    dataset_obj: Dataset = Depends(verify_dataset_access),
):
    """Get dataset file chunks"""

    vectorfile = await VectorFile.get_or_none(
        id=file_id, dataset_id=dataset_id, deleted_at__isnull=True
    )
    if not vectorfile:
        raise NotFoundException("File not found")
    if vectorfile.file_status != VectorFileStatus.COMPLETE:
        raise OperationFailedException("File not ready")
    embedding_params = dataset_obj.get_embedding_params()
    vector_storage = VectorStorageFactory.get_instance()

    offset = (params.page - 1) * params.size
    limit = params.size
    base_filters = [
        models.Filter(
            must=[
                models.FieldCondition(
                    key="group_id",
                    match=models.MatchValue(value=str(dataset_id)),
                ),
                models.FieldCondition(
                    key="metadata.file_id",
                    match=models.MatchValue(value=str(vectorfile.id)),
                ),
                models.IsEmptyCondition(
                    is_empty=models.PayloadField(key="metadata.short_chunk"),
                ),
            ]
        )
    ]

    page_filter = models.Filter(
        must=[
            models.FieldCondition(
                key="metadata.chunk_index",
                range=models.Range(
                    gt=None,
                    gte=offset,
                    lt=offset + limit,
                    lte=None,
                ),
            )
        ]
    )
    query_filters = []
    query_filters.extend(base_filters)
    if not query:
        query_filters.append(page_filter)
        offset = 0
    source_lang = dataset_obj.metadata.get("source_lang", "en")
    filters = models.Filter(must=query_filters)
    if query:
        resp, message_tokens = await user_intent_detect(
            session_id="",
            question=query,
            ai_language=source_lang,
            user_id=str(user.id),
        )
        if resp:
            query = resp.get("query_key_words", query)

    else:
        query = "*"
    query_vectors = await EmbeddingFactory.embedding(
        [query],
        **embedding_params.dict(),
    )
    query_vector = query_vectors[0]
    collection_name = get_collection_name(
        embedding_params.model_name, embedding_params.dimensions
    )
    vector_datas = await vector_storage.query_list(
        query_vector,
        collection_name,
        limit=limit,
        filters=filters,
    )
    if query == "*":
        vector_datas = sorted(vector_datas, key=lambda x: x.metadata["chunk_index"])
    for data in vector_datas:
        data.metadata["tokens"] = num_tokens_from_string(data.text)
    total_filters = []
    total_filters.extend(base_filters)
    filters = models.Filter(must=total_filters)
    total = await vector_storage.index_count(collection_name, filters=filters.dict())
    page = params.page
    page_size = params.size
    items = vector_datas
    return {
        "total": total,
        "page": page,
        "size": page_size,
        "pages": total // page_size + 1,
        "items": items,
    }


dataset_locks = {}


async def set_robot_source_lang(dataset_id, source_lang):
    robots = await Robot.filter(datasets__id=dataset_id).prefetch_related(
        "robotconfigs"
    )
    for robot in robots:
        no_source_lang = True
        for robotconfig in robot.robotconfigs:
            if robotconfig.key == AIConfigType.SOURCE_LANG.value and robotconfig.value:
                no_source_lang = False
                break
        if no_source_lang:
            await RobotConfig.set_config(
                robot.id, AIConfigType.SOURCE_LANG, source_lang
            )


async def unset_robot_source_lang(robot_id):
    # 在解除绑定前调用
    if await Dataset.filter(robots__id=robot_id).count() == 1:
        await RobotConfig.set_config(robot_id, AIConfigType.SOURCE_LANG, None)


async def init_dataset(dataset_id: str, documents):
    """
    初始化数据集,注意这里有加锁
    """
    if dataset_id not in dataset_locks:
        dataset_locks[dataset_id] = asyncio.Lock()
    async with dataset_locks[dataset_id]:
        dataset_obj = await Dataset.get_or_none(id=dataset_id)
        if "source_lang" not in dataset_obj.metadata:
            # 初始化数据集语言
            page_content = ""
            try:
                for document in documents:
                    if len(document.page_content) < 300:
                        page_content = document.page_content
                        continue
                    page_content = document.page_content
                    break
                lang, _ = await language_detect(page_content, trust_small_model=True)
            except Exception as e:
                logging.warning(
                    f"fail to detect language in error:{e} page_content:{page_content}"
                )
                lang = "en"
            dataset_obj.metadata["source_lang"] = lang
            await set_robot_source_lang(dataset_id, lang)
            await dataset_obj.save()
        os_client = get_open_search_client()
        if not os_client:
            dataset_obj.metadata["opensearch_init"] = True
            await dataset_obj.save()
        if "opensearch_init" not in dataset_obj.metadata and os_client:
            # 初始化Opensearch索引
            analyzer = get_analyzer(dataset_obj.metadata["source_lang"])
            await asyncio.get_event_loop().run_in_executor(
                None, os_client.create_index, dataset_obj.collection_name, analyzer
            )
            dataset_obj.metadata["opensearch_init"] = True
            await dataset_obj.save()
        return dataset_obj


logging.debug(
    f"FILE_LEARNING_TASKS_PER_SERVER: {FILE_LEARNING_TASKS_PER_SERVER}, {semaphore_old}, FILE_LEARNING_TASKS_PER_SERVER_ADV: {FILE_LEARNING_TASKS_PER_SERVER_ADV}, {semaphore_adv}",
)


# condition = asyncio.Condition()


@datasets_api_router.post(
    "/{dataset_id}/files",
    summary="Add file to dataset",
    dependencies=[Depends(auth.implicit_scheme)],
)
@router.post(
    "/{dataset_id}/files",
    summary="Add file to dataset",
    dependencies=[Depends(auth.implicit_scheme)],
)
async def add_file_to_dataset(
    dataset_id: UUID,
    learn_type: Optional[int] = settings.VECTOR_FILE_DEFAULT_LEARN_TYPE,
    files: list[UploadFile] = File(...),
    dataset_obj=Depends(verify_dataset_access),
):
    # 文件新增上传学习
    if settings.NEW_TRAIN_ENABLED:
        from mygpt.train import train

        return await train.add_files(str(dataset_id), learn_type, files)

    if len(files) > settings.MAX_FILES_ALLOWED:
        raise OperationFailedException(
            f"Too many files, please limit the number of files to {settings.MAX_FILES_ALLOWED}"
        )
    for file in files:
        if file.size > MAX_FILE_SIZE:
            raise OperationFailedException(
                f"File {file.filename} size {file.size} exceeds the limit of {MAX_FILE_SIZE} bytes"
            )

        if file.size == 0:
            raise OperationFailedException(f"Empty file not allowed: {file.filename}")
        unstructured_type = filename_to_unstructured_type(file.filename)
        if unstructured_type == UnstructuredType.UNKNOWN:
            raise OperationFailedException(
                f"File extension not supported for file {file.filename}"
            )
    file_objs: List[VectorFile] = []
    file_results: List[dict] = []

    for file in files:
        filename = file.filename
        failed_reason = None
        file_obj_id = uuid.uuid4()
        file_location = get_file_tmp_location(filename)
        with open(file_location, "wb") as file_object:
            await asyncio.to_thread(shutil.copyfileobj, file.file, file_object)
        file_results.append(
            {
                "path": file_location,
                "filename": filename,
            }
        )
        file_obj = await VectorFile.create(
            id=file_obj_id,
            dataset_id=dataset_id,
            filename=filename,
            file_type=VectorFileType.UPLOAD.value,
            file_status=VectorFileStatus.READY,
            file_size=file.size,
            failed_reason=failed_reason,
            key="",
        )
        file_objs.append(file_obj)
    await Dataset.filter(id=dataset_obj.id).update(
        updated_at=datetime.now(),
    )

    async def handle_file():
        task_files = []
        for index, file_result in enumerate(file_results):
            filename = file_result.get("filename")

            name, suffix = os.path.splitext(filename)
            file_location = file_result.get("path")
            # txt 转 pdf

            unstructured_type = filename_to_unstructured_type(filename)
            if unstructured_type == UnstructuredType.TXT:
                pdf_content = await text_to_pdf(file_location)
                file_location = get_file_tmp_location(f"{name}.pdf")
                suffix = ".pdf"
                with open(file_location, "wb") as file_object:
                    file_object.write(pdf_content)

            file_obj = file_objs[index]

            upload_key = f"{file_obj.id.hex}{suffix}"
            try:
                await upload_binary(upload_key, file_location)
            except Exception as e:
                logging.warning(
                    f"fail to upload file in error 444:{e}; {traceback.format_exc()}"
                )
                await VectorFile.filter(id=file_obj.id).update(
                    file_status=VectorFileStatus.FAIL,
                    failed_reason=str(e),
                )
                continue
            await VectorFile.filter(id=file_obj.id).update(
                key=upload_key,
            )
            task_files.append((file_obj, file_location))
        await upload_file_handler(dataset_obj, task_files)

    asyncio.create_task(handle_file())
    result_objs = []
    for file_obj in file_objs:
        result_objs.append(await VectorFileOut.from_tortoise_orm(file_obj))
    return result_objs


@datasets_api_router.post(
    "/{dataset_id}/links",
    summary="Add link to dataset",
    dependencies=[Depends(auth.implicit_scheme)],
    status_code=HTTP_204_NO_CONTENT,
)
@router.post(
    "/{dataset_id}/links",
    summary="Add link to dataset",
    dependencies=[Depends(auth.implicit_scheme)],
    status_code=HTTP_204_NO_CONTENT,
)
async def add_link_to_dataset(
    links: list[str] = Body(..., embed=True),
    spa: bool = Body(False, embed=True),
    max_images: int = Body(10, embed=True),
    links_type: DatasourceType = DatasourceType.WEBSITE,
    dataset_obj: Dataset = Depends(verify_dataset_access),
):
    dataset_owner_id = (await dataset_obj.user).id

    # 检查网页学习配额
    if settings.QUOTA_CHECK and not settings.IS_USE_LOCAL_VLLM:
        from mygpt.models import UsageType
        from mygpt.services.quota_service import QuotaService
        from mygpt.exceptions import QuotaException

        # 获取数据集所有者

        try:
            # 检查用户是否有足够的网页学习配额
            # 每个链接消耗一个配额
            has_quota = await QuotaService.check_quota(
                user_id=str(dataset_owner_id),
                resource_type=UsageType.WEB_PAGE_LEARNED,
                amount_to_consume=len(links),
            )

            if not has_quota:
                # 获取配额详情以显示更有用的错误消息
                available, total, used = await QuotaService.get_effective_quota(
                    user_id=str(dataset_owner_id),
                    resource_type=UsageType.WEB_PAGE_LEARNED,
                )

                # 根据配额类型格式化错误消息
                if total is None:  # 无限配额的情况
                    # 这种情况不应该发生，因为如果是无限配额，has_quota应该为True
                    quota_message = "Web page learning quota exceeded."
                else:
                    quota_message = f"Web page learning quota exceeded. You have used {used} out of {total} web pages in your current plan."

                logging.warning(
                    f"User {dataset_owner_id} has exceeded their web page learning quota. Available: {available}, Total: {total}, Used: {used}"
                )
                raise QuotaException(
                    status_code=402,  # Payment Required
                    detail=f"{quota_message} Please upgrade your plan to continue.",
                )

            logging.info(
                f"User {dataset_owner_id} web page learning quota check passed. Proceeding with learning {len(links)} web pages."
            )

        except QuotaException as e:
            # 重新抛出配额异常
            raise e
        except Exception as e:
            # 记录其他异常但不阻止请求
            logging.error(
                f"Error checking web page learning quota: {e}, {traceback.format_exc()}"
            )
            sentry_sdk_capture_exception(e)

    # 网页新增上传学习
    if settings.NEW_TRAIN_ENABLED:
        from mygpt.train import train

        return await train.add_links(
            dataset_obj.id, links, spa, max_images, DatasourceType.WEBSITE
        )

    # 添加单个或多个网页的学习，或者整个网站递归爬行得到url列表后的学习
    if len(links) == 0:
        raise InvalidParameterException()
    site_crawler = SiteCrawler(is_spa=spa)
    doc: Document = None
    async with aiohttp.ClientSession() as session:
        for link in links:
            try:
                doc = await site_crawler.parse_base(link, client=session)
                if doc is not None:
                    break
            except Exception:
                continue
    if doc is None:
        raise OperationFailedException("No content found in links")
    chunks = site_crawler.to_chunk(doc)
    docs = [Document(page_content=doc.content) for doc in chunks]
    await init_dataset(str(dataset_obj.id), docs)
    vectorfiles, fileids = [], []
    for url in links:
        file_type = VectorFileType.HTML
        if links_type == DatasourceType.SITEMAP:
            file_type = VectorFileType.SITEMAP
        elif links_type == DatasourceType.GITBOOK:
            file_type = VectorFileType.GITBOOK
        if url.endswith(".pdf"):
            file_type = VectorFileType.HTML_PDF
        vector_file_obj = await VectorFile.create(
            dataset=dataset_obj,
            filename=url[:2048],
            file_type=file_type,
            metadata={"spa": spa, "max_images": max_images},
            file_status=VectorFileStatus.READY,
        )
        vectorfiles.append(vector_file_obj)
        fileids.append(str(vector_file_obj.id))
    task_id = str(uuid.uuid4())
    await Dataset.filter(id=dataset_obj.id).update(
        updated_at=datetime.now(),
    )
    vector_storage_type = VectorStorageType(
        dataset_obj.metadata.get("vector_storage", VectorStorageType.PINECONE)
    )
    if vector_storage_type == VectorStorageType.QDRANT:
        raise OperationFailedException(
            "Upgrade vector storage type to qdrant_one_collection"
        )
    asyncio.create_task(handler_vectorfiles(vectorfiles, callback))

    # 消费网页学习配额
    if settings.QUOTA_CHECK and not settings.IS_USE_LOCAL_VLLM:
        try:
            from mygpt.models import UsageType
            from mygpt.services.quota_service import QuotaService

            # 消费网页学习配额，每个链接消耗一个配额
            asyncio.create_task(
                QuotaService.consume_quota(
                    user_id=dataset_owner_id,
                    resource_type=UsageType.WEB_PAGE_LEARNED,
                    amount_to_consume=len(links),
                )
            )
            logging.info(
                f"Consumed {len(links)} web page learning quota for user {dataset_owner_id}"
            )
        except Exception as e:
            # 记录错误但不阻止操作
            logging.error(f"Error consuming web page learning quota: {e}")
            sentry_sdk_capture_exception(e)


@router.get(
    "/{dataset_id}/events",
)
async def fetch_events(
    dataset_id: str,
) -> EventSourceResponse:
    logging.info(f"fetch_events: {dataset_id}")
    stream = Stream(stream_id=uuid.uuid4(), stream_name=dataset_id)
    _streams.append(stream)
    await stream.ping()
    return EventSourceResponse(stream)


@datasets_api_router.post(
    "/{dataset_id}/configs",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[Depends(auth.implicit_scheme)],
)
@router.post(
    "/{dataset_id}/configs",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[Depends(auth.implicit_scheme)],
)
async def update_dataset_configs(
    dataset_id: UUID,
    metadata: dict = Body(..., embed=True),
    dataset_obj=Depends(verify_dataset_access),
):
    old_metadata = dataset_obj.metadata
    for item in metadata:
        if item in ["vector_storage", "embeddings_model", "faq_vector_storage"]:
            raise OperationFailedException(
                "Can not update vector_storage, embeddings_model, faq_vector_storage"
            )

    old_metadata.update(metadata)
    await Dataset.filter(id=dataset_id).update(
        metadata=metadata, updated_at=datetime.now()
    )


@router.post(
    "/{dataset_id}/status",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_dataset_access),
    ],
)
async def update_dataset_status(
    dataset_id: UUID,
    status: DATASET_STATUS = Body(..., embed=True),
):
    await Dataset.filter(id=dataset_id).update(data_status=status)


@router.post(
    "/{dataset_id}/full_update",
    status_code=starlette.status.HTTP_202_ACCEPTED,
    summary="Update full dataset, totally retrain webpages and docs like creating a new dataset using default settings, url rules, embeddings model, database storage, etc.",
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_dataset_access),
    ],
)
async def retrain_dataset_web_pages(
    dataset_id: UUID, user_obj: User = Depends(current_user)
):
    pass


@router.post(
    "/{dataset_id}/update_web_pages",
    status_code=starlette.status.HTTP_202_ACCEPTED,
    summary="Update dataset web pages",
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_dataset_access),
    ],
)
async def retrain_dataset_web_pages(
    dataset_id: UUID, user_obj: User = Depends(current_user)
):
    user_id = user_obj.user_id
    dataset_id_str = str(dataset_id)
    dataset_info = await Dataset.get_or_none(
        user_id=user_id, id=dataset_id_str, deleted_at=None
    ).prefetch_related("user")
    if not dataset_info:
        raise NotFoundException(NOT_FOUND_OR_NO_ACCESS_TO_DATASET)

    # 检查网页学习配额
    if settings.QUOTA_CHECK and not settings.IS_USE_LOCAL_VLLM:
        from mygpt.models import UsageType
        from mygpt.services.quota_service import QuotaService, QuotaException

        # 获取数据集所有者
        dataset_owner_id = dataset_info.user.id

        # 获取HTML类型的文件数量
        html_vector_files = await VectorFile.filter(
            dataset_id=dataset_id_str, file_type=VectorFileType.HTML, deleted_at=None
        )
        web_page_count = len(html_vector_files)

        if web_page_count > 0:
            try:
                # 检查用户是否有足够的网页学习配额
                # 每个网页消耗一个配额
                has_quota = await QuotaService.check_quota(
                    user_id=str(dataset_owner_id),
                    resource_type=UsageType.WEB_PAGE_LEARNED,
                    amount_to_consume=web_page_count,
                )

                if not has_quota:
                    # 获取配额详情以显示更有用的错误消息
                    available, total, used = await QuotaService.get_effective_quota(
                        user_id=str(dataset_owner_id),
                        resource_type=UsageType.WEB_PAGE_LEARNED,
                    )

                    # 根据配额类型格式化错误消息
                    if total is None:  # 无限配额的情况
                        # 这种情况不应该发生，因为如果是无限配额，has_quota应该为True
                        quota_message = "Web page learning quota exceeded."
                    else:
                        quota_message = f"Web page learning quota exceeded. You have used {used} out of {total} web pages in dataset owner's current plan."

                    logging.warning(
                        f"User {dataset_owner_id} has exceeded their web page learning quota. Available: {available}, Total: {total}, Used: {used}"
                    )
                    raise QuotaException(
                        status_code=402,  # Payment Required
                        detail=f"{quota_message} Please upgrade your plan to continue.",
                    )

                logging.info(
                    f"User {dataset_owner_id} web page learning quota check passed. Proceeding with retraining {web_page_count} web pages."
                )

            except QuotaException as e:
                # 重新抛出配额异常
                raise e
            except Exception as e:
                # 记录其他异常但不阻止请求
                logging.error(f"Error checking web page learning quota: {e}")
                sentry_sdk_capture_exception(e)

    # 检查网页学习配额
    if settings.QUOTA_CHECK and not settings.IS_USE_LOCAL_VLLM:
        from mygpt.models import UsageType
        from mygpt.services.quota_service import QuotaService, QuotaException

        # 获取数据集所有者
        dataset_owner_id = dataset_info.user.id

        # 获取HTML类型的文件数量
        html_vector_files = await VectorFile.filter(
            dataset_id=dataset_id_str, file_type=VectorFileType.HTML, deleted_at=None
        )
        web_page_count = len(html_vector_files)

        if web_page_count > 0:
            try:
                # 检查用户是否有足够的网页学习配额
                # 每个网页消耗一个配额
                has_quota = await QuotaService.check_quota(
                    user_id=str(dataset_owner_id),
                    resource_type=UsageType.WEB_PAGE_LEARNED,
                    amount_to_consume=web_page_count,
                )

                if not has_quota:
                    # 获取配额详情以显示更有用的错误消息
                    available, total, used = await QuotaService.get_effective_quota(
                        user_id=str(dataset_owner_id),
                        resource_type=UsageType.WEB_PAGE_LEARNED,
                    )

                    quota_message = f"Web page learning quota exceeded. You have used {used} out of {total} web pages in dataset owner's current plan."

                    logging.warning(
                        f"User {dataset_owner_id} has exceeded their web page learning quota. Available: {available}, Total: {total}, Used: {used}"
                    )
                    raise QuotaException(
                        detail=f"{quota_message} Please upgrade your plan to continue.",
                        status_code=402,  # Payment Required
                    )

                logging.info(
                    f"User {dataset_owner_id} web page learning quota check passed. Proceeding with retraining {web_page_count} web pages."
                )

            except QuotaException as e:
                # 重新抛出配额异常
                raise e
            except Exception as e:
                # 记录其他异常但不阻止请求
                logging.error(f"Error checking web page learning quota: {e}")
                sentry_sdk_capture_exception(e)

    html_vector_files = await VectorFile.filter(
        dataset_id=dataset_id_str, file_type=VectorFileType.HTML, deleted_at=None
    )
    embedding_params = dataset_info.get_embedding_params()
    file_list = []
    if html_vector_files:
        for vector_file in html_vector_files:
            file_list.append(
                {
                    "type": "vector_file",
                    "file_id": str(vector_file.id),
                    "vector_storage": dataset_info.metadata.get("vector_storage", ""),
                    "file_type": vector_file.file_type,
                    "dataset_id": dataset_id_str,
                    "embedding_params": embedding_params.json(),
                }
            )
    stream_name = "retrain_task_stream-" + dataset_id_str

    # new_dataset_info = dict(dataset_info)
    # del new_dataset_info["created_at"]
    # del new_dataset_info["updated_at"]

    async with RedisClient.get_client().pipeline() as pipe:
        for entry in file_list:
            pipe.xadd(stream_name, entry)
        await pipe.execute()
    # new_dataset = await Dataset.update_from_dict(**new_dataset_info)

    # 消费网页学习配额
    if settings.QUOTA_CHECK and not settings.IS_USE_LOCAL_VLLM and len(file_list) > 0:
        try:
            from mygpt.models import UsageType
            from mygpt.services.quota_service import QuotaService

            # 消费网页学习配额，每个网页消耗一个配额
            asyncio.create_task(
                QuotaService.consume_quota(
                    user_id=dataset_owner_id,  # Corrected: Was str(dataset_info.user_id)
                    resource_type=UsageType.WEB_PAGE_LEARNED,
                    amount_to_consume=len(file_list),
                )
            )
            logging.info(
                f"Consumed {len(file_list)} web page learning quota for user {dataset_owner_id}"
            )
        except Exception as e:
            # 记录错误但不阻止操作
            logging.error(f"Error consuming web page learning quota: {e}")
            sentry_sdk_capture_exception(e)

    return dataset_info


user_locks = {}


def generate_clone_name(dataset_name: str, existing_names: list[str]):
    base_name = f"{dataset_name}-copy"
    if base_name not in existing_names:
        return base_name

    pattern = re.compile(f"^{re.escape(base_name)}\((\d+)\)$")
    existing_versions = []

    for name in existing_names:
        match = pattern.match(name)
        if match:
            version = int(match.group(1))
            existing_versions.append(version)

    existing_versions = list(sorted(set(existing_versions)))
    existing_versions = [version for version in existing_versions if version != 1]

    # 寻找最小的空缺版本号
    next_version = 2
    for version in existing_versions:
        if version == next_version:
            next_version += 1
        else:
            break  # 找到空缺，退出循环

    return f"{base_name}({next_version})"


async def get_dataset_names_by_user(user_id: str) -> List[str]:
    dataset_names = await Dataset.filter(user_id=user_id, deleted_at=None).values_list(
        "name", flat=True
    )
    return dataset_names


def is_file_type(t: VectorFileType) -> bool:
    return t in (
        VectorFileType.UPLOAD,
        VectorFileType.HTML,
        VectorFileType.HTML_PDF,
        VectorFileType.SITEMAP,
        VectorFileType.GITBOOK,
    )


class EmbeddingModel(str, Enum):
    TEXT_EMBEDDING_ADA_002 = OpenAIModel.TEXT_EMBEDDING_ADA_002.value
    TEXT_EMBEDDING_3_SMALL = OpenAIModel.TEXT_EMBEDDING_3_SMALL.value
    TEXT_EMBEDDING_3_LARGE = OpenAIModel.TEXT_EMBEDDING_3_LARGE.value


@router.post(
    "/{dataset_id}/clone",
    summary="Clone this dataset for its current user",
    status_code=starlette.status.HTTP_202_ACCEPTED,
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_dataset_access),
    ],
)
async def clone_dataset(
    dataset_id: UUID,
    target_embedding_model: EmbeddingModel = Body(
        EmbeddingModel.TEXT_EMBEDDING_3_LARGE, embed=True
    ),
    user_obj: User = Depends(current_user),
):
    user_id = user_obj.user_id
    old_dataset_id = str(dataset_id)
    old_dataset_obj = await Dataset.get_or_none(
        user_id=user_id, id=old_dataset_id, deleted_at=None
    )
    if not old_dataset_obj:
        if IS_LOCAL:
            old_dataset_obj = await Dataset.get_or_none(
                id=old_dataset_id, deleted_at=None
            )
            if not old_dataset_obj:
                raise NotFoundException("dataset not found by id")
        else:
            raise NotFoundException(NOT_FOUND_OR_NO_ACCESS_TO_DATASET)
    old_dataset_obj.data_status = DATASET_STATUS.CLONING_SOURCE
    await old_dataset_obj.save(update_updated_at=False)
    try:
        file_list = []
        new_dataset_id = str(uuid.uuid4())
        vector_files = await VectorFile.filter(
            dataset_id=old_dataset_id, deleted_at=None
        )
        stream_name = "clone_task_stream-" + old_dataset_id + "__" + new_dataset_id
        embedding_params = old_dataset_obj.get_embedding_params()
        should_regenerate_embeddings = ""
        # 如果embedding模型和原来不同，才重新生成
        if embedding_params.model_name != target_embedding_model:
            should_regenerate_embeddings = "True"
        if vector_files:
            for vector_file in vector_files:
                if not is_file_type(vector_file.file_type):
                    continue
                # vector_file.file_type
                old_index_ids = vector_file.index_ids
                index_id_map = {}
                if old_index_ids:
                    for index_id in old_index_ids:
                        index_id_map[index_id] = str(uuid.uuid4())
                file_list.append(
                    {
                        "type": "vector_file",
                        "file_id": str(vector_file.id),
                        "new_file_id": str(uuid4()),
                        "new_dataset_id": new_dataset_id,
                        "vector_storage": old_dataset_obj.metadata.get(
                            "vector_storage", ""
                        ),
                        "file_type": vector_file.file_type,
                        "dataset_id": old_dataset_id,
                        "embedding_params": embedding_params.json(),
                        "index_id_map": json.dumps(index_id_map),
                        "target_embedding_model": target_embedding_model.value,
                        "should_regenerate_embeddings": should_regenerate_embeddings,
                    }
                )
        new_dataset_info = dict(old_dataset_obj)
        new_dataset_info["collection_name"] = str(new_dataset_id)
        new_dataset_info["id"] = new_dataset_id
        new_dataset_info["metadata"][
            "embedding_model_name"
        ] = target_embedding_model.value
        # 更新数据集储存方式，克隆完都更新为 VectorStorageType.QDRANT_ONE_COLLECTION
        new_dataset_info["metadata"][
            "vector_storage"
        ] = VectorStorageType.QDRANT_ONE_COLLECTION.value
        del new_dataset_info["created_at"]
        del new_dataset_info["updated_at"]
        if user_id not in user_locks:
            user_locks[user_id] = threading.Lock()
        with user_locks[user_id]:
            existing_names = await get_dataset_names_by_user(user_id)
            new_dataset_name = generate_clone_name(old_dataset_obj.name, existing_names)
            new_dataset_info["name"] = new_dataset_name
            new_dataset_info["data_status"] = DATASET_STATUS.CLONING_TARGET
            new_dataset_obj = await Dataset.create(**new_dataset_info)

        faqs = await Faqs.filter(dataset_id=dataset_id, deleted_at=None)
        if faqs:
            faq_id_map = {}
            for faq in faqs:
                faq_id_map[str(faq.id)] = str(uuid4())
            task = {
                "type": "faq",
                "new_dataset_id": new_dataset_id,
                "dataset_id": old_dataset_id,
                "faq_id_map": json.dumps(faq_id_map),
                "target_embedding_model": target_embedding_model.value,
                "should_regenerate_embeddings": should_regenerate_embeddings,
            }
            file_list.append(task)

        function_call_apis = await FunctionCallApi.filter(dataset_id=dataset_id)
        if function_call_apis:
            for function_call_api in function_call_apis:
                file_list.append(
                    {
                        "type": "function_call_api",
                        "id": str(function_call_api.id),
                        "new_dataset_id": new_dataset_id,
                        "new_id": str(uuid4()),
                        "target_embedding_model": target_embedding_model.value,
                        "should_regenerate_embeddings": should_regenerate_embeddings,
                    }
                )
        if file_list:
            try:
                async with RedisClient.get_client().pipeline() as pipe:
                    for entry in file_list:
                        await pipe.xadd(stream_name, entry)
                    await pipe.execute()
            except Exception as e:
                logging.error(e)
                raise e
        else:
            new_dataset_obj.data_status = DATASET_STATUS.READY
            await new_dataset_obj.save()
            old_dataset_obj.data_status = DATASET_STATUS.READY
            await old_dataset_obj.save(update_updated_at=False)
    except Exception as e:
        logging.error(e)
        logging.error(traceback.format_exc())
        old_dataset_obj.data_status = DATASET_STATUS.READY
        await old_dataset_obj.save(update_updated_at=False)
        new_dataset_obj.data_status = DATASET_STATUS.CLONING_FAILED
        await new_dataset_obj.save()
        raise OperationFailedException("Clone failed")
    return new_dataset_obj


@datasets_api_router.post(
    "/{ai_id}/datasets/{dataset_id}",
    summary="Bind dataset to ai",
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_dataset_owner),
        Depends(verify_admin_access),
    ],
    status_code=HTTP_204_NO_CONTENT,
)
@robot_router.post(
    "/{ai_id}/datasets/{dataset_id}",
    summary="Bind dataset to ai",
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_dataset_owner),
        Depends(verify_robots_owner),
    ],
    status_code=HTTP_204_NO_CONTENT,
)
async def bind_dataset_to_ai(
    ai_id: UUID,
    dataset_id: UUID,
):
    """Bind dataset to ai"""
    dataset_obj = await Dataset.get_or_none(
        id=dataset_id,
        deleted_at__isnull=True,
    ).prefetch_related("robots")
    if not dataset_obj:
        raise NotFoundException("Dataset not found")
    for robot in dataset_obj.robots:
        if robot.id == ai_id:
            raise OperationFailedException("Dataset already bound to ai")
    robot_obj = await Robot.get(id=ai_id).prefetch_related("robotconfigs")
    dataset_embedding_params = dataset_obj.get_embedding_params()
    if dataset_embedding_params != robot_obj.get_embedding_params():
        raise OperationFailedException("Dataset and robot embedding params mismatch")

    await dataset_obj.robots.add(robot_obj)
    source_lang = dataset_obj.metadata.get("source_lang")
    if source_lang:
        await set_robot_source_lang(dataset_id, source_lang)


@robot_router.post(
    "/{ai_id}/datasets",
    summary="Bind datasets to ai",
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_robots_owner),
    ],
    status_code=HTTP_204_NO_CONTENT,
)
async def bind_datasets_to_ai(
    ai_id: UUID,
    dataset_ids: list[UUID] = Body(..., embed=True),
):
    """Bind datasets to ai"""
    ai_obj = await Robot.get(id=ai_id).prefetch_related("robotconfigs")
    ai_embedding_params = ai_obj.get_embedding_params()
    dataset_count = await Dataset.filter(robots__id=ai_id).count()
    async with in_transaction("default") as conn:
        for dataset_id in dataset_ids:
            dataset_obj = await Dataset.get_or_none(
                id=dataset_id,
                deleted_at__isnull=True,
            ).prefetch_related("robots")
            if not dataset_obj:
                raise NotFoundException("Dataset not found")
            for robot in dataset_obj.robots:
                if robot.id == ai_id:
                    raise OperationFailedException("Dataset already bound to ai")
            if (
                not settings.IS_USE_LOCAL_VLLM
                and dataset_obj.get_embedding_params() != ai_embedding_params
            ):
                if dataset_count > 0:
                    raise OperationFailedException(
                        "Dataset and robot embedding params mismatch"
                    )
                else:
                    await RobotConfig.update_or_create(
                        robot_id=ai_id,
                        key=AIConfigType.EMBEDDING_MODEL_NAME,
                        defaults={
                            "value": dataset_obj.get_embedding_params().model_name
                        },
                    )
            await dataset_obj.robots.add(ai_obj, using_db=conn)
            source_lang = dataset_obj.metadata.get("source_lang")
            if source_lang:
                await set_robot_source_lang(dataset_id, source_lang)


@datasets_api_router.delete(
    "/{ai_id}/datasets/{dataset_id}",
    summary="Unbind dataset to ai",
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_dataset_owner),
        Depends(verify_robots_owner),
    ],
    status_code=HTTP_204_NO_CONTENT,
)
@robot_router.delete(
    "/{ai_id}/datasets/{dataset_id}",
    summary="Unbind dataset to ai",
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_dataset_owner),
        Depends(verify_robots_owner),
    ],
    status_code=HTTP_204_NO_CONTENT,
)
async def unbind_dataset_to_ai(
    ai_id: UUID,
    dataset_id: UUID,
):
    """Unbind dataset to ai"""
    dataset_obj = await Dataset.get_or_none(
        id=dataset_id,
        deleted_at__isnull=True,
    ).prefetch_related("robots")
    if not dataset_obj:
        raise NotFoundException("Dataset not found")
    for robot in dataset_obj.robots:
        if robot.id == ai_id:
            await unset_robot_source_lang(ai_id)
            await dataset_obj.robots.remove(robot)
            return
    raise OperationFailedException("Dataset not bound to ai")


@robot_router.delete(
    "/{ai_id}/datasets",
    summary="Unbind datasets to ai",
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_robots_owner),
    ],
    status_code=HTTP_204_NO_CONTENT,
)
async def unbind_datasets_to_ai(
    ai_id: UUID,
    dataset_ids: list[UUID] = Body(..., embed=True),
):
    """Unbind datasets to ai"""
    ai_obj = await Robot.get(id=ai_id)
    async with in_transaction("default") as conn:
        for dataset_id in dataset_ids:
            dataset_obj = await Dataset.get_or_none(
                id=dataset_id,
                deleted_at__isnull=True,
            ).prefetch_related("robots")
            if not dataset_obj:
                raise OperationFailedException("Dataset not found")
            for robot in dataset_obj.robots:
                if robot.id == ai_id:
                    await unset_robot_source_lang(ai_id)
                    await dataset_obj.robots.remove(ai_obj, using_db=conn)
                    break
            else:
                raise OperationFailedException("Dataset not bound to ai")


@datasets_api_router.get(
    "/{ai_id}/datasets",
    summary="Get ai datasets",
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_admin_access),
    ],
    response_model=Page[DatasetCloneProgressOut],
)
@robot_router.get(
    "/{ai_id}/datasets",
    summary="Get ai datasets",
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_admin_access),
    ],
    response_model=Page[DatasetCloneProgressOut],
)
async def get_ai_datasets(
    ai_id: UUID,
    params: ListAPIParams = Depends(),
):
    """Get ai datasets"""
    queryset = Dataset.filter(
        robots__id=ai_id,
        deleted_at__isnull=True,
    )
    dataset_info = await tortoise_paginate(queryset, params, ["robots__apis"])
    for dataset in dataset_info.items:
        dataset.clone_progress = await dataset_clone_progress(dataset.id)
    return dataset_info


async def handle_unfinished_html():
    vector_files = await VectorFile.filter(
        file_status__in=[VectorFileStatus.PROCESS, VectorFileStatus.READY],
        file_type__in=[
            VectorFileType.HTML,
            VectorFileType.HTML_PDF,
            VectorFileType.GITBOOK,
            VectorFileType.SITEMAP,
        ],
        deleted_at__isnull=True,
        dataset_id__isnull=False,
        created_at__gte=datetime.now() - timedelta(days=7),
    ).order_by("-created_at")

    for vector_file in vector_files:
        dataset_obj = await Dataset.get_or_none(
            id=vector_file.dataset_id,
            deleted_at__isnull=True,
        )
        if not dataset_obj:
            continue
        await refresh_dataset_file(dataset_id=dataset_obj.id, file_id=vector_file.id)


async def handle_unfinished_html_dataset():
    datasets_objs = await Dataset.filter(
        deleted_at__isnull=True,
        data_status=DATASET_STATUS.INIT,
    ).order_by("-created_at")
    for dataset_obj in datasets_objs:
        vectorfiles = await VectorFile.filter(
            dataset_id=dataset_obj.id,
            file_type__in=[
                VectorFileType.HTML.value,
                VectorFileType.HTML_PDF.value,
                VectorFileType.GITBOOK.value,
                VectorFileType.SITEMAP.value,
            ],
            file_status__in=[VectorFileStatus.PROCESS, VectorFileStatus.READY],
        )
        if len(vectorfiles) == 0:
            continue
        for vectorfile in vectorfiles:
            await refresh_dataset_file(dataset_id=dataset_obj.id, file_id=vectorfile.id)
    await handle_unfinished_html()


async def handle_unfinished_file():
    # 将最近一周的文件，初始化为待处理状态。重新进行处理
    vector_files = await VectorFile.filter(
        file_status__in=[VectorFileStatus.PROCESS, VectorFileStatus.READY],
        file_type=VectorFileType.UPLOAD,
        deleted_at__isnull=True,
        dataset_id__isnull=False,
        created_at__gte=datetime.now() - timedelta(days=7),
    ).order_by("-created_at")
    for vectorfile in vector_files:
        dataset_obj = await Dataset.get_or_none(
            id=vectorfile.dataset_id,
            deleted_at__isnull=True,
        )

        if not dataset_obj:
            continue
        await refresh_dataset_file(dataset_id=dataset_obj.id, file_id=vectorfile.id)


async def get_dataset_info(dataset_id: UUID, user_obj: User = Depends(current_user)):
    user_id = user_obj.user_id
    dataset_id_str = str(dataset_id)
    dataset_info = await Dataset.get_or_none(
        user_id=user_id, id=dataset_id_str, deleted_at=None
    )
    if not dataset_info:
        raise NotFoundException(NOT_FOUND_OR_NO_ACCESS_TO_DATASET)
    return dataset_info


from pydantic import BaseModel, Field
from typing import List


class URLRules(BaseModel):
    """
    URL rules to include or exclude
    example:
    Allow all commands starting with x
        include = ["https://redis.io/commands/x*"]
        https://redis.io/commands/xadd
        https://redis.io/commands/xreadgroup
        https://redis.io/commands/xclaim
        ...
    To exclude xreadgroup from above
        exclude = ["https://redis.io/commands/xreadgroup"]
    To exclude path starts with "xa"
        exclude = ["https://redis.io/commands/xa*"]
    """

    include: List[str] = Field(default_factory=list)
    exclude: List[str] = Field(default_factory=list)


@router.get(
    "/{dataset_id}/url_rules",
    summary="Get datasets URL rules",
    dependencies=[
        Depends(verify_dataset_owner),
    ],
)
async def get_datasets_url_rules(
    dataset_info: Dataset = Depends(get_dataset_info),
) -> URLRules:
    if dataset_info.url_rules is None:
        return URLRules()
    else:
        return URLRules(**dataset_info.url_rules)


@router.post(
    "/{dataset_id}/url_rules",
    summary="Update datasets URL rules",
    dependencies=[
        Depends(verify_dataset_owner),
    ],
)
async def update_datasets_url_rules(
    url_rules: URLRules,
    dataset_info: Dataset = Depends(get_dataset_info),
):
    # Validate URL rules
    for rule_type, rules in url_rules.dict().items():
        if rule_type not in ["include", "exclude"]:
            raise HTTPException(
                status_code=400, detail=f"Invalid rule type: {rule_type}"
            )
        for rule in rules:
            if not re.match(r"^https?://", rule):
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid URL rule: {rule}, must starts with 'http://' or 'https://'",
                )
            # if "**" in rule:
            #     if rule.count("**") > 1:
            #         raise HTTPException(
            #             status_code=400,
            #             detail=f"Invalid use of '**' in URL rule: {rule}, at most one '**' at the of the url rule",
            #         )
            #     if not rule.endswith("**"):
            #         raise HTTPException(
            #             status_code=400,
            #             detail=f"Invalid use of '**' in URL rule: {rule}, at most one '**' at the end of the url rule",
            #         )
    # Update URL rules in the dataset
    dataset_info.url_rules = url_rules.dict()
    await dataset_info.save()

    return dataset_info


async def dataset_clone_progress(dataset_id: UUID) -> CloneProgressOut:
    total, done, remaining = 0, 0, 0
    # stream_name_pattern = f"clone_task_stream*{dataset_id}"
    # redis_client = await RedisClient.get_client()
    # keys = await redis_client.keys(stream_name_pattern)
    #
    # if keys:
    #     first_key = keys[0]
    #     groups_info = await redis_client.execute_command("XINFO", "GROUPS", first_key)
    #
    #     if groups_info:
    #         group_info = groups_info[0]
    #         entries_read = int(group_info[group_info.index("entries-read") + 1])
    #         pending = int(group_info[group_info.index("pending") + 1])
    #         lag = int(group_info[group_info.index("lag") + 1])
    #
    #         total = entries_read + lag
    #         done = entries_read - pending
    #         remaining = lag + pending

    return CloneProgressOut(total=total, done=done, remaining=remaining)


async def download_file(file, client):
    """下载单个文件"""
    request = DownloadFileRequest.builder().file_token(file["token"]).build()

    response = client.drive.v1.file.download(request)
    if not response.success():
        print(f"下载文件 {file['name']} 失败: {response.msg}")
        return False

    file_path = get_file_tmp_location(file["name"])
    with open(file_path, "wb") as f:
        f.write(response.file.read())
    # print(f"response: {response.file.read()}")
    print(f"文件 {file['name']} 已下载到 {file_path}")
    return True


# @datasets_api_router.post(
#     "/{dataset_id}/integrations",
#     summary="Add integration to dataset",
#     dependencies=[Depends(auth.implicit_scheme)],
# )
# @router.post(
#     "/{dataset_id}/integrations",
#     summary="Add integration to dataset",
#     dependencies=[Depends(auth.implicit_scheme)],
# )
# async def add_integration_to_dataset(
#     rule: IntegrationRuleInput, # type "lark"
#     dataset_obj=Depends(verify_dataset_access),
#     user_obj: User = Depends(current_user),
# ):
#    pass


@datasets_api_router.post(
    "/{dataset_id}/integrations/lark",
    summary="Add Lark files to dataset by appid, app secret and share urls",
    dependencies=[Depends(auth.implicit_scheme)],
)
@router.post(
    "/{dataset_id}/integrations/lark",
    summary="Add Lark files to dataset by appid, app secret and share urls",
    dependencies=[Depends(auth.implicit_scheme)],
)
async def add_lark_file_to_dataset(
    rule: LarkIntegrationRuleInput,
    dataset_obj=Depends(verify_dataset_access),
    user_obj: User = Depends(current_user),
):
    existing_rules = await LarkIntegrationRule.filter(
        integration_rule__dataset_id=dataset_obj.id,
        integration_rule__deleted_at__isnull=True,
    ).prefetch_related("share_urls")
    existing_tokens = set()
    for existing_rule in existing_rules:
        existing_share_urls = await existing_rule.share_urls.all()
        for su in existing_share_urls:
            parsed_url = urlparse(su.url)
            match = re.match(r"^.*/(wiki|drive/folder)/([^/]+)", parsed_url.path)
            if match:
                existing_tokens.add(match.group(2))

    new_tokens = set(url.token for url in rule.share_urls)
    if new_tokens & existing_tokens:
        raise HTTPException(
            status_code=400,
            detail=f"Duplicated folder {', '.join(new_tokens & existing_tokens)} found.",
        )
    try:
        token = await get_tenant_access_token(rule.app_id, rule.app_secret)
        logging.info(f"lark tenant access token: {token}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")

    if not token:
        raise HTTPException(
            status_code=400,
            detail="Lark credentials are invalid: Failed to get tenant access token",
        )

    file_list = await check_rules_lark_file_exists(
        rule.app_id, rule.app_secret, rule.share_urls
    )
    if not file_list:
        raise HTTPException(
            status_code=400, detail="No files found in the Lark share URLs provided."
        )

    try:
        async with in_transaction() as connection:
            lark_integration_rule = (
                await LarkIntegrationRule.create_with_integration_rule(
                    dataset=dataset_obj,
                    rule=rule,
                    user=user_obj,
                    using_db=connection,
                )
            )
    except Exception as e:
        logging.error(f"{traceback.format_exc()}")
        raise HTTPException(status_code=400, detail=f"Failed to save configs: {e}")

    try:
        # Enqueue a Celery task to process the integration rule
        process_lark_integration_rule.apply_async(
            args=[str(lark_integration_rule.integration_rule.id)],
            queue="lark_integration_rule",
        )
    except Exception as e:
        logging.error(f"Failed to enqueue Lark integration rule processing task: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to enqueue background task: {e}"
        )
    return {
        "message": "New Lark integration rule created and files are being processed.",
        "rule_id": str(lark_integration_rule.integration_rule.id),
    }


class ShareUrlSchema(BaseModel):
    url: str
    recursive: bool


LarkIntegrationRuleSchema = pydantic_model_creator(
    mygpt.models.LarkIntegrationRule,
    name="LarkIntegrationRule",
    exclude=("deleted_at", "integration_rule_id", "integration_rule"),
    # computed=("tokens",),
    # optional=("lark_file",)
)


class LarkIntegrationRulesResponse(BaseModel):
    integration_rules: List[LarkIntegrationRuleSchema]


class IntegrationRuleUpdateSchema(BaseModel):
    """更新集成规则的请求模型"""

    # type: IntegrationRuleType
    name: Optional[str] = Field(None, max_length=255)
    notification_emails: Optional[List[str]] = None
    sync_interval: Optional[int] = Field(None, description="sync interval in seconds")


from typing import List, Optional
from fastapi import Depends, Query
from mygpt.models import (
    IntegrationRule,
    LarkIntegrationRule,
)
from mygpt.parameters import ListAPIParams, tortoise_paginate

from mygpt.enums import IntegrationRuleType
from tortoise.expressions import Q, RawSQL

IntegrationRuleBaseSchema = pydantic_model_creator(
    IntegrationRule,
    name="IntegrationRuleBase",
    exclude=(
        "deleted_at",
        "dataset",
        "vector_files",
        "integrationrule_vectorfiles",
        "integration_sync_logs",
        "dataset_id",
    ),
)


class IntegrationRuleSchema(IntegrationRuleBaseSchema):
    file_count: int
    detail: LarkIntegrationRuleSchema

    class Config:
        orm_mode = True


class IntegrationRulesResponse(BaseModel):
    items: List[IntegrationRuleSchema]
    total: int
    page: int
    size: int
    pages: int


def mask_sensitive_info(text: str) -> str:
    """将敏感信息转换为带星号的格式，保留前3位和后3位"""
    if not text:
        return text
    elif len(text) <= 6:
        return "*" * 6  # 至少6个星号
    elif len(text) <= 12:
        return text[0] + "*" * 6 + text[-1]  # 至少6个星号
    else:
        return text[:3] + "*" * 6 + text[-3:]  # 至少6个星号


@datasets_api_router.get(
    "/{dataset_id}/integrations",
    summary="get integration rules",
    dependencies=[Depends(auth.implicit_scheme)],
    response_model=IntegrationRulesResponse,
)
@router.get(
    "/{dataset_id}/integrations",
    summary="get integration rules",
    dependencies=[Depends(auth.implicit_scheme)],
    response_model=IntegrationRulesResponse,
)
async def get_integration_rules_from_dataset(
    dataset_obj=Depends(verify_dataset_access),
    types: Optional[List[IntegrationRuleType]] = Query(None, alias="type"),
    params: ListAPIParams = Depends(),
):
    """
    获取指定数据集的集成规则，支持按类型过滤和分页。
    """
    try:
        # 构建查询条件
        query = Q(dataset=dataset_obj)
        if types:
            query &= Q(type__in=types)

        integration_rules = (
            IntegrationRule.filter(query, deleted_at__isnull=True)
            .select_related("lark_integration_rule")
            .prefetch_related("lark_integration_rule__share_urls", "created_by")
        )

        paginated_rules = await tortoise_paginate(integration_rules, params)

        # 序列化规则
        serialized_rules = []
        for integration_rule in paginated_rules.items:
            if integration_rule.type == IntegrationRuleType.LARK:
                lark_rule = await integration_rule.lark_integration_rule
                details = await LarkIntegrationRuleSchema.from_tortoise_orm(lark_rule)
                details.app_id = mask_sensitive_info(details.app_id)
                details.app_secret = mask_sensitive_info(details.app_secret)
                file_count = await integration_rule.vector_files.filter(
                    deleted_at__isnull=True
                ).count()
                serialized_rules.append(
                    IntegrationRuleSchema(
                        id=str(integration_rule.id),
                        name=integration_rule.name,
                        file_count=file_count,
                        type=integration_rule.type,
                        created_by=integration_rule.created_by,
                        file_list_sync_status=integration_rule.file_list_sync_status,
                        last_synced_at=integration_rule.last_synced_at,
                        sync_interval=integration_rule.sync_interval,
                        created_at=integration_rule.created_at,
                        updated_at=integration_rule.updated_at,
                        detail=details,
                    )
                )
            else:
                logging.error(
                    f"Unsupported integration rule type: {integration_rule.type}"
                )
                # serialized_rules.append(
                #     IntegrationRuleSchema(id=str(rule.id), type=rule.type, details={})
                # )
                raise HTTPException(
                    status_code=500,
                    detail=f"Unsupported integration rule type: {integration_rule.type}",
                )

        return IntegrationRulesResponse(
            items=serialized_rules,
            total=paginated_rules.total,
            page=paginated_rules.page,
            size=paginated_rules.size,
            pages=paginated_rules.pages,
        )
    except Exception as e:
        logging.error(f"{traceback.format_exc()}")
        raise HTTPException(
            status_code=400, detail=f"Failed to get Lark integration rules: {e}"
        )


class IntegrationRuleAction(Enum):
    SYNC = "sync"


@router.patch(
    "/{dataset_id}/integrations/{integration_rule_id}",
    summary="update integration rule by id",
    dependencies=[Depends(auth.implicit_scheme)],
    response_model=IntegrationRuleSchema,
)
async def update_integration_rule_by_id(
    dataset_id: UUID,
    integration_rule_id: UUID,
    update_data: IntegrationRuleUpdateSchema,
    dataset_obj=Depends(verify_dataset_access),
):
    """更新集成规则的字段，支持跨表更新"""
    try:
        # 获取集成规则及其关联的 Lark 规则
        integration_rule = await IntegrationRule.get_or_none(
            id=integration_rule_id, dataset_id=dataset_id, deleted_at__isnull=True
        ).prefetch_related("lark_integration_rule")

        if not integration_rule:
            raise NotFoundException("Integration rule not found")

        lark_rule = integration_rule.lark_integration_rule

        if not lark_rule:
            raise NotFoundException("Lark integration rule not found")

        # 准备更新数据
        integration_rule_updates = {}

        if update_data.name is not None:
            integration_rule_updates["name"] = update_data.name

        if update_data.sync_interval is not None:
            integration_rule_updates["sync_interval"] = update_data.sync_interval

        if update_data.notification_emails is not None:
            integration_rule_updates["notification_emails"] = (
                update_data.notification_emails
            )

        # 如果没有要更新的字段，返回错误
        if not integration_rule_updates:
            raise HTTPException(
                status_code=400, detail="No valid fields provided for update"
            )

        # 使用事务同时更新两个表
        async with in_transaction() as connection:
            # 更新 IntegrationRule
            if integration_rule_updates:
                await integration_rule.update_from_dict(integration_rule_updates).save(
                    using_db=connection
                )

        # 重新获取更新后的数据
        updated_rule = await IntegrationRule.get(
            id=integration_rule_id
        ).prefetch_related("lark_integration_rule", "lark_integration_rule__share_urls")

        # 构造响应数据
        lark_rule = await updated_rule.lark_integration_rule
        details = await LarkIntegrationRuleSchema.from_tortoise_orm(lark_rule)
        # 脱敏处理
        details.app_id = mask_sensitive_info(details.app_id)
        details.app_secret = mask_sensitive_info(details.app_secret)
        file_count = await updated_rule.vector_files.all().count()

        return IntegrationRuleSchema(
            id=str(updated_rule.id),
            type=updated_rule.type,
            name=updated_rule.name,
            file_list_sync_status=updated_rule.file_list_sync_status,
            notification_emails=updated_rule.notification_emails,
            sync_interval=updated_rule.sync_interval,
            last_synced_at=updated_rule.last_synced_at,
            created_at=updated_rule.created_at,
            updated_at=updated_rule.updated_at,
            file_count=file_count,
            detail=details,
        )
    except NotFoundException as e:
        raise HTTPException(status_code=404, detail=str(e))
    except HTTPException as e:
        raise e
    except Exception as e:
        logging.error(f"Failed to update integration rule: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500, detail=f"Failed to update integration rule: {str(e)}"
        )


async def delete_lark_file(lark_file: LarkFile):
    """
    totally delete a lark_file in db
    """
    vector_file = lark_file.vector_file
    if not vector_file:
        logging.error(f"No vector file found for lark file {lark_file.id}")
        return
    dataset_obj = await vector_file.dataset.get()
    if not dataset_obj:
        logging.error(
            f"No dataset found for lark file {lark_file.id}, vector file: {vector_file.id}"
        )
        return

    await task_delete_files([vector_file], dataset_obj)


async def get_lark_files_by_integration_rule(integration_rule_id: str):
    """
    获取与指定 integration rule 关联的所有 LarkFile

    Args:
        integration_rule_id (UUID): Integration Rule 的 ID

    Returns:
        List[LarkFile]: LarkFile 列表
    """
    try:
        existing_lark_files_by_integration_rule = (
            await LarkFile.filter(
                vector_file__integration_rules__id=integration_rule_id,
                vector_file__deleted_at__isnull=True,
            )
            .prefetch_related("vector_file", "vector_file__integration_rules")
            .order_by("-modified_time")
        )

        return existing_lark_files_by_integration_rule

    except Exception as e:
        logging.error(
            f"Error getting lark files for integration rule {integration_rule_id}: {e}"
        )
        raise


import asyncio
from datetime import datetime, timedelta, timezone
from mygpt.models import IntegrationRule

LOCK_KEY = "background_sync_lock"
LOCK_EXPIRE_SECONDS = 70
SLEEP_INTERVAL = 60


async def sync_logic():
    """
    仅负责筛选需要被同步的 IntegrationRule，并异步创建任务。
    """
    now = datetime.now(timezone.utc)
    now_str = now.isoformat()

    rules = await IntegrationRule.filter(
        Q(last_synced_at__isnull=True)
        | Q(
            last_synced_at__lte=RawSQL(
                f"CAST('{now_str}' AS timestamptz) - GREATEST(sync_interval, 1200) * interval '1 second'"
            )
        ),
        sync_interval__isnull=False,
        deleted_at__isnull=True,
    )

    logging.info(f"Found {len(rules)} integration rules to sync")

    for rule in rules:
        # 异步并行处理每个 rule 的同步逻辑
        asyncio.create_task(sync_lark_file_list_by(rule.id, trigger_user_id=None))
        rule.last_synced_at = datetime.now(timezone.utc)
        await rule.save()


async def background_sync_task():
    """
    周期性检查并执行同步任务，通过 Redis 分布式锁确保在多实例环境中只有一个实例执行。
    """
    redis_client = RedisClient.get_client()
    while True:
        try:
            lock_acquired = await redis_client.set(
                LOCK_KEY,
                "1",
                nx=True,  # 仅当 key 不存在时才设置
                ex=LOCK_EXPIRE_SECONDS,  # 自动过期时间，防止死锁
            )
            if lock_acquired:
                logging.debug(
                    "Distributed lock acquired, starting scheduled sync logic..."
                )
                try:
                    await sync_logic()
                except Exception as task_err:
                    # 在这里捕获同步逻辑中的异常
                    logging.error(f"Error during sync logic: {task_err}", exc_info=True)
                finally:
                    await redis_client.delete(LOCK_KEY)
                    logging.debug("Distributed lock released.")
            else:
                logging.info("Another instance is holding the lock, skip this round...")
        except Exception as e:
            logging.error(f"Error in background sync task: {e}", exc_info=True)
        finally:
            try:
                await asyncio.sleep(SLEEP_INTERVAL + random.uniform(0, 2))
            except Exception as sleep_err:
                logging.error(f"Error during sleep: {sleep_err}")


async def sync_lark_file_list_by(
    integration_rule_id: str, trigger_user_id: Optional[str] = None
):
    integration_rule = None
    try:
        trigger_user = None
        if trigger_user_id:
            trigger_user = await User.get_or_none(id=trigger_user_id)
        lark_integration_rule = await LarkIntegrationRule.get(
            integration_rule_id=integration_rule_id
        ).select_related("integration_rule")
        if not lark_integration_rule:
            raise ValueError(f"IntegrationRule {integration_rule_id} not found")
        integration_rule = await IntegrationRule.get(id=integration_rule_id)
        integration_rule.file_list_sync_status = (
            IntegrationRuleFileListSyncingStatus.SYNCING
        )
        integration_rule.last_synced_at = datetime.now(timezone.utc)
        await integration_rule.save()
        # integration_rule = lark_integration_rule.integration_rule.first()
        # if integration_rule:
        # from mygpt.enums import IntegrationRuleFileListSyncingStatus

        # integration_rule.file = IntegrationRuleFileListSyncingStatus.SYNCING
        await lark_integration_rule.integration_rule.fetch_related(
            "dataset", "dataset__user"
        )
        dataset = lark_integration_rule.integration_rule.dataset
        if not dataset:
            raise ValueError(
                f"Dataset not found for IntegrationRule {integration_rule_id}"
            )
        share_urls = await lark_integration_rule.share_urls.all()

        # Convert LarkShareUrl instances to LarkShareUrlInput instances
        share_url_inputs = [
            LarkShareUrlInput(url=share_url.url, recursive=share_url.recursive)
            for share_url in share_urls
        ]
        any_lark_folder_exists, err = await check_any_lark_folder_exists(
            app_id=lark_integration_rule.app_id,
            app_secret=lark_integration_rule.app_secret,
            share_urls=share_url_inputs,
        )
        if err:
            logging.error(f"Failed to check lark folder exists: {err}")
            return
        if not any_lark_folder_exists:
            try:
                dataset_id = dataset.id
                integration_rule: IntegrationRule = await IntegrationRule.get_or_none(
                    id=integration_rule_id, dataset_id=dataset_id
                ).prefetch_related("vector_files", "vector_files__lark_file")
                if not integration_rule:
                    raise NotFoundException("Integration rule not found")
                await integration_rule.delete()
                await VectorFile.filter(
                    id__in=[
                        vector_file.id for vector_file in integration_rule.vector_files
                    ],
                    dataset_id=dataset_id,
                    deleted_at__isnull=True,
                ).update(deleted_at=datetime.now())
                # delete_integration_rule_task.apply_async(args=[str(integration_rule.id)])
                for vector_file in integration_rule.vector_files:
                    asyncio.create_task(delete_files_w_semaphore(vector_file, dataset))
                await Dataset.filter(id=dataset_id).update(updated_at=datetime.now())
                return
            except NotFoundException as e:
                raise HTTPException(status_code=404, detail=str(e))
            except Exception as e:
                logging.error(
                    f"Failed to delete integration rule: {traceback.format_exc()}"
                )
                raise HTTPException(status_code=500, detail=str(e))

        # Now pass the list of LarkShareUrlInput instances to the function
        remote_lark_files = await fetch_lark_file_list(
            app_id=lark_integration_rule.app_id,
            app_secret=lark_integration_rule.app_secret,
            share_urls=share_url_inputs,
        )
        lark_files_filtered: list[FileInfo] = []
        for file in remote_lark_files:
            if file.type == "folder":
                continue
            elif file.type in lark_export_file_extension_dict:
                lark_files_filtered.append(file)
            elif file.type == "file":
                if filename_to_unstructured_type(file.name) != UnstructuredType.UNKNOWN:
                    lark_files_filtered.append(file)
            else:
                logging.info(f"unsupported type: {file.type}")
        logging.info(
            f"get total lark files: {len(remote_lark_files)}, after filter: {len(lark_files_filtered)}"
        )
        file_to_create: list[FileInfo] = []
        file_to_delete: list[LarkFile] = []
        file_to_update: list[Tuple[LarkFile, FileInfo]] = []
        for file_info in lark_files_filtered:
            existing_lark_files_by_token = (
                await LarkFile.filter(
                    token=file_info.token,
                    vector_file__deleted_at__isnull=True,
                    vector_file__integration_rules__id=integration_rule.id,
                )
                .prefetch_related("vector_file")
                .order_by("-modified_time")
            )
            if existing_lark_files_by_token:
                if len(existing_lark_files_by_token) > 1:
                    # delete duplicate
                    for lark_file_to_delete in existing_lark_files_by_token[1:]:
                        asyncio.create_task(delete_lark_file(lark_file_to_delete))
                # use latest updated
                lark_file = existing_lark_files_by_token[0]
                if (
                    lark_file.modified_time != file_info.modified_time
                    or lark_file.vector_file.file_status == VectorFileStatus.FAIL
                    # or (
                    #     # if task pending for > 1 day, try to relearn
                    #     lark_file.vector_file.file_status != VectorFileStatus.COMPLETE
                    #     and (
                    #         datetime.now(timezone.utc)
                    #         - lark_file.vector_file.updated_at
                    #     ).total_seconds()
                    #     > 86400
                    # )
                ):
                    file_to_update.append((lark_file, file_info))
            else:
                file_to_create.append(file_info)
        # get all lark files in db by integration rule
        existing_lark_files_by_integration_rule = (
            await get_lark_files_by_integration_rule(integration_rule_id)
        )
        existing_lark_files_by_integration_rule_token_map = {
            lark_file.token: lark_file
            for lark_file in existing_lark_files_by_integration_rule
        }
        lark_files_tokens_filtered_map = {
            lark_file.token: lark_file for lark_file in lark_files_filtered
        }
        for lark_file_token in existing_lark_files_by_integration_rule_token_map:
            if lark_file_token not in lark_files_tokens_filtered_map:
                file_to_delete.append(
                    existing_lark_files_by_integration_rule_token_map[lark_file_token]
                )
        logging.info(
            f"sync diff result: c: {len(file_to_create)}, u: {len(file_to_update)}, d: {len(file_to_delete)}; {integration_rule_id=}, {trigger_user_id=}"
        )
        if file_to_create:
            vector_files_data = []
            lark_files_data = []

            for file_info in file_to_create:
                vector_file_id = uuid4()
                # Prepare VectorFile data
                vector_files_data.append(
                    VectorFile(
                        id=vector_file_id,
                        dataset=dataset,
                        filename=get_vector_file_name_for_lark_file(file_info),
                        title=file_info.name,
                        file_type=VectorFileType.INTEGRATION,
                        source_type=VectorFileSourceType.LARK,
                        file_status=VectorFileStatus.READY,
                    )
                )

                # Prepare LarkFile data
                lark_files_data.append(
                    LarkFile(
                        vector_file_id=vector_file_id,
                        name=file_info.name,
                        type=file_info.type,
                        token=file_info.token,
                        parent_token=file_info.parent_token,
                        created_time=file_info.created_time,
                        modified_time=file_info.modified_time,
                        owner_id=file_info.owner_id,
                        url=file_info.url,
                    )
                )

            # Bulk create within a transaction
            async with in_transaction():
                created_vector_files = await VectorFile.bulk_create(vector_files_data)
                await LarkFile.bulk_create(lark_files_data)
                integration_rule_vector_files = [
                    IntegrationRuleVectorFile(
                        integration_rule_id=integration_rule.id,
                        vectorfile_id=vector_file.id,
                    )
                    for vector_file in created_vector_files
                ]
                await IntegrationRuleVectorFile.bulk_create(
                    integration_rule_vector_files
                )
                sync_logs = [
                    IntegrationSyncLog(
                        integration_rule_id=integration_rule.id,
                        vectorfile_id=vector_file.id,
                        operation=IntegrationSyncOperation.CREATE,
                        trigger_user=trigger_user,
                        trigger_type=(
                            IntegrationSyncTriggerType.MANUAL
                            if trigger_user
                            else IntegrationSyncTriggerType.SCHEDULED
                        ),
                        sync_status=IntegrationSyncStatus.SUCCESS,
                    )
                    for vector_file in created_vector_files
                ]
                await IntegrationSyncLog.bulk_create(sync_logs)
            user_id = str(dataset.user.id)
            queue_name = f"lark_file.{user_id}"
            # routing_key = f"lark_file.{user_id}"
            reply = celery_app.control.add_consumer(
                queue=queue_name,
                reply=True,
                destination=["celery@lark_file_worker"],
            )
            logging.info(f"add_consumer {queue_name=}; reply: {reply=}")
            for vector_file in vector_files_data:
                task_result = process_lark_file.apply_async(
                    args=[str(vector_file.id), str(integration_rule_id)],
                    queue=queue_name,
                    # routing_key=routing_key,
                )
                try:
                    await store_task_id(str(vector_file.id), task_result.id)
                except Exception as e:
                    logging.error(f"store_task_id error: {e}, {traceback.format_exc()}")
        if file_to_update:
            for lark_file, file_info in file_to_update:
                lark_file.name = file_info.name
                lark_file.type = file_info.type
                lark_file.token = file_info.token
                lark_file.parent_token = file_info.parent_token
                lark_file.created_time = file_info.created_time
                lark_file.modified_time = file_info.modified_time
                lark_file.owner_id = file_info.owner_id
                lark_file.url = file_info.url
                await lark_file.save()
                vector_file = lark_file.vector_file
                vector_file.filename = get_vector_file_name_for_lark_file(file_info)
                vector_file.title = file_info.name
                vector_file.file_status = VectorFileStatus.READY
                await vector_file.save()
                await IntegrationSyncLog.create(
                    integration_rule_id=integration_rule.id,
                    vectorfile_id=vector_file.id,
                    operation=IntegrationSyncOperation.UPDATE,
                    trigger_user=trigger_user,
                    trigger_type=(
                        IntegrationSyncTriggerType.MANUAL
                        if trigger_user
                        else IntegrationSyncTriggerType.SCHEDULED
                    ),
                    sync_status=IntegrationSyncStatus.SUCCESS,
                )
                await vector_file.delete_search_index()
                queue_name = f"lark_file.{str(dataset.user.id)}"
                reply = celery_app.control.add_consumer(
                    queue=queue_name,
                    reply=True,
                    destination=["celery@lark_file_worker"],
                )
                logging.info(f"add_consumer {queue_name=}; reply: {reply=}")
                task_result = process_lark_file.apply_async(
                    args=[str(vector_file.id), str(integration_rule_id)],
                    queue=queue_name,
                )
                try:
                    await store_task_id(str(vector_file.id), task_result.id)
                except Exception as e:
                    logging.error(f"store_task_id error: {e}, {traceback.format_exc()}")
        if file_to_delete:
            for lark_file in file_to_delete:
                vector_file = lark_file.vector_file
                await IntegrationSyncLog.create(
                    integration_rule_id=integration_rule.id,
                    vectorfile_id=vector_file.id,
                    operation=IntegrationSyncOperation.DELETE,
                    trigger_user=trigger_user,
                    trigger_type=(
                        IntegrationSyncTriggerType.MANUAL
                        if trigger_user
                        else IntegrationSyncTriggerType.SCHEDULED
                    ),
                    sync_status=IntegrationSyncStatus.SUCCESS,
                )
                # 获取所有关联的 integration_rules
                integration_rules = await vector_file.integration_rules.all()

                # 只删除当前 integration_rule 的关联关系
                await IntegrationRuleVectorFile.filter(
                    integration_rule_id=integration_rule.id,
                    vectorfile_id=vector_file.id,
                ).delete()

                # 检查是否还有其他 rule 引用这个文件
                remaining_rules = len(integration_rules)
                if remaining_rules <= 1:  # 只有当前规则或没有规则时才删除文件
                    await vector_file.delete()
                    await task_delete_files([vector_file], dataset)
        integration_rule.file_list_sync_status = (
            IntegrationRuleFileListSyncingStatus.COMPLETED
        )
        await integration_rule.save()
    except Exception as e:
        logging.error(
            f"Error processing Lark integration rule {integration_rule_id}: {e}, {traceback.format_exc()}"
        )
        if integration_rule:
            integration_rule.file_list_sync_status = (
                IntegrationRuleFileListSyncingStatus.FAILED
            )
            await integration_rule.save()
        raise


@router.post(
    "/{dataset_id}/integrations/{integration_rule_id}/sync",
    summary="manual sync on integration rules",
    dependencies=[Depends(auth.implicit_scheme)],
    status_code=HTTP_204_NO_CONTENT,
)
async def sync_by_integration_rule(
    integration_rule_id: UUID,
    user: User = Depends(current_user),
    dataset_obj=Depends(verify_dataset_access),
):
    try:
        # 获取集成规则
        integration_rule = await IntegrationRule.get_or_none(
            id=integration_rule_id, dataset=dataset_obj, deleted_at__isnull=True
        ).prefetch_related("lark_integration_rule")

        if not integration_rule:
            raise NotFoundException("Integration rule not found")

        # 检查规则类型并执行相应操作
        if integration_rule.type == IntegrationRuleType.LARK:
            # 触发 Lark 同步任务
            # process_lark_integration_rule.apply_async(
            #     args=[str(integration_rule.id), "sync"],
            #     queue="lark_integration_rule",
            # )
            # 更新同步状态
            integration_rule.file_list_sync_status = (
                IntegrationRuleFileListSyncingStatus.SYNCING
            )
            await integration_rule.save()
            asyncio.create_task(sync_lark_file_list_by(integration_rule.id, user.id))
            return {
                "message": "Sync task started successfully",
                "rule_id": str(integration_rule.id),
            }
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported integration rule type: {integration_rule.type}",
            )

    except Exception as e:
        logging.error(f"err: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500, detail=f"Failed to sync integration rule: {str(e)}"
        )


delete_file_semaphore = asyncio.Semaphore(
    min(20, max(5, settings.FILE_PROCESSING_CONCURRENCY * 2))
)


async def delete_files_w_semaphore(vector_file, dataset_obj):
    async with delete_file_semaphore:
        await task_delete_files([vector_file], dataset_obj)


@router.delete(
    "/{dataset_id}/integrations/{integration_rule_id}",
    summary="Delete integration rule and associated files",
    dependencies=[Depends(auth.implicit_scheme)],
    status_code=HTTP_204_NO_CONTENT,
)
async def delete_integration_rule(
    dataset_id: UUID,
    integration_rule_id: UUID,
    dataset_obj=Depends(verify_dataset_access),
):
    """
    Delete integration rule and queue individual files
    参数：integration_rule_id, vector_file_id
    """
    try:
        integration_rule: IntegrationRule = await IntegrationRule.get_or_none(
            id=integration_rule_id, dataset_id=dataset_id
        ).prefetch_related("vector_files", "vector_files__lark_file")

        if not integration_rule:
            raise NotFoundException("Integration rule not found")
        await integration_rule.delete()
        await VectorFile.filter(
            id__in=[vector_file.id for vector_file in integration_rule.vector_files],
            dataset_id=dataset_id,
            deleted_at__isnull=True,
        ).update(deleted_at=datetime.now())
        # delete_integration_rule_task.apply_async(args=[str(integration_rule.id)])
        for vector_file in integration_rule.vector_files:
            asyncio.create_task(delete_files_w_semaphore(vector_file, dataset_obj))
        await Dataset.filter(id=dataset_id).update(updated_at=datetime.now())
    except NotFoundException as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logging.error(f"Failed to delete integration rule: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))


class LarkFileInfo(BaseModel):
    name: str
    type: str
    token: str
    parent_token: str
    created_time: str
    modified_time: str
    owner_id: str
    url: str


class VectorFileWithLarkFileOut(VectorFileOut):
    # tokens: Optional[int] = Field(None, description="Token count of the file")
    lark_file: Optional[LarkFileInfo] = None

    class Config:
        orm_mode = True


import asyncio
from fastapi import HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List
from uuid import UUID

# Global in-memory dictionary to store re-embedding status for each dataset
re_embed_status: dict[str, str] = {}


# Pydantic models for re-embedding endpoints
class ReEmbedRequest(BaseModel):
    dataset_ids: List[UUID]


class ReEmbedStatusItem(BaseModel):
    dataset_id: str
    status: str


class ReEmbedStatusResponse(BaseModel):
    status: List[ReEmbedStatusItem]


# Background task to process re-embedding for a dataset
async def process_reembed(dataset_id: UUID):
    # Set status to "processing"
    re_embed_status[str(dataset_id)] = "processing"
    try:
        # Retrieve the dataset object
        dataset_obj = await Dataset.get_or_none(id=dataset_id, deleted_at__isnull=True)
        if not dataset_obj:
            re_embed_status[str(dataset_id)] = "failed"
            return

        vector_files = await VectorFile.filter(
            dataset_id=dataset_obj.id, deleted_at__isnull=True
        ).all()
        for vector_file in vector_files:
            file_id = vector_file.id
            if vector_file.metadata is None:
                vector_file.metadata = {}
            if "re_embed_success_time" in vector_file.metadata:
                re_embed_status[str(dataset_id)] = "completed"
                logging.info(f"Skip re-embedding for vector file {file_id}")
                continue
            logging.info(f"Rebuilding embedding for vector file {vector_file.id}")
            # Set file status to PROCESS indicating the re-embedding is in progress
            vector_file.file_status = VectorFileStatus.PROCESS
            await vector_file.save()
            embedding_params = dataset_obj.get_embedding_params()
            # collection_name = get_collection_name(
            #     embedding_params.model_name,
            #     embedding_params.dimensions,
            # )
            # get_collection_name 函数有坑，这里只能写死
            collection_name = "gptbase_local_bce"
            offset = None
            t1 = time.monotonic()
            chunk_count = 0
            while True:
                res = await async_qdrant_client.scroll(
                    collection_name=collection_name,
                    scroll_filter=Filter(
                        must=[
                            FieldCondition(
                                key="metadata.file_id",
                                match=MatchValue(value=str(file_id)),
                            )
                        ]
                    ),
                    with_payload=True,
                    offset=offset,
                    limit=50,
                )
                chunks, offset = res
                chunk_count += len(chunks)
                for chunk in chunks:
                    try:
                        text_content = chunk.payload["page_content"]
                        if not text_content:
                            logging.warning(
                                f"No page_content found in chunk with id {chunk.id}"
                            )
                            return

                        # Create an instance of the embedding model with the latest parameters
                        model_instance = EmbeddingFactory.get_instance(
                            provider=embedding_params.provider,
                            async_mode=True,
                            model_name=embedding_params.model_name,
                            dimensions=embedding_params.dimensions,
                        )

                        new_embedding = await model_instance.aembed_documents(
                            [text_content]
                        )
                        new_collection_name = get_collection_name(
                            settings.LOCAL_EMBEDDING_MODEL_NAME,
                            settings.LOCAL_EMBEDDING_VECTOR_SIZE,
                        )
                        point = PointStruct(
                            id=chunk.id, vector=new_embedding[0], payload=chunk.payload
                        )
                        await async_qdrant_client.upsert(new_collection_name, [point])
                        logging.info(
                            f"Successfully updated embedding for chunk {chunk.id}"
                        )
                    except Exception as e:
                        logging.error(
                            f"Error updating embedding for chunk {chunk.id}: {e}",
                            exc_info=True,
                        )
                if offset is None:
                    break
            t2 = time.monotonic()
            logging.info(
                f"{file_id=}, reembed process all chunks time: {t2 - t1}, {chunk_count=}"
            )
            vector_file.metadata["re_embed_success_time"] = (
                datetime.utcnow().isoformat()
            )
            # After processing, update vector file status to COMPLETE
            vector_file.file_status = VectorFileStatus.COMPLETE
            await vector_file.save()

        # Mark the dataset re-embedding as completed
        re_embed_status[str(dataset_id)] = "completed"
    except Exception as e:
        logging.error(
            f"Re-embedding failed for dataset {dataset_id}: {e}, {traceback.format_exc()}"
        )
        re_embed_status[str(dataset_id)] = "failed"


@router.post(
    "/re-embed",
    summary="Rebuild dataset embeddings using the latest embedding model",
    dependencies=[Depends(auth.implicit_scheme)],
)
async def re_embed_datasets(
    request: ReEmbedRequest, user: User = Depends(current_user)
):
    if not settings.IS_USE_LOCAL_VLLM:
        raise HTTPException(
            status_code=400, detail="Re-embedding is only supported in on premises mode"
        )
    # Retrieve dataset objects for the provided IDs
    dataset_objs = await Dataset.filter(
        id__in=request.dataset_ids, deleted_at__isnull=True
    ).all()
    if len(dataset_objs) != len(request.dataset_ids):
        raise HTTPException(status_code=400, detail="dataset not found")

    # Set initial status to "pending" and queue background tasks for each dataset
    for dataset_id in request.dataset_ids:
        re_embed_status[str(dataset_id)] = "pending"
        asyncio.create_task(process_reembed(dataset_id))

    return JSONResponse(content={"message": "task accepted"})


@router.post(
    "/re-embed/_status",
    summary="Get re-embedding status for specified datasets",
    response_model=ReEmbedStatusResponse,
    dependencies=[Depends(auth.implicit_scheme)],
)
async def get_re_embed_status(
    request: ReEmbedRequest, user: User = Depends(current_user)
):
    dataset_ids = request.dataset_ids
    status_list = []
    for ds_id in dataset_ids:
        # Retrieve the current status, defaulting to "empty" if no task has been queued
        status = re_embed_status.get(str(ds_id), "empty")
        status_list.append(ReEmbedStatusItem(dataset_id=str(ds_id), status=status))
    return ReEmbedStatusResponse(status=status_list)


# Global dictionary to store data cleanup status for each dataset
cleanup_status: dict[str, str] = {}


class DataCleanupRequest(BaseModel):
    dataset_ids: List[UUID]


class DataCleanupStatusItem(BaseModel):
    dataset_id: str
    status: str


class DataCleanupStatusResponse(BaseModel):
    status: List[DataCleanupStatusItem]


@router.post(
    "/data-cleanup",
    summary="Batch clean up indexes for deleted vectorfiles for multiple datasets and record cleanup time",
    dependencies=[Depends(auth.implicit_scheme)],
)
async def batch_cleanup_datasets(
    request: DataCleanupRequest,
    background_tasks: BackgroundTasks,
    user: User = Depends(current_user),
):
    """
    Batch schedule data cleanup tasks for multiple datasets.

    This endpoint accepts a list of dataset IDs via the DataCleanupRequest.
    For each dataset, it queries vectorfiles with 'deleted' status and schedules a background task
    to clean up their indexes from Qdrant and OpenSearch, then record the cleanup success time.
    """
    scheduled = []
    not_found = []
    for ds_id in request.dataset_ids:
        # Retrieve dataset object; if not found, update cleanup_status accordingly.
        dataset_obj = await Dataset.get_or_none(id=ds_id, deleted_at__isnull=True)
        if not dataset_obj:
            cleanup_status[str(ds_id)] = "not_found"
            not_found.append(str(ds_id))
        else:
            cleanup_status[str(ds_id)] = "pending"
            # Schedule a background task for cleaning up this dataset
            background_tasks.add_task(process_cleanup_tasks, dataset_obj)
            scheduled.append(str(ds_id))
    return JSONResponse(
        content={
            "message": "Batch data cleanup tasks scheduled.",
            "scheduled_datasets": scheduled,
            "not_found_datasets": not_found,
        }
    )


async def file_vector_count(collection_name: str, vector_file: VectorFile) -> int:
    count_result = await async_qdrant_client.count(
        collection_name=collection_name,
        count_filter={
            "must": [
                {"key": "metadata.file_id", "match": {"value": str(vector_file.id)}}
            ]
        },
    )
    return count_result.count


async def process_cleanup_tasks(dataset: Dataset):
    """
    Background task to cleanup indexes for deleted vectorfiles.
    Updates the global cleanup_status and dataset's data_cleanup_success_time.
    """
    try:
        # Query vectorfiles that are marked as deleted
        deleted_vectorfiles = await VectorFile.filter(
            dataset_id=dataset.id, deleted_at__isnull=False
        ).all()
        logging.info(
            f"Found {len(deleted_vectorfiles)} deleted vectorfiles to clean up for dataset {dataset.id}."
        )
        for vector_file in deleted_vectorfiles:
            if not vector_file.metadata:
                vector_file.metadata = {}
            if "data_cleanup_success_time" in vector_file.metadata:
                logging.info(
                    f"Cleanup already completed for this vectorfile {vector_file.id}, {vector_file.filename}, skipping."
                )
                continue
            # embedding_params = dataset.get_embedding_params()
            # collection_name = get_collection_name(
            #     embedding_params.model_name, embedding_params.dimensions
            # )
            # vector_count = await file_vector_count(collection_name, vector_file)
            # if vector_count > 0:
            #     logging.info(
            #         f"dirty vector found in vectorfile {vector_file.id}, {vector_file.filename}, count: {vector_count}"
            #     )
            delete_search_indexes_res = await vector_file.delete_search_index()
            logging.info(f"{delete_search_indexes_res=}")
            # else:
            #     logging.info(
            #         f"vectorfile clean, no dirty vector found in vectorfile {vector_file.id}, {vector_file.filename}"
            #     )
            if delete_search_indexes_res == [True, True]:
                logging.info(
                    f"Successfully cleaned up indexes for vectorfile {vector_file.id}, {vector_file.filename}"
                )
                vector_file.metadata["data_cleanup_success_time"] = datetime.now(
                    timezone.utc
                )
            else:
                logging.error(
                    f"Failed to clean up indexes for vectorfile {vector_file.id}, {vector_file.filename}"
                )
            await vector_file.save()
        logging.info(f"Cleanup completed for dataset {dataset.id}")
        # Update global cleanup status to "completed"
        cleanup_status[str(dataset.id)] = "completed"

    except Exception as e:
        logging.error(
            f"Error during data cleanup for dataset {dataset.id}: {e}",
            exc_info=True,
        )
        cleanup_status[str(dataset.id)] = "failed"


@router.post(
    "/data-cleanup/_status",
    summary="Get data cleanup status for specified datasets",
    response_model=DataCleanupStatusResponse,
    dependencies=[Depends(auth.implicit_scheme)],
)
async def get_data_cleanup_status(
    request: DataCleanupRequest, user: User = Depends(current_user)
):
    """
    This endpoint returns the data cleanup status for each provided dataset.
    It reads the global cleanup_status dictionary for status information.
    If no task has been queued for a dataset, the status is returned as "empty".
    """
    status_list = []
    for ds_id in request.dataset_ids:
        status = cleanup_status.get(str(ds_id), "empty")
        status_list.append(DataCleanupStatusItem(dataset_id=str(ds_id), status=status))
    return DataCleanupStatusResponse(status=status_list)


from mygpt.schemas.datasets_schema import (
    DatasetsTrainingStateOut,
    IntegrationTrainingStateOut,
)


@router.get(
    "/training/progress/{ai_id}",
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_admin_access),
    ],
)
async def get_datasets_training_state(
    ai_id: UUID,
    params: ListAPIParams = Depends(),
) -> DatasetsTrainingStateOut:
    query = """
    SELECT
    d.id,
    d.name,
    vf.file_type,
    vf.file_status,
    count(*) as type_count
    FROM robot as r
             JOIN dataset_robot as dr ON r.id = dr.robot_id
             JOIN dataset as d ON dr.dataset_id = d.id
             JOIN vectorfile as vf ON d.id = vf.dataset_id
    WHERE r.id = $1
      AND vf.deleted_at IS NULL
    group by d.id, d.name, vf.file_type, vf.file_status order by d.id;
    """
    try:
        raw_results = await Tortoise.get_connection("default").execute_query_dict(
            query, [str(ai_id)]
        )
        logging.info(
            f"[get_datasets_training_state] Query results count: {len(raw_results)}"
        )
        if raw_results:
            logging.info(
                f"[get_datasets_training_state] First record sample: {raw_results[0]}"
            )
    except Exception as e:
        logging.error(f"Error querying records: {str(e)}")
        # 在开发阶段，返回详细错误信息有助于调试
        raise HTTPException(status_code=500, detail=f"Error querying records: {str(e)}")
    if raw_results:
        logging.info(
            f"[get_datasets_training_state] First record sample: {raw_results[0]}"
        )
    response = DatasetsTrainingStateOut()
    """
    FIFE = "file"
    WEBSITE = "website"
    SITEMAP = "sitemap"
    GITBOOK = "gitbook"
    FAQ = "faq"
    API = "api"
    INTEGRATION = "integration"
    """
    file_types = {
        "file",
        "website",
        "sitemap",
        "gitbook",
        "faq",
        "api",
        "integration",
        "upload",
        "html_pdf",
        "html",
    }
    try:
        for record in raw_results:
            dataset_id = str(record.get("id"))
            dataset_name = record.get("name")
            file_type = record.get("file_type")
            if file_type == "upload":
                file_type = "file"
            elif file_type == "html_pdf":
                file_type = "file"
            elif file_type == "html":
                file_type = "website"
            elif file_type == "faq_question":
                file_type = "faq"
            if file_type not in file_types:
                file_type = "file"

            file_status = record.get("file_status")
            type_count = record.get("type_count")
            dataset_state = response.dataset_state.get(dataset_id)
            if not dataset_state:
                dataset_state = DatasetsTrainingStateOut.DatasetStatisticalInfo(
                    dataset_id=dataset_id, dataset_name=dataset_name
                )
                dataset_state.datasource_statistical_map[file_type][
                    file_status
                ] = type_count
                response.dataset_state[dataset_id] = dataset_state
            else:
                dataset_state.datasource_statistical_map[file_type][
                    file_status
                ] = type_count
            # 修改total
            cur_total = dataset_state.datasource_statistical_map[file_type].get(
                "total", 0
            )
            dataset_state.datasource_statistical_map[file_type]["total"] = (
                cur_total + type_count
            )
        # 统计total
        for dataset_id, dataset_state in response.dataset_state.items():
            # 遍历datasource_statistical_map
            total_map = {}
            for (
                file_type,
                state_map,
            ) in dataset_state.datasource_statistical_map.items():
                for file_status, count in state_map.items():
                    if file_status != "total":
                        total_map[file_status] = total_map.get(file_status, 0) + count
            sum_total = sum(total_map.values())
            total_map["total"] = sum_total
            dataset_state.datasource_statistical_map["total"] = total_map
        return response
    except Exception as e:
        logging.error(f"Error mapping records: {str(e)}")
        # 在开发阶段，返回详细错误信息有助于调试
        raise HTTPException(
            status_code=500, detail=f"Error processing results: {str(e)}"
        )


@router.get(
    "/training/progress/integration/{dataset_id}",
    dependencies=[
        Depends(auth.implicit_scheme),
    ],
)
async def get_integration_state_by_dataset_id(
    dataset_id: UUID,
    request: Request,
    user: Optional[Auth0User] = Security(get_current_oauth_user_with_gbase_user),
) -> IntegrationTrainingStateOut:
    query = """
    select
        ir.id,
        ir.name,
        vf.file_status,
        COUNT(*) as type_count
    from dataset as d
        join integration_rule as ir on d.id=ir.dataset_id
        join integrationrule_vectorfile as irv on ir.id=irv.integration_rule_id
        join vectorfile as vf on irv.vectorfile_id=vf.id
    where d.id= $1 and vf.deleted_at is null
    group by ir.id, ir.name, vf.file_status
    order by ir.id, vf.file_status;
    """
    raw_results = await Tortoise.get_connection("default").execute_query_dict(
        query, [str(dataset_id)]
    )
    logging.info(
        f"[get_datasets_training_state] Query results count: {len(raw_results)}"
    )
    if raw_results:
        logging.info(
            f"[get_datasets_training_state] First record sample: {raw_results[0]}"
        )
    response = IntegrationTrainingStateOut()
    try:
        for record in raw_results:
            integration_rule_id = str(record.get("id"))
            name = record.get("name")
            file_status = record.get("file_status")
            type_count = record.get("type_count")
            integration_state = response.integration_state.get(integration_rule_id)
            if not integration_state:
                integration_state = (
                    IntegrationTrainingStateOut.IntegrationStatisticalInfo(
                        integration_id=integration_rule_id, integration_name=name
                    )
                )
                integration_state.type_count[file_status] = type_count
                response.integration_state[integration_rule_id] = integration_state
            else:
                integration_state.type_count[file_status] = type_count
        return response
    except Exception as e:
        logging.error(f"Error mapping records: {str(e)}")
        # 在开发阶段，返回详细错误信息有助于调试
        raise HTTPException(
            status_code=500, detail=f"Error processing results: {str(e)}"
        )
