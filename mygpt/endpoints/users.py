import asyncio
import traceback
from datetime import datetime, timezone, timedelta
from typing import Op<PERSON>
from urllib.parse import urlenco<PERSON>

from auth0.exceptions import Auth0Error
from fastapi import APIRouter, Body, Depends, HTTPException, Response, Security
from fastapi.responses import RedirectResponse
from fastapi_auth0 import Auth0<PERSON>ser
from fastapi_pagination import Page
from jose import jwt
from loguru import logger as logging
from starlette.status import HTTP_204_NO_CONTENT, HTTP_400_BAD_REQUEST
from tortoise.queryset import Q
from tortoise.transactions import in_transaction

from mygpt.auth0.init import auth
from mygpt.auth0.utils import check_user_role, create_jwt_token_for_user
from mygpt.auth0.utils import get_current_user as _get_current_user
from mygpt.auth0.utils import (
    get_oauth_token,
    get_user_info,
    list_user_roles,
    refresh_jwt_token,
    refresh_oauth_token,
    resend_email_verification,
)
from mygpt.authorization import (
    current_user,
    verify_super_admin_access,
    depend_api_key_with_none,
    Auth0UserWGbaseUser,
    get_current_oauth_user_with_gbase_user,
)
from mygpt.enums import (
    SUPER_USER_MANAGE_TYPE,
    ResourceType,
    UserConfigType,
    UserRole,
)
from mygpt.error import (
    ForbiddenException,
    InvalidParameterException,
    NotFoundException,
    OperationFailedException,
    ServerErrorException,
    UnauthorizedException,
)
from mygpt.models import (
    AccountMember,
    Apikey,
    InsiderPreviewUser,
    OpenaiApikey,
    Robot,
    User,
    UserConfig,
    Dataset,
    SubscriptionStatus,
    Plan,
    PlanSubscription,
    PlanType,
)
from mygpt.parameters import ListAPIParams, tortoise_paginate
from mygpt.schemata import (
    AISimpleOut,
    ApiKeyOut,
    OpenaiApiKeyOut,
    UpdateOpenaiApiKeyIn,
    UserOutBase,
    UserMemberIn,
    UserOut,
    DatasetWithUserOut,
    AdminUserOut,
)
from mygpt.schemata import ApiKey
from mygpt.settings import (
    AUTH0_CLIENT_ID,
    AUTH0_REDIRECT_URI,
    AUTHORIZATION_TYPE,
    AUTHORIZATION_URL,
    JWT_HEADER_KID,
    LOGOUT_URL,
    api_key,
)
from mygpt.utils_stripe import clean_question_cache

router = APIRouter(prefix="", tags=["users"])


@router.get("/login")
def do_login(
    response_type: str = "token",
    state: Optional[str] = None,
    screen_hint: Optional[str] = None,
    redirect_uri: Optional[str] = None,
):
    response_type_qs = urlencode({"response_type": response_type})
    client_id_qs = urlencode({"client_id": AUTH0_CLIENT_ID})
    redirect_uri_qs = urlencode({"redirect_uri": AUTH0_REDIRECT_URI})
    state_qs = urlencode({"state": state})
    scope_qs = urlencode({"scope": "offline_access"})
    auth_url = f"{AUTHORIZATION_URL}&{response_type_qs}&{client_id_qs}&{redirect_uri_qs}&{scope_qs}&{state_qs}&prompt=login"
    if not AUTHORIZATION_URL:
        raise InvalidParameterException()
    if screen_hint:
        auth_url = f"{auth_url}&screen_hint={screen_hint}"

    if AUTHORIZATION_TYPE == "jwt":
        if not redirect_uri:
            raise InvalidParameterException()
        return RedirectResponse(url=redirect_uri, status_code=302)
    return RedirectResponse(url=auth_url, status_code=302)


@router.post("/oauth/token")
async def get_token(code: str = Body(embed=True)):
    try:
        return await asyncio.to_thread(get_oauth_token, code, AUTH0_REDIRECT_URI)
    except Auth0Error as e:
        logging.warning(f"fail to get token for error:{e}")
        raise UnauthorizedException(e.message)
    except Exception as e:
        logging.error(f"fail to get token for error:{e}")
        raise ServerErrorException(e)


@router.post("/oauth/token/refresh")
async def refresh_token(refresh_token: str = Body(embed=True)):
    try:
        try:
            unverified_header = jwt.get_unverified_header(refresh_token)
        except Exception as e:
            unverified_header = None

        if unverified_header and unverified_header["kid"] == JWT_HEADER_KID:
            return await refresh_jwt_token(refresh_token)
        return await asyncio.to_thread(refresh_oauth_token, refresh_token)
    except Auth0Error as e:
        logging.warning(f"fail to refresh token for error:{e}")
        raise UnauthorizedException(e.message)
    except Exception as e:
        logging.error(f"fail to get token for error:{e}")
        raise ServerErrorException(e)


@router.post("/oauth/authorize")
async def authorize(username: str = Body(embed=True), password: str = Body(embed=True)):
    if AUTHORIZATION_TYPE != "jwt":
        raise InvalidParameterException()
    users = await User.filter(email=username, deleted_at__isnull=True)
    user_obj = users[0] if users else None
    if not user_obj:
        raise UnauthorizedException("Invalid username or password.")
    if not await User.verify_password(user_obj.id, password):
        raise UnauthorizedException("Invalid username or password.")

    return await create_jwt_token_for_user(user_obj)


@router.get("/logout")
def do_logout(redirect_uri: Optional[str] = None):
    if AUTHORIZATION_TYPE == "jwt":
        resp = RedirectResponse(
            url=redirect_uri if redirect_uri else "/", status_code=302
        )
        return resp
    return RedirectResponse(url=LOGOUT_URL, status_code=302)


@router.get(
    "/admin/users/{user_id}",
    response_model=AdminUserOut,
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_super_admin_access)],
)
async def get_user_by_id(user_id: str):
    """
    Get a specific user's information by user ID.
    Requires super admin access.
    """
    user_obj = await User.get_or_none(
        id=user_id, deleted_at__isnull=True
    ).prefetch_related("userconfigs")
    if not user_obj:
        raise NotFoundException("User not found.")

    # Auto-subscribe trial plan for users without corporate flag and without existing plan
    if not user_obj.corporate:
        # If no active plan subscription, subscribe to trial
        existing_plan = await user_obj.get_active_plan_subscription()
        if not existing_plan:
            trial_plan = await Plan.get_or_none(
                plan_type=PlanType.TRIAL, deleted_at__isnull=True
            )
            if trial_plan:
                now = datetime.now(timezone.utc)
                expires_at = now + timedelta(days=14)
                await PlanSubscription.create(
                    user_id=user_obj.id,
                    plan_id=trial_plan.id,
                    start_at=now,
                    expires_at=expires_at,
                )

    # Fetch active subscriptions
    active_plan_subscription = await user_obj.get_active_plan_subscription()
    user_obj.active_plan_subscription = active_plan_subscription
    add_on_subscriptions = await user_obj.get_active_add_on_subscription()
    user_obj.active_add_on_subscriptions = add_on_subscriptions

    return user_obj


@router.get(
    "/admin/users",
    response_model=Page[AdminUserOut],
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_super_admin_access)],
)
async def get_all_user(
    page_params: ListAPIParams = Depends(), email: Optional[str] = None
):
    """
    Get a paginated list of users.
    Includes active plan and active addons for each user on the current page.
    Requires super admin access.

    Parameters:
    - page_params: Pagination parameters
    - email: Optional filter to search users by email (case-insensitive partial match)
    """
    try:
        # 使用join一次性获取用户、计划和附加组件
        if email:
            # 如果提供了email参数，使用模糊匹配过滤用户
            user_query = User.filter(
                deleted_at__isnull=True, email__icontains=email
            ).prefetch_related(
                "userconfigs",
                "plan_subscriptions__plan",
                "add_on_subscriptions__add_on",
            )
        else:
            user_query = User.filter(deleted_at__isnull=True).prefetch_related(
                "userconfigs",
                "plan_subscriptions__plan",
                "add_on_subscriptions__add_on",
            )

        # # Prefetch related data needed for the response model UserOutBase
        # user_query = user_query.prefetch_related(
        #     "userconfigs", "plan_subscriptions", "add_on_subscriptions"
        # )

        paginated_users_page = await tortoise_paginate(user_query, page_params)

        # 获取当前UTC时间
        now = datetime.now(timezone.utc)

        # 处理每个用户的活跃计划和附加组件
        for user in paginated_users_page.items:
            # 获取活跃计划 - 根据start_at和expires_at判断
            active_plan_sub = next(
                (
                    sub
                    for sub in user.plan_subscriptions
                    if sub.status == SubscriptionStatus.ACTIVE
                    and sub.deleted_at is None
                    and sub.start_at <= now < sub.expires_at
                ),
                None,
            )
            user.active_plan_subscription = active_plan_sub

            # 获取活跃附加组件 - 根据start_at和expires_at判断
            user.active_add_on_subscriptions = [
                sub
                for sub in user.add_on_subscriptions
                if sub.status == SubscriptionStatus.ACTIVE
                and sub.deleted_at is None
                and sub.start_at <= now < sub.expires_at
            ]
        paginated_users_page.items = [
            await AdminUserOut.from_tortoise_orm(user)
            for user in paginated_users_page.items
        ]
        return paginated_users_page

    except Exception as e:
        logging.error(f"Error retrieving users: {e}, {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Failed to retrieve users.")


@router.get(
    "/users/me",
    response_model=AdminUserOut,
    dependencies=[Depends(auth.implicit_scheme)],
)
async def get_current_user(
    response: Response,
    auth0_user: Auth0User = Security(_get_current_user),
    api_key: ApiKey = Depends(depend_api_key_with_none),
):
    # First try to get user from API key
    user_id = None
    if api_key:
        user_id = api_key.user_id
    # If no API key, try to get user from Auth0
    elif auth0_user:
        user_id = auth0_user.id

    if not user_id:
        raise UnauthorizedException("Login required.")

    # Check if user is insider preview
    is_insider_preview = await InsiderPreviewUser.is_inside_preview_user(user_id)
    if is_insider_preview:
        response.set_cookie(
            key="insider_preview",
            value="always",
            httponly=True,
            max_age=60 * 60 * 24 * 30,
            expires=60 * 60 * 24 * 30,
            path="/",
        )
    else:
        response.delete_cookie(key="insider_preview")

    # Get user object
    user_obj = await User.get_or_none(user_id=user_id).prefetch_related("userconfigs")
    if user_obj:
        # Auto-subscribe trial plan for users without corporate flag and without existing plan
        if not user_obj.corporate:
            # If no active plan subscription, subscribe to trial
            existing_plan = await user_obj.get_active_plan_subscription()
            if not existing_plan:
                trial_plan = await Plan.get_or_none(
                    id="00000001-f0a9-46e4-b866-a8d9cd82fcb2"
                )
                if trial_plan:
                    now = datetime.now(timezone.utc)
                    expires_at = now + timedelta(days=14)
                    await PlanSubscription.create(
                        user_id=user_obj.id,
                        plan_id=trial_plan.id,
                        start_at=now,
                        expires_at=expires_at,
                    )

        # Fetch active subscriptions
        active_plan_subscription = await user_obj.get_active_plan_subscription()
        user_obj.active_plan_subscription = active_plan_subscription
        add_on_subscriptions = await user_obj.get_active_add_on_subscription()
        user_obj.active_add_on_subscriptions = add_on_subscriptions

        return await user_obj

    # If user not found, and we have Auth0 user, try to create new user
    if auth0_user and not api_key:
        try:
            # 当前 jwt 模式不支持创建新用户
            if AUTHORIZATION_TYPE == "jwt":
                raise InvalidParameterException()
            user_dict = await get_user_info(auth0_user.id)
            user_obj = await User.get_or_none(user_id=auth0_user.id).prefetch_related(
                "userconfigs"
            )
            if not user_obj:
                logging.info(f"new user:{auth0_user} created")
                user_obj = await User.create(**user_dict)
                trial_plan = await Plan.get_or_none(
                    id="00000001-f0a9-46e4-b866-a8d9cd82fcb2"
                )
                if trial_plan:
                    now = datetime.now(timezone.utc)
                    expires_at = now + timedelta(days=14)
                    await PlanSubscription.create(
                        user_id=user_obj.id,
                        plan_id=trial_plan.id,
                        start_at=now,
                        expires_at=expires_at,
                    )
                user_obj = await User.get_or_none(user_id=user_id).prefetch_related(
                    "userconfigs"
                )
            active_plan_subscription = await user_obj.get_active_plan_subscription()
            user_obj.active_plan_subscription = active_plan_subscription
            user_obj.active_add_on_subscriptions = []
            return user_obj
        except Exception as e:
            logging.warning(f"fail to get users for error:{e}")
            raise ServerErrorException(e)

    # If we reach here, we couldn't find or create a user
    raise UnauthorizedException("Invalid user credentials.")


@router.get("/users/roles", dependencies=[Depends(auth.implicit_scheme)])
async def get_user_roles(user: Auth0User = Security(auth.get_user)):
    user_roles = await list_user_roles(user.id)
    print(await check_user_role(user.id, UserRole.SUPER_ADMIN))
    return user_roles


@router.post(
    "/users/email-verifications",
    status_code=HTTP_204_NO_CONTENT,
)
async def send_email_verification(
    email: str = Body(embed=True),
):
    await resend_email_verification(email)


@router.post("/user/openai/apikey", dependencies=[Depends(auth.implicit_scheme)])
async def create_openai_api_keys(
    api_key: str, user: Auth0User = Security(auth.get_user)
):
    num = await OpenaiApikey.filter(
        api_key=api_key, user_id=user.id, deleted_at__isnull=True
    ).count()
    if num > 0:
        raise HTTPException(
            status_code=HTTP_400_BAD_REQUEST,
            detail="openai apikey is existed.",
        )

    num = await OpenaiApikey.filter(user_id=user.id, deleted_at__isnull=True).count()
    if num >= 10:
        raise HTTPException(
            status_code=HTTP_400_BAD_REQUEST,
            detail="maximum limit exceeded",
        )
    apikey_obj = await OpenaiApikey.create(
        user_id=user.id,
        api_key=api_key,
    )
    return await OpenaiApiKeyOut.from_tortoise_orm(apikey_obj)


@router.get("/user/openai/apikey", dependencies=[Depends(auth.implicit_scheme)])
async def get_openai_api_keys(user: Auth0User = Security(auth.get_user)):
    apikeys = OpenaiApikey.filter(user_id=user.id, deleted_at__isnull=True)
    apikeys_out = await OpenaiApiKeyOut.from_queryset(apikeys)
    for apikey in apikeys_out:
        apikey.api_key = apikey.api_key[:3] + "***" + apikey.api_key[-4:]
    return apikeys_out


@router.delete("/user/openai/{id}", dependencies=[Depends(auth.implicit_scheme)])
async def delete_openai_api_keys(id: str, user: Auth0User = Security(auth.get_user)):
    query = Q(id=id, user_id=user.id, deleted_at__isnull=True)
    apikey_obj = await OpenaiApikey.get_or_none(query)
    if not apikey_obj:
        raise NotFoundException("Invalid openai apikey id")
    await apikey_obj.soft_delete()


@router.patch(
    "/user/openai/{id}",
    response_model=OpenaiApiKeyOut,
    dependencies=[Depends(auth.implicit_scheme)],
)
async def update_openai_api_key(
    id: str,
    api_key_in: UpdateOpenaiApiKeyIn,
    user: Auth0User = Security(auth.get_user),
):
    openai_apikey = await OpenaiApikey.get_or_none(
        id=id, user_id=user.id, deleted_at__isnull=True
    )
    if not openai_apikey:
        raise NotFoundException("Invalid openai apikey id.")

    openai_apikey.in_use = api_key_in.in_use
    await openai_apikey.save()
    return await OpenaiApiKeyOut.from_tortoise_orm(openai_apikey)


@router.patch(
    "/user/enterprise-plan",
    response_model=UserOutBase,
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_super_admin_access),
    ],
)
async def set_enterprise_plan(
    email: str = Body(embed=True),
    set: bool = Body(embed=True),
):
    users = await User.filter(email=email, deleted_at__isnull=True)
    if not users:
        raise OperationFailedException("Invalid email.")

    for user_obj in users:
        user_obj.corporate = set
        await user_obj.save()
        await clean_question_cache(user_id=user_obj.user_id)

    return await UserOutBase.from_tortoise_orm(users[0])


@router.get(
    "/user/enterprise-plan-list",
    response_model=Page[UserOutBase],
)
async def get_enterprise_plan_list(
    user: Optional[Auth0UserWGbaseUser] = Security(
        get_current_oauth_user_with_gbase_user
    ),
    api_key: ApiKey | None = Depends(depend_api_key_with_none),
    email: str = None,
    params: ListAPIParams = Depends(),
):
    # 验证权限：用户必须是超级管理员
    # 如果使用 API Key，检查 API Key 是否有超级管理员权限
    if api_key:
        if not await api_key.user.is_super_admin(api_key.user_id):
            raise ForbiddenException("Super admin access required")
    # 如果使用 JWT 认证，检查用户是否有超级管理员角色
    elif user and hasattr(user, "user") and user.user:
        # 如果 user 对象已经包含 user 属性（Auth0UserWGbaseUser 类型）
        user_obj = user.user
        if not await user_obj.is_super_admin(user_obj.user_id):
            # 检查 Auth0 角色
            if await check_user_role(user_obj.user_id, UserRole.SUPER_ADMIN):
                await UserConfig.set_config(
                    user_obj.user_id, UserConfigType.SUPER_ADMIN, True
                )
                # 设置了配置后，再次检查
                if await user_obj.is_super_admin(user_obj.user_id):
                    # 成功设置为超级管理员
                    pass
                else:
                    raise ForbiddenException("Super admin access required")
            else:
                raise ForbiddenException("Super admin access required")
    else:
        # 如果既没有 API Key 也没有有效的 JWT 认证，则拒绝访问
        raise UnauthorizedException("Authentication required")

    # Query enterprise users without any related PlanSubscription records at all.
    if email:
        queryset = User.filter(
            corporate=True,
            deleted_at__isnull=True,
            email__icontains=email,
            plan_subscriptions__isnull=True,  # Filter for users with no related subscriptions
        )
    else:
        queryset = User.filter(
            corporate=True,
            deleted_at__isnull=True,
            plan_subscriptions__isnull=True,  # Filter for users with no related subscriptions
        )

    # Prefetch related data needed for the response model UserOutBase
    queryset = queryset.prefetch_related(
        "userconfigs",
        # "plan_subscriptions",
        # "add_on_subscriptions"
    )

    pages = await tortoise_paginate(queryset, params)
    return pages


@router.patch(
    "user/independent",
    response_model=UserOutBase,
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_super_admin_access),
    ],
)
async def set_independent(
    email: str = Body(embed=True),
    set: bool = Body(embed=True),
):
    users = await User.filter(email=email, deleted_at__isnull=True)
    if not users:
        raise OperationFailedException("Invalid email.")

    for user_obj in users:
        user_obj.is_independent = set
        await user_obj.save()

    return await UserOutBase.from_tortoise_orm(users[0])


@router.get(
    "/user/independent-list",
    response_model=Page[UserOutBase],
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_super_admin_access)],
)
async def get_independent_list(email: str = None, params: ListAPIParams = Depends()):
    if email:
        queryset = User.filter(
            is_independent=True, deleted_at__isnull=True, email__icontains=email
        )
    else:
        queryset = User.filter(is_independent=True, deleted_at__isnull=True)
    pages = await tortoise_paginate(queryset, params)
    return pages


@router.post("/auth/keys", dependencies=[Depends(auth.implicit_scheme)])
async def create_api_keys(
    user: Auth0User = Security(auth.get_user),
    description: str = Body(embed=True, default=""),
    with_super_admin: bool = Body(embed=True, default=False),
):
    if with_super_admin and verify_super_admin_access(user) == False:
        raise ForbiddenException()
    if not with_super_admin:
        num = await Apikey.filter(user_id=user.id, deleted_at__isnull=True).count()
        if num >= 10:
            raise OperationFailedException("The Maximum key limit exceeded.")
    apikey_obj = await Apikey.create(
        user_id=user.id,
        description=description,
        api_key=api_key(),
        is_super_admin=with_super_admin,
    )
    return await ApiKeyOut.from_tortoise_orm(apikey_obj)


@router.put("/auth/keys/{api_key}", dependencies=[Depends(auth.implicit_scheme)])
async def update_api_keys(
    api_key: str,
    user: Auth0User = Security(auth.get_user),
    description: str = Body(embed=True, default=""),
    with_super_admin: bool = Body(embed=True, default=False),
):
    if with_super_admin and await verify_super_admin_access(user) == False:
        raise ForbiddenException()
    _filter = {
        "api_key": api_key,
        "deleted_at__isnull": True,
    }
    if not with_super_admin:
        _filter["user_id"] = user.id
    apikey_obj = await Apikey.get_or_none(**_filter)
    if not apikey_obj:
        raise NotFoundException("Invalid api_key")
    apikey_obj.description = description
    await apikey_obj.save()
    return await ApiKeyOut.from_tortoise_orm(apikey_obj)


@router.get("/auth/keys", dependencies=[Depends(auth.implicit_scheme)])
async def get_api_keys(
    user: Auth0User = Security(auth.get_user), with_super_admin: bool = False
):
    _filter = {
        "deleted_at__isnull": True,
        "is_super_admin": with_super_admin,
    }

    if not with_super_admin:
        _filter["user_id"] = user.id
    elif await verify_super_admin_access(user) == False:
        raise ForbiddenException()

    apikeys = Apikey.filter(**_filter)
    apikeys_out = await ApiKeyOut.from_queryset(apikeys)
    for apikey in apikeys_out:
        apikey.api_key = apikey.api_key[:3] + "***" + apikey.api_key[-4:]
    return apikeys_out


@router.delete("/auth/keys/{api_key}", dependencies=[Depends(auth.implicit_scheme)])
async def delete_api_keys(
    api_key: str,
    with_super_admin: bool = False,
    user: Auth0User = Security(auth.get_user),
):
    if with_super_admin and await verify_super_admin_access(user) == False:
        raise ForbiddenException()
    query = Q(deleted_at__isnull=True)
    if not with_super_admin:
        query &= Q(user_id=user.id)
    query &= Q(**{"api_key__icontains": api_key[-4:]})
    apikey_obj = await Apikey.get_or_none(query)
    if not apikey_obj:
        raise NotFoundException("Invalid api_key")
    await apikey_obj.soft_delete()


@router.patch(
    "/users/configs",
    response_model=UserOut,
    dependencies=[Depends(auth.implicit_scheme)],
)
async def update_configs(
    configs: dict = Body(embed=True),
    user: Auth0User = Security(auth.get_user),
):
    user_obj = await User.get_or_none(user_id=user.id)
    if not user_obj:
        raise NotFoundException("Invalid user id.")

    for key in configs.keys():
        try:
            user_config_type = UserConfigType(key)
        except ValueError:
            raise OperationFailedException(
                f"Invalid config: {key}",
            )
        if user_config_type in SUPER_USER_MANAGE_TYPE:
            raise OperationFailedException(f"Not allow to update config: {key}")

    await UserConfig.set_configs(user_obj.user_id, configs)
    user_obj = await User.get_or_none(user_id=user.id)
    return await UserOut.from_tortoise_orm(user_obj)


@router.patch(
    "/configs/email",
    response_model=UserOut,
    dependencies=[Depends(auth.implicit_scheme)],
)
async def update_user_configs(
    email: str = Body(embed=True),
    user_id: Optional[str] = Body(default=None, embed=True),
    configs: dict = Body(embed=True),
    super_user: Auth0User = Security(verify_super_admin_access),
):
    queryset = User.filter(email=email, deleted_at__isnull=True)
    if user_id:
        queryset = queryset.filter(user_id=user_id)
    for config_key in configs.keys():
        try:
            UserConfigType(config_key)
        except ValueError:
            raise OperationFailedException(f"Invalid config: {config_key}")
    user_objs = await queryset
    if not user_objs:
        raise NotFoundException("Invalid user id.")
    if len(user_objs) > 1:
        raise OperationFailedException("More than one user.Try use email with user_id.")
    user_obj = user_objs[0]

    await UserConfig.set_configs(user_obj.user_id, configs)
    await clean_question_cache(user_id=user_obj.user_id)
    user_obj = await User.get_or_none(user_id=user_obj.user_id)
    return await UserOut.from_tortoise_orm(user_obj)


@router.get(
    "/users/email",
    response_model=list[UserOutBase],
    dependencies=[Depends(auth.implicit_scheme)],
)
async def search_users_by_email(
    email: str = None,
):
    # 模糊匹配email
    queryset = User.filter(email__icontains=email, deleted_at__isnull=True)
    return await UserOutBase.from_queryset(queryset)


@router.get(
    "/users/email/equals",
    response_model=list[UserOutBase],
    dependencies=[Depends(auth.implicit_scheme)],
)
async def search_users_by_exact_email(
    email: str = None,
):
    # 精确匹配email
    queryset = User.filter(email__iexact=email, deleted_at__isnull=True)
    return await UserOutBase.from_queryset(queryset)


@router.get(
    "/users/members",
    response_model=Page[AISimpleOut | DatasetWithUserOut],
    dependencies=[Depends(auth.implicit_scheme)],
)
async def get_members(
    resource_type: ResourceType = ResourceType.ROBOT,
    user: User = Depends(current_user),
    params: ListAPIParams = Depends(),
):
    # 模糊匹配email
    queryset = AccountMember.filter(
        member_id=user.user_id,
        resource_type=resource_type,
        deleted_at__isnull=True,
    )
    pages = await tortoise_paginate(queryset, params, ["member"])
    account_members = pages.items
    resource_ids = [m.resource_id for m in account_members]
    resource_klass = Robot if resource_type is ResourceType.ROBOT else Dataset
    resources = await resource_klass.filter(
        id__in=resource_ids,
        deleted_at__isnull=True,
    ).prefetch_related("user")
    resources_dict = {str(r.id): r for r in resources}

    simple_out_klass = (
        AISimpleOut if resource_type is ResourceType.ROBOT else DatasetWithUserOut
    )
    pages.items = [
        await simple_out_klass.from_tortoise_orm(resources_dict[m.resource_id])
        for m in account_members
        if m.resource_id in resources_dict
    ]

    return pages


@router.post(
    "/users/members",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[Depends(auth.implicit_scheme)],
)
async def add_members(
    params: UserMemberIn,
    user: User = Depends(current_user),
):
    # 启用一个数据库事物
    async with in_transaction("default") as conn:
        for item in params.req:
            resource_klass = (
                Robot if item.resource_type is ResourceType.ROBOT else Dataset
            )
            # check user own robot
            resource = await resource_klass.get_or_none(
                id=item.resource_id,
                user_id=user.user_id,
                deleted_at__isnull=True,
            )
            if not resource:
                raise OperationFailedException(
                    f"Invalid resource id: {item.resource_id}"
                )
            # check user own member
            for user_id in item.user_ids:
                if user_id == user.user_id:
                    raise OperationFailedException(
                        f"User {user.email} cannot add self."
                    )
                member = await User.get_or_none(
                    user_id=user_id, deleted_at__isnull=True
                )
                if not member:
                    raise OperationFailedException(f"Invalid member id: {user_id}")
                # check member is already added
                exists = await AccountMember.filter(
                    user_id=user.user_id,
                    member_id=member.user_id,
                    resource_id=resource.id,
                    resource_type=item.resource_type,
                    deleted_at__isnull=True,
                ).exists()
                if exists:
                    raise OperationFailedException(
                        f"User {member.email} already added."
                    )
                await AccountMember.create(
                    using_db=conn,
                    user=user,
                    member=member,
                    resource_id=resource.id,
                    resource_type=item.resource_type,
                )
                await UserConfig.set_config(
                    member.user_id,
                    UserConfigType.INVITE_NOTIFICATION,
                    True,
                    using_db=conn,
                )


@router.delete(
    "/users/members",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[Depends(auth.implicit_scheme)],
)
async def delete_members(
    params: UserMemberIn,
    user: User = Depends(current_user),
):
    # 启用一个数据库事物
    async with in_transaction("default") as conn:
        for item in params.req:
            resource_klass = (
                Robot if item.resource_type is ResourceType.ROBOT else Dataset
            )
            # check user own robot
            resource = await resource_klass.get_or_none(
                id=item.resource_id,
                user_id=user.user_id,
                deleted_at__isnull=True,
            )
            if not resource:
                raise OperationFailedException(
                    f"Invalid resource id: {item.resource_id}"
                )
            # check user own member
            for user_id in item.user_ids:
                member = await User.get_or_none(
                    user_id=user_id, deleted_at__isnull=True
                )
                if not member:
                    raise OperationFailedException(f"Invalid member id: {user_id}")
                # check member is already added
                account_member = await AccountMember.get_or_none(
                    user_id=user.user_id,
                    member_id=member.user_id,
                    resource_id=resource.id,
                    resource_type=item.resource_type,
                    deleted_at__isnull=True,
                )
                if not account_member:
                    raise OperationFailedException(f"User {member.email} not added.")
                account_member.deleted_at = datetime.now()
                await account_member.save(
                    using_db=conn,
                    update_fields=["deleted_at"],
                )


@router.delete(
    "/users/members/{resource_id}",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[Depends(auth.implicit_scheme)],
)
async def leave_members(
    resource_id: str,
    resource_type: ResourceType = ResourceType.ROBOT,
    user: User = Depends(current_user),
):
    resource_klass = Robot if resource_type is ResourceType.ROBOT else Dataset
    resource = await resource_klass.get_or_none(id=resource_id, deleted_at__isnull=True)
    if not resource:
        raise OperationFailedException(f"Invalid resource id: {resource_id}")
    account_member = await AccountMember.get_or_none(
        member_id=user.user_id,
        resource_id=resource.id,
        resource_type=resource_type,
        deleted_at__isnull=True,
    )
    if not account_member:
        raise OperationFailedException(f"User {user.email} not added.")
    await account_member.soft_delete()
