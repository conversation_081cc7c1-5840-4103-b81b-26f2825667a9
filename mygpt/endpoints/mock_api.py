"""
Mock API for testing purposes
这个文件是用来模拟API的，用于测试目的, 所以枚举类, 模型类, 接口类, 函数等都定义在这个文件中, 防止与业务代码混淆.
代码会根据业务需求动态调整, 请注意代码的变化.
如果需要删除和修改代码, 请联系相关人员告知一下.
"""

import uuid
from enum import Enum

from dataclasses import dataclass

import asyncio
import json
import re
import time
import json
import datetime
from loguru import logger as logging
from typing import Optional, List, Union
from uuid import UUID, uuid4

from pydantic.main import BaseModel

from fastapi import APIRouter, Depends, Request, Security

from mygpt import settings

mock_router = APIRouter(prefix="/mock", tags=["Functions"])


"""
    id: int                           # 改为 int 类型
    uid: str
    title: str
    date: str                         # YYYY-MM-DD
    time: str                         # HH:mm
    endTime: str                      # HH:mm
    description: str
    location: Optional[str] = None    # 改为可选
    user: str
    guests: List[str]
    status: MeetingStatus            # 使用枚举类型
    category: Optional[str] = None
    priority: Optional[int] = None
    created: Optional[str] = None     # ISO 格式时间字符串
    lastModified: Optional[str] = None # ISO 格式时间字符串
    timezone: Optional[str] = None
"""


# 定义会议状态枚举
class MeetingStatus(str, Enum):
    CONFIRMED = "CONFIRMED"
    TENTATIVE = "TENTATIVE"
    CANCELLED = "CANCELLED"


class Meeting(BaseModel):
    id: int
    uid: uuid.UUID
    title: str
    date: datetime.date
    start_time: datetime.time
    end_time: datetime.time
    user: str
    description: str
    location: Optional[str]
    participants: List[str]
    status: MeetingStatus
    category: Optional[str]

    def to_meeting_out(self) -> "MeetingOut":
        """转换为 MeetingOut"""
        return MeetingOut(
            id=self.id,
            uid=str(self.uid),
            title=self.title,
            date=self.date.isoformat(),
            time=self.start_time.strftime("%H:%M"),
            endTime=self.end_time.strftime("%H:%M"),
            description=self.description,
            location=self.location,
            user=self.user if self.user else "システム",  # 可以添加默认值
            guests=self.participants,
            status=self.status,  # 可以添加默认值
            category="MEETING",
            priority=1,
            timezone="Asia/Tokyo",
            created=datetime.datetime.now().isoformat(),
            lastModified=datetime.datetime.now().isoformat(),
        )


class RedisMeetingStorage:
    def __init__(self, ttl: Optional[int] = None):
        self.key_prefix = "meeting:"
        self.detail_prefix = f"{self.key_prefix}detail:"
        self.all_meetings_key = f"{self.key_prefix}all"
        self.id_counter_key = f"{self.key_prefix}id:counter"
        self.ttl = ttl
        try:
            import redis
        except ImportError:
            raise ImportError(
                "Could not import redis python package. "
                "Please install it with `pip install redis`."
            )

        try:
            self.redis_client = settings.RedisClient.get_client()
        except redis.exceptions.ConnectionError as error:
            logging.error(f"Redis connection error: {error}")

    @property
    def key(self) -> str:
        """Construct the record key to use"""
        return self.key_prefix

    async def save_meeting(self, meeting: Meeting):
        """Save a meeting to Redis"""
        meeting.id = await self.redis_client.incr(self.id_counter_key)
        key = f"{self.detail_prefix}{meeting.uid}"
        # 将Meeting对象转换为dict并存储
        meeting_dict = meeting.dict()
        # 对一些字段特殊处理
        meeting_dict["date"] = meeting.date.isoformat()
        meeting_dict["start_time"] = meeting.start_time.strftime("%H:%M")
        meeting_dict["end_time"] = meeting.end_time.strftime("%H:%M")
        meeting_dict["uid"] = str(meeting.uid)
        meeting_dict["participants"] = json.dumps(
            meeting.participants, ensure_ascii=False
        )
        meeting_dict["status"] = meeting.status.value
        # 使用hash结构存储会议详情
        res1 = await self.redis_client.hset(key, mapping=meeting_dict)
        # 添加到会议集合中
        res2 = await self.redis_client.sadd(self.all_meetings_key, str(meeting.uid))
        return res1, res2

    async def get_all_meetings(self) -> List[Meeting]:
        """获取所有会议"""
        # 获取所有会议的uid
        all_meetings = await self.redis_client.smembers(self.all_meetings_key)
        # 获取所有会议的详情
        meetings = []
        for uid in all_meetings:
            key = f"{self.detail_prefix}{uid}"
            meeting_dict = await self.redis_client.hgetall(key)
            if meeting_dict:
                # 对一些字段特殊处理
                meeting_dict["id"] = int(meeting_dict["id"])
                meeting_dict["date"] = datetime.datetime.strptime(
                    meeting_dict["date"], "%Y-%m-%d"
                ).date()
                meeting_dict["start_time"] = datetime.datetime.strptime(
                    meeting_dict["start_time"], "%H:%M"
                ).time()
                meeting_dict["end_time"] = datetime.datetime.strptime(
                    meeting_dict["end_time"], "%H:%M"
                ).time()
                meeting_dict["uid"] = uuid.UUID(meeting_dict["uid"])
                meeting_dict["participants"] = json.loads(meeting_dict["participants"])
                meeting_dict["status"] = MeetingStatus(meeting_dict["status"])
                # 创建Meeting对象
                meeting = Meeting(**meeting_dict)
                meetings.append(meeting)
        return meetings


# 创建一个RedisMeetingStorage实例
storage = RedisMeetingStorage(ttl=604800)

# 全局信息
meetings_list: List[Meeting] = []
meeting_map: dict[uuid.UUID, Meeting] = {}
cur_meeting_id = 0
test_meeting = Meeting(
    id=cur_meeting_id,
    uid=uuid.uuid4(),
    title="业务会议",
    user="山田",
    date=datetime.date(2025, 1, 16),
    start_time=datetime.time(9, 0),
    end_time=datetime.time(9, 30),
    description="讨论业务合作事宜",
    location="腾讯会议",
    participants=["<EMAIL>", "<EMAIL>"],
    status=MeetingStatus.CONFIRMED,
)
meetings_list.append(test_meeting)
meeting_map[test_meeting.uid] = test_meeting


class MeetingIn(BaseModel):
    title: str
    date: datetime.date
    time: datetime.time
    endTime: datetime.time
    description: Optional[str] = ""
    location: Optional[str] = ""
    user: str
    guests: Union[List[str], str] = ""
    status: MeetingStatus = MeetingStatus.CONFIRMED
    category: Optional[str] = ""
    priority: Optional[int] = 1


class BaseResponse(BaseModel):
    success: bool = True
    message: Optional[str] = None
    data: Optional[Union[dict, BaseModel]] = None


class MeetingOut(BaseModel):
    id: int  # 改为 int 类型
    uid: str
    title: str
    date: str  # YYYY-MM-DD
    time: str  # HH:mm
    endTime: str  # HH:mm
    description: str
    location: Optional[str] = None  # 改为可选
    user: str
    guests: List[str]
    status: MeetingStatus  # 使用枚举类型
    category: Optional[str] = None
    priority: Optional[int] = None
    created: Optional[str] = None  # ISO 格式时间字符串
    lastModified: Optional[str] = None  # ISO 格式时间字符串
    timezone: Optional[str] = None


# API 响应模型
class MeetingListResponse(BaseResponse):
    data: Optional[dict] = {"events": List[MeetingOut], "total": int}


def parse_guests(guests_input: Union[List[str], str]) -> List[str]:
    """解析guests参数，返回列表格式
    支持以下格式：
    1. JSON数组字符串: '["<EMAIL>", "<EMAIL>"]'
    2. Python列表字符串: "['<EMAIL>', '<EMAIL>']"
    3. 逗号分隔的字符串: '<EMAIL>, <EMAIL>'
    4. 已经是列表类型: ["<EMAIL>", "<EMAIL>"]
    5. 单个邮箱字符串: '<EMAIL>'
    """
    if not guests_input:
        return []

    if isinstance(guests_input, list):
        return guests_input

    if isinstance(guests_input, str):
        # 去除首尾空格
        guests_input = guests_input.strip()

        if not guests_input:
            return []

        try:
            # 尝试解析JSON
            guests_list = json.loads(guests_input)
            if isinstance(guests_list, list):
                return guests_list
            elif isinstance(guests_list, str):
                return [guests_list]
        except json.JSONDecodeError:
            try:
                # 尝试解析Python列表字符串
                if guests_input.startswith("[") and guests_input.endswith("]"):
                    # 使用ast.literal_eval安全地解析Python字面量
                    import ast

                    guests_list = ast.literal_eval(guests_input)
                    if isinstance(guests_list, list):
                        return guests_list
            except (ValueError, SyntaxError):
                # 如果Python列表解析失败，尝试按逗号分隔
                guests_list = [email.strip() for email in guests_input.split(",")]
                # 过滤掉空字符串
                return [email for email in guests_list if email]
    return []


@mock_router.post(
    "/meeting",
    # response_model=HisTrafficInformationOut
)
async def mock_create_meeting(meeting_in: MeetingIn) -> MeetingListResponse:
    """创建会议"""
    print(meeting_in)
    response = MeetingListResponse()
    # 检查会议时间是否冲突
    for meeting in meetings_list:
        if meeting.date == meeting_in.date:
            if (
                meeting.start_time <= meeting_in.time <= meeting.end_time
                or meeting.start_time <= meeting_in.endTime <= meeting.end_time
            ):
                response.message = "会议时间冲突"
                response.success = False
                return response
    if isinstance(meeting_in.guests, str):
        participants = parse_guests(meeting_in.guests)
    elif isinstance(meeting_in.guests, list):
        participants = meeting_in.guests
    else:
        participants = []
    # 创建会议
    global cur_meeting_id
    cur_meeting_id += 1
    meeting_obj = Meeting(
        id=cur_meeting_id,
        uid=uuid.uuid4(),
        title=meeting_in.title,
        date=meeting_in.date,
        start_time=meeting_in.time,
        end_time=meeting_in.endTime,
        description=meeting_in.description,
        location=meeting_in.location,
        participants=participants,
        user=meeting_in.user,
        status=meeting_in.status,
        category=meeting_in.category,
    )
    # 保存到Redis
    res1, res2 = await storage.save_meeting(meeting_obj)
    # meetings_list.append(meeting_obj)
    # meeting_map[meeting_obj.uid] = meeting_obj
    if not res1 or not res2:
        response.message = "创建会议失败"
        response.success = False
    else:
        response.message = "创建会议成功"
        response.success = True
        response.data = meeting_obj.to_meeting_out()
    return response


@mock_router.get("/meeting")
async def mock_list_meetings() -> MeetingListResponse:
    """列出所有会议"""
    response = MeetingListResponse()
    try:
        # 获取所有会议
        meetings = await storage.get_all_meetings()
        # 转换为输出格式
        meeting_outs = [meeting.to_meeting_out() for meeting in meetings]
        response.message = f"获取会议列表成功"
        response.success = True
        response.data = {"events": meeting_outs, "total": len(meeting_outs)}
        # 模拟数据
        # meetings = [meeting.to_meeting_out() for meeting in meetings_list]
    except Exception as e:
        response.message = f"获取会议列表失败: {e}"
        response.success = False
        response.data = {"events": [], "total": 0}
    return response


@mock_router.put("/meeting/{meeting_id}")
async def mock_update_meeting(meeting_id: str, meeting: MeetingIn):
    """更新会议"""
    pass


@mock_router.delete("/meeting/{meeting_id}")
async def mock_delete_meeting(meeting_id: str):
    """删除会议"""
    pass
