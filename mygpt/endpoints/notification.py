import asyncio
import base64
import json
from datetime import datetime
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, Query
from fastapi.security import SecurityScopes
from fastapi_auth0.auth import HTTPAuthorizationCredentials
from loguru import logger as logging
from pydantic import BaseModel
from starlette.websockets import WebSocket, WebSocketState, WebSocketDisconnect

from mygpt.auth0.init import auth
from mygpt.authorization import verify_admin_access
from mygpt.enums import AIConfigType
from mygpt.error import UnauthorizedException
from mygpt.models import Robot, RobotConfig
from mygpt.schemata import ClientWebsocketsOut
from mygpt.settings import SERVER_RUNNING, RedisClient

router = APIRouter(prefix="/notification", tags=["Push Notification"])


class Message:
    class WebsocketRequest(BaseModel):
        type: str
        cmd: str
        data: dict

    class WebsocketResponse(BaseModel):
        type: str
        cmd: str
        result: str
        data: dict

    class WebsocketNotification(BaseModel):
        type: str
        event: str
        data: dict

    ping_request = WebsocketRequest(type="request", cmd="ping", data={})

    ping_response = WebsocketResponse(
        type="response", cmd="pong", result="", data={"time": ""}
    )

    login_request = WebsocketRequest(
        type="request",
        cmd="login_req",
        data={
            "ai_id": "",
            "user_id": "",
            "email": "",
            "page_url": "",
            "session_id": "",
            "session_type": "",
        },
    )

    login_response = WebsocketResponse(
        type="response",
        cmd="login_resp",
        result="",
        data={"err_code": "", "err_msg": ""},
    )

    client_login_notification = WebsocketNotification(
        type="notification",
        event="login",
        data={
            "connection_id": "",
            "toast": True,
            "ai_id": "",
            "user_id": "",
            "page_url": "",
            "email": "",
            "session_id": "",
            "session_type": "",
        },
    )

    client_logout_notification = WebsocketNotification(
        type="notification",
        event="logout",
        data={
            "connection_id": "",
            "toast": True,
            "ai_id": "",
            "user_id": "",
            "page_url": "",
            "email": "",
            "session_id": "",
            "session_type": "",
        },
    )


class Notifier:
    def __init__(self):
        self.client_local_connections: Dict[str, WebSocket] = {}  # 本机client连接
        self.admin_local_connections: Dict[str, WebSocket] = {}  # 本机admin连接
        self.redis_key_template = "notification:{ai_id}:{structure}"
        self.notification_channel = "notification_client_login"
        self.expire_interval = 60

    async def init(self):
        # 订阅 notification_channel
        asyncio.create_task(self.clear_expire_connections())

        await self.init_message_handlers()

    async def connect(self, ip_addr: str, websocket: WebSocket, is_admin: bool):
        await websocket.accept()

        client_addr = ip_addr if ip_addr else websocket.client.host
        client_addr = f"{client_addr}:{websocket.client.port}"
        connection_id = base64.b64encode(client_addr.encode("utf-8")).decode("utf-8")

        client_data = ClientWebsocketsOut(
            connection_id=connection_id,
            client_addr=client_addr,
            created_at=datetime.utcnow().isoformat(),
        )

        websocket.client_data = client_data
        websocket.active_time = datetime.utcnow()

        if is_admin:
            self.admin_local_connections[connection_id] = websocket
        else:
            self.client_local_connections[connection_id] = websocket

    # client连接保存到redis，为了集群部署可以获取AI的所有client连接
    async def update_client_connection(self, client_data: ClientWebsocketsOut):
        hash_key = self.redis_key_template.format(
            ai_id=client_data.ai_id, structure="hash"
        )
        zset_key = self.redis_key_template.format(
            ai_id=client_data.ai_id, structure="zset"
        )

        # Add data to the hash
        await RedisClient.get_client().hset(
            hash_key, str(client_data.connection_id), json.dumps(client_data.dict())
        )
        await RedisClient.get_client().expire(
            hash_key, self.expire_interval
        )  # 更新整个ai_id数据结构的过期时间，如果这个AI没有任何client发送心跳，这个数据结构将被清除

        # Add data to the sorted set with the current timestamp
        current_timestamp = await RedisClient.get_client().time()
        score = int(current_timestamp[0])  # Extract the seconds part
        await RedisClient.get_client().zadd(
            zset_key, {str(client_data.connection_id): score}
        )
        await RedisClient.get_client().expire(
            zset_key, self.expire_interval
        )  # 更新整个ai_id数据结构的过期时间，如果这个AI没有任何client发送心跳，这个数据结构将被清除

    # 移除client连接
    async def remove_client(self, websocket: WebSocket):
        # 从本地cache移除
        connection_id = websocket.client_data.connection_id
        if connection_id in self.client_local_connections:
            self.client_local_connections.pop(connection_id)
        else:
            return

        ai_id = websocket.client_data.ai_id
        if not ai_id:
            return

        # await self.pub_client_log_event('Logout', websocket.client_data)
        hash_key = self.redis_key_template.format(ai_id=ai_id, structure="hash")
        zset_key = self.redis_key_template.format(ai_id=ai_id, structure="zset")

        # Remove data from the hash
        await RedisClient.get_client().hdel(hash_key, str(connection_id))

        # Remove data from the sorted set
        await RedisClient.get_client().zrem(zset_key, str(connection_id))

        await self.pub_client_log_event("Logout", websocket.client_data)

    # 移除admin连接
    async def remove_admin(self, websocket: WebSocket):
        connection_id = websocket.client_data.connection_id
        if connection_id in self.admin_local_connections:
            self.admin_local_connections.pop(connection_id)

    # 获取AI所有client连接
    async def get_ai_client_connections(self, ai_id: str):
        hash_key = self.redis_key_template.format(ai_id=ai_id, structure="hash")
        zset_key = self.redis_key_template.format(ai_id=ai_id, structure="zset")

        all_members_with_scores = await RedisClient.get_client().zrange(
            zset_key, 0, -1, withscores=True
        )  # Get both members and scores
        all_members, all_scores = (
            zip(*all_members_with_scores) if all_members_with_scores else ([], [])
        )

        active_members = []
        expired_members = []
        current_timestamp = await RedisClient.get_client().time()
        current_timestamp = int(current_timestamp[0])
        min_timestamp = current_timestamp - self.expire_interval
        for member, score in zip(all_members, all_scores):
            if min_timestamp <= score <= current_timestamp:
                active_members.append(member)
            else:
                expired_members.append(member)

        # # Get data from the hash
        hash_data = await RedisClient.get_client().hgetall(hash_key)
        recent_data = [
            json.loads(hash_data.get(member, "{}")) for member in active_members
        ]

        if expired_members:
            await RedisClient.get_client().zrem(zset_key, *expired_members)
            await RedisClient.get_client().hdel(hash_key, *expired_members)

        return recent_data

    # 处理client登录请求
    async def handle_client_login_request(self, websocket: WebSocket, json_obj, token):
        data = json_obj.get("data")
        if not data:
            logging.error(f"login request missing data field")
            return

        # ai_id必须存在
        ai_id = data.get("ai_id", None)
        ai_obj = await Robot.get_or_none(
            id=ai_id, deleted_at__isnull=True
        ).prefetch_related("user")
        if not ai_obj:
            logging.error(f"ai_id:{ai_id} does not exist")
            await self.respond_login_request(websocket, False, "AI not found")
            return

        user_id = data.get("user_id", "")
        email = data.get("email", "")
        if token is not None:
            creds = HTTPAuthorizationCredentials(scheme="Bearer", credentials=token)
            user = await auth.get_user(SecurityScopes(scopes=[]), creds)
            if user is not None:
                user_id = user.id
                email = user.email

        logging.info(f"client login success, user_id:{user_id}, email:{email}")

        websocket.client_data.user_id = user_id
        websocket.client_data.email = email
        websocket.client_data.ai_id = data.get("ai_id", "")
        websocket.client_data.session_id = data.get("session_id", "")
        websocket.client_data.session_type = data.get("session_type", "")
        websocket.client_data.page_url = data.get("page_url", "")

        # client添加到redis
        await self.update_client_connection(websocket.client_data)

        # 回复登录请求
        await self.respond_login_request(websocket, True, "")

        # 通知AI的管理员
        await self.pub_client_log_event("Login", websocket.client_data)

    # 处理client发来的ping请求
    async def handle_client_ping_request(self, websocket: WebSocket, json_obj):
        if not websocket.client_data or not websocket.client_data.ai_id:
            logging.warning(
                f"client does not login, drop the ping request, connection_id:{websocket.client_data.connection_id}"
            )
            return

        # 更新连接的活动时间
        websocket.active_time = datetime.utcnow()
        await self.update_client_connection(websocket.client_data)

        # 回复ping请求
        await self.respond_ping_request(websocket)

    # 处理client发来的ping请求
    async def handle_admin_ping_request(self, websocket: WebSocket, json_obj):
        user_id = websocket.client_data.user_id
        if not user_id:
            logging.error(
                f"client does not login, drop the ping request, connection_id:{websocket.connection_id}"
            )
            return

        websocket.active_time = datetime.utcnow()

        # 回复ping请求
        await self.respond_ping_request(websocket)

    # 处理admin登录
    async def handle_admin_login_request(self, websocket: WebSocket, token):
        creds = HTTPAuthorizationCredentials(scheme="Bearer", credentials=token)
        user = await auth.get_user(SecurityScopes(scopes=[]), creds)
        if not user:
            await self.respond_login_request(websocket, False, "Authentication failed")

            await asyncio.sleep(3)  # sleep 3秒再断开，让客户端有机会处理登录失败回复
            raise UnauthorizedException("Authentication failed")
        else:
            websocket.client_data.user_id = user.id
            await self.respond_login_request(websocket, True, "")

    # 通过redis广播client登录/登出事件
    async def pub_client_log_event(self, event, client_data):
        logging.info(
            f"publish {event} event to redis, connection_id:{client_data.connection_id}"
        )

        notification_msg = Message.client_login_notification.copy()
        if event == "Logout":
            notification_msg = Message.client_logout_notification.copy()

        notification_msg.data["connection_id"] = client_data.connection_id
        notification_msg.data["ai_id"] = client_data.ai_id
        notification_msg.data["session_id"] = client_data.session_id
        notification_msg.data["session_type"] = client_data.session_type
        notification_msg.data["user_id"] = client_data.user_id
        notification_msg.data["page_url"] = client_data.page_url
        notification_msg.data["email"] = client_data.email
        notification_msg.data["created_at"] = client_data.created_at
        await RedisClient.get_client().publish(
            self.notification_channel, json.dumps(notification_msg.dict())
        )

    # 清除过期连接
    async def clear_expire_connections(self):
        async def _clean(client_type: str, websocket: WebSocket):
            try:
                if websocket.application_state == WebSocketState.CONNECTED:
                    # logging.info(f'websocket timeout, close the websocket')
                    await websocket.close()  # 这里断开后，endpoint会捕获到异常，然后清除连接
            except Exception as e:
                # logging.warning(f'websocket close failed, error:{e}')
                if client_type == "client":
                    await self.remove_client(websocket)
                else:
                    await self.remove_admin(websocket)

        while SERVER_RUNNING:
            current_time = datetime.utcnow()
            for websocket in list(self.admin_local_connections.values()):
                if (
                    current_time - websocket.active_time
                ).total_seconds() > self.expire_interval:
                    await _clean("admin", websocket)

            for websocket in list(self.client_local_connections.values()):
                if (
                    current_time - websocket.active_time
                ).total_seconds() > self.expire_interval:
                    await _clean("client", websocket)

            await asyncio.sleep(3)  # pause to prevent high CPU usage

    async def handle_redis_message(self, message):
        # 通过list拷贝一份，避免在遍历时删除元素导致问题：dictionary changed size during iteration
        for admin_websocket in list(self.admin_local_connections.values()):
            notification_msg = json.loads(message)  # redis的消息包了一层data
            data = notification_msg["data"]
            ai_id = data["ai_id"]
            ai_obj = await Robot.get_or_none(
                id=ai_id, deleted_at__isnull=True
            ).prefetch_related("user")
            if not ai_obj:
                logging.error(f"User not found for ai_id:{ai_id}")
                return

            toast = True
            notify_admin = await RobotConfig.get_config(
                ai_obj.id, key=AIConfigType.NOTIFICATION_ADMIN
            )
            if notify_admin is None:
                toast = True
            elif notify_admin == "False":
                toast = False

            notification_msg["data"]["toast"] = toast

            if admin_websocket.client_data.user_id == ai_obj.user.user_id:
                logging.info(f"notify admin websocket, ai_id:{ai_id}")
                try:
                    await admin_websocket.send_json(notification_msg)
                except Exception as e:
                    logging.warning(f"websocket send response failed, error:{e}")

    # 回复login，client和admin统一回复
    async def respond_login_request(
        self, websocket: WebSocket, succ: bool, error_message: str
    ):
        login_response = Message.login_response.copy()
        login_response.result = "success" if succ else "error"
        login_response.data["err_code"] = "0"
        login_response.data["err_msg"] = error_message
        try:
            await websocket.send_json(login_response.dict())
        except Exception as e:
            logging.warning(f"websocket send login response failed, error:{e}")

    # 回复ping，client和admin统一回复
    async def respond_ping_request(self, websocket: WebSocket):
        if websocket.application_state == WebSocketState.CONNECTED:
            ping_response = Message.ping_response.copy()
            ping_response.data["time"] = datetime.utcnow().isoformat()
            try:
                await websocket.send_json(ping_response.dict())
            except Exception as e:
                logging.warning(f"websocket send ping response failed, error:{e}")
        else:
            logging.info(f"WebSocket connection is closed, cannot send data.")

    async def init_message_handlers(self):
        self.message_handlers = {
            ("client", "login_req"): self.handle_client_login_request,
            ("client", "ping"): self.handle_client_ping_request,
            # ('admin', 'login_req'): self.handle_admin_login_request, 管理员登录不进这里
            ("admin", "ping"): self.handle_admin_ping_request,
        }

    async def handle_message(self, websocket: WebSocket, json_obj, token, channel_type):
        cmd = json_obj.get("cmd")
        handler = self.message_handlers.get((channel_type, cmd))
        if handler:
            if cmd == "login_req":
                await handler(websocket, json_obj, token)
            else:
                await handler(websocket, json_obj)
        else:
            # logging.error(f'unknown message, drop the request')
            pass


notifier = Notifier()


@router.websocket("/client/ws")
async def websocket_client_endpoint(websocket: WebSocket, token: Optional[str] = None):
    ip_addr = websocket.headers.get("x-real-ip")
    await notifier.connect(ip_addr, websocket, False)
    try:
        while SERVER_RUNNING:
            if websocket.application_state == WebSocketState.CONNECTING:
                # 如果状态还是在连接中，等待一下
                asyncio.sleep(0.01)
                continue
            if websocket.application_state == WebSocketState.DISCONNECTED:
                break
            json_obj = await websocket.receive_json()
            await notifier.handle_message(websocket, json_obj, token, "client")

    except WebSocketDisconnect:
        logging.info(f"client websocket disconnected")
        await notifier.remove_client(websocket)


@router.websocket("/admin/ws")
async def websocket_admin_endpoint(websocket: WebSocket, token: str = Query(...)):
    ip_addr = websocket.headers.get("x-real-ip")
    await notifier.connect(ip_addr, websocket, True)
    try:
        # 只需要token就可以完成登录
        await notifier.handle_admin_login_request(websocket, token)

        while SERVER_RUNNING:
            if websocket.application_state == WebSocketState.CONNECTING:
                # 如果状态还是在连接中，等待一下
                asyncio.sleep(0.01)
                continue
            if websocket.application_state == WebSocketState.DISCONNECTED:
                break
            json_obj = await websocket.receive_json()
            await notifier.handle_message(websocket, json_obj, token, "admin")

    except WebSocketDisconnect:
        logging.info(f"admin websocket disconnected")
        await notifier.remove_admin(websocket)


@router.get(
    "/{ai_id}/client.connections",
    response_model=List[ClientWebsocketsOut],
    dependencies=[Depends(verify_admin_access)],
)
async def get_ai_client_connections(ai_id: str):
    return await notifier.get_ai_client_connections(ai_id)


@router.on_event("startup")
async def startup():
    await notifier.init()
