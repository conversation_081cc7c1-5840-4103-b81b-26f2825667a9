from fastapi import APIRouter, File, Form, UploadFile
from starlette.status import HTTP_201_CREATED

from mygpt.enums import FileSource, FileType
from mygpt.error import NotFoundException
from mygpt.models import Dataset, ImageAttachment, Robot
from mygpt.schemata import ImageAttachmentOut
from mygpt.upload import upload_image_attachment as _upload_image_attachment

router = APIRouter(prefix="/attachments", tags=["Attachment"])


@router.post("/images", response_model=ImageAttachmentOut, status_code=HTTP_201_CREATED)
async def upload_image_attachment(
    file_type: FileType = Form(...),
    file: UploadFile = File(...),
    ai_id: str = Form(None),
):
    dataset_obj = await Dataset.get_or_none(id=ai_id)
    if not dataset_obj:
        robot_obj = await Robot.get_or_none(id=ai_id)
        if not robot_obj:
            raise NotFoundException("ai_id or dataset_id not found")

    return await _upload_image_attachment(
        file_type=file_type, file=file, ai_id=ai_id, file_source=FileSource.DATASET
    )


@router.get("/images/{attachment_id}", response_model=ImageAttachmentOut)
async def get_image_attachment(
    attachment_id: str,
):
    return await ImageAttachmentOut.from_queryset_single(
        ImageAttachment.get(id=attachment_id)
    )
