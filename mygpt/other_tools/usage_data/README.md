# 统计过去三个月内每个机器人的消息数量，排除测试消息

## 统计：
机器人 ID，名称，创建时间，数量消息总数(排查测试类型的消息)，结果按照机器人创建时间降序排列结果

```sql
SELECT
    r.id AS bot_id,
    r.name AS bot_name,
    r.created_at AS bot_create_at,
--     u.user_id AS creator_id,
--     u.name AS creator_username,
    u.email AS creator_email,
    COUNT(sm.id) AS total_message_count
FROM
    robot r
LEFT JOIN
    "user" u ON r.user_id = u.user_id
LEFT JOIN
    session s ON r.id = s.robot_id
LEFT JOIN
    sessionmessage sm ON s.session_id = sm.session_id
WHERE
    sm.created_at >= CURRENT_DATE - INTERVAL '3 months'
    AND sm.created_at <= CURRENT_DATE
    AND sm.is_test = false
GROUP BY
    r.id, r.name, r.created_at, u.user_id, u.name, u.email
ORDER BY
    total_message_count DESC;
```


注册和活跃用户统计，某个已注册用户有bot聊天记录就认为用户活跃
```sql
WITH monthly_stats AS (
    SELECT
        DATE_TRUNC('month', u.created_at) AS month,
        COUNT(DISTINCT u.user_id) AS new_users,
        COUNT(DISTINCT CASE WHEN sm.id IS NOT NULL THEN u.user_id END) AS active_users
    FROM
        "user" u
    LEFT JOIN
        robot r ON u.user_id = r.user_id
    LEFT JOIN
        session s ON r.id = s.robot_id
    LEFT JOIN
        sessionmessage sm ON s.session_id = sm.session_id
        AND sm.created_at >= DATE_TRUNC('month', u.created_at)
        AND sm.created_at < DATE_TRUNC('month', u.created_at) + INTERVAL '1 month'
    WHERE
        u.created_at >= '2023-09-01' AND u.created_at < '2024-09-01'
    GROUP BY
        DATE_TRUNC('month', u.created_at)
)
SELECT
    TO_CHAR(month, 'YYYY/MM') AS month,
    new_users AS "新注册用户数",
    active_users AS "月活跃用户数"
FROM
    monthly_stats
ORDER BY
    month;
```
