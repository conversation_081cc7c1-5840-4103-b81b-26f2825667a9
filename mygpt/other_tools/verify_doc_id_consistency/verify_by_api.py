import argparse
import asyncio
import json
import logging
import os
import random
import uuid
from datetime import datetime
from typing import List, Dict

import aiohttp

# 设置日志格式
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)


async def get_files_by_dataset_id(
    dataset_id: str, api_key: str, file_count_limit: int
) -> List[Dict]:
    base_url = "http://126.143.82.230:30080"
    endpoint = f"/api/datasets/{dataset_id}/files"
    headers = {"accept": "application/json", "authorization": f"Bearer {api_key}"}
    params = {
        "file_type": "file",
        "size": 100,
        "query": "",
        "order_by": "",
    }

    all_files = []
    page = 1

    logging.info(f"Starting to fetch files for dataset {dataset_id}")
    async with aiohttp.ClientSession() as session:
        while True:
            params["page"] = page
            logging.info(f"Fetching page {page}")
            async with session.get(
                f"{base_url}{endpoint}", headers=headers, params=params
            ) as response:
                if response.status != 200:
                    logging.error(f"Error fetching files: HTTP {response.status}")
                    break

                data = await response.json()
                files = data.get("items", [])
                all_files.extend(files)

                total = data.get("total", 0)
                logging.info(
                    f"Retrieved {len(files)} files. Total files so far: {len(all_files)}/{total}"
                )
                if len(all_files) >= total or len(all_files) >= file_count_limit:
                    break
            page += 1

    logging.info(f"Finished fetching files. Total files retrieved: {len(all_files)}")
    return all_files[:file_count_limit]


async def get_file_chunks(
    file_id: str, api_key: str, max_retries: int = 3
) -> List[Dict]:
    base_url = "http://126.143.82.230:30080"
    endpoint = f"/api/debug/file/{file_id}/large_chunk"
    headers = {"accept": "application/json", "authorization": f"Bearer {api_key}"}
    params = {}

    for attempt in range(max_retries):
        try:
            timeout = aiohttp.ClientTimeout(total=300 * (attempt + 1))
            logging.info(
                f"Attempting to fetch chunks for file {file_id}. Attempt {attempt + 1}/{max_retries}"
            )
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(
                    f"{base_url}{endpoint}", headers=headers, params=params
                ) as response:
                    if response.status != 200:
                        logging.error(
                            f"Error fetching chunks for file {file_id}: HTTP {response.status}"
                        )
                        return []

                    data = await response.json()
                    logging.info(f"Successfully fetched chunks for file {file_id}")
                    return data
        except asyncio.TimeoutError:
            if attempt < max_retries - 1:
                wait_time = 2**attempt + random.uniform(0, 1)
                logging.warning(
                    f"Timeout occurred for file {file_id}. Retrying in {wait_time:.2f} seconds..."
                )
                await asyncio.sleep(wait_time)
            else:
                logging.error(
                    f"Failed to fetch chunks for file {file_id} after {max_retries} attempts"
                )
                return []
        except Exception as e:
            logging.error(
                f"An error occurred while fetching chunks for file {file_id}: {str(e)}"
            )
            return []


async def check_file_chunks(file_id: str, api_key: str) -> tuple[set, list]:
    chunks = await get_file_chunks(file_id, api_key, 5)
    if not chunks:
        return set(), chunks

    doc_ids = set()
    for chunk in chunks:
        doc_id = chunk["payload"]["metadata"]["doc_id"]
        doc_ids.add(doc_id)

    return doc_ids, chunks


async def check(
    dataset_id: str, api_key: str, data_dir: str, file_count_limit: int, start_from: int
):
    try:
        uuid.UUID(dataset_id, version=4)
    except ValueError as e:
        raise ValueError(f"Invalid dataset_id: {dataset_id}. It must be a valid UUID4.")

    logging.info(f"Starting check for dataset {dataset_id}")
    files = await get_files_by_dataset_id(
        dataset_id, api_key, file_count_limit + start_from - 1
    )
    logging.info(f"Number of files from api: {len(files)}")

    error_files = []

    os.makedirs(data_dir, exist_ok=True)
    logging.info(f"Data directory: {data_dir}")

    # 从指定的文件开始处理
    for index, file in enumerate(files[start_from - 1 :], start=start_from):
        file_id = file["id"]
        logging.info(f"Processing file {index}/{len(files)}: {file_id}")
        doc_ids, chunks = await check_file_chunks(file_id, api_key)
        if len(doc_ids) > 1:
            logging.warning(f"File {file_id} has inconsistent doc_ids: {doc_ids}")
            error_files.append((file_id, doc_ids))
            with open(os.path.join(data_dir, f"{file_id}.json"), "w") as f:
                json.dump(chunks, f, indent=2)
            logging.info(f"Saved error data for file {file_id}")
        else:
            logging.info(
                f"File {file_id} is consistent with doc_id: {next(iter(doc_ids), None)}"
            )

    if error_files:
        error_file_path = os.path.join(data_dir, "error_files.txt")
        with open(error_file_path, "w") as f:
            for file_id, doc_ids in error_files:
                f.write(f"{file_id}: {', '.join(doc_ids)}\n")
        logging.info(f"Saved list of error files to {error_file_path}")

    logging.info(
        f"Check completed. Number of files with inconsistent doc_id: {len(error_files)}"
    )
    return files


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Check file chunks consistency")
    parser.add_argument("--api_key", required=True, help="API key for authentication")
    parser.add_argument("--dataset_id", required=True, help="Dataset ID to check")
    parser.add_argument(
        "--data_dir", default="./data", help="Directory to store output data"
    )
    parser.add_argument(
        "--file_count_limit",
        type=int,
        default=1,
        help="Limit the number of files to check",
    )
    parser.add_argument(
        "--start_from",
        type=int,
        default=1,
        help="Start processing from the Nth file",
    )

    args = parser.parse_args()

    start_time = datetime.now()
    logging.info(f"Script started at {start_time}")

    asyncio.run(
        check(
            args.dataset_id,
            args.api_key,
            args.data_dir,
            args.file_count_limit,
            args.start_from,
        )
    )

    end_time = datetime.now()
    logging.info(f"Script ended at {end_time}")
    logging.info(f"Total execution time: {end_time - start_time}")
