# test small batch
python ./verify_by_api.py --api_key ak-1vJRnWh28PC95C4LOuc7P550XYUa0zyAWmegRLNxkkPPJu0o --dataset_id 8bf50cc8-261a-4b0a-8dcf-e9d257837e17 --data_dir ./chunk_data --file_count_limit 10

python ./verify_by_api.py --api_key ak-1vJRnWh28PC95C4LOuc7P550XYUa0zyAWmegRLNxkkPPJu0o --dataset_id 8bf50cc8-261a-4b0a-8dcf-e9d257837e17 --data_dir ./chunk_data_1 --file_count_limit 10 --start_from 2000

# to run on server
nohup python ./verify_by_api.py --api_key ak-1vJRnWh28PC95C4LOuc7P550XYUa0zyAWmegRLNxkkPPJu0o --dataset_id 8bf50cc8-261a-4b0a-8dcf-e9d257837e17 --data_dir ./chunk_data --file_count_limit 100000 > output.log 2>&1 &

# to run on server disable python output buffer
nohup python -u ./verify_by_api.py --api_key ak-1vJRnWh28PC95C4LOuc7P550XYUa0zyAWmegRLNxkkPPJu0o --dataset_id 8bf50cc8-261a-4b0a-8dcf-e9d257837e17 --data_dir ./chunk_data --file_count_limit 100000 >> output.log 2>&1 &

# to run on server disable python output buffer
nohup python -u ./verify_by_api.py --api_key ak-1vJRnWh28PC95C4LOuc7P550XYUa0zyAWmegRLNxkkPPJu0o --dataset_id 8bf50cc8-261a-4b0a-8dcf-e9d257837e17 --data_dir ./chunk_data_1 --start_from 2000 --file_count_limit 100000 >> output.log 2>&1 &
