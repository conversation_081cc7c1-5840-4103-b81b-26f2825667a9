import os

os.environ["ON_PREMISES_ENV"] = "1"

import uuid

from tortoise import Tortoise

from mygpt.endpoints.debug import get_sorted_large_chunks_by_file_id
from mygpt.models import VectorFile


from mygpt.settings import TORTOISE_ORM


async def check(dataset_id: str):
    # dataset_id must be an uuid.uuid4()
    try:
        uuid.UUID(dataset_id, version=4)
    except ValueError as e:
        raise
    await Tortoise.init(config=TORTOISE_ORM)

    files = await VectorFile.filter(dataset_id=dataset_id, deleted_at=None).all()
    # Check if the files have single doc_id, one by one
    for file in files:
        chunks = await get_sorted_large_chunks_by_file_id(file.id)
        doc_ids = set()
        for chunk in chunks:
            doc_ids.update(chunk.metadata.get("doc_ids", []))
        if len(doc_ids) > 1:
            print(f"File {file.id} has multiple doc_ids: {doc_ids}")


if __name__ == "__main__":
    import asyncio

    asyncio.run(check("8bf50cc8-261a-4b0a-8dcf-e9d257837e17"))
