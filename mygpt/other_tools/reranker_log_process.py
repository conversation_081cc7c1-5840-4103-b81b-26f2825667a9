import ast
from datetime import datetime

import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns

# 设置 Seaborn 样式
sns.set_style("whitegrid")
plt.rcParams["font.sans-serif"] = ["Arial"]
plt.rcParams["font.size"] = 10

# 读取CSV文件
df = pd.read_csv("data/reranker-log-to-process.csv", quotechar='"', escapechar="\\")


# 解析日期和搜索次数
def parse_log(row):
    try:
        date = datetime.strptime(row["@timestamp"], "%b %d, %Y @ %H:%M:%S.%f").date()

        message = row["message"]
        if "timing-str_lengths:" not in message:
            return pd.Series([date, 0])

        timing_str = message.split("timing-str_lengths:")[1].split("], max")[0] + "]"
        timing_list = ast.literal_eval(timing_str)
        searches = sum(item[2] for item in timing_list)

        return pd.Series([date, searches])
    except Exception as e:
        print(f"Error parsing log: {e}")
        print(f"Problematic row: {row}")
        return pd.Series([None, 0])


df[["date", "searches"]] = df.apply(parse_log, axis=1)

# 移除无效的行
df = df.dropna(subset=["date"])

# 按日期汇总搜索次数
daily_searches = df.groupby("date")["searches"].sum().reset_index()

# 计算每日价格
daily_searches["price"] = daily_searches["searches"] * 2 / 1000

# 计算累计搜索次数和费用
daily_searches["cumulative_searches"] = daily_searches["searches"].cumsum()
daily_searches["cumulative_price"] = daily_searches["price"].cumsum()

# 创建图形和子图
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 16))

# 颜色设置
bar_color = "#3498db"
price_color = "#e74c3c"
cumulative_searches_color = "#2ecc71"
cumulative_price_color = "#e74c3c"

# 绘制每日搜索次数直方图
bars = ax1.bar(
    daily_searches["date"],
    daily_searches["searches"],
    align="center",
    color=bar_color,
    alpha=0.7,
)

# 在每个柱子上添加标签
for bar in bars:
    height = bar.get_height()
    ax1.text(
        bar.get_x() + bar.get_width() / 2.0,
        height,
        f"{int(height)}\n${height*2/1000:.2f}",
        ha="center",
        va="bottom",
        fontweight="bold",
    )

ax1.set_title("Daily Searches and Prices", fontsize=16, fontweight="bold", pad=20)
ax1.set_xlabel("Date", fontsize=12, fontweight="bold")
ax1.set_ylabel("Number of Searches", fontsize=12, fontweight="bold")
ax1.tick_params(axis="x", rotation=45)

# 调整 y 轴以适应所有数据
ax1.set_ylim(0, daily_searches["searches"].max() * 1.1)

# 添加网格线
ax1.grid(True, linestyle="--", alpha=0.7)

# 绘制累计搜索次数和费用折线图
ax2.plot(
    daily_searches["date"],
    daily_searches["cumulative_searches"],
    label="Cumulative Searches",
    color=cumulative_searches_color,
    linewidth=2,
)
ax2.set_xlabel("Date", fontsize=12, fontweight="bold")
ax2.set_ylabel(
    "Cumulative Searches",
    fontsize=12,
    fontweight="bold",
    color=cumulative_searches_color,
)
ax2.tick_params(axis="x", rotation=45)
ax2.tick_params(axis="y", labelcolor=cumulative_searches_color)

ax3 = ax2.twinx()
ax3.plot(
    daily_searches["date"],
    daily_searches["cumulative_price"],
    color=cumulative_price_color,
    label="Cumulative Price",
    linewidth=2,
)
ax3.set_ylabel(
    "Cumulative Price ($)", fontsize=12, fontweight="bold", color=cumulative_price_color
)
ax3.tick_params(axis="y", labelcolor=cumulative_price_color)

# 添加图例
lines1, labels1 = ax2.get_legend_handles_labels()
lines2, labels2 = ax3.get_legend_handles_labels()
ax2.legend(lines1 + lines2, labels1 + labels2, loc="upper left", fontsize=10)

ax2.set_title("Cumulative Searches and Price", fontsize=16, fontweight="bold", pad=20)

# 添加网格线
ax2.grid(True, linestyle="--", alpha=0.7)

# 标注最终的总费用
total_price = daily_searches["cumulative_price"].iloc[-1]
ax3.annotate(
    f"Total Price: ${total_price:.2f}",
    xy=(daily_searches["date"].iloc[-1], total_price),
    xytext=(10, 10),
    textcoords="offset points",
    ha="left",
    va="bottom",
    bbox=dict(boxstyle="round,pad=0.5", fc="yellow", alpha=0.5),
    arrowprops=dict(arrowstyle="->", connectionstyle="arc3,rad=0"),
)

plt.tight_layout()

# 保存图片
plt.savefig("data/daily_searches_histogram.png", dpi=300, bbox_inches="tight")

# 显示图片
plt.show()

print("图表已保存为 data/daily_searches_histogram.png 并已显示")
