import hashlib
import json
import logging
import os
import re
import shutil
import tempfile
import traceback
from contextlib import asynccontextmanager
from functools import wraps
from typing import IO, Any, List, Optional, Iterator
from uuid import UUID

import aioboto3
import aiohttp
import boto3
import loguru
import sentry_sdk
import tiktoken
from aiobotocore.response import StreamingBody
from langchain.schema import BaseMessage, messages_to_dict
from loguru import logger as logging
from skywalking.decorators import trace
from starlette.responses import HTMLResponse
from types_aiobotocore_s3.literals import ObjectCannedACLType
from types_aiobotocore_s3.service_resource import S3ServiceResource

from mygpt import settings
from mygpt.enums import FileType, FileStorage, DATASET_STATUS, AIStatus
from mygpt.loader.transcoder import DocumentTranscoder
from mygpt.models import Dataset, Robot
from mygpt.settings import (
    AWS_ACCESS_KEY_ID,
    AWS_S3_BUCKET_NAME,
    AWS_S3_REGION,
    AWS_SECRET_ACCESS_KEY,
    DOMAIN,
)
from mygpt.settings import FILE_LEARNING_TASKS_PER_SERVER
from mygpt.settings import FILE_LEARNING_TASKS_PER_SERVER_ADV

tokenizer = None


def num_tokens_from_string(string: str, encoding_name: str = "cl100k_base") -> int:
    """
    Returns the number of tokens in a text string.
    cl100k_base: gpt-4, gpt-3.5-turbo, text-embedding-ada-002
    """
    encoding = tiktoken.get_encoding(encoding_name)
    num_tokens = len(encoding.encode(string))
    return num_tokens


def num_tokens_from_messages(messages, model="gpt-3.5-turbo-0613"):
    """Return the number of tokens used by a list of messages."""
    try:
        encoding = tiktoken.encoding_for_model(model)
    except KeyError:
        print("Warning: model not found. Using cl100k_base encoding.")
        encoding = tiktoken.get_encoding("cl100k_base")
    if model in {
        "gpt-3.5-turbo-0613",
        "gpt-3.5-turbo-16k-0613",
        "gpt-4-0314",
        "gpt-4-32k-0314",
        "gpt-4-0613",
        "gpt-4-32k-0613",
    }:
        tokens_per_message = 3
        tokens_per_name = 1
    elif model == "gpt-3.5-turbo-0301":
        # every message follows <|start|>{role/name}\n{content}<|end|>\n
        tokens_per_message = 4
        tokens_per_name = -1  # if there's a name, the role is omitted
    elif "gpt-3.5-turbo" in model:
        print(
            "Warning: gpt-3.5-turbo may update over time. Returning num tokens assuming gpt-3.5-turbo-0613."
        )
        return num_tokens_from_messages(messages, model="gpt-3.5-turbo-0613")
    elif "gpt-4" in model:
        print(
            "Warning: gpt-4 may update over time. Returning num tokens assuming gpt-4-0613."
        )
        return num_tokens_from_messages(messages, model="gpt-4-0613")
    else:
        raise NotImplementedError(
            f"""num_tokens_from_messages() is not implemented for model {model}. See https://github.com/openai/openai-python/blob/main/chatml.md for information on how messages are converted to tokens."""
        )
    num_tokens = 0
    for message in messages:
        num_tokens += tokens_per_message
        for key, value in message.items():
            num_tokens += len(encoding.encode(value))
            if key == "name":
                num_tokens += tokens_per_name
    num_tokens += 3  # every reply is primed with <|start|>assistant<|message|>
    return num_tokens


def num_tokens_for_langchain_messages(
    messages: List[BaseMessage], model="gpt-3.5-turbo-0613"
):
    messages = messages_to_dict(messages)
    openai_messages = []
    for message in messages:
        openai_message = {
            "role": message["type"],
            "content": message["data"]["content"],
        }
        openai_messages.append(openai_message)

    return num_tokens_from_messages(openai_messages, model=model)


def get_redoc_html(
    *,
    openapi_url: str,
    title: str,
    redoc_js_url: str = "https://cdn.jsdelivr.net/npm/redoc@next/bundles/redoc.standalone.js",
    redoc_favicon_url: str = "https://fastapi.tiangolo.com/img/favicon.png",
    with_google_fonts: bool = True,
) -> HTMLResponse:
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
    <title>{title}</title>
    <!-- needed for adaptive design -->
    <meta charset='utf-8'/>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    """
    if with_google_fonts:
        html += """
    <link href='https://fonts.googleapis.com/css?family=Montserrat:300,400,700|Roboto:300,400,700' rel='stylesheet'>
    """
    html += f"""
    <link rel='shortcut icon' href='{redoc_favicon_url}'>
    <!--
    ReDoc doesn't change outer page styles
    -->
    <style>
      body {{
        margin: 0;
        padding: 0;
      }}
    </style>
    </head>
    <body>
    <noscript>
        ReDoc requires Javascript to function. Please enable it to browse the documentation.
    </noscript>
    <div id='redoc-container'></div>
    <redoc spec-url='{openapi_url}'></redoc>
    <script src='{redoc_js_url}'> </script>
    <script src='https://cdn.jsdelivr.net/gh/wll8/redoc-try@1.4.1/dist/try.js'></script>
    <script>
    initTry({{
    openApi: `{openapi_url}`,
        redocOptions: {{scrollYOffset: 50}},
    }})
    </script>
    </body>
    </html>
    """
    return HTMLResponse(html)


S3_SESSION = aioboto3.Session(
    aws_access_key_id=AWS_ACCESS_KEY_ID,
    aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
    region_name=AWS_S3_REGION,
)


async def upload_binary(
    blob_s3_key: str,
    file_path: str,
    bucket_name: str = AWS_S3_BUCKET_NAME,
) -> str:
    """Upload a binary file to S3"""
    if settings.FILE_STORAGE == FileStorage.S3:
        with open(file_path, "rb") as f:
            async with S3_SESSION.resource("s3") as s3:
                bucket = await s3.Bucket(bucket_name)
                await bucket.put_object(
                    Key=blob_s3_key,
                    Body=f,
                )
            return (
                f"https://{bucket_name}.s3.{AWS_S3_REGION}.amazonaws.com/{blob_s3_key}"
            )
    else:
        """Upload a binary file to local"""
        temp_dir = "tmp"
        destination_path = os.path.join("uploads", temp_dir, blob_s3_key)
        _save_file_locally(file_path, destination_path)
        return destination_path.replace(os.path.sep, "/")


async def copy_binary(
    blob_s3_key: str,
    new_blob_s3_key: str,
    bucket_name: str = AWS_S3_BUCKET_NAME,
) -> str:
    n = 0
    while n < 3:
        n += 1
        try:
            if settings.FILE_STORAGE == FileStorage.S3:
                async with S3_SESSION.resource("s3") as s3:
                    bucket = await s3.Bucket(bucket_name)
                    copy_source = {
                        "Bucket": bucket_name,
                        "Key": blob_s3_key,
                    }
                    await bucket.copy(
                        CopySource=copy_source,
                        Key=new_blob_s3_key,
                    )
                    logging.info(
                        "copy_binary success, from %s to %s",
                        blob_s3_key,
                        new_blob_s3_key,
                    )
                    return f"https://{bucket_name}.s3.{AWS_S3_REGION}.amazonaws.com/{new_blob_s3_key}"
            else:
                temp_dir = "tmp"
                src_path = os.path.join("uploads", temp_dir, blob_s3_key)
                dest_path = os.path.join("uploads", temp_dir, new_blob_s3_key)
                _save_file_locally(src_path, dest_path)
                return dest_path.replace(os.path.sep, "/")
        except Exception as e:
            logging.error(f"copy_binary error:{e}")
            sentry_sdk.capture_exception(e)
            logging.error(traceback.format_exc())
    return ""


def _save_file_locally(file_path: str, destination_path: str):
    os.makedirs(os.path.dirname(destination_path), exist_ok=True)
    shutil.copy2(file_path, destination_path)


async def upload_file_to_s3(
    blob_s3_key: str,
    binary: str | bytes | IO[Any] | StreamingBody,
    acl: ObjectCannedACLType = "public-read",
    content_type: str = "application/octet-stream",
    bucket_name: str = AWS_S3_BUCKET_NAME,
) -> str:
    if settings.FILE_STORAGE == FileStorage.S3:
        s3: S3ServiceResource
        async with S3_SESSION.resource("s3") as s3:
            bucket = await s3.Bucket(bucket_name)
            await bucket.put_object(
                Key=blob_s3_key,
                ACL=acl,
                Body=binary,
                ContentType=content_type,
            )
        return get_file_url_from_s3(blob_s3_key, bucket_name)
    else:
        """Upload a binary file to local"""
        path = get_file_tmp_location(blob_s3_key)
        if isinstance(binary, str):
            _save_file_locally(binary, path)
        else:
            with open(path, "wb") as f:
                f.write(binary)
        return get_file_url_from_local(path)


def get_file_url_from_s3(
    blob_s3_key: str,
    bucket_name: str = AWS_S3_BUCKET_NAME,
) -> str:
    return f"https://{bucket_name}.s3.{AWS_S3_REGION}.amazonaws.com/{blob_s3_key}"


def get_file_url_from_local(
    key: str,
) -> str:
    return f"{DOMAIN}/{key}"


@asynccontextmanager
async def s3_session() -> S3ServiceResource:
    async with aioboto3.Session(
        aws_access_key_id=AWS_ACCESS_KEY_ID,
        aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
        region_name=AWS_S3_REGION,
    ).resource("s3") as s3:
        yield s3


async def download_file_s3(
    blob_s3_key: str,
    bucket_name: str = AWS_S3_BUCKET_NAME,
) -> Optional[bytes]:
    async with s3_session() as s3:
        bucket = await s3.Bucket(bucket_name)
        obj = await bucket.Object(blob_s3_key)
        response = await obj.get()
        body: StreamingBody = response["Body"]
        return await body.read()


async def download_tmp_file_from_s3(
    blob_s3_key: str,
    bucket_name: str = AWS_S3_BUCKET_NAME,
) -> str:
    if settings.FILE_STORAGE == FileStorage.S3:
        async with s3_session() as s3:
            bucket = await s3.Bucket(bucket_name)
            obj = await bucket.Object(blob_s3_key)
            response = await obj.get()
            body: StreamingBody = response["Body"]
            path = get_file_tmp_location(blob_s3_key)
            with open(path, "wb") as f:
                f.write(await body.read())
            return path
    else:
        path = get_file_tmp_location(blob_s3_key)
        if os.name != "nt":  # not windows
            path = f"uploads{path}"
        return path


async def delete_file_from_s3(
    blob_s3_key: str,
    bucket_name: str = AWS_S3_BUCKET_NAME,
):
    if settings.FILE_STORAGE == FileStorage.S3:

        def _delete_file_from_s3():
            s3 = boto3.client(
                "s3",
                aws_access_key_id=AWS_ACCESS_KEY_ID,
                aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            )
            s3.delete_object(Bucket=bucket_name, Key=blob_s3_key)

        try:
            await asyncio.get_event_loop().run_in_executor(None, _delete_file_from_s3)
        except Exception as e:
            logging.error(f"delete_file_from_s3 error:{e} blob_s3_key:{blob_s3_key}")
    else:

        def _delete_file_from_local():
            path = get_file_tmp_location(blob_s3_key)
            if os.path.exists(f"uploads{path}"):
                os.remove(f"uploads{path}")

        try:
            await asyncio.get_event_loop().run_in_executor(
                None, _delete_file_from_local
            )
        except Exception as e:
            logging.error(f"delete_file_from_local error:{e} key:{blob_s3_key}")


async def delete_files_from_s3(
    blob_s3_keys: List[str],
    bucket_name: str = AWS_S3_BUCKET_NAME,
):
    if not blob_s3_keys:
        return
    if settings.FILE_STORAGE == FileStorage.S3:

        def _delete_files_from_s3():
            s3 = boto3.client("s3")
            s3.delete_objects(
                Bucket=bucket_name,
                Delete={"Objects": [{"Key": key} for key in blob_s3_keys]},
            )

        await asyncio.get_event_loop().run_in_executor(None, _delete_files_from_s3)
    else:

        def _delete_files_from_local():
            for key in blob_s3_keys:
                path = get_file_tmp_location(key)
                if os.path.exists(f"uploads{path}"):
                    os.remove(f"uploads{path}")

        await asyncio.get_event_loop().run_in_executor(None, _delete_files_from_local)


def get_image_s3_key(
    attachment_id: UUID, suffix: str, image_type: FileType, ai_id: str = None
):
    if image_type == FileType.AVATAR:
        prefix = "avatars"

    if image_type == FileType.ORIGINAL:
        prefix = "images"

    if image_type == FileType.FAQ_IMAGE:
        prefix = "faq_image"

    if ai_id is not None:
        return f"{prefix}/{ai_id}/{attachment_id.hex}.{suffix}"
    else:
        return f"{prefix}/{attachment_id.hex}.{suffix}"


def normalize_bm25_formula(score, max_score):
    return score / max_score


def normalize_bm25(max_score, hits):
    rs = []
    for hit in hits:
        if hit["_score"] < 5:
            continue
        hit["_score"] = normalize_bm25_formula(hit["_score"], max_score)

        content = hit["_source"]["content"]
        document_transcoder = DocumentTranscoder(content)
        content = document_transcoder.decode()
        hit["_source"]["content"] = content

        rs.append(hit)
    return rs


def get_min_score(common_elements, elements_dictionary):
    if len(common_elements):
        return min([min(v) for v in elements_dictionary.values()])
    else:
        # No common results - assign arbitrary minimum score value
        return 0.01


def combined_chunks(vector_hits: list, bm25_hits: list[dict]):
    # # 处理关键字查询结果得分

    # 得到同时存在indexs与hits的id
    index_ids = [index.id for index in vector_hits]
    hits_ids = [hit["_id"] for hit in bm25_hits]
    final_ids = list(set(index_ids).intersection(set(hits_ids)))

    results_dictionary = dict((key, []) for key in final_ids)
    for id in final_ids:
        for index, vector_hit in enumerate(vector_hits):
            if vector_hit.id == id:
                results_dictionary[id].append(vector_hit.score)
        for index, BM_hit in enumerate(bm25_hits):
            if BM_hit["_id"] == id:
                results_dictionary[id].append(BM_hit["_score"])
    logging.debug(f"combined_chunks results_dictionary:{results_dictionary}")
    if final_ids:
        min_value = get_min_score(final_ids, results_dictionary)
        for vector_hit in vector_hits:
            if vector_hit.id not in final_ids:
                results_dictionary[vector_hit.id] = [min_value]
        for bm_hit in bm25_hits:
            if bm_hit["_id"] not in final_ids:
                results_dictionary[bm_hit["_id"]] = [min_value]
    else:
        for vector_hit in vector_hits:
            if vector_hit.id not in final_ids:
                results_dictionary[vector_hit.id] = [vector_hit.score]
    logging.debug(f"combined_chunks results_dictionary:{results_dictionary}")
    return results_dictionary


def get_analyzer(language: str):
    if language == "en":
        return "english"
    elif language == "ja":
        return "kuromoji"
    elif language == "zh":
        return "smartcn"
    else:
        return "standard"


def get_hash(string):
    # 创建MD5哈希对象
    hash_object = hashlib.md5()
    # 将字符串编码为字节流并进行哈希计算
    hash_object.update(string.encode("utf-8"))
    # 获取哈希值的十六进制表示
    hash_value = hash_object.hexdigest()
    return hash_value


# def get_cannot_answer_text(lang: str = ''):
#     if lang == 'ja':
#         return '残念ながら、私は現在の知識ではこの質問に答えることができません。他の何かをお聞きいただけますか？'
#     if lang == 'zh':
#         return '很抱歉，我现在还无法回答这个问题。您可以问我其他问题吗？'
#     if lang == 'zh-TW':
#         return '很抱歉，我現在還無法回答這個問題。您可以問我其他問題嗎？'
#     return 'Unfortunately, I cannot answer this question with my current knowledge. Can you ask me something else?'


# def get_say_hello_answer_text(lang: str):
#     if lang == 'ja':
#         return 'こんにちは、私はAIアシスタントです。何かお手伝いできますか？'
#     if lang == 'zh':
#         return '您好，我是AI助手，有什么可以帮您的吗？'
#     if lang == 'zh-TW':
#         return '您好，我是AI助手，有什麼可以幫您的嗎？'
#     return 'I\'m a AI assitant, what can I help you?'


# def get_query_key_words_ai_language_key(ai_language: str):
#     if ai_language == "zh":
#         ai_language = "zh-CN"

#     query_key_words_ai_language_key = "query_key_words_in_" + ai_language
#     return query_key_words_ai_language_key


async def download_file(url):
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            data = await response.read()
            return data


class UUIDEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, uuid.UUID):
            return str(obj)
        return super().default(obj)


def is_debug_mode():
    # 获取当前日志记录器的有效级别
    # effective_level = logging.getLogger().getEffectiveLevel()

    # 判断当前是否是 Debug 模式
    # return effective_level == logging.DEBUG
    return settings.DEBUG


def convert_language_code(language: str | None):
    if not language:
        return None
    code = language.lower()

    # if code == "zh" or code == "zh-cn" or code == "zh-hans":
    #     return "Chinese (Simplified)"
    #
    # if code == "zh-tw" or code == "zh-hant":
    #     return "Chinese (Traditional)"
    if code == "zh" or code.startswith("zh-"):
        return "Chinese"

    if code == "ja":
        return "Japanese"

    if code == "en" or code == "en-us" or code == "en-uk":
        return "English"

    if code == "ko":
        return "Korean"

    if code == "fr":
        return "French"

    if code == "de":
        return "German"

    if code == "es":
        return "Spanish"

    if code == "it":
        return "Italian"

    if code == "pt":
        return "Portuguese"

    if code == "ru":
        return "Russian"

    if code == "ar":
        return "Arabic"

    if code == "tr":
        return "Turkish"

    if code == "vi":
        return "Vietnamese"

    if code == "th":
        return "Thai"

    if code == "id":
        return "Indonesian"

    if code == "ms":
        return "Malay"

    if code == "hi":
        return "Hindi"

    if code == "bn":
        return "Bengali"

    if code == "fa":
        return "Persian"

    if code == "ur":
        return "Urdu"
    loguru.logger.error(f"Unknown language code: {code}")
    return code


def extract_first_valid_json(content):
    if not content:
        return None

    start_index = content.find("{")
    end_index = content.find("}", start_index) + 1
    if start_index != -1 and end_index != 0:
        json_str = content[start_index:end_index]
        return json_str
    else:
        return None


# 定义颜色转义码


class TerminalColors:
    GREEN = "\033[92m"
    YELLOW = "\033[93m"
    RED = "\033[91m"
    RESET = "\033[0m"


def colored_info_green(message):
    return f"{TerminalColors.GREEN}{message}{TerminalColors.RESET}"


def colored_info_yellow(message):
    return f"{TerminalColors.YELLOW}{message}{TerminalColors.RESET}"


def colored_info_red(message):
    return f"{TerminalColors.RED}{message}{TerminalColors.RESET}"


def get_file_tmp_location(filename: str):
    temp_dir = "/tmp"
    if os.name == "nt":  # Windows
        temp_dir = tempfile.gettempdir()

    return os.path.join(temp_dir, filename)


def content_include_images(content: str):
    if not content:
        return False

    return True if re.search(r"!\[.*\]\(.*\)", content) else False


def content_include_tables(content: str):
    if not content:
        return False
    match = re.search("\s*\+[-+:]+\+", content)
    if match:
        return True
    return False


def content_include_simple_tables(content: str):
    if not content:
        return False
    match = re.search("^\s*-[\s-]+-$", content)
    if match:
        return True
    return False


@trace()
def check_content_is_rich_text(contents: List[str]):
    include_image = False
    include_table = False
    single_count = 0
    for content in contents:
        if include_image and include_table:
            break
        if not include_image and content_include_images(content):
            include_image = True
        if include_table:
            continue
        rows = content.split("\n")
        for row in rows:
            text = row.strip()
            if content_include_tables(text):
                include_table = True
                break
            # if content_include_simple_tables(text):
            #     single_count += 1
            #     if single_count > 1:
            #         include_table = True
            #         break
    return include_image, include_table


def extract_image_from_content(content: str) -> List[dict[str, str]]:
    if not content:
        return []
    url_pattern = re.compile(r"Image: !\[[^[]*?\]\(([^\s]*)\)")
    urls = re.findall(url_pattern, content)

    infos: List[dict] = []
    for url in urls:
        # 提取url中的uuid (https://prd-mygpt.s3.ap-northeast-1.amazonaws.com/parser_file/70a34cd78b99499990c55c3332feda93.png)
        match = re.search(r"\/([a-f0-9]{32})\.([a-z]{3,6})$", url)
        if match:
            infos.append({"url": url, "uuid": match.group(1), "suffix": match.group(2)})
    return infos


def remove_image_from_content(content: str) -> str:
    if not content:
        return ""
    url_pattern = re.compile(r"Image: !\[[^[]*?\]\(([^\s]*)\)")
    return re.sub(url_pattern, "", content)


def replace_markdown_images(text: str) -> str:
    if not text:
        return ""
    # 定义正则表达式来匹配 Image: 和 Markdown 图片格式 ![alt](url)
    pattern = r"(?:Image:\s*)?!\[(.*?)\]\((.*?)\)"
    # 使用re.sub替换图片链接和 "Image:" 为 alt 标签内容
    replaced_text = re.sub(pattern, r"\1", text)
    return replaced_text


def replace_image_url_to_uuid(content: str, url: str, uuid: str) -> str:
    return content.replace(url, f"https://{uuid}.png")


def replace_image_uuid_to_url(content: str, uuid: str, url: str) -> str:
    return content.replace(f"https://{uuid}.png", url)


def find_md_uuid_images(content: str) -> List[str]:
    uuid_pattern = re.compile(r"!\[.*\]\(https:\/\/([a-f0-9]{32})\.png\)")
    return re.findall(uuid_pattern, content)


def replace_image_with_link(markdown_text, cache_links: List[str] | None = None):
    try:
        # 定义图片语法的正则表达式
        image_pattern = re.compile(r"!\[([^[]*?)]\((.*?)\)")

        # 查找所有匹配的图片语法
        matches = image_pattern.findall(markdown_text)

        # 替换每个图片语法为链接语法
        for match in matches:
            # 自定义图片不需要处理，仅替换gpt生成的图片或者使用其它链接做为图片的情况
            # 链接以 png jpg jpeg 结尾的图片不需要处理
            url = match[1].split("?")[0]
            if re.search(r"\.(png|jpg|jpeg|gif|svg|icon)$", url):
                continue
            image_syntax = f"![{match[0]}]({match[1]})"
            link_syntax = f"[{match[1]}]({match[1]})"
            if cache_links:
                if match[1] not in cache_links:
                    cache_links.append(match[1])
                else:
                    markdown_text.replace(image_syntax, "")
                    continue

            markdown_text = markdown_text.replace(image_syntax, link_syntax)
    except Exception as e:
        logging.error(f"replace_image_with_link error:{e}")

    return markdown_text


def replace_link_with_image(markdown_text):
    try:
        # 定义链接语法的正则表达式
        link_pattern = re.compile(r"([^!]|^)\[([^[]*?)]\((.*?)\)")

        # 查找所有匹配的链接语法
        matches = link_pattern.findall(markdown_text)

        # 替换每个链接语法为图片语法
        for match in matches:
            # 处理自定义图片
            url = match[2].split("?")[0]
            if re.search(r"\.(png|jpg|jpeg|gif|svg|icon)$", url):
                image_syntax = f"![]({match[2]})"
                link_syntax = f"[{match[1]}]({match[2]})"
                markdown_text = markdown_text.replace(link_syntax, image_syntax)
    except Exception as e:
        logging.error(f"replace_link_with_image error:{e}")

    return markdown_text


def replace_md_uuid_images(
    content: str, image_resources: dict, cache_links: List[str] | None = None
) -> str:
    content = replace_image_with_link(content, cache_links=cache_links)
    content = replace_link_with_image(content)
    uuids = find_md_uuid_images(content)
    for uuid in uuids:
        url = image_resources.get(uuid)
        if not url:
            continue
        content = replace_image_uuid_to_url(content, uuid, url)

    return content


def ends_with_markdown_image_syntax_index(content: str) -> int:
    if not content:
        return -1
    if content.endswith("!"):
        return len(content) - 1
    if content.endswith("!["):
        return len(content) - 2
    # !\[[^\]]{0,}$ 匹配 ![xxx
    match = re.search(r"!\[[^]]+$", content)
    if match:
        return match.start()
    # !\[.*\]$ 匹配 ![xxx]
    match = re.search(r"!\[.*]\(?$", content)
    if match:
        return match.start()
    # !\[.*\]\([^\)]{0,}$ 匹配 ![xxx](xxx
    match = re.search(r"!\[.*]\([^)]+$", content)
    if match:
        return match.start()
    # !\[[^\]]{0,}\]\([^\)]{0,}\)$ 匹配 ![xxx](xxx)
    return -1


def ends_with_markdown_link_syntax_index(content: str) -> int:
    if not content:
        return -1
    if content.endswith("["):
        return len(content) - 1
    # !\[[^\]]{0,}$ 匹配 ![xxx
    match = re.search(r"\[[^]]+$", content)
    if match:
        return match.start()
    # !\[.*\]$ 匹配 ![xxx]
    match = re.search(r"\[.*]\(?$", content)
    if match:
        return match.start()
    # !\[.*\]\([^\)]{0,}$ 匹配 ![xxx](xxx
    match = re.search(r"\[.*]\([^)]+$", content)
    if match:
        return match.start()
    # \[[^\]]{0,}\]\([^\)]{0,}\)$ 匹配 [xxx](xxx)
    return -1


def compute_charactors_count(content: str) -> int:
    """过滤markdown图片、表格"""
    # 定义图片语法的正则表达式
    image_pattern = re.compile(r"!\[([^[]*?)\]\((.*?)\)")
    # 定义表格语法的正则表达式
    table_pattern = re.compile(r"\s*\+[-+:]+\+")
    # 定义简单表格语法的正则表达式
    simple_table_pattern = re.compile(r"^\s*-[\s-]+-$")
    # 过滤图片，算作一个字符
    content = image_pattern.sub("1", content)
    # 过滤表格无效字符
    content = table_pattern.sub("", content)
    content = simple_table_pattern.sub("", content)
    # 移除\r换行符
    content = content.replace("\r", "")
    # 计算字符数
    return len(content)


def filter_dictionary(query: str, dictionary: list):
    if not query or not dictionary:
        return dictionary
    query = query.lower()
    return [
        (item.source, item.target)
        for item in dictionary
        if item.source.lower() in query
    ]


class SingletonManager:
    instances = {}

    @classmethod
    def singleton(cls, class_, *args, **kwargs):
        if class_ not in cls.instances:
            cls.instances[class_] = class_(*args, **kwargs)
        return cls.instances[class_]


def is_url_match_rules(url: str, rules: list[str]) -> bool:
    if url.endswith("/"):
        url1 = url[:-1]
    else:
        url1 = url + "/"
    for rule in rules:
        r0 = re.escape(rule)
        r1 = r0
        if "\\*\\*" in r0:
            r1 = re.sub(r"(\\\*){2,}", ".*", r0)
        r2 = r1
        if "\\*" in r2:
            r2 = r1.replace("\\*", "[^/]*/?")
        pattern = "^" + r2 + "$"
        if re.match(pattern, url) or re.match(pattern, url1):
            return True
    return False


def async_timing_decorator(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
        except Exception as e:
            end_time = time.time()
            loguru.logger.error(
                f"[async_timing_decorator] Function {func.__name__} failed after {end_time - start_time} seconds. Error: {str(e)}"
            )
            raise
        else:
            end_time = time.time()
            loguru.logger.info(
                f"[async_timing_decorator] Function {func.__name__} executed in {end_time - start_time} seconds"
            )
            return result

    return wrapper


# """Redis Semaphore
# - must have timeout for a key, otherwise it will be a deadlock if the key is not released (server crash), we use lease to avoid deadlock
# - timeout = 10s + lease
# - if timeout, key will be released automatically by redis
# """
import uuid
import time

from uuid import uuid4
from redis import Redis
import collections


class AsyncRedisSemaphore:
    def __init__(self, redis: Redis, name: str, value: int, lease_timeout: int):
        self.redis = redis
        self.name = name
        self._value = value
        self.lease_timeout = lease_timeout
        self.holder_set_key = f"{name}:holders"
        self.value_key = f"{name}:value"
        self._waiters = None

    async def __aenter__(self):
        await self.acquire()
        return self

    async def __aexit__(self, exc_type, exc, tb):
        await self.release()

    async def _init_semaphore(self):
        # 初始化信号量值
        await self.redis.set(self.value_key, self._value, nx=True)

    async def locked(self) -> bool:
        value = await self.redis.get(self.value_key)
        return int(value or 0) == 0

    async def acquire(self):
        await self._init_semaphore()

        while True:
            acquired = await self._try_acquire()
            if acquired:
                return True

            if self._waiters is None:
                self._waiters = collections.deque()
            fut = asyncio.get_running_loop().create_future()
            self._waiters.append(fut)

            try:
                await fut
            except asyncio.CancelledError:
                if not fut.cancelled():
                    self._wake_up_next()
                raise
            finally:
                self._waiters.remove(fut)

    async def _try_acquire(self) -> bool:
        acquire_script = """
        local value_key = KEYS[1]
        local holder_set = KEYS[2]
        local holder_id = ARGV[1]
        local expiration_time = tonumber(ARGV[2])
        local current_time = tonumber(ARGV[3])

        local value = tonumber(redis.call('GET', value_key) or 0)
        if value > 0 then
            redis.call('DECR', value_key)
            redis.call('ZADD', holder_set, expiration_time, holder_id)
            return 1
        else
            return 0
        end
        """

        holder_id = str(uuid4())
        current_time = int(asyncio.get_running_loop().time())
        expiration_time = current_time + self.lease_timeout

        result = await self.redis.eval(
            acquire_script,
            2,
            self.value_key,
            self.holder_set_key,
            holder_id,
            str(expiration_time),
            str(current_time),
        )

        return bool(result)

    async def release(self):
        release_script = """
        local value_key = KEYS[1]
        redis.call('INCR', value_key)
        return 1
        """

        await self.redis.eval(release_script, 1, self.value_key)
        self._wake_up_next()

    def _wake_up_next(self):
        if self._waiters:
            waiter = self._waiters.popleft()
            if not waiter.done():
                waiter.set_result(None)

    def __repr__(self):
        return f"<AsyncRedisSemaphore [{self.name}]>"

    async def get_semaphore_info(self) -> dict:
        info_script = """
        local holder_set = KEYS[1]
        local current_time = tonumber(ARGV[1])
        local total_count = redis.call('ZCARD', holder_set)
        local active_count = redis.call('ZCOUNT', holder_set, current_time, '+inf')
        local expired_count = total_count - active_count
        return {tostring(total_count), tostring(active_count), tostring(expired_count)}
        """
        current_time = int(asyncio.get_event_loop().time())
        result = await self.redis.eval(
            info_script, 1, self.holder_set_key, str(current_time)
        )  # 转换为字符串
        return {
            "total_holders": int(result[0]),
            "active_holders": int(result[1]),
            "expired_holders": int(result[2]),
        }

    async def __aenter__(self):
        if not await self.acquire():
            raise RuntimeError("can not acquire semaphore")
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.release()


from tortoise import fields
from tortoise.models import Model


class Semaphore(Model):
    id = fields.IntField(pk=True)
    count = fields.IntField(default=10)
    max_count = fields.IntField(default=10)

    class Meta:
        table = "semaphore"

    @classmethod
    async def initialize(cls, max_count=10):
        await cls.raw(
            "INSERT INTO semaphore (id, count, max_count) VALUES (1, :max_count, :max_count) "
            "ON CONFLICT (id) DO UPDATE SET count = :max_count, max_count = :max_count",
            values={"max_count": max_count},
        )
        print(f"Semaphore initialized or updated with max count: {max_count}")

    @classmethod
    async def acquire(cls):
        try:
            async with atomic():
                result = await cls.raw(
                    "UPDATE semaphore SET count = count - 1 "
                    "WHERE id = 1 AND count > 0 RETURNING count"
                )
                return len(result) > 0
        except OperationalError:
            return False

    @classmethod
    async def release(cls):
        try:
            async with atomic():
                await cls.raw(
                    "UPDATE semaphore SET count = LEAST(count + 1, max_count) WHERE id = 1"
                )
        except OperationalError:
            print("Failed to release semaphore")

    @classmethod
    async def set_max_count(cls, new_max_count):
        try:
            async with atomic():
                await cls.raw(
                    "UPDATE semaphore SET max_count = :new_max_count, "
                    "count = LEAST(count, :new_max_count) WHERE id = 1",
                    values={"new_max_count": new_max_count},
                )
                print(f"Semaphore max count updated to: {new_max_count}")
        except OperationalError:
            print("Failed to update semaphore max count")

    @classmethod
    async def __aenter__(cls):
        acquired = await cls.acquire()
        if not acquired:
            raise RuntimeError("Failed to acquire semaphore")
        return cls

    @classmethod
    async def __aexit__(cls, exc_type, exc_val, exc_tb):
        await cls.release()


async def process_request():
    async with Semaphore():
        print("Processing request...")
        await asyncio.sleep(5)


from tortoise import fields
from tortoise.models import Model
import asyncio
from datetime import timedelta


class Semaphore(Model):
    id = fields.IntField(pk=True)
    count = fields.IntField()
    max_count = fields.IntField()
    lease_expiration = fields.DatetimeField()

    class Meta:
        table = "semaphore"

    @classmethod
    async def initialize(cls, max_count=10):
        try:
            async with atomic():
                now = datetime.utcnow()
                await cls.raw(
                    "INSERT INTO semaphore (id, count, max_count, lease_expiration) "
                    "VALUES (1, :max_count, :max_count, :now) "
                    "ON CONFLICT (id) DO UPDATE SET "
                    "max_count = :max_count, "
                    "count = CASE WHEN count > :max_count THEN :max_count ELSE count END, "
                    "lease_expiration = :now",
                    values={"max_count": max_count, "now": now},
                )
            print(f"Semaphore initialized or updated with max count: {max_count}")
        except OperationalError as e:
            print(f"Error initializing semaphore: {e}")

    @classmethod
    async def acquire(cls, lease_duration=timedelta(minutes=5)):
        try:
            async with atomic():
                now = datetime.utcnow()
                expiration = now + lease_duration
                result = await cls.raw(
                    "UPDATE semaphore SET count = count - 1, lease_expiration = :expiration "
                    "WHERE id = 1 AND count > 0 "
                    "RETURNING count",
                    values={"expiration": expiration},
                )
                if len(result) > 0:
                    return SemaphoreContext(cls, lease_duration)
                return None
        except OperationalError:
            return None

    @classmethod
    async def release(cls):
        try:
            async with atomic():
                await cls.raw(
                    "UPDATE semaphore SET count = LEAST(count + 1, max_count) WHERE id = 1"
                )
        except OperationalError:
            print("Failed to release semaphore")

    @classmethod
    async def cleanup_expired_leases(cls):
        try:
            async with atomic():
                now = datetime.utcnow()
                await cls.raw(
                    "UPDATE semaphore SET count = LEAST(max_count, count + "
                    "(SELECT max_count - count FROM semaphore WHERE id = 1)) "
                    "WHERE id = 1 AND lease_expiration < :now",
                    values={"now": now},
                )
        except OperationalError:
            print("Failed to cleanup expired leases")

    @classmethod
    async def renew_lease(cls, lease_duration):
        try:
            async with atomic():
                new_expiration = datetime.utcnow() + lease_duration
                await cls.raw(
                    "UPDATE semaphore SET lease_expiration = :new_expiration WHERE id = 1",
                    values={"new_expiration": new_expiration},
                )
        except OperationalError:
            print("Failed to renew lease")


class SemaphoreContext:
    def __init__(self, semaphore_cls, lease_duration):
        self.semaphore_cls = semaphore_cls
        self.lease_duration = lease_duration
        self.renew_task = None

    async def __aenter__(self):
        self.renew_task = asyncio.create_task(self._auto_renew())
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.renew_task:
            self.renew_task.cancel()
        await self.semaphore_cls.release()

    async def _auto_renew(self):
        while True:
            await asyncio.sleep(self.lease_duration.total_seconds() / 2)
            await self.semaphore_cls.renew_lease(self.lease_duration)


# 使用示例
async def main():
    # 初始化 semaphore，设置初始最大计数为 10
    await Semaphore.initialize(max_count=10)

    # 模拟一些请求
    tasks = [asyncio.create_task(process_request()) for _ in range(5)]
    await asyncio.gather(*tasks)

    # 假设我们需要在运行时将最大计数调整为 30
    await Semaphore.set_max_count(30)

    # 再次模拟一些请求，这次可以同时处理更多请求
    tasks = [asyncio.create_task(process_request()) for _ in range(15)]
    await asyncio.gather(*tasks)


if __name__ == "__main__":
    asyncio.run(main())


# 定期清理过期的租约
async def periodic_cleanup(interval=300):  # 每5分钟清理一次
    while True:
        await Semaphore.cleanup_expired_leases()
        await asyncio.sleep(interval)


# 在应用启动时开始定期清理
async def start_periodic_cleanup():
    asyncio.create_task(periodic_cleanup())


# 在 FastAPI 应用中使用
from fastapi import FastAPI

app = FastAPI()


@app.on_event("startup")
async def startup_event():
    await Semaphore.initialize(max_count=30)
    await start_periodic_cleanup()


# 使用示例
async def process_request():
    async with await Semaphore.acquire() as sem:
        if sem is None:
            print("Failed to acquire semaphore")
            return
        print("Processing request...")
        await asyncio.sleep(10)  # 模拟长时间运行的任务
        print("Request processed")


# ... 其余的 FastAPI 代码 ...
from tortoise import fields
from tortoise.models import Model
from tortoise.transactions import atomic
from tortoise.exceptions import OperationalError
import asyncio
from datetime import datetime, timedelta
from fastapi import FastAPI, HTTPException


class Semaphore(Model):
    id = fields.IntField(pk=True)
    count = fields.IntField()
    max_count = fields.IntField()
    lease_expiration = fields.DatetimeField()

    class Meta:
        table = "semaphore"

    @classmethod
    async def initialize(cls, max_count=10):
        try:
            async with atomic():
                now = datetime.utcnow()
                await cls.raw(
                    "INSERT INTO semaphore (id, count, max_count, lease_expiration) "
                    "VALUES (1, :max_count, :max_count, :now) "
                    "ON CONFLICT (id) DO UPDATE SET "
                    "max_count = :max_count, "
                    "count = CASE WHEN count > :max_count THEN :max_count ELSE count END, "
                    "lease_expiration = :now",
                    values={"max_count": max_count, "now": now},
                )
            print(f"Semaphore initialized or updated with max count: {max_count}")
        except OperationalError as e:
            print(f"Error initializing semaphore: {e}")

    @classmethod
    async def acquire(cls, lease_duration=timedelta(minutes=5)):
        try:
            async with atomic():
                now = datetime.utcnow()
                expiration = now + lease_duration
                result = await cls.raw(
                    "UPDATE semaphore SET count = count - 1, lease_expiration = :expiration "
                    "WHERE id = 1 AND count > 0 "
                    "RETURNING count",
                    values={"expiration": expiration},
                )
                if len(result) > 0:
                    return SemaphoreContext(cls, lease_duration)
                return None
        except OperationalError:
            return None

    @classmethod
    async def release(cls):
        try:
            async with atomic():
                await cls.raw(
                    "UPDATE semaphore SET count = LEAST(count + 1, max_count) WHERE id = 1"
                )
        except OperationalError:
            print("Failed to release semaphore")

    @classmethod
    async def cleanup_expired_leases(cls):
        try:
            async with atomic():
                now = datetime.utcnow()
                await cls.raw(
                    "UPDATE semaphore SET count = LEAST(max_count, count + "
                    "(SELECT max_count - count FROM semaphore WHERE id = 1)) "
                    "WHERE id = 1 AND lease_expiration < :now",
                    values={"now": now},
                )
        except OperationalError:
            print("Failed to cleanup expired leases")

    @classmethod
    async def renew_lease(cls, lease_duration):
        try:
            async with atomic():
                new_expiration = datetime.utcnow() + lease_duration
                await cls.raw(
                    "UPDATE semaphore SET lease_expiration = :new_expiration WHERE id = 1",
                    values={"new_expiration": new_expiration},
                )
        except OperationalError:
            print("Failed to renew lease")


class SemaphoreContext:
    def __init__(self, semaphore_cls, lease_duration):
        self.semaphore_cls = semaphore_cls
        self.lease_duration = lease_duration
        self.renew_task = None

    async def __aenter__(self):
        self.renew_task = asyncio.create_task(self._auto_renew())
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.renew_task:
            self.renew_task.cancel()
        await self.semaphore_cls.release()

    async def _auto_renew(self):
        while True:
            await asyncio.sleep(self.lease_duration.total_seconds() / 2)
            await self.semaphore_cls.renew_lease(self.lease_duration)


# FastAPI 应用
app = FastAPI()


@app.on_event("startup")
async def startup_event():
    # 初始化 Semaphore，设置最大计数为 5
    await Semaphore.initialize(max_count=5)


@app.get("/acquire")
async def acquire_semaphore():
    async with await Semaphore.acquire() as sem:
        if sem is None:
            raise HTTPException(status_code=503, detail="Semaphore not available")
        # 模拟一些需要 semaphore 的操作
        await asyncio.sleep(10)
        return {"message": "Operation completed successfully"}


@app.get("/status")
async def get_semaphore_status():
    semaphore = await Semaphore.get(id=1)
    return {
        "current_count": semaphore.count,
        "max_count": semaphore.max_count,
        "lease_expiration": semaphore.lease_expiration,
    }


# 定期清理过期租约的任务
async def periodic_cleanup():
    while True:
        await Semaphore.cleanup_expired_leases()
        await asyncio.sleep(60)  # 每分钟清理一次


@app.on_event("startup")
async def start_periodic_tasks():
    asyncio.create_task(periodic_cleanup())


# 使用示例
async def example_usage():
    async with await Semaphore.acquire() as sem:
        if sem is None:
            print("Failed to acquire semaphore")
            return
        print("Semaphore acquired, performing operation...")
        await asyncio.sleep(10)  # 模拟长时间操作
    print("Operation completed, semaphore released")


# 如果直接运行此脚本，执行示例
if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)


async def upload_file_handler(dataset_obj, file_objs):
    async with aiohttp.ClientSession() as session:
        file_objs_iter = iter(file_objs)
        await create_and_run_tasks(session, file_objs_iter, dataset_obj)

    await Dataset.filter(id=dataset_obj.id).update(data_status=DATASET_STATUS.READY)
    await Robot.update_ai_status_from_dataset(dataset_obj.id, AIStatus.READY)


async def create_and_run_tasks(session, file_objs_iter: Iterator, dataset_obj):
    from mygpt.file_utils import process_file

    tasks = []

    async def submit_and_process_task():
        for file_obj, file_location in file_objs_iter:
            while True:
                lock = lock_old if file_obj.learn_type == 0 else lock_adv
                semaphore = semaphore_old if file_obj.learn_type == 0 else semaphore_adv
                await lock.acquire()
                try:
                    async with semaphore:
                        task = asyncio.create_task(
                            process_file(
                                session,
                                dataset_obj,
                                file_obj,
                                file_location,
                            )
                        )
                        tasks.append(task)
                        break  # 当任务被创建后，退出内部循环
                except Exception as e:
                    logging.error(e)
                    logging.error(traceback.format_exc())
                    if lock.locked():
                        logging.error(f"unexpected locked")
                        lock.release()
                # finally:
                #     if lock.locked():
                #         logging.error(f"unexpected locked")
                #         lock.release()

    await submit_and_process_task()
    res = await asyncio.gather(*tasks, return_exceptions=True)
    logging.info(f"data learning tasks finished, {res}")

def format_sensitive_key(api_key: str, num_front: int = 8, num_back: int = 8) -> str:
    """将api key 做安全处理，消除敏感信息"""
    if not isinstance(api_key, str):
        return "*" * 10

    if not isinstance(num_front, int) or not isinstance(num_back, int):
        return "*" * 10

    if num_front < 0 or num_back < 0:
        return "*" * 10

    if not api_key:
        return "*" * 10
    key_length = len(api_key)
    mask_length = 10  # 固定数量的星号用于掩码
    if key_length < num_front + num_back:
        logging.warning(
            f"API key length ({key_length}) is less than num_front ({num_front}) + num_back ({num_back})."
            " Adjusting masking strategy."
        )
        if key_length <= 2:
            return "*" * key_length
        return f"{api_key[0]}{'*' * (key_length - 2)}{api_key[-1]}"
    masked_key = f"{api_key[:num_front]}{'*' * mask_length}{api_key[-num_back:]}"
    logging.debug(f"Formatted API key: {masked_key}")
    return masked_key

lock_old = asyncio.Lock()
semaphore_old = asyncio.Semaphore(FILE_LEARNING_TASKS_PER_SERVER)

lock_adv = asyncio.Lock()
semaphore_adv = asyncio.Semaphore(FILE_LEARNING_TASKS_PER_SERVER_ADV)
