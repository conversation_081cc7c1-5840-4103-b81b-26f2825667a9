import threading

from mygpt.enums import PROMPT_TYPE
from mygpt.models import InsiderPreviewUser, Prompt
from mygpt.prompt import load_latest_prompt


class PromptCache:
    """
    A cache of prompts for a given user. This is used to avoid querying the database.
    :param insider_prompts: A dictionary of PROMPT_TYPE to Prompt objects 内部Prompt
    :param official_prompts: A dictionary of PROMPT_TYPE to Prompt objects 官方Prompt
    """

    _instance = None
    insider_prompts: dict[PROMPT_TYPE, Prompt]
    official_prompts: dict[PROMPT_TYPE, Prompt]
    use_latest: bool = False
    lock = threading.Lock()

    def __new__(cls, use_latest: bool = False):
        if cls._instance is None:
            with cls.lock:
                cls._instance = super().__new__(cls)
                cls._instance.insider_prompts = {}
                cls._instance.official_prompts = {}
                cls._instance.use_latest = use_latest  # 是否使用最新的Prompt
                # cls._instance.init()
        return cls._instance

    async def reload_insider_preview_users(self):
        """
        Reload the insider preview users from the database.
        """
        insider_preview_users = await InsiderPreviewUser.all()
        self.insider_preview_users = {
            user.user_id if user.user_id else user.robot_id: user
            for user in insider_preview_users
        }

    async def reload_prompts(self):
        """
        Reload the prompts from the database.
        """
        for prompt_type in PROMPT_TYPE:
            if self.use_latest:
                content = load_latest_prompt(prompt_type)
                self.insider_prompts[prompt_type] = Prompt(
                    prompt_type=prompt_type,
                    content=content,
                )
                continue
            prompt_obj = await Prompt.filter(
                prompt_type=prompt_type,
                deleted_at__isnull=True,
            ).first()
            self.insider_prompts[prompt_type] = prompt_obj
            self.official_prompts[prompt_type] = await Prompt.filter(
                prompt_type=prompt_type,
                is_insider=False,
                deleted_at__isnull=True,
            ).first()

    @classmethod
    async def setup(cls, use_latest: bool = False):
        """
        Initialize the cache.
        """
        cls._instance = cls(use_latest)
        if not cls._instance.use_latest:
            await cls._instance.reload_insider_preview_users()
        await cls._instance.reload_prompts()

    @classmethod
    async def get_prompt(
        cls,
        prompt_type: PROMPT_TYPE,
        user_id: str = None,
        ai_id: str = None,
    ) -> Prompt:
        """
        Get the prompt for the given user and prompt type.
        :param user_id: The user ID
        :param prompt_type: The prompt type
        :return: The prompt
        """
        if cls._instance.use_latest:
            return cls._instance.insider_prompts[prompt_type]
        user = None
        if user_id:
            user = cls._instance.insider_preview_users.get(user_id)
        if ai_id and not user:
            user = cls._instance.insider_preview_users.get(ai_id)
        if user:
            return cls._instance.insider_prompts[prompt_type]
        else:
            return cls._instance.official_prompts[prompt_type]

    @classmethod
    async def get_prompt_content(
        cls,
        prompt_type: PROMPT_TYPE,
        user_id: str = None,
        ai_id: str = None,
    ) -> dict:
        """
        Get the prompt text for the given user and prompt type.
        :param user_id: The user ID
        :param prompt_type: The prompt type
        :return: The prompt text
        """
        prompt = await cls.get_prompt(prompt_type, user_id, ai_id)
        return prompt.content.copy()
