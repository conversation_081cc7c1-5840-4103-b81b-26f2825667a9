import time
import uuid

import pandas as pd
import requests

filename = "C:\\Users\\<USER>\\Downloads\\test-qa-3.xlsx"


# import openpyxl
#
# wb = openpyxl.load_workbook(filename)
# sheet = wb.active
# print("sheet", sheet)

# 读取Excel文件
# wb = openpyxl.load_workbook(filename)
# print("Sheet names:", wb.sheetnames)

# 读取Excel文件
df = pd.read_excel(filename)
print(df.columns)

df.columns = (
    df.columns.str.strip().str.upper()
)  # This will make all column names uppercase.

q_c = "質問"
a_c = "最新回复情况（2023.12.15）"
oa_c = "回答"
# 获取C列的数据
questions = df[q_c]
oas = df[oa_c]


# 开始从第二行读取问题并发送请求
for index in range(0, len(questions)):
    if index == 50:
        break
    # 生成session_id
    session_id = uuid.uuid4()

    oa = oas[index]

    # 构建请求的数据
    data = {
        "session_id": str(session_id),
        "stream": False,
        "simple_question": True,
        "with_source": True,
        "question": questions[index],
        "use_faq": True,
        "is_test": True,
    }

    while True:
        try:
            # 发送请求到指定的URL
            response = requests.post(
                "https://api.gbase.ai/v1/question/7fac4caa-fc54-41c2-9e2f-8b4c8e7adb4e",
                json=data,
            )
            break
        except Exception as e:
            print(e)
            time.sleep(1000)
            continue

    # 获取回复
    res_json = response.json()
    answer = res_json["answer"]
    print("question: ", questions[index], "\nanswer", answer)

    # 将回复保存到Excel的B列
    df.loc[index, a_c] = answer

# 开始从第二行读取问题并发送请求
for index in range(len(questions) - 1, len(questions) - 1 - 50, -1):
    if index == 3:
        break
    # 生成session_id
    session_id = uuid.uuid4()

    oa = oas[index]

    # 构建请求的数据
    data = {
        "session_id": str(session_id),
        "stream": False,
        "simple_question": True,
        "with_source": True,
        "question": questions[index],
        "use_faq": True,
        "is_test": True,
    }

    while True:
        try:
            # 发送请求到指定的URL
            response = requests.post(
                "https://api.gbase.ai/v1/question/7fac4caa-fc54-41c2-9e2f-8b4c8e7adb4e",
                json=data,
            )
            break
        except Exception as e:
            print(e)
            time.sleep(1000)
            continue

    # 获取回复
    res_json = response.json()
    answer = res_json["answer"]
    print("question: ", questions[index], "\nanswer", answer)

    # 将回复保存到Excel的B列
    df.loc[index, a_c] = answer

# 保存修改
df.to_excel(f"C:\\Users\\<USER>\\Downloads\\out-test-qa-{time.time()}.xlsx", index=False)
