import asyncio
import logging
import ssl
import traceback
from typing import List, Optional
from urllib.parse import urljoin, urlparse

import aiohttp
from langchain.docstore.document import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter

from mygpt.error import OperationFailedException
from mygpt.loader.chunk import Chunk
from mygpt.loader.document import DocumentLoader
from mygpt.loader.enums import DocumentFormat, DocumentParserPlugin
from mygpt.loader.html import HTMLParserLoader
from mygpt.loader.service import ParserPlugin
from mygpt.settings import LOCAL_PROXY

"""Loader that uses unstructured to load HTML files."""

text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=1000,
    chunk_overlap=100,
)

short_text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=200,
    chunk_overlap=50,
)

ssl_context = ssl.create_default_context()
ssl_context.set_ciphers("DEFAULT@SECLEVEL=1")


class SiteCrawler:
    def __init__(self, base_url: Optional[str] = None, is_spa: bool = False):
        self.base_url = base_url
        self.is_spa = is_spa

    # This class method is an asynchronous function that accepts a URL argument,
    # and returns the response content if it's an HTML text, otherwise raises a ValueError.
    async def crawl(self, url, retry_await: int = 2, max_retry: int = 3) -> str:
        status = 200
        # An aiohttp client session is created asynchronously.
        async with aiohttp.ClientSession() as session:
            # An HTTP GET request is sent to the given URL asynchronously.
            async with session.get(
                url,
                timeout=5,
                allow_redirects=False,
                proxy=LOCAL_PROXY,
                ssl_context=ssl_context,
            ) as response:
                # If the response status code is not 200 (OK), raise a ValueError exception.
                if response.status != 200:
                    status = response.status
                else:
                    # Get the Content-type of the response.
                    content_type = response.headers.get("content-type")
                    # If the content type is not text/html,
                    # Raise a ValueError exception informing that the content_type is incorrect.
                    if "text/html" not in content_type.lower():
                        logging.error(
                            f"Content_type is:{content_type.lower()}.Not text/html"
                        )
                        raise ValueError(
                            f"Content_type is:{content_type.lower()}.Not text/html"
                        )
                    # Check if the Content-type is text/html,
                    # And return the response contents if it is.
                    if content_type and "text/html" in content_type.lower():
                        return await response.text()
        if status == 429:
            retry_after = response.headers.get("Retry-After", None)
            if retry_after:
                retry_after = int(retry_after)
                await asyncio.sleep(retry_after)
            else:
                retry_await = retry_await * 2
                await asyncio.sleep(retry_await)

            logging.warn(f"Response status is:{response.status}")
            if max_retry <= 0:
                logging.error(f"Response status is:{response.status}.Not 200")
                raise ValueError(f"Response status is:{response.status}.Not 200")
            max_retry -= 1
            return await self.crawl(url, retry_await, max_retry=max_retry)
        if status == 301 or status == 302:
            location = response.headers.get("location", None)
            # 处理location为相对路径的情况
            if location and not location.startswith("http"):
                location = urljoin(self.base_url, location)
            await asyncio.sleep(1)
            logging.warn(
                f"Response status is:{response.status},and location to:{location}"
            )
            return await self.crawl(location)
        logging.error(f"Response status is:{response.status}.Not 200")
        raise ValueError(f"Response status is:{response.status}.Not 200")

    def to_chunk(self, doc: Document):
        """
        简单的将文档拆分成多个chunk，如果使用 DocumentLoader 的话，需要使用 split_by_file 方法
        """
        content = doc.page_content
        url = doc.metadata.get("source")
        loader = HTMLParserLoader(content, url)
        docs = loader.load_and_split(text_splitter)
        return [Chunk(content=doc.page_content, metadata=doc.metadata) for doc in docs]

    async def parse_base(
        self,
        url: Optional[str] = None,
        format: DocumentFormat = DocumentFormat.TEXT,
        client: Optional[aiohttp.ClientSession] = None,
    ):
        try:
            document_loader = DocumentLoader(self.base_url or url)
            docs = await document_loader.async_load(
                format,
                spa=self.is_spa,
                max_images=0,
                plugins=[{"plugin": DocumentParserPlugin.METADATA}],
                client=client,
            )
            if not docs:
                raise Exception("Empty HTML")
            doc = docs[0]
            return doc
        except Exception as e:
            logging.error(f"parse_base error: {traceback.format_exc()}")
            raise OperationFailedException(
                "Invalid URL. Please check the URL and try again."
            )

    async def parse(
        self,
        url: Optional[str] = None,
        format: DocumentFormat = DocumentFormat.TEXT,
        plugins: List[ParserPlugin] = None,
        max_images: int | None = None,
        client: Optional[aiohttp.ClientSession] = None,
    ) -> Document:
        try:
            document_loader = DocumentLoader(self.base_url or url)
            docs = await document_loader.async_load(
                format,
                spa=self.is_spa,
                max_images=max_images,
                plugins=plugins,
                client=client,
            )
            if not docs:
                raise Exception("Empty HTML")
            return docs[0]
        except Exception as e:
            raise OperationFailedException(
                "Invalid URL. Please check the URL and try again."
            )

    def handler_links(self, url, link) -> str | None:
        href = link.get("href")
        if not href or href.startswith("#"):
            return None
        # print(href)
        next_url = urljoin(url, href)

        parsed_next_url = urlparse(next_url)
        parsed_base_url = urlparse(self.base_url)

        # 仅爬取站点内的url
        if (
            parsed_next_url.netloc == parsed_base_url.netloc
            and parsed_next_url.scheme == parsed_base_url.scheme
        ):
            # 处理以/路径开头的url相对路径
            if parsed_next_url.path.startswith("/"):
                return parsed_next_url.geturl()
            # 处理二级路径可能不带/路径的url相对路径
            elif not parsed_next_url.path.startswith(
                "."
            ) and not parsed_next_url.path.startswith("/"):
                return urljoin(url + "/", href)
            else:
                return href
