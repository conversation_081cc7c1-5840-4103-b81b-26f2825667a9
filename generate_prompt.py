import asyncio
import getopt
import json
import logging
import os
import uuid
from pathlib import Path

from aerich import MIGRATE_TEMPLATE, Migrate
from aerich.utils import get_tortoise_config
from tortoise import Tortoise

from mygpt.enums import PROMPT_TYPE
from mygpt.models import Prompt
from mygpt.prompt import (
    chat,
    faq_answer_turbo,
    faq_ask_for_model_detect_turbo,
    faq_ask_user_to_provide_model_turbo,
    faq_model_detect_turbo,
    function_call,
    function_call_api,
    intent,
    language_detect,
    query_key_words,
    template_parse,
    session_message_title,
)

prompt_insert_tmpl = """INSERT INTO "prompt" (\
"id","created_at","updated_at",\
"version","prompt_type","is_insider","content") \
VALUES ('{id}',{created_at},{updated_at},'{version}','{prompt_type}',{is_insider},'{content}');"""

prompt_delete_tmpl = """DELETE FROM prompt WHERE version = '{version}';"""

offical_update_sql = "UPDATE prompt SET is_insider = false WHERE id = '{id}';"
offical_undo_sql = "UPDATE prompt SET is_insider = true WHERE id = '{id}';"


async def check_prompt_diff(prompt_type: PROMPT_TYPE = None):
    """
    检查Prompt版本是否有差异
    """
    need_generate_prompt = {}

    def _prepare(prompt_type, prompt, new_prompt_dict):
        new_prompt_content = json.dumps(new_prompt_dict, ensure_ascii=False)
        if prompt is None:
            need_generate_prompt[prompt_type] = new_prompt_content
            return
        if new_prompt_dict["type"] == "completion":
            # completion 判断是否有差异，并得到新的prompt content
            old_prompt_content = template_parse(prompt.content.get("prompt"))
            if new_prompt_dict.get("prompt") == old_prompt_content:
                return
            need_generate_prompt[prompt_type] = new_prompt_content
            return

        # chat 判断是否有差异，并得到新的prompt content
        old_system = template_parse(prompt.content.get("system"))
        old_user = template_parse(prompt.content.get("user"))
        if (
            new_prompt_dict.get("type") == prompt.content.get("type")
            and new_prompt_dict.get("system") == old_system
            and new_prompt_dict.get("user") == old_user
        ):
            return
        need_generate_prompt[prompt_type] = new_prompt_content

    def _prepare_with_type(prompt_type, prompt):
        if prompt_type == PROMPT_TYPE.LANGUAGE_DETECT:
            new_prompt_dict = language_detect()
        elif prompt_type == PROMPT_TYPE.INTENT_V1:
            new_prompt_dict = intent()
        elif prompt_type == PROMPT_TYPE.QUERY_KEY_WORDS:
            new_prompt_dict = query_key_words()
        elif prompt_type == PROMPT_TYPE.CHAT:
            new_prompt_dict = chat()
        elif prompt_type == PROMPT_TYPE.FAQ_ANSWER_TURBO:
            new_prompt_dict = faq_answer_turbo()
        elif prompt_type == PROMPT_TYPE.FAQ_ASK_USER_TO_PROVIDE_MODEL_TURBO:
            new_prompt_dict = faq_ask_user_to_provide_model_turbo()
        elif prompt_type == PROMPT_TYPE.FAQ_ASK_FOR_MODEL_DETECT_TURBO:
            new_prompt_dict = faq_ask_for_model_detect_turbo()
        elif prompt_type == PROMPT_TYPE.FAQ_MODEL_DETECT_TURBO:
            new_prompt_dict = faq_model_detect_turbo()
        elif prompt_type == PROMPT_TYPE.FUNCTION_CALL:
            new_prompt_dict = function_call()
        elif prompt_type == PROMPT_TYPE.FUNCTION_CALL_API:
            new_prompt_dict = function_call_api()
        elif prompt_type == PROMPT_TYPE.SESSION_MESSAGE_TITLE:
            new_prompt_dict = session_message_title()
        else:
            raise Exception(f"Prompt type {prompt_type} not found.")
        _prepare(prompt_type, prompt, new_prompt_dict)

    if prompt_type:
        # 检查指定类型的Prompt是否有差异
        prompt = await Prompt.filter(prompt_type=prompt_type).first()
        _prepare_with_type(prompt_type, prompt)
        return need_generate_prompt

    # 检查所有的Prompt是否有差异
    for prompt_type in PROMPT_TYPE:
        prompt = await Prompt.filter(prompt_type=prompt_type).first()
        _prepare_with_type(prompt_type, prompt)

    return need_generate_prompt


async def check_prompt_diff_official(prompt_type: PROMPT_TYPE):
    prompt = await Prompt.filter(prompt_type=prompt_type).first()
    if not prompt:
        raise Exception(f"Prompt type {prompt_type} not found.")
    elif prompt.is_insider:
        return prompt
    return None


async def generate_sql(need_generate_prompt: dict, version: str, is_inside: bool):
    upgrade_sql, downgrade_sql = "", ""
    for prompt_type, new_prompt_content in need_generate_prompt.items():
        prompt_insert_sql = prompt_insert_tmpl.format(
            id=uuid.uuid4(),
            created_at="now()",
            updated_at="now()",
            version=version,
            prompt_type=prompt_type,
            is_insider=is_inside,
            content=new_prompt_content,
        )
        upgrade_sql += prompt_insert_sql + "\n"
    if upgrade_sql:
        prompt_delete_sql = prompt_delete_tmpl.format(version=version)
        downgrade_sql = prompt_delete_sql + "\n"
    return upgrade_sql, downgrade_sql


async def migrate_official(
    prompt_type: PROMPT_TYPE = None, need_generate_prompt: dict = None
):
    def no_need_handle(t: PROMPT_TYPE):
        if not need_generate_prompt:
            return False
        return t in need_generate_prompt.keys()

    upgrade_sql, downgrade_sql = "", ""
    if prompt_type and not no_need_handle(prompt_type):
        prompt_obj = await check_prompt_diff_official(prompt_type)
        if not prompt_obj:
            logging.info("No prompt diff found.")
            return upgrade_sql, downgrade_sql
        prompt_obj.is_insider = False
        upgrade_sql += offical_update_sql.format(id=prompt_obj.id) + "\n"
        downgrade_sql += offical_undo_sql.format(id=prompt_obj.id) + "\n"
    else:
        for prompt_type in PROMPT_TYPE:
            if no_need_handle(prompt_type):
                continue
            prompt_obj = await check_prompt_diff_official(prompt_type)
            if not prompt_obj:
                continue
            prompt_obj.is_insider = False
            upgrade_sql += offical_update_sql.format(id=prompt_obj.id) + "\n"
            downgrade_sql += offical_undo_sql.format(id=prompt_obj.id) + "\n"
        if not upgrade_sql:
            logging.info("No prompt need update to official.")
    return upgrade_sql, downgrade_sql


async def migrate(is_inside: bool, prompt_type: PROMPT_TYPE = None):
    """
    生成新的Prompt版本
    """
    # 生成器初始化
    Migrate.app = "models"
    tortoise_config = get_tortoise_config(None, "mygpt.settings.TORTOISE_ORM")
    await Tortoise.init(config=tortoise_config)
    Migrate.migrate_location = Path("./migrations", Migrate.app)

    # 生成一个新的版本
    version = await Migrate.generate_version("prompt_insert")

    # delete if same version exists
    for version_file in Migrate.get_all_version_files():
        if version_file.startswith(version.split("_")[0]):
            os.unlink(Path(Migrate.migrate_location, version_file))
    version_file = Path(Migrate.migrate_location, version)
    need_generate_prompt = await check_prompt_diff(prompt_type)
    upgrade_sql, downgrade_sql = "", ""
    if not need_generate_prompt:
        logging.info("No prompt diff found.")
    else:
        upgrade_sql, downgrade_sql = await generate_sql(
            need_generate_prompt, version, is_inside
        )
    if not is_inside:
        # 更新prompt为正式版本
        official_upgrade_sql, official_downgrade_sql = await migrate_official(
            prompt_type, need_generate_prompt
        )
        upgrade_sql += official_upgrade_sql
        downgrade_sql += official_downgrade_sql
    # 写入文件
    if not upgrade_sql:
        return
    content = MIGRATE_TEMPLATE.format(
        upgrade_sql=upgrade_sql,
        downgrade_sql=downgrade_sql,
    )
    with open(version_file, "w", encoding="utf-8") as f:
        f.write(content)
    logging.info(f"Migration file {version_file} created.")


def __main__(argv):
    """
    :param type 生成的prompt类型,默认为全部
    :param inside 是否是内部版本，默认为内部版本
    example: python generator.py -m --type=lanuage_detect --inside=True
    """
    try:
        opts, args = getopt.getopt(argv, "hm", ["migrate=", "type=", "inside="])
    except getopt.GetoptError:
        print("test.py -m")
        sys.exit(2)
    is_inside, prompt_type = True, None
    for arg in args:
        if arg.startswith("inside="):
            inside = arg.split("=")[1]
            if inside.lower() == "false":
                is_inside = False
        if arg.startswith("type="):
            prompt_type_str = arg.split("=")[1]
            if prompt_type_str not in tuple(PROMPT_TYPE):
                print(
                    f"error for prompt type{prompt_type_str} not in {tuple(PROMPT_TYPE)}"
                )
                sys.exit(2)
            prompt_type = PROMPT_TYPE(prompt_type_str)
    for opt, arg in opts:
        if opt == "-h":
            print("test.py -m --type=lanuage_detect")
            sys.exit()
        elif opt in ("-m", "--migrate"):
            asyncio.run(migrate(is_inside, prompt_type))


if __name__ == "__main__":
    import sys

    __main__(sys.argv[1:])

# for check prompt in db is same as template in prompt.py
# async def check_prompt():
#     await Tortoise.init(config=tortoise_config)
#     prompt = await Prompt.filter(prompt_type=PROMPT_TYPE.LANUAGE_DETECT).first()
#     print(prompt.content['system'])
#     print(detect_language_system_template)
#     print(prompt.content['system'] == detect_language_system_template)
