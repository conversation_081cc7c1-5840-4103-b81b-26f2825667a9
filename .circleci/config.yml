version: 2.1
orbs:
  aws-ecr: circleci/aws-ecr@7.0.0

jobs:
  build-and-push:
    machine:
      image: ubuntu-2204:current
    resource_class: medium
    parameters:
      docker-tag:
        type: string
      path:
        type: string
      repo:
        type: string
      dockerfile:
        type: string
    working_directory: ~/project
    steps:
      - aws-ecr/build-and-push-image:
          checkout: true
          account-url: AWS_ECR_ACCOUNT_URL
          aws-access-key-id: AWS_ACCESS_KEY_ID
          aws-secret-access-key: AWS_SECRET_ACCESS_KEY
          create-repo: false
          dockerfile: <<parameters.dockerfile>>
          path: <<parameters.path>>
          region: AWS_REGION
          repo: <<parameters.repo>>
          tag: '<<parameters.docker-tag>>${CIRCLE_SHA1}'
  docker-hub-build-push:
    machine:
      image: ubuntu-2204:current
    resource_class: medium
    parameters:
      path:
        type: string
      repo:
        type: string
      dockerfile:
        type: string
      docker-tag:
        type: string
    steps:
      - checkout
      - run:
          name: Build Docker image
          command: |
            docker build -t <<parameters.repo>>:<<parameters.docker-tag>> -f <<parameters.path>>/<<parameters.dockerfile>> .
            docker build -t <<parameters.repo>>:<<parameters.docker-tag>> -f <<parameters.path>>/<<parameters.dockerfile>> .
      - run:
          name: Publish Docker Image to Docker Hub
          command: |
            echo "$DOCKERHUB_PASS" | docker login -u "$DOCKERHUB_USERNAME" --password-stdin
            IMAGE_TAG="0.0.${CIRCLE_BUILD_NUM}"
            docker tag <<parameters.repo>>:<<parameters.docker-tag>> <<parameters.repo>>:$IMAGE_TAG
            docker push <<parameters.repo>>:<<parameters.docker-tag>>
            docker push <<parameters.repo>>:$IMAGE_TAG
  deploy:
    machine:
      image: ubuntu-2204:current
    resource_class: medium
    parameters:
      docker-tag:
        type: string
      path:
        type: string
      deploy-name:
        type: string
      deploy-namespace:
        type: string
    steps:
      # - add_ssh_keys:
      - run:
          name: kubectl apply
          command: |
            CMD='/home/<USER>/cluster/deploy.sh <<parameters.path>> <<parameters.docker-tag>>'${CIRCLE_SHA1}' <<parameters.deploy-name>> <<parameters.deploy-namespace>>'
            echo $CMD
            ssh ${USER_NAME}@${HOST_NAME} ${CMD}

            # 判断正则表达式是否匹配，匹配规则从下面的示例中提取，示例：2023.wk42,2023.wk43
            if [[ "${CIRCLE_BRANCH}" =~ ^([0-9]{4}.wk[0-9]{2},?)+$ ]]; then
              curl -X POST -H "Content-Type: application/json" -d '{"msg_type":"text","content":{"text":"'${CIRCLE_USERNAME}' 触发了正式环境分支 '${CIRCLE_BRANCH}' 更新与部署"}}' https://open.larksuite.com/open-apis/bot/v2/hook/68004e4a-1381-4886-a982-cd77d5f2e6a1
            fi
            # 判断正则表达式是否匹配，匹配规则从下面的示例中提取，示例：canary.2023.wk42,canary.2023.wk43
            if [[ "${CIRCLE_BRANCH}" =~ ^canary.([0-9]{4}.wk[0-9]{2},?)+$ ]]; then
              curl -X POST -H "Content-Type: application/json" -d '{"msg_type":"text","content":{"text":"'${CIRCLE_USERNAME}' 触发了灰度环境分支 '${CIRCLE_BRANCH}' 更新与部署"}}' https://open.larksuite.com/open-apis/bot/v2/hook/68004e4a-1381-4886-a982-cd77d5f2e6a1
            fi

workflows:
  version: 2
  backend_build_and_push_tag:
    jobs:
      - docker-hub-build-push:
          name: tag-build
          context:
            - ecr-new
          path: .
          dockerfile: Dockerfile_pyc
          repo: gptbasesparticle/gb_backend
          docker-tag: "${CIRCLE_SHA1}"
          filters:
            tags:
              only: /^build.*/
            branches:
              ignore: /.*/
  backend_build_and_push:
    jobs:
      - build-and-push:
          name: backend-build-for-test
          context:
            - ecr-new
          path: .
          dockerfile: Dockerfile
          repo: felo-mygpt
          docker-tag: ''
          filters:
            tags:
              only: /^test.*/
            branches:
              ignore: /.*/
      - deploy:
          name: backend-deploy-for-test
          docker-tag: ''
          path: '/home/<USER>/cluster/felo-sandbox/felo-mygpt/deploy.yaml'
          deploy-name: felo-mygpt-backend
          deploy-namespace: default
          context:
            - ecr-new
          filters:
            tags:
              only: /^test.*/
            branches:
              ignore: /.*/
          requires:
            - backend-build-for-test
      - build-and-push:
          name: backend-build
          context:
            - ecr-new
          path: .
          dockerfile: Dockerfile
          repo: felo-mygpt
          docker-tag: ''
          filters:
            branches:
              only:
                - /^[0-9]*.wk[0-9]*$/
      - deploy:
          name: prod-backend-deploy
          docker-tag: ''
          path: '/home/<USER>/cluster/default/felo-mygpt/deploy.yaml'
          deploy-name: felo-mygpt-backend
          deploy-namespace: default
          context:
            - ecr-new
          filters:
            branches:
              only:
                - /^[0-9]*.wk[0-9]*$/
          requires:
            - backend-build
      - build-and-push:
          name: hande-dev-ecr-backend-build
          context:
            - ecr-new
          path: .
          dockerfile: Dockerfile_Celery_pyc
          repo: felo-mygpt
          docker-tag: 'celery-dev'
          filters:
            tags:
              only: /^test.*/
            branches:
              ignore: /.*/
      - deploy:
          name: backend-deploy-celery
          docker-tag: 'celery-dev'
          path: '/home/<USER>/cluster/felo-sandbox/felo-mygpt-celery/deploy-celery.yaml'
          deploy-name: felo-mygpt-backend-celery
          deploy-namespace: default
          context:
            - ecr-new
          filters:
            tags:
              only: /^test.*/
            branches:
              ignore: /.*/
          requires:
            - hande-dev-ecr-backend-build
      - build-and-push:
          name: hande-backend-build
          context:
            - ecr-new
          path: .
          dockerfile: Dockerfile_Celery_pyc
          repo: felo-mygpt
          docker-tag: 'celery'
          filters:
            branches:
              only:
                - /^[0-9]*.wk[0-9]*$/
      - deploy:
          name: prod-backend-deploy-celery
          docker-tag: 'celery'
          path: '/home/<USER>/cluster/default/felo-mygpt-celery/deploy-celery.yaml'
          deploy-name: felo-mygpt-backend-celery
          deploy-namespace: felo-sandbox
          context:
            - ecr-new
          filters:
            branches:
              only:
                - /^[0-9]*.wk[0-9]*$/
          requires:
            - hande-backend-build
      - docker-hub-build-push:
          name: hande-celery-main-build-dockerhub
          context:
            - ecr-new
          path: .
          dockerfile: Dockerfile_Celery_pyc
          repo: gptbasesparticle/gb_backend
          docker-tag: celery-main
          filters:
            branches:
              only:
                - main
      - docker-hub-build-push:
          name: hande-celery-release-build-dockerhub
          context:
            - ecr-new
          path: .
          dockerfile: Dockerfile_Celery_pyc
          repo: gptbasesparticle/gb_backend
          docker-tag: celery-release
          filters:
            branches:
              only:
                - release
      - docker-hub-build-push:
          name: hande-celery-wk-build-dockerhub
          context:
            - ecr-new
          path: .
          dockerfile: Dockerfile_Celery_pyc
          repo: gptbasesparticle/gb_backend
          docker-tag: wk-celery
          filters:
            branches:
              only:
                - /^[0-9]*.wk[0-9]*$/
      - docker-hub-build-push:
          name: main-backend-build-dockerhub
          context:
            - ecr-new
          path: .
          dockerfile: Dockerfile_pyc
          repo: gptbasesparticle/gb_backend
          docker-tag: main
          filters:
            branches:
              only:
                - main
      - docker-hub-build-push:
          name: release-backend-build-dockerhub
          context:
            - ecr-new
          path: .
          dockerfile: Dockerfile_pyc
          repo: gptbasesparticle/gb_backend
          docker-tag: release
          filters:
            branches:
              only:
                - release
      - docker-hub-build-push:
          name: wk-backend-build-dockerhub
          context:
            - ecr-new
          path: .
          dockerfile: Dockerfile_pyc
          repo: gptbasesparticle/gb_backend
          docker-tag: wk
          filters:
            branches:
              only:
                - /^[0-9]*.wk[0-9]*$/
