import asyncio
import time
from datetime import datetime

import aiohttp


async def test_endpoint(session, name, url, headers, expected_size=None):
    start_time = time.time()
    try:
        async with session.get(url, headers=headers) as response:
            elapsed_time = time.time() - start_time
            data = await response.json()

            # 状态码检查
            if response.status != 200:
                print(
                    f"❌ {name} 失败: 状态码 {response.status} (耗时: {elapsed_time:.2f}秒)"
                )
                return False

            # items 检查
            if "items" not in data:
                print(
                    f"❌ {name} 失败: 响应中没有 'items' 字段 (耗时: {elapsed_time:.2f}秒)"
                )
                return False

            # 数量检查
            if expected_size and len(data["items"]) != expected_size:
                print(
                    f"❌ {name} 失败: 预期 {expected_size} 个项目, 实际获得 {len(data['items'])} 个 (耗时: {elapsed_time:.2f}秒)"
                )
                return False

            print(f"✅ {name} 通过 (耗时: {elapsed_time:.2f}秒)")
            return True

    except Exception as e:
        elapsed_time = time.time() - start_time
        print(f"❌ {name} 出错: {str(e)} (耗时: {elapsed_time:.2f}秒)")
        return False


async def run_tests():
    headers = {
        "authorization": "bearer ak-bTzWCJNikIPwXix4XSZPfPh2P1Yv0GJJQUYDc1SL6wPlwcVS",
    }

    test_cases = [
        {
            "name": "Robots API",
            "url": "http://localhost:8000/robots?page=1&size=3",
            "expected_size": 3,
        },
        {
            "name": "Datasets API",
            "url": "http://localhost:8000/datasets?page=1&size=10",
            "expected_size": 10,
        },
        {
            "name": "Dataset Files API (file_type=file)",
            "url": "http://localhost:8000/datasets/e81aa3f4-5a8a-418b-be5f-d7f3e914f6ea/files?file_type=file&page=1&size=5",
            "expected_size": 1,
        },
        {
            "name": "Dataset Files API (file_type=integration)",
            "url": "http://localhost:8000/datasets/e81aa3f4-5a8a-418b-be5f-d7f3e914f6ea/files?file_type=integration&page=1&size=50",
            "expected_size": 50,
        },
    ]

    async with aiohttp.ClientSession() as session:
        tasks = [
            test_endpoint(
                session, case["name"], case["url"], headers, case["expected_size"]
            )
            for case in test_cases
        ]
        await asyncio.gather(*tasks)


if __name__ == "__main__":
    start_time = time.time()
    print(f"开始测试: {datetime.now()}")
    asyncio.run(run_tests())
    end_time = time.time()
    print(f"测试完成: {datetime.now()}, 总耗时: {end_time - start_time:.2f}秒")
