import time
from uuid import uuid4

import aiohttp
import pytest
import pytest_asyncio

# Constants
BASE_URL = "http://localhost:8000"
# BASE_URL = "https://api-dev.gbase.ai"

HEADERS = {
    # 这里也可以换成 oauth 的 token，用来模拟用户在浏览器中使用时，调用接口的情况
    # "Authorization": "bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IlZ1a25jYmROeHFzb0U4Wlp6VFJCTCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.WzMKJ8ZNMc75QM7-OH7hQl-DjaoD6OINYlHdGJ7aNiDfZsJUBBaYAxycPFGXOWwFdv988h3n_xXZzovpq69KDUQmZTKS9OsKVIp_z8do1wQ_uHAZp7mqflpcEpNi0mpbw-9cdj1rISahbb0Z0gN7nYmEvI-MNg69uCtrJLQE3AqWl4mBoMhQv754LJdpIZRxY_kCqbi7JpGsZ68M6rtQVHxhwX-1TeAfS2vMLFNytcanoza72AeCWQL4qw2hK2bWKsvmlyD98884p6XMiy5JJbUqchBdCAe6sLwP1JgYx1eKX2rusP5kAXxkMiulhcbrfwTB_5iEO7U5eh1f2uHexA",
    "Authorization": "bearer ak-aCWvLTmtJLo4FnuFco5Ti0v105UHxMOFh9CqEkSlfbGdYEv4",
}

# Use a fixed user ID for all tests
user_id = "678dbc67-37e9-4892-a453-06cfe6b292cd"


# Fixture to create an aiohttp session for tests
@pytest_asyncio.fixture(scope="function")
async def http_session():
    async with aiohttp.ClientSession(headers=HEADERS) as session:
        yield session


# Helper function to check response
async def check_response(
    response: aiohttp.ClientResponse,
    expected_size: int | None = None,
    check_items: bool = True,
):
    assert response.status == 200, f"Expected status 200, got {response.status}"
    data = await response.json()
    assert "items" in data, "Response JSON does not contain 'items' key"
    assert "total" in data, "Response JSON does not contain 'total' key"
    assert "page" in data, "Response JSON does not contain 'page' key"
    assert "size" in data, "Response JSON does not contain 'size' key"

    if check_items:
        assert "items" in data, "Response JSON does not contain 'items' key"
        if expected_size is not None:
            assert (
                len(data["items"]) == expected_size
            ), f"Expected {expected_size} items, got {len(data['items'])}"

    return data


# Helper function to check response for non-paginated endpoints
async def check_single_response(response: aiohttp.ClientResponse, expected_fields=None):
    assert response.status == 200, f"Expected status 200, got {response.status}"
    data = await response.json()

    if expected_fields:
        for field in expected_fields:
            assert field in data, f"Response JSON does not contain '{field}' key"

    return data


# Helper function to check response for endpoints that return 204 No Content
async def check_no_content_response(response: aiohttp.ClientResponse):
    assert response.status == 204, f"Expected status 204, got {response.status}"


# --- Test Functions ---

# --- Plans API Tests ---


@pytest.mark.asyncio
async def test_plans_get_all(http_session: aiohttp.ClientSession):
    start_time = time.time()
    url = f"{BASE_URL}/plans?page=1&size=5&order_by=-created_at"
    expected_size = 5
    try:
        async with http_session.get(url) as response:
            data = await check_response(response, expected_size)
            elapsed_time = time.time() - start_time
            print(f"✅ Plans Get All API Passed (Duration: {elapsed_time:.2f}s)")
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Plans Get All API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_plans_create(http_session: aiohttp.ClientSession):
    start_time = time.time()
    url = f"{BASE_URL}/plans"
    unique_id = str(uuid4())
    payload = {
        "name": f"Test Plan {unique_id}",
        "price": 49.80,
        "description": "Test plan for API testing",
        "storage_limit": 51200000,
        "duration_length": 3,
        "duration_unit": "month",
        "bot_quota": 13,
        "message_quota": 2000,
        "message_quota_grant_type": "monthly",
        "storage_quota": 1000,
        "multi_modal_parsing_quota": 10,
        "web_page_quota": 10,
    }
    try:
        async with http_session.post(url, json=payload) as response:
            data = await check_single_response(
                response, ["id", "name", "price", "description"]
            )
            assert (
                data["name"] == payload["name"]
            ), f"Expected name {payload['name']}, got {data['name']}"
            elapsed_time = time.time() - start_time
            print(f"✅ Plans Create API Passed (Duration: {elapsed_time:.2f}s)")
            return data["id"]  # Return the created plan ID for subscription test
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Plans Create API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_plans_subscription_add(http_session: aiohttp.ClientSession):
    start_time = time.time()
    print(f"Using fixed user ID: {user_id}")

    # Get a plan ID to subscribe to
    url = f"{BASE_URL}/plans?page=1&size=1"
    plan_id = None
    try:
        async with http_session.get(url) as response:
            data = await check_response(response, 1)
            if data["items"]:
                plan_id = data["items"][0]["id"]
                print(f"Using plan ID: {plan_id}")
    except Exception as e:
        pytest.skip(f"Skipping test, failed to fetch plan: {e}")

    if not plan_id:
        pytest.skip("Skipping test, no plans found to test against.")

    # Now create a subscription
    url = f"{BASE_URL}/plans/subscription"
    payload = {
        "user_id": user_id,
        "plan_id": plan_id,
    }
    try:
        async with http_session.post(url, json=payload) as response:
            data = await check_single_response(
                response, ["id", "plan", "user", "is_cancelled"]
            )
            assert (
                data["plan"]["id"] == plan_id
            ), f"Expected plan_id {plan_id}, got {data['plan_id']}"
            assert (
                data["is_cancelled"] == False
            ), f"Expected is_cancelled 'False', got {data['is_cancelled']}"
            elapsed_time = time.time() - start_time
            print(
                f"✅ Plans Subscription Add API Passed (Duration: {elapsed_time:.2f}s)"
            )
            return data["id"]  # Return the subscription ID for cancel test
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Plans Subscription Add API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_plans_subscription_cancel(http_session: aiohttp.ClientSession):
    start_time = time.time()
    # First, create a subscription to cancel
    subscription_id = await test_plans_subscription_add(http_session)
    if not subscription_id:
        pytest.skip("Skipping test, failed to create subscription.")

    # Now cancel the subscription
    url = f"{BASE_URL}/plans/subscription/{subscription_id}/cancel"
    try:
        async with http_session.post(url) as response:
            await check_no_content_response(response)
            elapsed_time = time.time() - start_time
            print(
                f"✅ Plans Subscription Cancel API Passed (Duration: {elapsed_time:.2f}s)"
            )
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Plans Subscription Cancel API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


# --- Add-ons API Tests ---


@pytest.mark.asyncio
async def test_addons_create(http_session: aiohttp.ClientSession):
    start_time = time.time()
    url = f"{BASE_URL}/add-ons"
    unique_id = str(uuid4())
    payload = {
        "name": f"Test Add-on {unique_id}",
        "description": "Test add-on for API testing",
        "price": 0,
        "target_field": "message_quota",
        "duration_length": 3,
        "duration_unit": "month",
        "value": 4,
    }
    try:
        async with http_session.post(url, json=payload) as response:
            data = await check_single_response(response, ["id", "name", "description"])
            assert (
                data["name"] == payload["name"]
            ), f"Expected name {payload['name']}, got {data['name']}"
            elapsed_time = time.time() - start_time
            print(f"✅ Add-ons Create API Passed (Duration: {elapsed_time:.2f}s)")
            return data["id"]  # Return the created add-on ID for subscription test
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Add-ons Create API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_addons_create_valid_numeric(http_session: aiohttp.ClientSession):
    """测试创建有效的数值型 AddOn"""
    start_time = time.time()
    url = f"{BASE_URL}/add-ons"
    unique_id = str(uuid4())
    payload = {
        "name": f"Numeric Add-on {unique_id}",
        "description": "Numeric add-on for API testing",
        "price": 10.99,
        "target_field": "message_quota",
        "duration_length": 1,
        "duration_unit": "month",
        "value": 1000,  # 正整数值
    }
    try:
        async with http_session.post(url, json=payload) as response:
            assert (
                response.status == 200
            ), f"Expected status 200, got {response.status}. Response: {await response.text()}"
            data = await response.json()
            assert "id" in data, "Response JSON does not contain 'id' key"
            elapsed_time = time.time() - start_time
            print(
                f"✅ Add-ons Create Valid Numeric API Passed (Duration: {elapsed_time:.2f}s)"
            )
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Add-ons Create Valid Numeric API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_addons_create_valid_bot_quota(http_session: aiohttp.ClientSession):
    """测试创建有效的机器人配额 AddOn"""
    start_time = time.time()
    url = f"{BASE_URL}/add-ons"
    unique_id = str(uuid4())
    payload = {
        "name": f"Bot Quota Add-on {unique_id}",
        "description": "Bot quota add-on for API testing",
        "price": 20.99,
        "target_field": "bot_quota",
        "duration_length": 3,
        "duration_unit": "month",
        "value": 5,  # 增加5个机器人配额
    }
    try:
        async with http_session.post(url, json=payload) as response:
            assert (
                response.status == 200
            ), f"Expected status 200, got {response.status}. Response: {await response.text()}"
            data = await response.json()
            assert "id" in data, "Response JSON does not contain 'id' key"
            elapsed_time = time.time() - start_time
            print(
                f"✅ Add-ons Create Valid Bot Quota API Passed (Duration: {elapsed_time:.2f}s)"
            )
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Add-ons Create Valid Bot Quota API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_addons_create_valid_web_page_quota(http_session: aiohttp.ClientSession):
    """测试创建有效的网页配额 AddOn"""
    start_time = time.time()
    url = f"{BASE_URL}/add-ons"
    unique_id = str(uuid4())
    payload = {
        "name": f"Web Page Quota Add-on {unique_id}",
        "description": "Web page quota add-on for API testing",
        "price": 15.99,
        "target_field": "web_page_quota",
        "duration_length": 1,
        "duration_unit": "month",
        "value": 20,  # 增加20个网页配额
    }
    try:
        async with http_session.post(url, json=payload) as response:
            assert (
                response.status == 200
            ), f"Expected status 200, got {response.status}. Response: {await response.text()}"
            data = await response.json()
            assert "id" in data, "Response JSON does not contain 'id' key"
            elapsed_time = time.time() - start_time
            print(
                f"✅ Add-ons Create Valid Web Page Quota API Passed (Duration: {elapsed_time:.2f}s)"
            )
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Add-ons Create Valid Web Page Quota API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_addons_create_valid_multi_modal_parsing_quota(
    http_session: aiohttp.ClientSession,
):
    """测试创建有效的多模态解析配额 AddOn"""
    start_time = time.time()
    url = f"{BASE_URL}/add-ons"
    unique_id = str(uuid4())
    payload = {
        "name": f"Multi-Modal Parsing Quota Add-on {unique_id}",
        "description": "Multi-modal parsing quota add-on for API testing",
        "price": 25.99,
        "target_field": "multi_modal_parsing_quota",
        "duration_length": 1,
        "duration_unit": "month",
        "value": 10,  # 增加10个多模态解析配额
    }
    try:
        async with http_session.post(url, json=payload) as response:
            assert (
                response.status == 200
            ), f"Expected status 200, got {response.status}. Response: {await response.text()}"
            data = await response.json()
            assert "id" in data, "Response JSON does not contain 'id' key"
            elapsed_time = time.time() - start_time
            print(
                f"✅ Add-ons Create Valid Multi-Modal Parsing Quota API Passed (Duration: {elapsed_time:.2f}s)"
            )
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Add-ons Create Valid Multi-Modal Parsing Quota API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_addons_get_all(http_session: aiohttp.ClientSession):
    start_time = time.time()
    url = f"{BASE_URL}/add-ons?page=1&size=3&order_by=-created_at"

    expected_size = 3
    try:
        async with http_session.get(
            url,
        ) as response:
            data = await check_response(response, expected_size)
            elapsed_time = time.time() - start_time
            print(f"✅ Add-ons Get All API Passed (Duration: {elapsed_time:.2f}s)")
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Add-ons Get All API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_addons_create_invalid_negative_value(
    http_session: aiohttp.ClientSession,
):
    """测试创建无效的负数值 AddOn"""
    start_time = time.time()
    url = f"{BASE_URL}/add-ons"
    unique_id = str(uuid4())
    payload = {
        "name": f"Invalid Negative Value Add-on {unique_id}",
        "description": "Invalid add-on with negative value",
        "price": 10.99,
        "target_field": "message_quota",
        "duration_length": 1,
        "duration_unit": "month",
        "value": -100,  # 负数值，应该被拒绝
    }
    try:
        async with http_session.post(
            url, json=payload, ssl=True, allow_redirects=True
        ) as response:
            assert (
                response.status == 422
            ), f"Expected status 422, got {response.status}. Response: {await response.text()}"
            elapsed_time = time.time() - start_time
            print(
                f"✅ Add-ons Create Invalid Negative Value API Passed (Duration: {elapsed_time:.2f}s)"
            )
    except AssertionError as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Add-ons Create Invalid Negative Value API failed with assertion error: {e} (Duration: {elapsed_time:.2f}s)"
        )
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Add-ons Create Invalid Negative Value API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_addons_create_invalid_type_mismatch(http_session: aiohttp.ClientSession):
    """测试创建类型不匹配的 AddOn"""
    start_time = time.time()
    url = f"{BASE_URL}/add-ons"
    unique_id = str(uuid4())
    payload = {
        "name": f"Invalid Type Mismatch Add-on {unique_id}",
        "description": "Invalid add-on with type mismatch",
        "price": 10.99,
        "target_field": "message_quota",
        "duration_length": 1,
        "duration_unit": "month",
        "value": "not a number",  # 字符串而不是数字，应该被拒绝
    }
    try:
        async with http_session.post(url, json=payload) as response:
            assert (
                response.status == 422
            ), f"Expected status 422, got {response.status}. Response: {await response.text()}"
            elapsed_time = time.time() - start_time
            print(
                f"✅ Add-ons Create Invalid Type Mismatch API Passed (Duration: {elapsed_time:.2f}s)"
            )
    except AssertionError as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Add-ons Create Invalid Type Mismatch API failed with assertion error: {e} (Duration: {elapsed_time:.2f}s)"
        )
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Add-ons Create Invalid Type Mismatch API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_addons_create_invalid_zero_value(http_session: aiohttp.ClientSession):
    """测试创建无效的零值 AddOn"""
    start_time = time.time()
    url = f"{BASE_URL}/add-ons"
    unique_id = str(uuid4())
    payload = {
        "name": f"Invalid Zero Value Add-on {unique_id}",
        "description": "Invalid add-on with zero value",
        "price": 10.99,
        "target_field": "message_quota",
        "duration_length": 1,
        "duration_unit": "month",
        "value": 0,  # 零值，应该被拒绝
    }
    try:
        async with http_session.post(url, json=payload) as response:
            assert (
                response.status == 422
            ), f"Expected status 422, got {response.status}. Response: {await response.text()}"
            elapsed_time = time.time() - start_time
            print(
                f"✅ Add-ons Create Invalid Zero Value API Passed (Duration: {elapsed_time:.2f}s)"
            )
    except AssertionError as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Add-ons Create Invalid Zero Value API failed with assertion error: {e} (Duration: {elapsed_time:.2f}s)"
        )
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Add-ons Create Invalid Zero Value API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_addons_create_invalid_target_field(http_session: aiohttp.ClientSession):
    """测试创建无效的目标字段 AddOn"""
    start_time = time.time()
    url = f"{BASE_URL}/add-ons"
    unique_id = str(uuid4())
    payload = {
        "name": f"Invalid Target Field Add-on {unique_id}",
        "description": "Invalid add-on with non-existent target field",
        "price": 10.99,
        "target_field": "non_existent_field",  # 不存在的目标字段，应该被拒绝
        "duration_length": 1,
        "duration_unit": "month",
        "value": 100,
    }
    try:
        async with http_session.post(url, json=payload) as response:
            assert (
                response.status == 400 or response.status == 422
            ), f"Expected status 400 or 422, got {response.status}. Response: {await response.text()}"
            elapsed_time = time.time() - start_time
            print(
                f"✅ Add-ons Create Invalid Target Field API Passed (Duration: {elapsed_time:.2f}s)"
            )
    except AssertionError as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Add-ons Create Invalid Target Field API failed with assertion error: {e} (Duration: {elapsed_time:.2f}s)"
        )
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Add-ons Create Invalid Target Field API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_addons_subscription_add(http_session: aiohttp.ClientSession):
    start_time = time.time()
    # Get an add-on ID to subscribe to
    url = f"{BASE_URL}/add-ons?page=1&size=1"
    addon_id = None
    try:
        async with http_session.get(url) as response:
            data = await check_response(response, 1)
            if data["items"]:
                addon_id = data["items"][0]["id"]
                print(f"Using add-on ID: {addon_id}")
    except Exception as e:
        pytest.skip(f"Skipping test, failed to fetch add-on: {e}")

    if not addon_id:
        pytest.skip("Skipping test, no add-ons found to test against.")

    # Now create a subscription
    url = f"{BASE_URL}/add-ons/subscription"
    payload = {
        "user_id": user_id,
        "add_on_id": addon_id,
    }
    try:
        async with http_session.post(url, json=payload) as response:
            data = await check_single_response(response, ["id", "add_on", "user"])
            assert (
                data["add_on"]["id"] == addon_id
            ), f"Expected add_on_id {addon_id}, got {data['add_on_id']}"
            # The add-on subscription doesn't have a status field in the response
            elapsed_time = time.time() - start_time
            print(
                f"✅ Add-ons Subscription Add API Passed (Duration: {elapsed_time:.2f}s)"
            )
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Add-ons Subscription Add API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_addons_create_valid_multi_modal_parsing_quota(
    http_session: aiohttp.ClientSession,
):
    """测试创建有效的多模态解析配额 AddOn"""
    start_time = time.time()
    url = f"{BASE_URL}/add-ons"
    unique_id = str(uuid4())
    payload = {
        "name": f"Multi-Modal Parsing Quota Add-on {unique_id}",
        "description": "Multi-modal parsing quota add-on for API testing",
        "price": 25.99,
        "target_field": "multi_modal_parsing_quota",
        "duration_length": 10,
        "duration_unit": "day",
        "value": 10,  # 增加10个多模态解析配额
    }
    try:
        # 添加 allow_redirects=True 确保重定向时保留认证头
        async with http_session.post(
            url, json=payload, allow_redirects=True
        ) as response:
            assert (
                response.status == 200
            ), f"Expected status 200, got {response.status}. Response: {await response.text()}"
            data = await response.json()
            assert "id" in data, "Response JSON does not contain 'id' key"
            elapsed_time = time.time() - start_time
            print(
                f"✅ Add-ons Create Valid Multi-Modal Parsing Quota API Passed (Duration: {elapsed_time:.2f}s)"
            )
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Add-ons Create Valid Multi-Modal Parsing Quota API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


# --- Users API Tests ---


@pytest.mark.asyncio
async def test_enterprise_plan_list_with_super_admin_api_key():
    """测试使用超级管理员 API Key 访问企业用户列表"""
    start_time = time.time()
    # 使用本地服务器
    url = "http://localhost:8000/user/enterprise-plan-list?size=10&page=1"
    expected_size = 10

    # 使用超级管理员 API Key
    headers = {
        "Authorization": "bearer ak-aCWvLTmtJLo4FnuFco5Ti0v105UHxMOFh9CqEkSlfbGdYEv4"
    }

    try:
        async with aiohttp.ClientSession(headers=headers) as session:
            async with session.get(url) as response:
                data = await check_response(response, expected_size)
                elapsed_time = time.time() - start_time
                print(
                    f"✅ Enterprise Plan List API (Super Admin API Key) Passed (Duration: {elapsed_time:.2f}s)"
                )

                # 验证返回的用户都是企业用户
                for user in data["items"]:
                    assert (
                        user.get("corporate") is True
                    ), f"Expected corporate=True, got {user.get('corporate')}"

                return data
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Enterprise Plan List API (Super Admin API Key) failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_enterprise_plan_list_with_email_filter():
    """测试使用电子邮件过滤器访问企业用户列表"""
    start_time = time.time()
    # 使用一个可能存在的电子邮件域名进行过滤
    email_filter = "example.com"
    url = f"http://localhost:8000/user/enterprise-plan-list?size=10&page=1&email={email_filter}"

    # 使用超级管理员 API Key
    headers = {
        "Authorization": "bearer ak-aCWvLTmtJLo4FnuFco5Ti0v105UHxMOFh9CqEkSlfbGdYEv4"
    }

    try:
        async with aiohttp.ClientSession(headers=headers) as session:
            async with session.get(url) as response:
                data = await check_response(response)
                elapsed_time = time.time() - start_time
                print(
                    f"✅ Enterprise Plan List API (Email Filter) Passed (Duration: {elapsed_time:.2f}s)"
                )

                # 验证所有返回的用户都是企业用户
                for user in data["items"]:
                    assert (
                        user.get("corporate") is True
                    ), f"Expected corporate=True, got {user.get('corporate')}"

                # 验证所有返回的用户电子邮件都包含过滤器（如果有电子邮件字段）
                for user in data["items"]:
                    if "email" in user and user["email"]:
                        assert (
                            email_filter in user["email"].lower()
                        ), f"Email {user['email']} does not contain filter {email_filter}"

                return data
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Enterprise Plan List API (Email Filter) failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_enterprise_plan_list_pagination():
    """测试企业用户列表的分页功能"""
    start_time = time.time()

    # 获取第一页数据
    url_page1 = "http://localhost:8000/user/enterprise-plan-list?size=5&page=1"
    # 获取第二页数据
    url_page2 = "http://localhost:8000/user/enterprise-plan-list?size=5&page=2"

    # 使用超级管理员 API Key
    headers = {
        "Authorization": "bearer ak-aCWvLTmtJLo4FnuFco5Ti0v105UHxMOFh9CqEkSlfbGdYEv4"
    }

    try:
        # 获取第一页
        async with aiohttp.ClientSession(headers=headers) as session:
            # 获取第一页
            async with session.get(url_page1) as response1:
                data1 = await check_response(response1, 5)

                # 获取第二页
                async with session.get(url_page2) as response2:
                    data2 = await check_response(response2, 5)

                # 验证两页数据不同
                page1_ids = [item["id"] for item in data1["items"]]
                page2_ids = [item["id"] for item in data2["items"]]

                # 检查两页的用户 ID 是否有重复
                common_ids = set(page1_ids).intersection(set(page2_ids))
                assert (
                    len(common_ids) == 0
                ), f"Found {len(common_ids)} duplicate user IDs between pages"

                # 验证分页信息正确
                assert data1["page"] == 1, f"Expected page=1, got {data1['page']}"
                assert data2["page"] == 2, f"Expected page=2, got {data2['page']}"
                assert data1["size"] == 5, f"Expected size=5, got {data1['size']}"
                assert data2["size"] == 5, f"Expected size=5, got {data2['size']}"

                elapsed_time = time.time() - start_time
                print(
                    f"✅ Enterprise Plan List API (Pagination) Passed (Duration: {elapsed_time:.2f}s)"
                )
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Enterprise Plan List API (Pagination) failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_enterprise_plan_list_with_regular_api_key():
    """测试使用非超级管理员 API Key 访问企业用户列表（应该失败）"""
    start_time = time.time()
    url = "http://localhost:8000/user/enterprise-plan-list?size=10&page=1"

    # 使用一个普通的 API Key（非超级管理员）
    headers = {
        "Authorization": "bearer ak-1IBgnYBJ6O1Kz5w3K31joiG6iU47qGUYyzkiUbplnI4ZDEih"
    }

    try:
        async with aiohttp.ClientSession(headers=headers) as session:
            async with session.get(url) as response:
                # 应该返回 403 Forbidden
                assert (
                    response.status == 403
                ), f"Expected status 403, got {response.status}. Response: {await response.text()}"
                elapsed_time = time.time() - start_time
                print(
                    f"✅ Enterprise Plan List API (Regular API Key) Correctly Rejected (Duration: {elapsed_time:.2f}s)"
                )
    except AssertionError as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Enterprise Plan List API (Regular API Key) test failed with assertion error: {e} (Duration: {elapsed_time:.2f}s)"
        )
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Enterprise Plan List API (Regular API Key) test failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_enterprise_plan_list_with_no_auth():
    """测试无认证访问企业用户列表（应该失败）"""
    start_time = time.time()
    url = "http://localhost:8000/user/enterprise-plan-list?size=10&page=1"

    try:
        # 不提供任何认证信息
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                # 应该返回 401 Unauthorized
                assert (
                    response.status == 401
                ), f"Expected status 401, got {response.status}. Response: {await response.text()}"
                elapsed_time = time.time() - start_time
                print(
                    f"✅ Enterprise Plan List API (No Auth) Correctly Rejected (Duration: {elapsed_time:.2f}s)"
                )
    except AssertionError as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Enterprise Plan List API (No Auth) test failed with assertion error: {e} (Duration: {elapsed_time:.2f}s)"
        )
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Enterprise Plan List API (No Auth) test failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_enterprise_plan_list_with_oauth_token(
    http_session: aiohttp.ClientSession,
):
    """测试使用 OAuth token 访问企业用户列表"""
    start_time = time.time()
    url = "http://localhost:8000/user/enterprise-plan-list?size=10&page=1"
    expected_size = 10

    try:
        async with http_session.get(url) as response:
            data = await check_response(response, expected_size)
            elapsed_time = time.time() - start_time
            print(
                f"✅ Enterprise Plan List API (OAuth Token) Passed (Duration: {elapsed_time:.2f}s)"
            )

            # 验证返回的用户都是企业用户
            for user in data["items"]:
                assert (
                    user.get("corporate") is True
                ), f"Expected corporate=True, got {user.get('corporate')}"

            return data
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Enterprise Plan List API (OAuth Token) failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_admin_users_get_all(http_session: aiohttp.ClientSession):
    start_time = time.time()
    url = f"{BASE_URL}/admin/users?page=1&size=3&order_by=-updated_at"
    expected_size = 3
    try:
        async with http_session.get(url) as response:
            data = await check_response(response, expected_size)
            elapsed_time = time.time() - start_time
            print(f"✅ Admin Users Get All API Passed (Duration: {elapsed_time:.2f}s)")
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Admin Users Get All API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_admin_users_get_by_id(http_session: aiohttp.ClientSession):
    start_time = time.time()
    url = f"{BASE_URL}/admin/users/{user_id}"
    try:
        async with http_session.get(url) as response:
            data = await check_single_response(response, ["user_id", "email", "name"])
            assert data["user_id"].startswith(
                "google-oauth2"
            ), f"Expected user_id to start with 'google-oauth2', got {data['user_id']}"
            elapsed_time = time.time() - start_time
            print(
                f"✅ Admin Users Get By ID API Passed (Duration: {elapsed_time:.2f}s)"
            )
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Admin Users Get By ID API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_users_me(http_session: aiohttp.ClientSession):
    """
    测试 /users/me 接口

    现在该接口支持 API 密钥认证，我们使用测试中的 API 密钥进行测试。
    """
    start_time = time.time()
    url = f"{BASE_URL}/users/me"
    try:
        async with http_session.get(url) as response:
            # 检查响应状态码
            assert (
                response.status == 200
            ), f"Expected status 200, got {response.status}. Response: {await response.text()}"

            # 检查响应内容
            data = await response.json()
            assert "user_id" in data, "Response JSON does not contain 'user_id' key"
            assert "email" in data, "Response JSON does not contain 'email' key"
            assert "name" in data, "Response JSON does not contain 'name' key"

            elapsed_time = time.time() - start_time
            print(f"✅ Users Me API Passed (Duration: {elapsed_time:.2f}s)")
            return data
    except AssertionError as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Users Me API failed with assertion error: {e} (Duration: {elapsed_time:.2f}s)"
        )
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Users Me API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_robots_api(http_session: aiohttp.ClientSession):
    start_time = time.time()
    url = f"{BASE_URL}/robots?page=1&size=3"
    expected_size = 3
    try:
        async with http_session.get(url) as response:
            await check_response(response, expected_size)
            elapsed_time = time.time() - start_time
            print(f"✅ Robots API Passed (Duration: {elapsed_time:.2f}s)")
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Robots API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_datasets_api(http_session: aiohttp.ClientSession):
    start_time = time.time()
    url = f"{BASE_URL}/datasets?page=1&size=10"
    expected_size = 10
    try:
        async with http_session.get(url) as response:
            await check_response(response, expected_size)
            elapsed_time = time.time() - start_time
            print(f"✅ Datasets API Passed (Duration: {elapsed_time:.2f}s)")
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Datasets API failed with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_dataset_files_api_file(http_session: aiohttp.ClientSession):
    start_time = time.time()
    # --- Dynamically get a dataset ID ---
    dataset_id = None
    try:
        async with http_session.get(
            f"{BASE_URL}/datasets?page=1&size=1"
        ) as list_response:
            if list_response.status == 200:
                data = await list_response.json()
                if data.get("items"):
                    dataset_id = data["items"][0].get("id")
    except Exception as e:
        pytest.skip(f"Skipping test, failed to fetch initial dataset list: {e}")

    if not dataset_id:
        pytest.skip("Skipping test, no datasets found to test against.")
    # --- End dynamic ID fetching ---

    url = f"{BASE_URL}/datasets/{dataset_id}/files?file_type=file&page=1&size=5"
    expected_size = None  # Relax the size check
    try:
        async with http_session.get(url) as response:
            # Check only for 200 and basic structure, not specific size
            await check_response(response, expected_size)
            elapsed_time = time.time() - start_time
            print(f"✅ Dataset Files API - File Passed (Duration: {elapsed_time:.2f}s)")
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Dataset Files API - File failed for dataset {dataset_id} with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_dataset_files_api_integration(http_session: aiohttp.ClientSession):
    start_time = time.time()
    # --- Dynamically get a dataset ID ---
    dataset_id = None
    try:
        async with http_session.get(
            f"{BASE_URL}/datasets?page=1&size=1"
        ) as list_response:
            if list_response.status == 200:
                data = await list_response.json()
                if data.get("items"):
                    dataset_id = data["items"][0].get("id")
    except Exception as e:
        pytest.skip(f"Skipping test, failed to fetch initial dataset list: {e}")

    if not dataset_id:
        pytest.skip("Skipping test, no datasets found to test against.")
    # --- End dynamic ID fetching ---

    url = f"{BASE_URL}/datasets/{dataset_id}/files?file_type=integration&page=1&size=50"
    expected_size = None  # Relax the size check
    try:
        async with http_session.get(url) as response:
            # Check only for 200 and basic structure, not specific size
            await check_response(response, expected_size)
            elapsed_time = time.time() - start_time
            print(
                f"✅ Dataset Files API - Integration Passed (Duration: {elapsed_time:.2f}s)"
            )
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Dataset Files API - Integration failed for dataset {dataset_id} with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )


@pytest.mark.asyncio
async def test_robots_datasets_api(http_session: aiohttp.ClientSession):
    start_time = time.time()
    # --- Dynamically get a robot ID ---
    robot_id = None
    try:
        async with http_session.get(
            f"{BASE_URL}/robots?page=1&size=1"
        ) as list_response:
            if list_response.status == 200:
                data = await list_response.json()
                if data.get("items"):
                    robot_id = data["items"][0].get("id")
    except Exception as e:
        pytest.skip(f"Skipping test, failed to fetch initial robot list: {e}")

    if not robot_id:
        pytest.skip("Skipping test, no robots found to test against.")
    # --- End dynamic ID fetching ---

    url = f"{BASE_URL}/robots/{robot_id}/datasets?page=1&size=1000"
    expected_size = None  # Relax the size check
    try:
        async with http_session.get(url) as response:
            # Check only for 200 and basic structure, not specific size
            await check_response(response, expected_size)
            elapsed_time = time.time() - start_time
            print(f"✅ Robots Datasets API Passed (Duration: {elapsed_time:.2f}s)")
    except Exception as e:
        elapsed_time = time.time() - start_time
        pytest.fail(
            f"Robots Datasets API failed for robot {robot_id} with exception: {e} (Duration: {elapsed_time:.2f}s)"
        )
